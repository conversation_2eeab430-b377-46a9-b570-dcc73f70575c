<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppBroadcastMapper">

    <resultMap type="AppBroadcast" id="AppBroadcastResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="userId" column="user_id"/>
        <result property="content" column="content"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectAppBroadcastVo">
        select id, create_time, user_id, content, is_del from app_broadcast
    </sql>

    <select id="selectAppBroadcastList" parameterType="AppBroadcast" resultMap="AppBroadcastResult">
        <include refid="selectAppBroadcastVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="content != null  and content != ''">and content = #{content}</if>
            <if test="isDel != null ">and is_del = #{isDel}</if>
        </where>
    </select>

    <select id="selectAppBroadcastById" parameterType="Long" resultMap="AppBroadcastResult">
        <include refid="selectAppBroadcastVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppBroadcast" parameterType="AppBroadcast">
        insert into app_broadcast
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="content != null">content,</if>
            <if test="isDel != null">is_del,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="content != null">#{content},</if>
            <if test="isDel != null">#{isDel},</if>
        </trim>
    </insert>

    <update id="updateAppBroadcast" parameterType="AppBroadcast">
        update app_broadcast
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteAppBroadcastById" parameterType="Long">
        update app_broadcast set is_del=true where id = #{id}
    </update>

    <delete id="deleteAppBroadcastByIds" parameterType="String">
        delete from app_broadcast where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="getBroadcastList" resultType="com.hzy.core.model.vo.app.AppBroadcastVo">
        select
        id as broadcastId,
        create_time as broadcastCreateTime,
        user_id as broadcastUserId,
        content as broadcastContent
        from app_broadcast
        where is_del=false
        order by id desc
    </select>
</mapper>