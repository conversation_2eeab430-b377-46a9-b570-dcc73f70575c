<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.GameRecordMapper">

    <select id="selectGameRecordList" parameterType="com.hzy.core.model.dto.admin.GameRecordDTO"
            resultType="com.hzy.core.model.vo.admin.GameRecordVo">
        SELECT DISTINCT gr.id as id,
        gr.pool_id as poolId,
        gc.id as gameId,
        gr.user_id as userId,
        u.recode_code as recodeCode,
        u.nick_name as username,
        u.phone as phone,
        gr.gift_info as giftInfo,
        gr.created_time as createdTime,
        gc.name as gameName,
        gup.name as poolName,
        gr.put_amount as putAmount,
        gr.get_amount as getAmount,
        amut.device_id as userLoginDeviceId,
        amut.user_login_ip as userLoginIp,
        gr.get_amount * 0.84 as totalGetAmount84,
        gr.round_num ,
        (gr.get_amount * 0.84) - gr.put_amount as profitLoss,
        (IFNULL(gr.get_amount, 0) / IFNULL(gr.put_amount, 1)) as profitLossRatio
        FROM game_record gr
        JOIN game_upgrade_pool gup ON gr.pool_id = gup.id
        JOIN game_config gc ON gup.game_id = gc.id
        JOIN app_user u ON u.id = gr.user_id
        JOIN app_mzls_user_tj amut on amut.user_id = u.id
        <where>
            <if test="userId != null">
                AND u.id = #{userId}
            </if>
            <if test="poolId != null">
                AND gup.id = #{poolId}
            </if>
            <if test="gameId != null">
                AND gc.id = #{gameId}
            </if>
            <if test="username != null">
                AND u.nick_name like #{username}
            </if>
            <if test="phone != null">
                AND u.phone = #{phone}
            </if>
            <if test="giftId != null">
                AND JSON_CONTAINS(gr.gift_info, CONCAT('{"giftId":', #{giftId}, '}'))
            </if>
            <if test="queryBeginTime != null and queryBeginTime != ''"><!-- 开始时间检索 -->
                AND date_format(gr.updated_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
            </if>
            <if test="queryEndTime != null and queryEndTime != ''"><!-- 结束时间检索 -->
                AND date_format(gr.updated_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
            </if>
        </where>
        <if test="sort == 0">
            ORDER BY profitLoss ASC
        </if>
        <if test="sort == 1">
            ORDER BY profitLoss DESC
        </if>
        <if test="sort == 2">
            ORDER BY profitLossRatio ASC
        </if>
        <if test="sort == 3">
            ORDER BY profitLossRatio DESC
        </if>
        <if test="sort ==null">
            order by gr.updated_time desc, gr.created_time desc
        </if>
    </select>


    <select id="selectGameRecordAmountTotal" parameterType="com.hzy.core.model.dto.admin.GameRecordDTO"
            resultType="com.hzy.core.model.vo.admin.GameRecordAmountTotalVo">
        SELECT SUM(IFNULL(gr.put_amount, 0)) AS putAmountTotal,
        SUM(IFNULL(gr.get_amount, 0)) AS getAmountTotal
        FROM game_record gr
        JOIN game_upgrade_pool gup ON gr.pool_id = gup.id
        JOIN game_config gc ON gup.game_id = gc.id
        JOIN app_user u ON u.id = gr.user_id
        <where>
            <if test="userId != null">
                AND u.id = #{userId}
            </if>
            <if test="poolId != null">
                AND gup.id = #{poolId}
            </if>
            <if test="gameId != null">
                AND gc.id = #{gameId}
            </if>
            <if test="username != null">
                AND u.nick_name like #{username}
            </if>
            <if test="phone != null">
                AND u.phone = #{phone}
            </if>
            <if test="giftId != null">
                AND JSON_CONTAINS(gr.gift_info, CONCAT('{"giftId":', #{giftId}, '}'))
            </if>
        </where>
    </select>

    <select id="selectUserCurrentDayPutAmount" resultType="java.math.BigDecimal">
        select sum(gr.put_amount) as put_amount
        from game_record gr
        left join game_upgrade_pool gup on gr.pool_id = gup.id and gup.deleted = false
        where gr.created_time >= CURDATE() AND gr.created_time &lt; (CURDATE() + INTERVAL 1 DAY)
        and gr.user_id = #{userId}
        and gup.game_id = #{gameId}
    </select>
    <select id="selectGameRecordListByUser" resultType="com.hzy.core.model.vo.admin.GameRecordVo"
            parameterType="java.lang.Integer">
        SELECT gr.id as id,
        gr.pool_id as poolId,
        gc.id as gameId,
        gr.user_id as userId,
        u.recode_code as recodeCode,
        u.nick_name as username,
        u.phone as phone,
        gr.gift_info as giftInfo,
        gr.put_amount as putAmount,
        gr.get_amount as getAmount,
        gr.created_time as createdTime,
        gc.name as gameName,
        gup.name as poolName
        FROM game_record gr
        JOIN game_upgrade_pool gup ON gr.pool_id = gup.id
        JOIN game_config gc ON gup.game_id = gc.id
        JOIN app_user u ON u.id = gr.user_id
        where
        gup.game_id = #{gameId}
        order by gr.updated_time desc, gr.created_time desc
    </select>


    <select id="selectChatRoomList" resultType="com.hzy.core.model.vo.admin.ChatRoomGameRecordVo"
            parameterType="com.hzy.core.model.dto.admin.ChatRoomGameRecodDTO">
        SELECT cr.id AS chatRoomId,
        gup.game_id AS gameId,
        gc.`name` AS gameName,
        cr.name AS chatRoomName,
        u.nick_name AS nickName,
        IFNULL(SUM(gr.put_amount), 0) AS totalPutAmount,
        IFNULL(SUM(gr.get_amount), 0) AS totalGetAmount,
        IFNULL(SUM(gr.get_amount), 0) * 0.84 AS totalPutAmount84,
        gr.created_time AS createTime,
        gr.round_num,
        gr.updated_time AS updateTime
        FROM app_chat_room cr
        LEFT JOIN game_record gr ON cr.id = gr.chat_room_id AND gr.deleted = 0
        LEFT JOIN app_user u ON u.id = cr.hall_owner_user_id
        INNER JOIN game_upgrade_pool gup ON gup.id = gr.pool_id
        LEFT JOIN game_config gc ON gup.game_id = gc.id
        WHERE cr.is_del = 0
        <if test="gameId != null">
            AND gc.id = #{gameId}
        </if>
        <if test="chatRoomId != null">
            AND cr.id = #{chatRoomId}
        </if>
        <if test="chatRoomName != null">
            AND cr.name = #{chatRoomName}
        </if>
        <if test="userId != null">
            AND u.id = #{userId}
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''"><!-- 开始时间检索 -->
            AND date_format(gr.updated_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''"><!-- 结束时间检索 -->
            AND date_format(gr.updated_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        GROUP BY cr.id, cr.name, gup.game_id
    </select>

    <select id="selectChatRoomList2" resultType="com.hzy.core.model.vo.admin.ChatRoomGameRecordVo"
            parameterType="com.hzy.core.model.dto.admin.ChatRoomGameRecodDTO">
        SELECT cr.id AS chatRoomId,
        gup.game_id AS gameId,
        gc.`name` AS gameName,
        cr.name AS chatRoomName,
        u.nick_name AS nickName,
        IFNULL(SUM(gr.put_amount), 0) AS totalPutAmount,
        IFNULL(SUM(gr.get_amount), 0) AS totalGetAmount,
        IFNULL(SUM(gr.get_amount), 0) * 0.85 AS totalPutAmount85
        gr.created_time AS createTime,
        gr.updated_time AS updateTime
        FROM app_chat_room cr
        LEFT JOIN game_record gr ON cr.id = gr.chat_room_id AND gr.deleted = 0
        LEFT JOIN app_user u ON u.id = cr.hall_owner_user_id
        INNER JOIN game_upgrade_pool gup ON gup.id = gr.pool_id
        LEFT JOIN game_config gc ON gup.game_id = gc.id
        WHERE cr.is_del = 0
        <if test="gameId != null">
            AND gc.id = #{gameId}
        </if>
        <if test="chatRoomId != null">
            AND cr.id = #{chatRoomId}
        </if>
        <if test="chatRoomName != null">
            AND cr.name = #{chatRoomName}
        </if>
        <if test="userId != null">
            AND u.id = #{userId}
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''"><!-- 开始时间检索 -->
            AND date_format(gr.updated_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''"><!-- 结束时间检索 -->
            AND date_format(gr.updated_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        GROUP BY cr.id, cr.name, gup.game_id
    </select>

    <select id="selectChatRoomList1" resultType="com.hzy.core.model.vo.admin.ChatRoomGameRecordVo"
            parameterType="com.hzy.core.model.dto.admin.ChatRoomGameRecodDTO">
        SELECT
        cr.id AS chatRoomId,
        gup.game_id AS gameId,
        gc.`name` AS gameName,
        cr.name AS chatRoomName,
        u.nick_name AS nickName,
        IFNULL(SUM(gr.put_amount), 0) AS totalPutAmount,
        IFNULL(SUM(gr.get_amount), 0) AS totalGetAmount,
        IFNULL(SUM(gr.get_amount), 0) * 0.84 AS totalGetAmount84,
        (IFNULL(SUM(gr.get_amount), 0) * 0.84) - IFNULL(SUM(gr.put_amount), 0) AS profitLoss,
        (IFNULL(SUM(gr.get_amount), 0) / SUM(gr.put_amount)) AS profitLossRatio,
        gr.created_time AS createTime,
        gr.round_num,
        gr.updated_time AS updateTime
        FROM app_chat_room cr
        LEFT JOIN game_record gr ON cr.id = gr.chat_room_id AND gr.deleted = 0
        LEFT JOIN app_user u ON u.id = cr.hall_owner_user_id
        INNER JOIN game_upgrade_pool gup ON gup.id = gr.pool_id
        LEFT JOIN game_config gc ON gup.game_id = gc.id
        WHERE cr.is_del = 0
        <if test="gameId != null">
            AND gc.id = #{gameId}
        </if>
        <if test="chatRoomId != null">
            AND cr.id = #{chatRoomId}
        </if>
        <if test="chatRoomName != null">
            AND cr.name = #{chatRoomName}
        </if>
        <if test="userId != null">
            AND u.id = #{userId}
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''"><!-- 开始时间检索 -->
            AND date_format(gr.updated_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''"><!-- 结束时间检索 -->
            AND date_format(gr.updated_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>

        GROUP BY cr.id, cr.name

        <if test="sort == 1">
            ORDER BY totalPutAmount ASC
        </if>
        <if test="sort == 2">
            ORDER BY totalPutAmount DESC
        </if>
        <if test="sort == 3">
            ORDER BY totalGetAmount ASC
        </if>
        <if test="sort == 4">
            ORDER BY totalGetAmount DESC
        </if>
        <if test="sort == 5">
            ORDER BY totalGetAmount84 ASC
        </if>
        <if test="sort == 6">
            ORDER BY totalGetAmount84 DESC
        </if>
        <if test="sort == 7">
            ORDER BY profitLoss ASC
        </if>
        <if test="sort == 8">
            ORDER BY profitLoss DESC
        </if>
        <if test="sort == 9">
            ORDER BY profitLossRatio ASC
        </if>
        <if test="sort == 10">
            ORDER BY profitLossRatio DESC
        </if>
        <if test="sort == 11">
            ORDER BY updateTime ASC
        </if>
        <if test="sort == 12">
            ORDER BY updateTime DESC
        </if>
        <if test="sort == null">
            ORDER BY updateTime DESC
        </if>
    </select>
    <select id="selectGameRecordList1" resultType="com.hzy.core.model.vo.admin.GameRecordTotalVo">
        SELECT
        IFNULL(SUM(gr.put_amount), 0) AS totalPutAmount,
        IFNULL(SUM(gr.get_amount), 0) AS totalGetAmount,
        IFNULL(SUM(gr.get_amount), 0) * 0.84 AS totalGetAmount84,
        (IFNULL(SUM(gr.get_amount), 0) * 0.84) - IFNULL(SUM(gr.put_amount), 0) AS totalProfitLoss,
        (IFNULL(SUM(gr.get_amount), 0) / SUM(gr.put_amount)) AS totalProfitLossRatio
        FROM game_record gr
        JOIN game_upgrade_pool gup ON gr.pool_id = gup.id
        JOIN game_config gc ON gup.game_id = gc.id
        JOIN app_user u ON u.id = gr.user_id
        <where>
            <if test="userId != null">
                AND u.id = #{userId}
            </if>
            <if test="poolId != null">
                AND gup.id = #{poolId}
            </if>
            <if test="gameId != null">
                AND gc.id = #{gameId}
            </if>
            <if test="username != null">
                AND u.nick_name like #{username}
            </if>
            <if test="phone != null">
                AND u.phone = #{phone}
            </if>
            <if test="giftId != null">
                AND JSON_CONTAINS(gr.gift_info, CONCAT('{"giftId":', #{giftId}, '}'))
            </if>
        </where>
        <if test="sort == 0">
            ORDER BY totalProfitLoss ASC
        </if>
        <if test="sort == 1">
            ORDER BY totalProfitLoss DESC
        </if>
        <if test="sort == 2">
            ORDER BY totalProfitLossRatio ASC
        </if>
        <if test="sort == 3">
            ORDER BY totalProfitLossRatio DESC
        </if>
    </select>
</mapper>