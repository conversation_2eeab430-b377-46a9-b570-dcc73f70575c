<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.SysLicenseManagementMapper">

    <select id="getLicenseList" resultType="com.hzy.core.entity.LicenseManagementEntity">
        SELECT
            acr.id,
            acr.third_id AS licenseId,
            acr.type AS licenseType,
            au.nick_name as bindNickName,
            acr.`status` as licenseStatus,
            acr.update_time as updatedTime,
            acr.create_time as createdTime,
            agm.guild_id as guildId,
            acr.license_name as licenseName
        FROM
            app_guild_member agm
                JOIN app_chat_room acr ON acr.hall_owner_user_id = agm.user_id
                JOIN app_user au ON au.id = acr.hall_owner_user_id
        <where>
            <if test="guildIds != null and guildIds.size() > 0">
                agm.guild_id IN
                <foreach collection="guildIds" item="guildId" separator="," open="(" close=")">
                    #{guildId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getLicenseListByGuidlIdOrAdmin" resultType="com.hzy.core.entity.LicenseManagementEntity">
        select *
        from sys_guild_license
        <where>
            <if test="guildIds != null and guildIds.size() > 0">
                guild_id IN
                <foreach collection="guildIds" item="guildId" separator="," open="(" close=")">
                    #{guildId}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>
</mapper>