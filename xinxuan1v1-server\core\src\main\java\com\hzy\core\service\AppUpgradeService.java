package com.hzy.core.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzy.core.entity.AppUpgrade;
import com.hzy.core.mapper.AppUpgradeMapper;
import com.hzy.core.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.nio.file.Path;

/**
 * App升级Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-19
 */
@Slf4j
@Service
public class AppUpgradeService extends ServiceImpl<AppUpgradeMapper, AppUpgrade> {
    @Autowired
    private AppUpgradeMapper appUpgradeMapper;

    /**
     * 查询App升级
     *
     * @param id App升级主键
     * @return App升级
     */
    public AppUpgrade selectAppUpgradeById(Long id) {
        return appUpgradeMapper.selectAppUpgradeById(id);
    }

    /**
     * 查询App升级列表
     *
     * @return App升级
     */
    public List<AppUpgrade> selectAppUpgradeList() {
        return appUpgradeMapper.selectAppUpgradeList();
    }

    /**
     * 新增App升级
     *
     * @param appUpgrade App升级
     * @return 结果
     */
    public int insertAppUpgrade(AppUpgrade appUpgrade) {
        appUpgrade.setCreateTime(DateUtils.getNowDate());
        extracted(appUpgrade);
        // 更新ios.plist文件
        if (appUpgrade.getType().equals("ios")) {
            updateIosPlist(appUpgrade.getUrl(), appUpgrade.getVersion());
        }
        return appUpgradeMapper.insertAppUpgrade(appUpgrade);
    }

    /**
     * 替换oss地址,改为自有域名
     *
     * @param appUpgrade 应用程序升级
     */
    private static void extracted(AppUpgrade appUpgrade) {
        // 如果是https，替换为http
        if (appUpgrade.getUrl().startsWith("https://")) {
            appUpgrade.setUrl(appUpgrade.getUrl().replace("https://", "http://"));
        }


        if (appUpgrade.getUrl().contains("chat-bucket-vv.oss-cn-beijing.aliyuncs.com")) {
            appUpgrade.setUrl(appUpgrade.getUrl()
                    .replace("chat-bucket-vv.oss-cn-beijing.aliyuncs.com", "dl.vvparty.cn"));
        }
    }

    /**
     * 更新iOS,重写生成plist文件
     *
     * @param downloadUrl IPA下载地址
     * @param version     版本
     */
    private void updateIosPlist(String downloadUrl,String version) {
        String plistContent = String.format("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<!DOCTYPE plist PUBLIC \"-//Apple//DTD PLIST 1.0//EN\" \"http://www.apple.com/DTDs/PropertyList-1.0.dtd\">\n" +
                "<plist version=\"1.0\">\n" +
                "<dict>\n" +
                "    <key>items</key>\n" +
                "    <array>\n" +
                "        <dict>\n" +
                "            <key>assets</key>\n" +
                "            <array>\n" +
                "                <dict>\n" +
                "                    <key>kind</key>\n" +
                "                    <string>software-package</string>\n" +
                "                    <key>url</key>\n" +
                "                    <string>%s</string>\n" +
                "                </dict>\n" +
                "            </array>\n" +
                "            <key>metadata</key>\n" +
                "            <dict>\n" +
                "                <key>bundle-identifier</key>\n" +
                "                <string>com.app.jishou</string>\n" +
                "                <key>bundle-version</key>\n" +
                "                <string>%s</string>\n" +
                "                <key>kind</key>\n" +
                "                <string>software</string>\n" +
                "                <key>title</key>\n" +
                "                <string>奶芙</string>\n" +
                "            </dict>\n" +
                "        </dict>\n" +
                "    </array>\n" +
                "</dict>\n" +
                "</plist>", downloadUrl,version);

        try {
            // 确保目录存在
            Path directory = Paths.get("/opt/i.yunduoyy.com");
            Files.createDirectories(directory);
            
            // 写入文件
            Path filePath = directory.resolve("ios.plist");
            Files.writeString(filePath, plistContent, StandardCharsets.UTF_8);
        } catch (IOException e) {
            // 处理异常
            log.error("写入plist文件失败", e);
            throw new RuntimeException("写入plist文件失败", e);
        }
    }

    /**
     * 修改App升级
     *
     * @param appUpgrade App升级
     * @return 结果
     */
    public int updateAppUpgrade(AppUpgrade appUpgrade) {
        appUpgrade.setUpdateTime(DateUtils.getNowDate());
        extracted(appUpgrade);
        // 更新ios.plist文件
        if (appUpgrade.getType().equals("ios")) {
            updateIosPlist(appUpgrade.getUrl(), appUpgrade.getVersion());
        }
        return appUpgradeMapper.updateAppUpgrade(appUpgrade);
    }

    /**
     * 批量删除App升级
     *
     * @param ids 需要删除的App升级主键
     * @return 结果
     */
    public int deleteAppUpgradeByIds(Long[] ids) {
        return appUpgradeMapper.deleteAppUpgradeByIds(ids);
    }

    /**
     * 删除App升级信息
     *
     * @param id App升级主键
     * @return 结果
     */
    public int deleteAppUpgradeById(Long id) {
        return appUpgradeMapper.deleteAppUpgradeById(id);
    }
}
