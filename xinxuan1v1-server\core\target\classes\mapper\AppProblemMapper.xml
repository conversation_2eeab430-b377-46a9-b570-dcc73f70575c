<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppProblemMapper">

    <resultMap type="AppProblem" id="AppProblemResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="sort" column="sort"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="problemType" column="problem_type"/>
    </resultMap>

    <sql id="selectAppProblemVo">
        select id, title, content, sort, create_time, update_time, create_by, update_by, problem_type from app_problem
    </sql>

    <select id="selectAppProblemList" parameterType="AppProblem" resultMap="AppProblemResult">
        <include refid="selectAppProblemVo"/>
        <where>
            <if test="title != null  and title != ''">and title like concat('%', #{title}, '%')</if>
            <if test="problemType != null ">and problem_type = #{problemType}</if>
        </where>
        order by sort asc,id asc
    </select>

    <select id="selectAppProblemById" parameterType="Long" resultMap="AppProblemResult">
        <include refid="selectAppProblemVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppProblem" parameterType="AppProblem" useGeneratedKeys="true" keyProperty="id">
        insert into app_problem
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="sort != null">sort,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="problemType != null">problem_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="sort != null">#{sort},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="problemType != null">#{problemType},</if>
        </trim>
    </insert>

    <update id="updateAppProblem" parameterType="AppProblem">
        update app_problem
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="problemType != null">problem_type = #{problemType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppProblemById" parameterType="Long">
        delete from app_problem where id = #{id}
    </delete>

    <delete id="deleteAppProblemByIds" parameterType="String">
        delete from app_problem where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getProblemListByType" resultType="com.hzy.core.model.vo.app.AppProblemVo"
            parameterType="java.lang.Long">
        select
        id,
        title,
        content,
        problem_type as problemType
        from app_problem
        where problem_type=#{problemType}
        order by sort asc,id asc
    </select>
</mapper>