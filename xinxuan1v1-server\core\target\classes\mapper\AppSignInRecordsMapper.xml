<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppSignInRecordsMapper">

    <resultMap type="AppSignInRecords" id="AppSignInRecordsResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="userId" column="user_id"/>
        <result property="gold" column="gold"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAppSignInRecordsVo">
        select id, create_time, user_id, gold, update_time from app_sign_in_records
    </sql>

    <select id="selectAppSignInRecordsList" parameterType="AppSignInRecords" resultMap="AppSignInRecordsResult">
        <include refid="selectAppSignInRecordsVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="gold != null ">and gold = #{gold}</if>
        </where>
    </select>

    <select id="selectAppSignInRecordsById" parameterType="Long" resultMap="AppSignInRecordsResult">
        <include refid="selectAppSignInRecordsVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppSignInRecords" parameterType="AppSignInRecords">
        insert into app_sign_in_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="gold != null">gold,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="gold != null">#{gold},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateAppSignInRecords" parameterType="AppSignInRecords">
        update app_sign_in_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="gold != null">gold = #{gold},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppSignInRecordsById" parameterType="Long">
        delete from app_sign_in_records where id = #{id}
    </delete>

    <delete id="deleteAppSignInRecordsByIds" parameterType="String">
        delete from app_sign_in_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="isTodaySignIn" resultType="java.lang.Boolean" parameterType="java.lang.Long">
        select if(count(*) > 0, 1, 0)
        from app_sign_in_records
        where user_id = #{userId}
        and to_days(create_time) = to_days(now())
    </select>

    <select id="isDateTodaySignIn" resultType="java.lang.Integer">
        select if(count(*) > 0, 1, 0)
        from app_sign_in_records
        where user_id = #{userId}
        and to_days(create_time) = to_days(#{date})
    </select>

    <select id="getWeeklySignInDays" resultType="java.lang.Integer">
        select COUNT(*)
        from app_sign_in_records
        where user_id = #{userId}
        and YEARWEEK(create_time, 1) = YEARWEEK(CURDATE(), 1);
    </select>
</mapper>