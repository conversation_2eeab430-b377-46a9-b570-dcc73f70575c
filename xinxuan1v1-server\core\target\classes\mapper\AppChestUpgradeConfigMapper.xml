<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppChestUpgradeConfigMapper">

    <resultMap type="AppChestUpgradeConfig" id="AppChestUpgradeConfigResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="minAmount" column="min_amount"/>
        <result property="maxAmount" column="max_amount"/>
        <result property="sumPut" column="sum_put"/>
        <result property="sumOut" column="sum_out"/>
        <result property="sumProfit" column="sum_profit"/>
        <result property="extractedNum" column="extracted_num"/>
        <result property="sumPutGl" column="sum_put_gl"/>
        <result property="sumOutGl" column="sum_out_gl"/>
        <result property="sumProfitGl" column="sum_profit_gl"/>
        <result property="sumTurnGl" column="sum_turn_gl"/>
        <result property="extractedNumGl" column="extracted_num_gl"/>
        <result property="chestGiftId" column="chest_gift_id"/>
        <result property="isDel" column="is_del"/>
        <result property="minKzl" column="min_kzl"/>
        <result property="maxKzl" column="max_kzl"/>
        <result property="slcBl" column="slc_bl"/>
        <result property="mzlsJcType" column="mzls_jc_type"/>
    </resultMap>

    <sql id="selectAppChestUpgradeConfigVo">
        select id, name, min_amount, max_amount, sum_put, sum_out, sum_profit, extracted_num, sum_put_gl, sum_out_gl,
        sum_profit_gl, sum_turn_gl, extracted_num_gl, chest_gift_id, is_del, min_kzl, max_kzl, slc_bl, mzls_jc_type from
        app_chest_upgrade_config
    </sql>

    <select id="selectAppChestUpgradeConfigList" parameterType="AppChestUpgradeConfig"
            resultMap="AppChestUpgradeConfigResult">
        <include refid="selectAppChestUpgradeConfigVo"/>
        where is_del=false
        <if test="chestGiftId != null ">and chest_gift_id = #{chestGiftId}</if>
        order by min_amount asc,max_amount asc,id desc
    </select>

    <select id="selectAppChestUpgradeConfigById" parameterType="Long" resultMap="AppChestUpgradeConfigResult">
        <include refid="selectAppChestUpgradeConfigVo"/>
        where id = #{id} and is_del=false
    </select>

    <select id="getChestUpgradeConfigById" parameterType="Long" resultMap="AppChestUpgradeConfigResult">
        <include refid="selectAppChestUpgradeConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppChestUpgradeConfig" parameterType="AppChestUpgradeConfig" useGeneratedKeys="true"
            keyProperty="id">
        insert into app_chest_upgrade_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="minAmount != null">min_amount,</if>
            <if test="maxAmount != null">max_amount,</if>
            <if test="sumPut != null">sum_put,</if>
            <if test="sumOut != null">sum_out,</if>
            <if test="sumProfit != null">sum_profit,</if>
            <if test="extractedNum != null">extracted_num,</if>
            <if test="sumPutGl != null">sum_put_gl,</if>
            <if test="sumOutGl != null">sum_out_gl,</if>
            <if test="sumProfitGl != null">sum_profit_gl,</if>
            <if test="sumTurnGl != null">sum_turn_gl,</if>
            <if test="extractedNumGl != null">extracted_num_gl,</if>
            <if test="chestGiftId != null">chest_gift_id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="minKzl != null">min_kzl,</if>
            <if test="maxKzl != null">max_kzl,</if>
            <if test="slcBl != null">slc_bl,</if>
            <if test="mzlsJcType != null">mzls_jc_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="minAmount != null">#{minAmount},</if>
            <if test="maxAmount != null">#{maxAmount},</if>
            <if test="sumPut != null">#{sumPut},</if>
            <if test="sumOut != null">#{sumOut},</if>
            <if test="sumProfit != null">#{sumProfit},</if>
            <if test="extractedNum != null">#{extractedNum},</if>
            <if test="sumPutGl != null">#{sumPutGl},</if>
            <if test="sumOutGl != null">#{sumOutGl},</if>
            <if test="sumProfitGl != null">#{sumProfitGl},</if>
            <if test="sumTurnGl != null">#{sumTurnGl},</if>
            <if test="extractedNumGl != null">#{extractedNumGl},</if>
            <if test="chestGiftId != null">#{chestGiftId},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="minKzl != null">#{minKzl},</if>
            <if test="maxKzl != null">#{maxKzl},</if>
            <if test="slcBl != null">#{slcBl},</if>
            <if test="mzlsJcType != null">#{mzlsJcType},</if>
        </trim>
    </insert>

    <update id="updateAppChestUpgradeConfig" parameterType="AppChestUpgradeConfig">
        update app_chest_upgrade_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="minAmount != null">min_amount = #{minAmount},</if>
            <if test="maxAmount != null">max_amount = #{maxAmount},</if>
            <if test="sumPut != null">sum_put = #{sumPut},</if>
            <if test="sumOut != null">sum_out = #{sumOut},</if>
            <if test="sumProfit != null">sum_profit = #{sumProfit},</if>
            <if test="extractedNum != null">extracted_num = #{extractedNum},</if>
            <if test="sumPutGl != null">sum_put_gl = #{sumPutGl},</if>
            <if test="sumOutGl != null">sum_out_gl = #{sumOutGl},</if>
            <if test="sumProfitGl != null">sum_profit_gl = #{sumProfitGl},</if>
            <if test="sumTurnGl != null">sum_turn_gl = #{sumTurnGl},</if>
            <if test="extractedNumGl != null">extracted_num_gl = #{extractedNumGl},</if>
            <if test="chestGiftId != null">chest_gift_id = #{chestGiftId},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="minKzl != null">min_kzl = #{minKzl},</if>
            <if test="maxKzl != null">max_kzl = #{maxKzl},</if>
            <if test="slcBl != null">slc_bl = #{slcBl},</if>
            <if test="mzlsJcType != null">mzls_jc_type = #{mzlsJcType},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteAppChestUpgradeConfigById" parameterType="Long">
        update app_chest_upgrade_config set is_del=true where id = #{id}
    </update>

    <delete id="deleteAppChestUpgradeConfigByIds" parameterType="String">
        delete from app_chest_upgrade_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <update id="addSumPut">
        update app_chest_upgrade_config set sum_put=sum_put+#{price}
        where id=#{id}
    </update>

    <update id="addSumOut">
        update app_chest_upgrade_config set sum_out=sum_out+#{price}
        where id=#{id}
    </update>

    <update id="addSumProfit">
        update app_chest_upgrade_config set sum_profit=(sum_put-sum_out)
        where id=#{id} and sum_put>=sum_out
    </update>

    <update id="addExtractedNum">
        update app_chest_upgrade_config set extracted_num=extracted_num+#{num}
        where id=#{id}
    </update>


    <update id="addSumPutBl">
        update app_bx_gl_config set sum_put_gl=sum_put_gl+#{price}
        where id=#{id}
    </update>

    <update id="addSumOutBl">
        update app_bx_gl_config set sum_out_gl=sum_out_gl+#{price}
        where id=#{id}
    </update>

    <update id="addExtractedNumBl">
        update app_bx_gl_config set extracted_num_gl=extracted_num_gl+#{num}
        where id=#{id}
    </update>

    <update id="addSumProfitBl">
        update app_bx_gl_config set sum_profit_gl=(sum_put_gl-sum_out_gl)
        where id=#{id} and sum_put_gl>=sum_out_gl
    </update>

    <update id="sumSumProfitBl">
        update app_bx_gl_config set sum_profit_gl=sum_profit_gl-#{price}
        where id=#{id}
    </update>

    <update id="czAll">
        update app_chest_upgrade_config set
        sum_put=0,
        sum_out=0,
        sum_profit=0,
        extracted_num=0,
        sum_put_gl=0,
        sum_out_gl=0,
        sum_profit_gl=0,
        extracted_num_gl=0
    </update>

    <select id="getName" resultType="java.lang.String" parameterType="java.lang.Long">
        select name from app_chest_upgrade_config where id=#{id}
    </select>
</mapper>