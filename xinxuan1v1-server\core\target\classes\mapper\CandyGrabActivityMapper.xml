<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.candy.CandyGrabActivityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hzy.core.entity.candy.CandyGrabActivity">
        <id column="activity_id" property="activityId" />
        <result column="config_id" property="configId" />
        <result column="period_no" property="periodNo" />
        <result column="status" property="status" />
        <result column="total_candy" property="totalCandy" />
        <result column="current_participants" property="currentParticipants" />
        <result column="create_time" property="createTime" />
        <result column="draw_time" property="drawTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        activity_id, config_id, period_no, status, total_candy, current_participants, create_time, draw_time
    </sql>

</mapper>
