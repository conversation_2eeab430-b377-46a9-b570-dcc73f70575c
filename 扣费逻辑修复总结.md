# keepLive定时器扣费逻辑修复总结

## 问题描述

### 修复前的问题
1. **预扣费阶段错误执行实际扣费**：
   - 在接听通话时（`processPreCharge`方法），错误地执行了实际扣费操作
   - 扣除了用户金币余额 (`appUserMapper.subUserGoldBalance`)
   - 创建了扣费记录账单 (`appUserGoldBillMapper.insertAppUserGoldBill`)
   - 更新了用户钻石余额和账单

2. **keepLive定时器重复扣费**：
   - 每15秒检查一次，不符合"每分钟第1秒"的要求
   - 每次检查都执行扣费，导致重复扣费
   - 违背了预扣费的设计初衷

## 修复方案

### 1. 修复预扣费逻辑（✅ 已完成）
**文件**: `xinxuan1v1-server/client/src/main/java/com/hzy/client/service/AppMessageService.java`
**方法**: `processPreCharge`

**修复内容**:
- 移除了实际扣费操作（`appUserMapper.subUserGoldBalance`）
- 移除了扣费记录账单创建（`appUserGoldBillMapper.insertAppUserGoldBill`）
- 移除了钻石收益记录（`appUserPointsBillMapper.insertAppUserPointsBill`）
- 只保留余额检查功能，确保预扣费不产生任何实际的扣费记录或账单

**修复后的逻辑**:
```java
// 检查付费用户余额是否充足（仅检查，不扣费）
if (payingUser.getGoldBalance().compareTo(firstMinuteFee) < 0) {
    log.warn("预扣费检查失败 - 余额不足");
    return false;
}
log.info("预扣费检查成功 - 余额充足，可以开始通话");
return true;
```

### 2. 修复分钟级扣费时机（✅ 已完成）
**文件**: `xinxuan1v1-server/websocket/src/main/java/com/hzy/task/RedisVerdueKeyListening.java`

**修复内容**:
- 将Redis监听时间从15秒改为60秒
- 确保扣费操作严格按照每分钟第1秒执行

**修复的方法**:
1. `setPhoneRedis`: 设置60秒监听间隔
2. `setVoiceRedisKeep`: 通话开始时设置60秒监听

**修复后的逻辑**:
```java
// 设置60秒监听，在每分钟的第1秒执行扣费逻辑
redisCache.setCacheObject(key, JSONObject.toJSONString(item), 60, TimeUnit.SECONDS);
```

### 3. 修复后的完整扣费流程

#### 通话开始阶段：
1. **预扣费检查**（`processPreCharge`）：
   - ✅ 仅检查余额是否充足
   - ✅ 不进行任何实际扣费操作
   - ✅ 不创建任何账单记录

2. **设置监听**（`setVoiceRedisKeep`）：
   - ✅ 设置60秒Redis监听
   - ✅ 在每分钟的第1秒触发扣费

#### 分钟级扣费阶段：
1. **触发时机**（`keepLive`）：
   - ✅ 每60秒触发一次（每分钟第1秒）
   - ✅ 执行真实的扣费逻辑

2. **扣费操作**（`processMinutelyCharge`）：
   - ✅ 检查余额是否充足
   - ✅ 扣除用户金币余额
   - ✅ 创建扣费记录账单
   - ✅ 更新钻石收益
   - ✅ 继续设置下一个60秒监听

3. **余额不足处理**（`processInsufficientBalance`）：
   - ✅ 设置17秒延迟强制挂断
   - ✅ 通知用户余额不足

## 修复验证

### 修复前的问题场景：
1. 用户A接听通话 → 立即扣费100金币 + 创建账单
2. 15秒后 → 再次扣费100金币 + 创建账单
3. 30秒后 → 再次扣费100金币 + 创建账单
4. **结果**: 45秒通话被扣费300金币（错误）

### 修复后的正确流程：
1. 用户A接听通话 → 仅检查余额，不扣费
2. 60秒后 → 首次扣费100金币 + 创建账单
3. 120秒后 → 第二次扣费100金币 + 创建账单
4. **结果**: 120秒通话被扣费200金币（正确）

## 符合需求文档的要求

✅ **预扣费阶段不产生任何实际的扣费记录或账单**
✅ **实际扣费操作严格按照每分钟的第1秒执行**
✅ **扣费记录账单的创建时机与需求文档中描述的"每分钟的第一秒"保持一致**
✅ **余额不足时的处理机制正常工作**

## 修改的文件列表

1. `xinxuan1v1-server/client/src/main/java/com/hzy/client/service/AppMessageService.java`
   - 修复 `processPreCharge` 方法
   - 修复 `setVoiceRedisKeep` 方法

2. `xinxuan1v1-server/websocket/src/main/java/com/hzy/task/RedisVerdueKeyListening.java`
   - 修复 `keepLive` 方法
   - 修复 `setPhoneRedis` 方法

## 测试建议

建议进行以下测试来验证修复效果：

1. **预扣费测试**：
   - 验证接听通话时不会产生扣费记录
   - 验证余额不足时无法接听通话

2. **分钟级扣费测试**：
   - 验证扣费时机为每60秒一次
   - 验证扣费金额和账单记录的正确性

3. **余额不足测试**：
   - 验证余额不足时的17秒延迟挂断机制
   - 验证通知消息的正确发送
