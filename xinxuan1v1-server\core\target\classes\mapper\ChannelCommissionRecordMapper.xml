<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.ChannelCommissionRecordMapper">

    <select id="getDetailList" resultType="com.hzy.core.entity.ChannelCommissionRecord">
        select *,
               app_user.nick_name     as userName,
               app_user.head_portrait as userAvatarUrl
        from channel_commission_record c
                 left join app_user on c.user_id = app_user.id
        where c.channel_code = #{code}
    </select>
    <select id="getRecordList" resultType="com.hzy.core.model.vo.admin.ChannelCommissionRecordVo">
        select distinct c.*, u.recode_code
        from channel_commission_record c
        left join app_user u on c.user_id = u.id
        left join channel_commission_record cr on c.recharge_user_code = cr.recharge_user_code
        <where>
            <if test="channelCode != null">
                and c.channel_code = #{channelCode}
            </if>
            <if test="rechargeUserCode != null">
                and cr.channel_code = #{rechargeUserCode}
            </if>
            <if test="recodeCode != null">
                and u.recode_code = #{recodeCode}
            </if>
            <if test="userId != null">
                and c.user_id = #{userId}
            </if>
            <if test="startTime != null">
                and c.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and c.create_time &lt;= DATE_FORMAT(#{endTime}, '%Y-%m-%d 23:59:59')
            </if>
            <if test="flag != null">
                and c.flag = #{flag}
            </if>
        </where>
        order by c.id desc
    </select>

    <select id="getRecordListOnlyOnce" resultType="com.hzy.core.model.vo.admin.ChannelCommissionRecordVo">
        SELECT
        au.recode_code,
        ccr.*
        from
        channel_user_relation cur
        left join sys_channel sc
        on
        sc.id = cur.channel_id
        left join app_user au
        on
        au.id = cur.user_id
        join channel_commission_record ccr
        on
        ccr.user_id = cur.user_id
        <where>
            <if test="channelCode != null">
                and ccr.channel_code = #{channelCode}
            </if>
            <if test="channelId != null">
                and cur.channel_id = #{channelId}
            </if>
            <if test="recodeCode != null">
                and au.recode_code = #{recodeCode}
            </if>
            <if test="userId != null">
                and cur.user_id = #{userId}
            </if>
            <if test="startTime != null">
                and ccr.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and ccr.create_time &lt;= DATE_FORMAT(#{endTime}, '%Y-%m-%d 23:59:59')
            </if>
            <if test="flag != null">
                and ccr.flag = #{flag}
            </if>
        </where>
        order by ccr.id desc
    </select>

</mapper>
