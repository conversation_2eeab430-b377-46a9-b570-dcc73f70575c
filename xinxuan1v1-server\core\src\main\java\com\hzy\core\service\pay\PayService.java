package com.hzy.core.service.pay;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.internal.util.AlipaySignature;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyResponse;
import com.github.binarywang.wxpay.v3.util.AesUtils;
import com.hzy.core.config.RedisCache;
import com.hzy.core.constant.RedisKeyConsts;
import com.hzy.core.entity.*;
import com.hzy.core.enums.*;
import com.hzy.core.exception.AliPayCallbackException;
import com.hzy.core.exception.AppException;
import com.hzy.core.exception.JxPayCallbackException;
import com.hzy.core.exception.WxPayCallbackException;
import com.hzy.core.mapper.*;
import com.hzy.core.model.vo.Ajax<PERSON>ult;
import com.hzy.core.model.vo.ApplePayCallbackVo;
import com.hzy.core.service.*;
import com.hzy.core.utils.DateUtils;
import com.hzy.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.IOUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 支付服务实现
 */
@Slf4j
@Service
public class PayService {

    @Resource
    private Redisson redisson;

    @Resource
    private RedisCache redisCache;

    @Resource
    private AppWxPayConfigMapper appWxPayConfigMapper;

    @Resource
    private AppAliPayConfigMapper appAliPayConfigMapper;

    @Resource
    private AppSdPayConfigMapper appSdPayConfigMapper;

    @Resource
    private AppOrderMapper appOrderMapper;

    @Resource
    private AppUserMapper appUserMapper;

    @Resource
    private CoreAppUserService coreAppUserService;

    @Resource
    private AppUserPointsBillMapper appUserPointsBillMapper;

    @Resource
    private AppUserGoldBillMapper appUserGoldBillMapper;

    @Resource
    private AppUserPersonalDressingMapper appUserPersonalDressingMapper;

    @Resource
    private AppGoodNumberMapper appGoodNumberMapper;

    @Resource
    private AppTopUpGradeConfigMapper appTopUpGradeConfigMapper;

    @Resource
    private AppUserTopUpGradeMapper appUserTopUpGradeMapper;

    @Resource
    private AppConfigMapper appConfigMapper;

    @Resource
    private AppTopUpRewardsMapper appTopUpRewardsMapper;

    @Resource
    private JxPayService jxPayService;

    @Resource
    private ChannelCommissionRecordService channelCommissionRecordService;

    @Resource
    private AppGuildMemberMapper appGuildMemberMapper;

    @Resource
    private AppVipMapper appVipMapper;

    @Resource
    private AppVipPrivilegesConfigMapper appVipPrivilegesConfigMapper;

    @Resource
    private AppVipPriceMapper appVipPriceMapper;

    @Resource
    private AppGiftService2 appGiftService2;

    @Resource
    private AppUserVideoCardService appUserVideoCardService;

    @Resource
    private AppVipRecordMapper appVipRecordMapper;


    /**
     * 微信支付回调
     *
     * @param request
     * @param response
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Object payCallbackWx(HttpServletRequest request, HttpServletResponse response, Long payConfigId) {
        if (null == payConfigId) {
            log.error("微信支付回调失败：{}", "payConfigId为空");
            return WxPayNotifyResponse.fail("payConfigId为空");
        }

        AppWxPayConfig appWxPayConfig = appWxPayConfigMapper.getAppWxPayConfigById(payConfigId);
        if (null == appWxPayConfig) {
            log.error("微信支付回调失败：{}", "位置支付配置信息不存在");
            return WxPayNotifyResponse.fail("位置支付配置信息不存在");
        }

        JSONObject jsonObject1 = null;
        String xmlResult = null;
        try {
            xmlResult = IOUtils.toString(request.getInputStream(), request.getCharacterEncoding());
            log.info("支付回调內容:{}", xmlResult);

            JSONObject jsonObject = JSONObject.parseObject(xmlResult);

            JSONObject resource = jsonObject.getJSONObject("resource");

            String ress = AesUtils.decryptToString(resource.getString("associated_data"), resource.getString("nonce"), resource.getString("ciphertext"), appWxPayConfig.getMchKey());

            log.info("解密结果:{}", ress);

            jsonObject1 = JSONObject.parseObject(ress);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("微信支付回调失败：获取回调消息内容错误!");
            throw new WxPayCallbackException(e.getMessage());
        }


//        WxPayConfig wxPayConfigVo = new WxPayConfig();
//        wxPayConfigVo.setAppId(appWxPayConfig.getAppId());
//        wxPayConfigVo.setMchId(appWxPayConfig.getMchId());
//        wxPayConfigVo.setMchKey(appWxPayConfig.getMchKey());
//        wxPayConfigVo.setSignType("MD5");
//        wxPayConfigVo.setTradeType("APP");
//        wxPayService.setConfig(wxPayConfigVo);

//        WxPayOrderNotifyResult result = null;

        // 订单编号
//        String orderNo = result.getOutTradeNo();
        // 拿出订单号
        String orderNo = jsonObject1.getString("out_trade_no");
        // 拿出金额
        String totalFee = jsonObject1.getJSONObject("amount").getString("total");
        totalFee = BigDecimal.valueOf(Double.valueOf(totalFee) / 100).setScale(2, RoundingMode.HALF_UP).toPlainString();
        if (StringUtils.isBlank(orderNo)) {
            log.error("微信支付回调失败：{}", "订单编号为空");
            return WxPayNotifyResponse.fail("订单编号为空");
        }

        // 根据订单编号加锁
        RLock lock = redisson.getLock("payCallback." + orderNo);
        if (lock.isLocked()) {
            log.info("微信支付重复回调,订单号编号:{}", orderNo);
            return WxPayNotifyResponse.fail("请求频繁");
        }
        lock.lock(60, TimeUnit.MINUTES);

        // 这里写自己的校验逻辑

        AppOrder order = appOrderMapper.selectAppOrderByOrderNo(orderNo);

        if (null == order) {
            log.error("微信支付回调失败：{},orderNo:{}", "订单不存在", orderNo);
            lock.unlock();
            return WxPayNotifyResponse.fail("订单不存在");
        }

        // 支付的总金额
//        totalFee = BaseWxPayResult.fenToYuan(result.getTotalFee());

        // 判断订单状态
        if (order.getOrderStatus().intValue() != AppOrderStatusEnums.TYPE0.getId()) {
            log.error("微信支付回调失败：{},orderNo:{}，订单状态:{}", "订单状态不支持!", orderNo, AppOrderStatusEnums.getEnum(order.getOrderStatus()
                    .intValue()).getDesc());
            lock.unlock();
            return WxPayNotifyResponse.fail("订单状态不支持");
        }

        if (!totalFee.equals(order.getOrderPrice().toString())) {
            log.error("微信支付回调失败：{}", "支付金额不符合,orderNo:{},实付:{},需付:{}", orderNo, totalFee, order.getOrderPrice());
            lock.unlock();
            return WxPayNotifyResponse.fail("支付金额不符合");
        }

        try {
            // 支付完成后处理购买
            paymentBuy(order);
        } catch (Exception e) {
            log.error("微信支付回调失败:{},orderNo:{},msg:{}", "支付完成后处理异常", orderNo, e.getMessage());
            lock.unlock();
            throw new WxPayCallbackException(e.getMessage());
        }


        log.info("【请求结束】微信支付回调:响应结果:{},orderNo:{}", "处理成功!", orderNo);
        lock.unlock();
        return WxPayNotifyResponse.success("处理成功!");
    }

    /**
     * 支付宝回调接口
     *
     * @param request
     * @param response
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public Object payCallbackAliPay(HttpServletRequest request, HttpServletResponse response, Long payConfigId) {
        // 获取支付宝POST过来反馈信息
        Map<String, String> params = new HashMap<>();
        Map requestParams = request.getParameterMap();
        for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
            }
            params.put(name, valueStr);
        }

        // 订单编号
        String orderNo = MapUtils.getString(params, "out_trade_no");
        if (StringUtils.isBlank(orderNo)) {
            log.error("支付宝回调失败：{}", "订单编号为空");
            return "fail";
        }

        // 根据订单编号加锁
        RLock lock = redisson.getLock("payCallback." + orderNo);
        if (lock.isLocked()) {
            return "fail";
        }
        lock.lock(3, TimeUnit.MINUTES);

        AppAliPayConfig appAliPayConfig = appAliPayConfigMapper.getAppAliPayConfigById(payConfigId);
        if (null == appAliPayConfig) {
            log.error("支付宝回调失败：{},orderNo:{}", "支付宝配置信息不存在", orderNo);
            lock.unlock();
            return "fail";
        }

        // 签名验证
        try {
            // 切记alipaypublickey是支付宝的公钥，请去open.alipay.com对应应用下查看。
            // 验签不通过支付宝会一直回调你的异步回调
            // 这里是支付宝公钥不是应用公钥
            boolean flag = AlipaySignature.rsaCertCheckV1(params, appAliPayConfig.getPublicKeyRsa2Path(), "UTF-8", "RSA2");
            if (!flag) {
                log.error("支付宝回调失败：{},orderNo:{}", "验证签名失败", orderNo);
                lock.unlock();
                return "fail";
            }
        } catch (AlipayApiException e) {
            log.error("支付宝回调失败：{},orderNo:{},msg:{}", "验证签名异常", orderNo, e.getMessage());
            lock.unlock();
            return "fail";
        }

        if ("TRADE_SUCCESS".equals(params.get("trade_status")) || "TRADE_FINISHED".equals(params.get("trade_status"))) {

            AppOrder order = appOrderMapper.selectAppOrderByOrderNo(orderNo);
            if (null == order) {
                log.error("支付宝回调失败：{},orderNo:{}", "订单不存在", orderNo);
                lock.unlock();
                return "fail";
            }

            // 判断订单状态
            if (order.getOrderStatus().intValue() != AppOrderStatusEnums.TYPE0.getId()) {
                log.error("支付宝回调失败：{},orderNo:{}，订单状态:{}", "订单状态不支持!", orderNo, AppOrderStatusEnums.getEnum(order.getOrderStatus()
                        .intValue()).getDesc());
                lock.unlock();
                return "fail";
            }

            try {
                // 支付完成后处理购买
                paymentBuy(order);
            } catch (Exception e) {
                log.error("支付宝回调失败:{},orderNo:{},msg:{}", "支付完成后处理异常", orderNo, e.getMessage());
                lock.unlock();
                throw new AliPayCallbackException(e.getMessage());
            }

            log.info("【请求结束】支付宝支付回调:响应结果:{},orderNo:{}", "处理成功!", orderNo);
            lock.unlock();
            return "success";
        } else {
            log.error("支付宝回调失败：{},orderNo:{}", "未支付成功", orderNo);
            lock.unlock();
            return "fail";
        }


    }


    /**
     * 苹果支付校验
     * <AUTHOR>
     * @date 2025/5/20 下午6:25
     */
    public AjaxResult payCallbackApplePay(ApplePayCallbackVo applePayCallbackVo, Long userId) {
        // 根据订单ID加锁
        RLock lock = redisson.getLock("payCallback." + applePayCallbackVo.getOrderId());
        if (lock.isLocked()) {
            return AjaxResult.error("请求频繁");
        }
        lock.lock(1, TimeUnit.MINUTES);

        // 查询订单信息
        AppOrder order = appOrderMapper.selectAppOrderById(applePayCallbackVo.getOrderId().toString());
        if (null == order) {
            lock.unlock();
            return AjaxResult.error("订单不存在");
        }

        // 判断订单状态
        if (order.getOrderStatus().intValue() != AppOrderStatusEnums.TYPE0.getId()) {
            lock.unlock();
            return AjaxResult.error("订单已支付");
        }

        // 判断是否是自己的订单
        if (!order.getUserId().equals(userId)) {
            lock.unlock();
            return AjaxResult.error("订单所属用户错误");
        }

        try {
            // 生产环境URL
            String certificateUrl = "https://buy.itunes.apple.com/verifyReceipt";

            // 沙盒环境URL
            String sandboxCertificateUrl = "https://sandbox.itunes.apple.com/verifyReceipt";

            JSONObject response = validateReceipt(applePayCallbackVo.getReceipt(), certificateUrl);
            log.info("获取苹果支付验证成功，订单id:{}， 验证信息：{}", applePayCallbackVo.getOrderId(), response);
            Integer status = response.getInteger("status");

            // 如果正式的不行就转沙盒
            if (status == 21007) {
                response = validateReceipt(applePayCallbackVo.getReceipt(), sandboxCertificateUrl);
                status = response.getInteger("status");
            }

            if (status != 0) {
                log.info("苹果支付状态码错误，验证的状态码为：{}" , status);
                throw new AppException("状态码错误，验证的状态码为" + status);
            }

            // 支付完成后处理购买
            paymentBuy(order);

            lock.unlock();
            return AjaxResult.success();
        }catch (Exception e) {
            lock.unlock();
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 验证苹果收据
     * <AUTHOR>
     * @date 2025/5/21 下午1:39
     */
    private JSONObject validateReceipt(String receipt, String url) {
        JSONObject param = new JSONObject();
        param.put("receipt-data", receipt);

        HttpClient httpClient = HttpClient.newHttpClient();
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(param.toString()))
                .build();

        HttpResponse<String> httpResponse;
        try {
            httpResponse = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            if (httpResponse.statusCode() != 200) {
                log.error("调用苹果支付验证失败：{}", httpResponse.statusCode());
                throw new AppException("调用苹果支付验证失败");
            }
        } catch (Exception e) {
            throw new AppException("调用苹果支付验证失败");
        }

        // 拿到返回结果
        return JSON.parseObject(httpResponse.body());
    }

    /**
     * 支付完成后处理购买
     *
     * @param order
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public void paymentBuy(AppOrder order) {
        try {
            // 删除订单支付过期监听，一定要删除，不然会流转到过期监听那
            redisCache.deleteObject(RedisKeyConsts.ORDER_PAY_TIME_OUT_LISTENING_KEY + order.getOrderNo());
        } catch (Exception e) {
        }

        Long userId = order.getUserId();
        Date time = new Date();

        // 充值金币
        if (order.getOrderType().intValue() == AppOrderTypeEnums.TYPE1.getId()) {
            // 获取订单用户信息
            AppUserEntity user = appUserMapper.selectAppUserById(userId);
            if (null != user) {

                // 获取购买的商品详细信息
                AppGoldTopupConfig goodsInfo = JSON.parseObject(order.getGoodsInfoJson(), AppGoldTopupConfig.class);
                // 用户充值的金币数量为：充值的数量+赠送的数量
                BigDecimal topupNum = new BigDecimal(goodsInfo.getGoldNum()).add(new BigDecimal(goodsInfo.getGiveNum()));
                appUserMapper.addUserGoldBalance(user.getId(), topupNum);
                // 金币账单记录
                AppUserGoldBill userGoldBill = new AppUserGoldBill();
                userGoldBill.setUserId(userId);
                userGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE1.getId());// 账单类型为账户充值
                userGoldBill.setAmount(topupNum);// 金额为充值的数量
                userGoldBill.setObjectId(order.getId());// 产生账单的相关id为订单id
                userGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
                appUserGoldBillMapper.insertAppUserGoldBill(userGoldBill);

               appGiftService2.levelUp(userId, topupNum, time);

               // 赠送视频卡
                if (goodsInfo.getVideoCardAmount() > 0) {
                    log.info("充值金币成功，赠送视频卡，用户id：{}，赠送数量：{}", userId, goodsInfo.getVideoCardAmount());
                    appUserVideoCardService.addUserVideoCard(userId, AppVideoCardBillTypeEnums.GOLD_TOP_UP_SEND, goodsInfo.getVideoCardAmount());
                }

                AppUserEntity recodeUser = null;
                // 判断充值用户有没有推荐人
                if (null != user.getRecodeUserId() && !user.getRecodeUserId().equals(0L)) {
                    recodeUser = appUserMapper.selectAppUserById(user.getRecodeUserId());
                }
                // 如果充值用户有推荐人，那就给推荐人分佣
                if (null != recodeUser) {
                    AppConfig appConfig = appConfigMapper.getAppConfig();// 获取app配置信息
                    // 计算本次充值的金币等于多少人民币
                    BigDecimal topUpRmb = new BigDecimal(goodsInfo.getGoldNum()).multiply(appConfig.getOneGoldEqRmb());
                    // 计算本次收入的人民币
                    BigDecimal earningsRmb = topUpRmb.multiply(appConfig.getInviteTopupScale());
                    // 计算本次收入的钻石
                    BigDecimal earningsPoints = earningsRmb.multiply(appConfig.getOneRmbEqPoints());
                    if (earningsPoints.compareTo(new BigDecimal("0")) > 0) {
                        appUserMapper.addUserPointsBalance(recodeUser.getId(), earningsPoints);
                        AppUserPointsBill recodeUserPointsBill = new AppUserPointsBill();
                        recodeUserPointsBill.setUserId(recodeUser.getId());
                        recodeUserPointsBill.setCreateTime(time);
                        recodeUserPointsBill.setBillType((long) AppPointsBillTypeEnums.TYPE14.getId());
                        recodeUserPointsBill.setObjectId(user.getId());
                        recodeUserPointsBill.setAmount(earningsPoints);
                        recodeUserPointsBill.setIsDel(WhetherTypeEnum.NO.getName());
                        recodeUserPointsBill.setGuildId(appGuildMemberMapper.getGuildIdByUserId(recodeUser.getId()));
                        appUserPointsBillMapper.insertAppUserPointsBill(recodeUserPointsBill);
                    }
                }

                // 判断用户来源渠道,将充值金额按照配置比例进行返点,并记账
                // 使用新的分佣逻辑：仅对男性用户进行固定30%返点
                channelCommissionRecordService.saveChannelCommissionRecordsForMale(user, order);

                // 首冲用户赠送礼物
                if (!appOrderMapper.getUserIsTopUp(userId)) {

                    AppTopUpRewards topUpRewards = appTopUpRewardsMapper.selectAppTopUpRewardsById();
                    if (null != topUpRewards && topUpRewards.getIsEnable() == WhetherTypeEnum.YES.getName() && order.getOrderPrice()
                            .compareTo(topUpRewards.getAmount()) >= 0) {

                        // 挂件
                        if (!topUpRewards.getPendantId().equals(0L)) {
                            AppUserPersonalDressing userPersonalDressing = new AppUserPersonalDressing();
                            userPersonalDressing.setUserId(userId);
                            userPersonalDressing.setPersonalDressingId(topUpRewards.getPendantId());
                            userPersonalDressing.setIsDel(WhetherTypeEnum.NO.getName());
                            userPersonalDressing.setIsUse(WhetherTypeEnum.NO.getName());
                            userPersonalDressing.setCreateTime(time);
                            userPersonalDressing.setExpiredTime(DateUtils.addDays(time, 7));
                            appUserPersonalDressingMapper.insertAppUserPersonalDressing(userPersonalDressing);
                        }

                        // 名牌
                        if (!topUpRewards.getFamousBrandId().equals(0L)) {
                            AppUserPersonalDressing userPersonalDressing = new AppUserPersonalDressing();
                            userPersonalDressing.setUserId(userId);
                            userPersonalDressing.setPersonalDressingId(topUpRewards.getFamousBrandId());
                            userPersonalDressing.setIsDel(WhetherTypeEnum.NO.getName());
                            userPersonalDressing.setIsUse(WhetherTypeEnum.NO.getName());
                            userPersonalDressing.setCreateTime(time);
                            userPersonalDressing.setExpiredTime(DateUtils.addDays(time, 7));
                            appUserPersonalDressingMapper.insertAppUserPersonalDressing(userPersonalDressing);
                        }

                        // 彩色昵称
                        if (!topUpRewards.getColorNickNameId().equals(0L)) {
                            AppUserPersonalDressing userPersonalDressing = new AppUserPersonalDressing();
                            userPersonalDressing.setUserId(userId);
                            userPersonalDressing.setPersonalDressingId(topUpRewards.getColorNickNameId());
                            userPersonalDressing.setIsDel(WhetherTypeEnum.NO.getName());
                            userPersonalDressing.setIsUse(WhetherTypeEnum.NO.getName());
                            userPersonalDressing.setCreateTime(time);
                            userPersonalDressing.setExpiredTime(DateUtils.addDays(time, 7));
                            appUserPersonalDressingMapper.insertAppUserPersonalDressing(userPersonalDressing);
                        }

                        // 坐骑
                        if (!topUpRewards.getMountId().equals(0L)) {
                            AppUserPersonalDressing userPersonalDressing = new AppUserPersonalDressing();
                            userPersonalDressing.setUserId(userId);
                            userPersonalDressing.setPersonalDressingId(topUpRewards.getMountId());
                            userPersonalDressing.setIsDel(WhetherTypeEnum.NO.getName());
                            userPersonalDressing.setIsUse(WhetherTypeEnum.NO.getName());
                            userPersonalDressing.setCreateTime(time);
                            userPersonalDressing.setExpiredTime(DateUtils.addDays(time, 7));
                            appUserPersonalDressingMapper.insertAppUserPersonalDressing(userPersonalDressing);
                        }

                        // 气泡框
                        if (!topUpRewards.getChatFrameId().equals(0L)) {
                            AppUserPersonalDressing userPersonalDressing = new AppUserPersonalDressing();
                            userPersonalDressing.setUserId(userId);
                            userPersonalDressing.setPersonalDressingId(topUpRewards.getChatFrameId());
                            userPersonalDressing.setIsDel(WhetherTypeEnum.NO.getName());
                            userPersonalDressing.setIsUse(WhetherTypeEnum.NO.getName());
                            userPersonalDressing.setCreateTime(time);
                            userPersonalDressing.setExpiredTime(DateUtils.addDays(time, 7));
                            appUserPersonalDressingMapper.insertAppUserPersonalDressing(userPersonalDressing);
                        }

                        // 头像框
                        if (!topUpRewards.getMicFrame().equals(0L)) {
                            AppUserPersonalDressing userPersonalDressing = new AppUserPersonalDressing();
                            userPersonalDressing.setUserId(userId);
                            userPersonalDressing.setPersonalDressingId(topUpRewards.getMicFrame());
                            userPersonalDressing.setIsDel(WhetherTypeEnum.NO.getName());
                            userPersonalDressing.setIsUse(WhetherTypeEnum.NO.getName());
                            userPersonalDressing.setCreateTime(time);
                            userPersonalDressing.setExpiredTime(DateUtils.addDays(time, 7));
                            appUserPersonalDressingMapper.insertAppUserPersonalDressing(userPersonalDressing);
                        }
                    }
                }
            }
        }else if (order.getOrderType().intValue() == AppOrderTypeEnums.TYPE2.getId()) {// 购买VIP
            // 购买会员时 商品id存入的是会员的时长 单位为月
            AppVipPrice appVipPrice = appVipPriceMapper.selectById(order.getGoodsId());
            Integer months = appVipPrice.getMonth();
            long duration = (long) months * 31 * 24 * 60 * 60 * 1000; // 将月转换为秒
            // 查询用户是否已经开通了会员
            AppVip appVip = appVipMapper.getVipByUserId(userId);
            if (appVip == null) {
                // 首次开通
                appVip = new AppVip();
                appVip.setUserId(userId);
                appVip.setExpireTime(new Date(System.currentTimeMillis() + duration));
                appVip.setCreateTime(new Date());
                appVip.setUpdateTime(new Date());
                appVipMapper.insert(appVip);

                // 首次开通需要初始化特权配置
                AppVipPrivilegesConfig appVipPrivilegesConfig = new AppVipPrivilegesConfig();
                appVipPrivilegesConfig.setUserId(userId);
                appVipPrivilegesConfig.setCreateTime(new Date());
                appVipPrivilegesConfigMapper.insert(appVipPrivilegesConfig);
            } else {
                if (appVip.getStatus() == WhetherTypeEnum.YES.getName()) {
                    // 续费
                    appVip.setExpireTime(new Date(appVip.getExpireTime().getTime() + duration));
                }else {
                    appVip.setStatus(WhetherTypeEnum.YES.getName());
                    appVip.setExpireTime(new Date(System.currentTimeMillis() + duration));
                }
                appVip.setUpdateTime(new Date());
                appVipMapper.updateById(appVip);
            }

            // 修改用户为vip
            AppUserEntity user = appUserMapper.selectById(userId);
            user.setIsVip(WhetherTypeEnum.YES.getName());
            appUserMapper.updateById(user);

            // 赠送视频卡
            if (appVipPrice.getVideoCardAmount() > 0) {
                log.info("购买VIP成功，赠送视频卡，用户id：{}，赠送数量：{}", userId, appVipPrice.getVideoCardAmount());
                appUserVideoCardService.addUserVideoCard(userId, AppVideoCardBillTypeEnums.VIP_TOP_UP_SEND, appVipPrice.getVideoCardAmount());
            }

            // 添加会员购买记录
            AppVipRecord vipRecord = new AppVipRecord();
            vipRecord.setUserId(userId);
            vipRecord.setType(appVipPrice.getType());
            vipRecord.setPrice(appVipPrice.getPrice());
            vipRecord.setMonth(appVipPrice.getMonth());
            vipRecord.setIsIos(appVipPrice.getIsIos());
            vipRecord.setOrderId(order.getId());
            vipRecord.setCreateTime(new Date());
            appVipRecordMapper.insert(vipRecord);

        } else if (order.getOrderType().intValue() == AppOrderTypeEnums.TYPE3.getId()) {// 购买商城商品

            AppPersonalDressing mallGoods = JSON.parseObject(order.getGoodsInfoJson(), AppPersonalDressing.class);

            // 扣除用户金币余额
            if (appUserMapper.subtractUserGoldBalance(userId, order.getOrderPrice()) <= 0) {
                throw new AppException("金币余额不足");
            }
            // 记录当前用户账单
            AppUserGoldBill userGoldBill = new AppUserGoldBill();
            userGoldBill.setUserId(userId);
            userGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE23.getId());
            userGoldBill.setObjectId(order.getId());
            userGoldBill.setAmount(order.getOrderPrice().negate());
            userGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
            appUserGoldBillMapper.insertAppUserGoldBill(userGoldBill);

            // 添加个性装扮到背包
            AppUserPersonalDressing userPersonalDressing = new AppUserPersonalDressing();
            userPersonalDressing.setUserId(userId);
            userPersonalDressing.setPersonalDressingId(mallGoods.getId());
            userPersonalDressing.setIsDel(WhetherTypeEnum.NO.getName());
            userPersonalDressing.setIsUse(WhetherTypeEnum.NO.getName());
            userPersonalDressing.setCreateTime(time);
            userPersonalDressing.setExpiredTime(DateUtils.addDays(time, mallGoods.getValidDays().intValue()));
            appUserPersonalDressingMapper.insertAppUserPersonalDressing(userPersonalDressing);

        } else if (order.getOrderType().intValue() == AppOrderTypeEnums.TYPE8.getId()) {// 购买靓号

            // 获取购买的靓号详细信息
            AppGoodNumber goodNumber = JSON.parseObject(order.getGoodsInfoJson(), AppGoodNumber.class);

            // 扣除用户金币余额
            if (appUserMapper.subtractUserGoldBalance(userId, order.getOrderPrice()) <= 0) {
                throw new AppException("金币余额不足");
            }
            // 记录当前用户账单
            AppUserGoldBill userGoldBill = new AppUserGoldBill();
            userGoldBill.setUserId(userId);
            userGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE22.getId());
            userGoldBill.setObjectId(order.getId());
            userGoldBill.setAmount(order.getOrderPrice().negate());
            userGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
            appUserGoldBillMapper.insertAppUserGoldBill(userGoldBill);

            // 修改用户为靓号
            AppUserEntity userUpd = new AppUserEntity();
            userUpd.setId(userId);
            userUpd.setIsGoodNumber(WhetherTypeEnum.YES.getName());
            userUpd.setRecodeCode(goodNumber.getGoodNumber());
            appUserMapper.updateAppUser(userUpd);
            // 修改靓号商品信息为已售出
            AppGoodNumber goodNumberUpd = new AppGoodNumber();
            goodNumberUpd.setId(goodNumber.getId());
            goodNumberUpd.setStatus(AppGoodNumberStatusTypeEnum.TYPE2.getName());
            goodNumberUpd.setBuyTime(time);
            goodNumberUpd.setBuyOrderId(order.getId());
            goodNumberUpd.setBuyUserId(userId);
            appGoodNumberMapper.updateAppGoodNumber(goodNumberUpd);
        }

        AppOrder orderUpd = new AppOrder();
        orderUpd.setId(order.getId());
        orderUpd.setPayEndTime(new Date());
        orderUpd.setOrderStatus((long) AppOrderStatusEnums.TYPE1.getId());// 订单状态改成已完成
        orderUpd.setPayTime(new Date());
        appOrderMapper.updateAppOrder(orderUpd);

    }


    /**
     * 精秀支付回调接口
     *
     * @param request
     * @param response
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String payCallbackJxPay(HttpServletRequest request, HttpServletResponse response) {


        AppSdPayConfig appSdPayConfig = appSdPayConfigMapper.getAppSdPayConfigInfo(1L);
        if (null == appSdPayConfig) {
            log.error("精秀回调失败：{},", "精秀支付配置信息不存在");
            throw new JxPayCallbackException("appSdPayConfig为空");
        }

        response.setCharacterEncoding("UTF-8");

        Map<String, String> params = new HashMap<>();

        Enumeration<String> paramNames = request.getParameterNames();

        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            params.put(paramName, request.getParameter(paramName));
        }

        if (MapUtil.isEmpty(params)) {
            log.error("精秀支付回调失败：{}", "params为空");
            throw new JxPayCallbackException("params为空");
        }

        String body = JSON.toJSONString(params);

        String outTradeNo = MapUtil.getStr(params, "out_trade_no");
        if (StringUtils.isBlank(outTradeNo)) {
            log.error("精秀支付回调失败：{}", "商家订单编号为空,入参报文:{}", body);
            throw new JxPayCallbackException("商家订单编号为空");
        }

        // 根据订单编号加锁
        RLock lock = redisson.getLock("js.payCallback." + outTradeNo);
        if (lock.isLocked()) {
            log.error("精秀支付回调失败：{}", "订单处理中,入参报文:{}", body);
            throw new JxPayCallbackException("订单处理中");
        }
        lock.lock(5, TimeUnit.MINUTES);

        if (!StringUtils.equals("SUCCESS", MapUtil.getStr(params, "order_status"))) {
            lock.unlock();
            log.error("精秀支付回调失败：{}", "订单状态未支付成功,入参报文:{}", body);
            throw new JxPayCallbackException("订单状态未支付成功");
        }

        String sign = MapUtil.getStr(params, "sign");
        if (StringUtils.isBlank(sign)) {
            lock.unlock();
            log.error("精秀支付回调失败：{}", "签名为空,入参报文:{}", body);
            throw new JxPayCallbackException("签名为空");
        }
        // 验证签名
        if (!jxPayService.verifySign(params, appSdPayConfig.getMchKey())) {
            lock.unlock();
            log.error("精秀支付回调失败：{}", "签名验证失败,入参报文:{}", body);
            throw new JxPayCallbackException("签名验证失败");
        }


        AppOrder order = appOrderMapper.selectAppOrderByOrderNo(outTradeNo);
        if (null == order) {
            lock.unlock();
            log.error("精秀支付回调失败：{}", "签名验证失败,入参报文:{}", body);
            throw new JxPayCallbackException("订单不存在");
        }

        // 判断订单状态
        if (order.getOrderStatus().intValue() != AppOrderStatusEnums.TYPE0.getId()) {
            lock.unlock();
            return "success";
        }

        try {
            // 支付完成后处理购买
            paymentBuy(order);
        } catch (Exception e) {
            lock.unlock();
            log.error("精秀支付回调失败:{},orderNo:{},msg:{}", "支付完成后处理异常,入参报文:{}", outTradeNo, e.getMessage(), body);
            throw new JxPayCallbackException(e.getMessage());
        }

        log.info("【请求结束】精秀支付回调:响应结果:{},orderNo:{}", "处理成功!", outTradeNo);

        lock.unlock();

        return "success";
    }


    /**
     * 精秀支付回调接口(新)
     *
     * @param request
     * @param response
     * @param payConfigId
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public String payCallbackJxPayNew(HttpServletRequest request, HttpServletResponse response, Long payConfigId) {
        if (null == payConfigId) {
            log.error("精秀回调失败：{}", "payConfigId为空,入参报文");
            throw new JxPayCallbackException("payConfigId为空");
        }
        AppSdPayConfig appSdPayConfig = appSdPayConfigMapper.getAppSdPayConfigInfo(payConfigId);
        if (null == appSdPayConfig) {
            log.error("精秀回调失败：{},", "精秀支付配置信息不存在");
            throw new JxPayCallbackException("appSdPayConfig为空");
        }


        response.setCharacterEncoding("UTF-8");

        Map<String, String> params = new HashMap<>();

        Enumeration<String> paramNames = request.getParameterNames();

        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            params.put(paramName, request.getParameter(paramName));
        }

        if (MapUtil.isEmpty(params)) {
            log.error("精秀支付回调失败：{}", "params为空");
            throw new JxPayCallbackException("params为空");
        }

        String body = JSON.toJSONString(params);

        String outTradeNo = MapUtil.getStr(params, "out_trade_no");
        if (StringUtils.isBlank(outTradeNo)) {
            log.error("精秀支付回调失败：{}", "商家订单编号为空,入参报文:{}", body);
            throw new JxPayCallbackException("商家订单编号为空");
        }

        // 根据订单编号加锁
        RLock lock = redisson.getLock("js.payCallback." + outTradeNo);
        if (lock.isLocked()) {
            log.error("精秀支付回调失败：{}", "订单处理中,入参报文:{}", body);
            throw new JxPayCallbackException("订单处理中");
        }
        lock.lock(5, TimeUnit.MINUTES);

        if (!StringUtils.equals("SUCCESS", MapUtil.getStr(params, "order_status"))) {
            lock.unlock();
            log.error("精秀支付回调失败：{}", "订单状态未支付成功,入参报文:{}", body);
            throw new JxPayCallbackException("订单状态未支付成功");
        }

        String sign = MapUtil.getStr(params, "sign");
        if (StringUtils.isBlank(sign)) {
            lock.unlock();
            log.error("精秀支付回调失败：{}", "签名为空,入参报文:{}", body);
            throw new JxPayCallbackException("签名为空");
        }
        // 验证签名
        if (!jxPayService.verifySign(params, appSdPayConfig.getMchKey())) {
            lock.unlock();
            log.error("精秀支付回调失败：{}", "签名验证失败,入参报文:{}", body);
            throw new JxPayCallbackException("签名验证失败");
        }


        AppOrder order = appOrderMapper.selectAppOrderByOrderNo(outTradeNo);
        if (null == order) {
            lock.unlock();
            log.error("精秀支付回调失败：{}", "签名验证失败,入参报文:{}", body);
            throw new JxPayCallbackException("订单不存在");
        }

        // 判断订单状态
        if (order.getOrderStatus().intValue() != AppOrderStatusEnums.TYPE0.getId()) {
            lock.unlock();
            return "success";
        }

        try {
            // 支付完成后处理购买
            paymentBuy(order);
        } catch (Exception e) {
            lock.unlock();
            log.error("精秀支付回调失败:{},orderNo:{},msg:{}", "支付完成后处理异常,入参报文:{}", outTradeNo, e.getMessage(), body);
            throw new JxPayCallbackException(e.getMessage());
        }

        log.info("【请求结束】精秀支付回调:响应结果:{},orderNo:{}", "处理成功!", outTradeNo);

        lock.unlock();

        return "success";

    }


}
