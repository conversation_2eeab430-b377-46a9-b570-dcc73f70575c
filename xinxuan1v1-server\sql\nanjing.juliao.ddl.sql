/*
 Navicat Premium Data Transfer

 Source Server         : ub开发库
 Source Server Type    : MySQL
 Source Server Version : 80036
 Source Host           : localhost:3306
 Source Schema         : nanjing

 Target Server Type    : MySQL
 Target Server Version : 80036
 File Encoding         : 65001

 Date: 13/07/2025 22:25:26
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for app_ali_pay_config
-- ----------------------------
DROP TABLE IF EXISTS `app_ali_pay_config`;
CREATE TABLE `app_ali_pay_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `is_enable` tinyint(1) NULL DEFAULT 0 COMMENT '是否启用:0否,1是',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `app_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'appId',
  `private_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '商户私钥',
  `public_key_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用公钥证书',
  `public_key_rsa2_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'rsa2公钥证书',
  `root_cert_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'CA根证书',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '名称',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `app_id`(`app_id` ASC) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `is_enable`(`is_enable` ASC) USING BTREE,
  INDEX `zh_index`(`is_del` ASC, `is_enable` ASC, `app_id` ASC) USING BTREE,
  INDEX `zh2_index`(`is_del` ASC, `is_enable` ASC) USING BTREE,
  INDEX `name`(`name` ASC) USING BTREE,
  INDEX `zh3_index`(`is_del` ASC, `is_enable` ASC, `app_id` ASC, `name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '支付宝配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_banned_record
-- ----------------------------
DROP TABLE IF EXISTS `app_banned_record`;
CREATE TABLE `app_banned_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `type` int NOT NULL COMMENT '类型 1.封禁 2.解封',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封号原因',
  `duration` int NULL DEFAULT NULL COMMENT '封号时间',
  `free_time` datetime NULL DEFAULT NULL COMMENT '解封时间',
  `create_user_id` bigint NULL DEFAULT NULL COMMENT '操作人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `key_user_id`(`user_id` ASC) USING BTREE,
  INDEX `key_reason`(`reason` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 33 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '封号记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_banner
-- ----------------------------
DROP TABLE IF EXISTS `app_banner`;
CREATE TABLE `app_banner`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标题',
  `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '1' COMMENT '类型:1首页,2我的',
  `img_url` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '轮播图地址',
  `h5_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '跳转h5地址',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  `sort` int NULL DEFAULT 0 COMMENT '排序,默认为0',
  `context` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '内容',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `title`(`title` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '轮播图' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_blacklist
-- ----------------------------
DROP TABLE IF EXISTS `app_blacklist`;
CREATE TABLE `app_blacklist`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `to_user_id` bigint NULL DEFAULT 0 COMMENT '被拉黑的用户id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `to_user_id`(`to_user_id` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `to_user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 110 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '黑名单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_broadcast
-- ----------------------------
DROP TABLE IF EXISTS `app_broadcast`;
CREATE TABLE `app_broadcast`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '内容',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `zh_index`(`is_del` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '广播' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_bx_bfjl
-- ----------------------------
DROP TABLE IF EXISTS `app_bx_bfjl`;
CREATE TABLE `app_bx_bfjl`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `jjc_id` bigint NULL DEFAULT 0 COMMENT '进阶池id',
  `gift_id` bigint NULL DEFAULT NULL COMMENT '补发的礼物id',
  `gift_num` bigint NULL DEFAULT 0 COMMENT '补发礼物的数量',
  `gift_price` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '补发的礼物单价',
  `sum_gift_price` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '本次补发总价',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `is_sub_kc` tinyint(1) NULL DEFAULT 0 COMMENT '核销状态:0未,1已核销',
  `sub_num` bigint NULL DEFAULT 0 COMMENT '剩余核销数量',
  `sub_time` datetime NULL DEFAULT NULL COMMENT '最近核销时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sub_num`(`sub_num` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `gift_id`(`gift_id` ASC) USING BTREE,
  INDEX `jjc_id`(`jjc_id` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `sub_num` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '宝箱补发记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_bx_gl_config
-- ----------------------------
DROP TABLE IF EXISTS `app_bx_gl_config`;
CREATE TABLE `app_bx_gl_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sum_put_gl` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '概率公池-总投入金额',
  `sum_out_gl` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '概率公池-总产出金额',
  `sum_profit_gl` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '概率公池-盈利金额',
  `extracted_num_gl` bigint NULL DEFAULT 0 COMMENT '概率公池-已抽取轮次',
  `bl_gl` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '概率公池爆率',
  `min_kzl` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '最小控制率',
  `max_kzl` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '最大控制率',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 409 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '宝箱概率配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_bx_user_tj
-- ----------------------------
DROP TABLE IF EXISTS `app_bx_user_tj`;
CREATE TABLE `app_bx_user_tj`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `bx_id` bigint NULL DEFAULT 0 COMMENT '宝箱id',
  `jjc_id` bigint NULL DEFAULT 0 COMMENT '进阶池id',
  `sr_sum` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '总收入',
  `zc_sum` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '总支出',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `jjc_id`(`jjc_id` ASC) USING BTREE,
  INDEX `create_time`(`create_time` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `jjc_id` ASC, `create_time` ASC) USING BTREE,
  INDEX `z2_index`(`user_id` ASC, `jjc_id` ASC) USING BTREE,
  INDEX `z3_index`(`user_id` ASC, `create_time` ASC) USING BTREE,
  INDEX `z4_index`(`jjc_id` ASC, `create_time` ASC) USING BTREE,
  INDEX `bx_id`(`bx_id` ASC) USING BTREE,
  INDEX `z1`(`user_id` ASC, `bx_id` ASC, `jjc_id` ASC, `create_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '宝箱用户开奖统计' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_charm_grade_config
-- ----------------------------
DROP TABLE IF EXISTS `app_charm_grade_config`;
CREATE TABLE `app_charm_grade_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '等级名称',
  `ico_url` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '等级图标',
  `grade_size` bigint NULL DEFAULT 0 COMMENT '等级大小',
  `gold_value` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '升级所需金币',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `grade_size`(`grade_size` ASC) USING BTREE,
  INDEX `gold_value`(`gold_value` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '魅力等级配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_chat_btn_type
-- ----------------------------
DROP TABLE IF EXISTS `app_chat_btn_type`;
CREATE TABLE `app_chat_btn_type`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `to_user_id` bigint NULL DEFAULT 0 COMMENT '对方用户id',
  `btn_type` int NULL DEFAULT 0 COMMENT '按钮类型:0打招呼,1私信',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `to_user_id`(`to_user_id` ASC) USING BTREE,
  INDEX `btn_type`(`btn_type` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `to_user_id` ASC) USING BTREE,
  INDEX `zh2_index`(`user_id` ASC, `to_user_id` ASC, `btn_type` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '聊天按钮类型' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_chat_room
-- ----------------------------
DROP TABLE IF EXISTS `app_chat_room`;
CREATE TABLE `app_chat_room`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '聊天室名称',
  `third_id` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '聊天室第三方id,字段备用',
  `hall_owner_user_id` bigint NULL DEFAULT 0 COMMENT '厅主用户id',
  `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
  `last_end_time` datetime NULL DEFAULT NULL COMMENT '上次结束时间',
  `last_start_time` datetime NULL DEFAULT NULL COMMENT '上次开始时间',
  `status` int NULL DEFAULT 0 COMMENT '聊天室状态:参考java枚举类AppChatRoomStatusTypeEnums',
  `greeting_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '聊天室欢迎语(废弃)',
  `notice_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '聊天室公告',
  `avatar_url` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '聊天室头像',
  `background_url` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '聊天室背景',
  `third_push_flow` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '聊天室第三方推流地址',
  `type` int NULL DEFAULT 0 COMMENT '房间类型:1派对,2电台,3音乐,4交友,具体参考java枚举类AppChatRoomTypeEnums',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `online_user_count` bigint NULL DEFAULT 0 COMMENT '在线用户总数(废弃)',
  `wheat_serving_type` int NULL DEFAULT 2 COMMENT '上麦模式:1审核上麦,2自由上麦,默认为1,参考java枚举类AppChatRoomWheatServingTypeEnums',
  `is_blanking` int NULL DEFAULT 0 COMMENT '房主是否闭麦:0否,1是(废弃)',
  `wheat_bit` int NULL DEFAULT 1 COMMENT '麦位索引,不为-1并且is_wheat_serving为1有效(废弃)',
  `use_music_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '使用的音乐json',
  `is_hide_gift_gold` tinyint(1) NULL DEFAULT 1 COMMENT '是否隐藏麦上金币:0否,1是',
  `password` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '密码',
  `father_id` bigint NULL DEFAULT 0 COMMENT '父id,车队房间有效',
  `label_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '标签json,仅车队',
  `motorcade_type` int NULL DEFAULT 0 COMMENT '车队类型',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热门:0否,1是',
  `is_top` tinyint NULL DEFAULT 0 COMMENT '是否置顶',
  `guild_id` bigint NULL DEFAULT 0 COMMENT '绑定的公会id',
  `heat` bigint NULL DEFAULT 0 COMMENT '热度',
  `hall_owner_commission_scale` decimal(20, 2) NULL DEFAULT NULL COMMENT '厅主收入比例',
  `gift_income_scale` decimal(20, 2) NULL DEFAULT NULL COMMENT '礼物收入比例',
  `license_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '牌照名称',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `user_id` bigint NULL DEFAULT 0 COMMENT '房主用户id(废弃)',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `status`(`status` ASC) USING BTREE,
  INDEX `third_id`(`third_id` ASC) USING BTREE,
  INDEX `type`(`type` ASC) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `id`(`id` ASC) USING BTREE,
  INDEX `father_id`(`father_id` ASC) USING BTREE,
  INDEX `guild_id`(`guild_id` ASC) USING BTREE,
  INDEX `is_hot`(`is_hot` ASC) USING BTREE,
  INDEX `zh10_index`(`is_del` ASC, `guild_id` ASC, `hall_owner_user_id` ASC) USING BTREE,
  INDEX `hall_owner_user_id`(`hall_owner_user_id` ASC) USING BTREE,
  INDEX `heat`(`heat` ASC) USING BTREE,
  INDEX `z1_index`(`user_id` ASC, `status` ASC, `is_del` ASC, `father_id` ASC, `is_hot` ASC, `guild_id` ASC, `heat` ASC) USING BTREE,
  INDEX `z4_index`(`user_id` ASC, `status` ASC, `is_del` ASC, `third_id` ASC, `father_id` ASC, `is_hot` ASC, `guild_id` ASC, `heat` ASC) USING BTREE,
  INDEX `z5_index`(`user_id` ASC, `status` ASC, `is_del` ASC, `father_id` ASC, `is_hot` ASC, `guild_id` ASC, `heat` ASC) USING BTREE,
  INDEX `z2_index`(`user_id` ASC, `status` ASC, `is_del` ASC, `type` ASC, `father_id` ASC, `is_hot` ASC, `guild_id` ASC, `heat` ASC) USING BTREE,
  INDEX `z3_index`(`user_id` ASC, `status` ASC, `is_del` ASC, `type` ASC, `third_id` ASC, `father_id` ASC, `is_hot` ASC, `guild_id` ASC, `heat` ASC) USING BTREE,
  INDEX `z6_index`(`user_id` ASC, `is_del` ASC, `type` ASC, `father_id` ASC, `is_hot` ASC, `guild_id` ASC, `heat` ASC) USING BTREE,
  INDEX `z7_index`(`status` ASC, `is_del` ASC, `type` ASC, `father_id` ASC, `is_hot` ASC, `guild_id` ASC, `heat` ASC) USING BTREE,
  INDEX `z8_index`(`status` ASC, `is_del` ASC, `third_id` ASC, `type` ASC, `father_id` ASC, `is_hot` ASC, `guild_id` ASC, `heat` ASC) USING BTREE,
  INDEX `z9_index`(`is_del` ASC, `status` ASC, `third_id` ASC, `name` ASC, `father_id` ASC, `is_hot` ASC, `guild_id` ASC, `heat` ASC) USING BTREE,
  INDEX `zh11_index`(`type` ASC, `is_del` ASC) USING BTREE,
  INDEX `password`(`password` ASC) USING BTREE,
  INDEX `zh12_index`(`is_del` ASC, `third_id` ASC) USING BTREE,
  INDEX `z_index_1`(`is_del` ASC, `type` ASC) USING BTREE,
  INDEX `z_index_2`(`is_del` ASC, `status` ASC) USING BTREE,
  INDEX `app_chat_room_is_top_IDX`(`is_top` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 87 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '聊天室' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_chat_room_background
-- ----------------------------
DROP TABLE IF EXISTS `app_chat_room_background`;
CREATE TABLE `app_chat_room_background`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `url` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '背景地址',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `thumb_url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '封面地址',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '聊天室背景' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_chat_room_category
-- ----------------------------
DROP TABLE IF EXISTS `app_chat_room_category`;
CREATE TABLE `app_chat_room_category`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类名称',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_name`(`category_name` ASC) USING BTREE,
  INDEX `is_del`(`deleted` ASC) USING BTREE,
  INDEX `zh_index`(`id` ASC, `deleted` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 90 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '房间分类' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_chat_room_music
-- ----------------------------
DROP TABLE IF EXISTS `app_chat_room_music`;
CREATE TABLE `app_chat_room_music`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `room_id` bigint NOT NULL COMMENT '房间id',
  `music_url` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '音乐地址',
  `sys_user_id` bigint NULL DEFAULT NULL COMMENT '创建人id 后台用户上传时使用',
  `music_name` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '音乐名称',
  `singer` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '歌手',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除0：否 1：是',
  `check_status` int NULL DEFAULT 1 COMMENT '审核状态',
  `create_user_id` bigint NULL DEFAULT NULL COMMENT '创建者id',
  `update_user_id` bigint NULL DEFAULT NULL COMMENT '更新者id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '聊天室音乐' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_chat_room_user
-- ----------------------------
DROP TABLE IF EXISTS `app_chat_room_user`;
CREATE TABLE `app_chat_room_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `last_join_time` datetime NULL DEFAULT NULL COMMENT '上次加入时间',
  `chat_room_id` bigint NULL DEFAULT 0 COMMENT '房间id',
  `is_admin` tinyint(1) NULL DEFAULT 0 COMMENT '是否是管理员:0否,1是',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除：0否,1是',
  `is_wheat_serving` tinyint(1) NULL DEFAULT 0 COMMENT '是否上麦:0否,1是',
  `is_blanking` tinyint(1) NULL DEFAULT 0 COMMENT '是否闭麦:0否,1是',
  `is_banned_to_post` tinyint(1) NULL DEFAULT 0 COMMENT '是否禁言:0否,1是',
  `next_join_room_time` datetime NULL DEFAULT NULL COMMENT '下次进入房间时间,is_kick_out为1时有效',
  `is_kick_out` tinyint(1) NULL DEFAULT 0 COMMENT '是否踢出房间:0否,1是',
  `is_join` tinyint(1) NULL DEFAULT 0 COMMENT '是否已加入房间:0否,1是',
  `wheat_bit` int NULL DEFAULT -1 COMMENT '麦位索引,不为-1并且is_wheat_serving为1有效',
  `is_emcee` tinyint(1) NULL DEFAULT 0 COMMENT '是否是主持人:0否,1是',
  `gift_gold_sum` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '收到礼物的金币总数',
  `father_id` bigint NULL DEFAULT 0 COMMENT '父id,车队房间有效',
  `add_emcee_time` datetime NULL DEFAULT NULL COMMENT '添加主持时间',
  `is_owner` tinyint(1) NULL DEFAULT 0 COMMENT '是否是厅主：0否，1是',
  `is_follow` tinyint(1) NULL DEFAULT 0 COMMENT '是否关注房间',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `chat_room_id`(`chat_room_id` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `is_join`(`is_join` ASC) USING BTREE,
  INDEX `is_kick_out`(`is_kick_out` ASC) USING BTREE,
  INDEX `wheat_bit`(`wheat_bit` ASC) USING BTREE,
  INDEX `is_emcee`(`is_emcee` ASC) USING BTREE,
  INDEX `is_wheat_serving`(`is_wheat_serving` ASC) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `is_admin`(`is_admin` ASC) USING BTREE,
  INDEX `is_banned_to_post`(`is_banned_to_post` ASC) USING BTREE,
  INDEX `last_join_time`(`last_join_time` ASC) USING BTREE,
  INDEX `father_id`(`father_id` ASC) USING BTREE,
  INDEX `z1_index`(`chat_room_id` ASC, `is_del` ASC, `is_join` ASC, `father_id` ASC) USING BTREE,
  INDEX `z2_index`(`chat_room_id` ASC, `is_del` ASC, `is_join` ASC, `is_admin` ASC, `father_id` ASC) USING BTREE,
  INDEX `z3_index`(`chat_room_id` ASC, `is_del` ASC, `is_wheat_serving` ASC, `is_join` ASC, `wheat_bit` ASC, `father_id` ASC) USING BTREE,
  INDEX `z4_index`(`is_join` ASC, `user_id` ASC, `father_id` ASC) USING BTREE,
  INDEX `z5_index`(`is_del` ASC, `chat_room_id` ASC, `is_join` ASC, `father_id` ASC) USING BTREE,
  INDEX `z6_index`(`user_id` ASC, `chat_room_id` ASC, `father_id` ASC) USING BTREE,
  INDEX `z7_index`(`is_del` ASC, `chat_room_id` ASC, `is_wheat_serving` ASC, `is_join` ASC, `wheat_bit` ASC, `father_id` ASC) USING BTREE,
  INDEX `zh8_index`(`is_del` ASC, `user_id` ASC, `is_emcee` ASC, `father_id` ASC) USING BTREE,
  INDEX `zh9_index`(`is_del` ASC, `user_id` ASC, `is_admin` ASC, `father_id` ASC) USING BTREE,
  INDEX `zh10_index`(`is_del` ASC, `is_emcee` ASC, `chat_room_id` ASC, `add_emcee_time` ASC) USING BTREE,
  INDEX `zh11_index`(`is_del` ASC, `chat_room_id` ASC, `is_wheat_serving` ASC, `is_join` ASC, `wheat_bit` ASC, `user_id` ASC) USING BTREE,
  INDEX `zh12_index`(`is_del` ASC, `chat_room_id` ASC, `is_wheat_serving` ASC, `is_join` ASC, `wheat_bit` ASC) USING BTREE,
  INDEX `zx_lb_index`(`chat_room_id` ASC, `is_join` ASC, `last_join_time` ASC) USING BTREE,
  INDEX `zh_lb2_index`(`chat_room_id` ASC, `last_join_time` ASC) USING BTREE,
  INDEX `zx_lb3_index`(`chat_room_id` ASC, `is_join` ASC, `is_admin` ASC, `last_join_time` ASC) USING BTREE,
  INDEX `zh_lb4_index`(`chat_room_id` ASC, `is_join` ASC, `is_emcee` ASC, `last_join_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7293 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'App聊天室用户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_chatting_records
-- ----------------------------
DROP TABLE IF EXISTS `app_chatting_records`;
CREATE TABLE `app_chatting_records`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `send_user_id` bigint NULL DEFAULT 0 COMMENT '发送人id',
  `receive_user_id` bigint NULL DEFAULT 0 COMMENT '接收人id',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '发送内容,格式由前端控制',
  `send_time` datetime NULL DEFAULT NULL COMMENT '发送时间',
  `is_read` tinyint(1) NULL DEFAULT 0 COMMENT '接收人是否已读,0未读,1已读',
  `msg_type` int NULL DEFAULT 0 COMMENT '消息类型:1文本,2图片,3视频,4语音,5自定义消息',
  `is_family` tinyint(1) NULL DEFAULT 0 COMMENT '是否是家族;0否,1是',
  `family_id` bigint NULL DEFAULT 0 COMMENT '家族id,is_family为1时有效',
  `is_del` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `sender_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '发送者是否删除:0否,1是',
  `receiver_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '接收者是否删除:0否,1是',
  `sender_delete_time` datetime NULL DEFAULT NULL COMMENT '发送者删除时间',
  `receiver_delete_time` datetime NULL DEFAULT NULL COMMENT '接收者删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `send_user_id`(`send_user_id` ASC) USING BTREE,
  INDEX `receive_user_id`(`receive_user_id` ASC) USING BTREE,
  INDEX `family_id`(`family_id` ASC) USING BTREE,
  INDEX `is_read`(`is_read` ASC) USING BTREE,
  INDEX `msg_type`(`msg_type` ASC) USING BTREE,
  INDEX `z1_index`(`send_user_id` ASC, `receive_user_id` ASC, `is_family` ASC) USING BTREE,
  INDEX `z2_index`(`send_user_id` ASC, `receive_user_id` ASC, `is_family` ASC, `is_read` ASC) USING BTREE,
  INDEX `z3_index`(`receive_user_id` ASC, `family_id` ASC, `is_family` ASC, `is_read` ASC) USING BTREE,
  INDEX `z4_index`(`family_id` ASC, `is_family` ASC) USING BTREE,
  INDEX `z5_index`(`send_user_id` ASC, `receive_user_id` ASC, `is_family` ASC) USING BTREE,
  INDEX `z6_index`(`send_user_id` ASC, `receive_user_id` ASC, `is_family` ASC, `is_read` ASC) USING BTREE,
  INDEX `z7_index`(`receive_user_id` ASC, `family_id` ASC, `is_family` ASC, `is_read` ASC) USING BTREE,
  INDEX `z8_index`(`family_id` ASC, `is_family` ASC) USING BTREE,
  INDEX `z9_index`(`family_id` ASC, `is_family` ASC, `receive_user_id` ASC) USING BTREE,
  INDEX `z10_index`(`family_id` ASC, `is_family` ASC, `receive_user_id` ASC) USING BTREE,
  INDEX `content`(`content`(255) ASC) USING BTREE,
  INDEX `send_time`(`send_time` ASC) USING BTREE,
  INDEX `zh1_admin_index`(`is_family` ASC, `send_time` ASC) USING BTREE,
  INDEX `zh2_admin_index`(`is_family` ASC, `content`(255) ASC) USING BTREE,
  INDEX `zh3_admin_index`(`is_family` ASC, `send_user_id` ASC, `receive_user_id` ASC) USING BTREE,
  INDEX `zj_admin_zh`(`is_family` ASC, `send_user_id` ASC, `receive_user_id` ASC, `content`(255) ASC, `send_time` ASC) USING BTREE,
  INDEX `idx_sender_deleted`(`sender_deleted` ASC) USING BTREE,
  INDEX `idx_receiver_deleted`(`receiver_deleted` ASC) USING BTREE,
  INDEX `idx_sender_receiver_deleted`(`send_user_id` ASC, `receive_user_id` ASC, `sender_deleted` ASC, `receiver_deleted` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 79834 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '聊天记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_chest_config
-- ----------------------------
DROP TABLE IF EXISTS `app_chest_config`;
CREATE TABLE `app_chest_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `prize_pool_type` int NULL DEFAULT 0 COMMENT '奖池类型:1数量公池,2概率公池(废弃)',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `prize_pool_type`(`prize_pool_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 409 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '宝箱配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_chest_gift_config
-- ----------------------------
DROP TABLE IF EXISTS `app_chest_gift_config`;
CREATE TABLE `app_chest_gift_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `ratio` decimal(20, 0) NULL DEFAULT 0 COMMENT '投放数量',
  `gitt_id` bigint NULL DEFAULT 0 COMMENT '进阶池id',
  `bind_gift_id` bigint NULL DEFAULT 0 COMMENT '绑定的礼物id',
  `gl` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '概率(概率池)',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `gitt_id`(`gitt_id` ASC) USING BTREE,
  INDEX `bind_gift_id`(`bind_gift_id` ASC) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `zh_index`(`is_del` ASC, `gitt_id` ASC) USING BTREE,
  INDEX `zh2_index`(`is_del` ASC, `bind_gift_id` ASC, `gitt_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '宝箱礼物配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_chest_upgrade_config
-- ----------------------------
DROP TABLE IF EXISTS `app_chest_upgrade_config`;
CREATE TABLE `app_chest_upgrade_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '名称',
  `min_amount` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '最小金额',
  `max_amount` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '最大金额',
  `sum_put` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '数量公池-总投入金额',
  `sum_out` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '数量公池-总产出金额',
  `sum_profit` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '数量公池-盈利金额',
  `extracted_num` bigint NULL DEFAULT 0 COMMENT '数量公池-已抽取轮次',
  `sum_put_gl` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '概率公池-总投入金额',
  `sum_out_gl` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '概率公池-总产出金额',
  `sum_profit_gl` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '概率公池-盈利金额',
  `sum_turn_gl` bigint NULL DEFAULT 0 COMMENT '概率公池-总轮次',
  `extracted_num_gl` bigint NULL DEFAULT 0 COMMENT '概率公池-已抽取轮次',
  `chest_gift_id` bigint NULL DEFAULT 0 COMMENT '宝箱礼物id',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `min_kzl` decimal(20, 2) NULL DEFAULT -1.00 COMMENT '最小控制率',
  `max_kzl` decimal(20, 2) NULL DEFAULT -1.00 COMMENT '最大控制率',
  `slc_bl` decimal(20, 2) NULL DEFAULT 1.00 COMMENT '数量公池爆率',
  `mzls_jc_type` int NULL DEFAULT 2 COMMENT '奖池类型:1数量公池,2概率池',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `chest_gift_id`(`chest_gift_id` ASC) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `zh_index`(`is_del` ASC, `chest_gift_id` ASC) USING BTREE,
  INDEX `min_amount`(`min_amount` ASC) USING BTREE,
  INDEX `max_amount`(`max_amount` ASC) USING BTREE,
  INDEX `z2_index`(`is_del` ASC, `chest_gift_id` ASC, `min_amount` ASC, `max_amount` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 418 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '宝箱进阶池配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_communicate_telephone_records
-- ----------------------------
DROP TABLE IF EXISTS `app_communicate_telephone_records`;
CREATE TABLE `app_communicate_telephone_records`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `initiate_user_id` bigint NULL DEFAULT 0 COMMENT '发起用户id',
  `receive_user_id` bigint NULL DEFAULT NULL COMMENT '接收用户id',
  `type` int NULL DEFAULT 0 COMMENT '通话类型:1语音,2视频,具体参考java枚举类AppCommunicateTelephoneTypeEnums',
  `status` int NULL DEFAULT 0 COMMENT '状态:0呼叫中,1通话中,2拒接,3通话结束,具体参考java枚举类AppCommunicateTelephoneStatusTypeEnums',
  `connect_time` datetime NULL DEFAULT NULL COMMENT '接通时间,状态1,3有效',
  `hang_up_time` datetime NULL DEFAULT NULL COMMENT '挂断时间,状态为2,3有效',
  `hang_up_user_id` bigint NULL DEFAULT 0 COMMENT '操作挂断的用户id,状态为2,3有效',
  `consumption_gold` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '本次通话消费的金币',
  `use_video_card` tinyint NULL DEFAULT 0 COMMENT '是否使用视频卡 0=否 1=是',
  `video_card_amount` int NULL DEFAULT NULL COMMENT '使用视频卡数量',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `type`(`type` ASC) USING BTREE,
  INDEX `status`(`status` ASC) USING BTREE,
  INDEX `initiate_user_id`(`initiate_user_id` ASC) USING BTREE,
  INDEX `receive_user_id`(`receive_user_id` ASC) USING BTREE,
  INDEX `zh_index`(`type` ASC, `status` ASC, `initiate_user_id` ASC, `receive_user_id` ASC) USING BTREE,
  INDEX `zh2_index`(`type` ASC, `status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14852 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '通话记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_community
-- ----------------------------
DROP TABLE IF EXISTS `app_community`;
CREATE TABLE `app_community`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '发布的内容',
  `picture_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '图片地址,json格式',
  `page_view` bigint NULL DEFAULT 0 COMMENT '浏览量',
  `longitude` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '经度,is_select_position为1有效',
  `latitude` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '纬度,is_select_position为1有效',
  `province` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '省,is_select_position为1有效',
  `city` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '市,is_select_position为1有效',
  `area` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '区,is_select_position为1有效',
  `is_select_position` tinyint(1) NULL DEFAULT 0 COMMENT '是否选择了位置,0否,1是',
  `video_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '视频地址,json格式',
  `is_video` tinyint(1) NULL DEFAULT 0 COMMENT '是否是视频:0否,1是',
  `voice_signature` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '语音标签',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '修改者账号',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除,0否,1是',
  `zone_id` tinyint NULL DEFAULT NULL COMMENT '圈子id',
  `topic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '话题',
  `only_me` tinyint NOT NULL DEFAULT 0 COMMENT '是否仅自己可见，0否，1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `z1_index`(`is_del` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2610 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '社区帖子' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_community_comment
-- ----------------------------
DROP TABLE IF EXISTS `app_community_comment`;
CREATE TABLE `app_community_comment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `community_id` bigint NULL DEFAULT 0 COMMENT '社区帖子id',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '评论内容',
  `father_comment_id` bigint NULL DEFAULT 0 COMMENT '父级评论id,大于0有效',
  `province` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '省',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '市',
  `district` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '区',
  `longitude` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '经度',
  `latitude` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '纬度',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '修改者账号',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `community_id`(`community_id` ASC) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `z1_index`(`community_id` ASC, `is_del` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 659 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '社区帖子评论' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_community_give_like
-- ----------------------------
DROP TABLE IF EXISTS `app_community_give_like`;
CREATE TABLE `app_community_give_like`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `community_id` bigint NULL DEFAULT 0 COMMENT '社区帖子id',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `community_id`(`community_id` ASC) USING BTREE,
  INDEX `z1_index`(`user_id` ASC, `community_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 622 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '社区帖子点赞' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_community_zone
-- ----------------------------
DROP TABLE IF EXISTS `app_community_zone`;
CREATE TABLE `app_community_zone`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '序号',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `icon` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
  `user_id` int NULL DEFAULT NULL COMMENT '圈主id',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '删除标记0有效,1删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '圈子' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_complete_task_record
-- ----------------------------
DROP TABLE IF EXISTS `app_complete_task_record`;
CREATE TABLE `app_complete_task_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `task_type` int NULL DEFAULT 0 COMMENT '任务类型:参考java枚举类AppTaskTypeEnums',
  `points` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '获得积分:除了每日签到外都是积分',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `task_type`(`task_type` ASC) USING BTREE,
  INDEX `create_time`(`create_time` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `task_type` ASC, `create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17932 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户完成任务记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_config
-- ----------------------------
DROP TABLE IF EXISTS `app_config`;
CREATE TABLE `app_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `service_user_id` bigint NULL DEFAULT 0 COMMENT '客服用户id',
  `family_max_num` int NULL DEFAULT 100 COMMENT '家族最大人数',
  `voice_minutes_gold` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '语音通话每分钟费用',
  `video_minutes_gold` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '视频通话每分钟费用',
  `is_enable_send_virtual_msg` tinyint(1) NULL DEFAULT 0 COMMENT '是否启动发送虚拟消息:0否,1是',
  `communicate_telephone_income_scale` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '通话收入比例',
  `temporarily_gold_price` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '打招呼每条价格',
  `send_msg_gold_price` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '发送消息每条价格',
  `temporarily_income_scale` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '打招呼收入比例',
  `send_msg_income_scale` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '发送消息收入比例',
  `gift_income_scale` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '礼物收入比例',
  `one_gold_eq_rmb` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '一金币等于多少人民币',
  `one_rmb_eq_points` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '一人民币等于多少积分',
  `one_points_eq_rmb` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '一积分等于多少人民币',
  `one_points_eq_gold` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '积分等于多少钻石',
  `one_video_card_eq_gold` decimal(10, 2) NULL DEFAULT NULL COMMENT '一视频卡等于多少金币',
  `is_enable_chat_helper` tinyint NULL DEFAULT NULL COMMENT '是否启用搭讪助手',
  `min_withdraw_points` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '最低提现积分',
  `invite_gold` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '邀请用户获得金币数量',
  `invite_topup_scale` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '邀请用户充值比例',
  `invite_give_gift_scale` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '邀请用户送礼比例',
  `share_url` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'h5分享url',
  `new_user_gold_count` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '新用户赠送金币数量',
  `withdraw_service_charge_scale` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '提现手续费比例',
  `recharge_limit_amount` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '充值限额',
  `guild_commission_scale` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '公会会长抽成比例',
  `hall_owner_commission_scale` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '厅主抽成比例',
  `is_floating_screen` tinyint(1) NULL DEFAULT 0 COMMENT '飘屏:0关,1开',
  `is_chest` tinyint(1) NULL DEFAULT 0 COMMENT '宝箱:0关,1开',
  `turntable_1_price` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '转盘抽1次的金币价格',
  `turntable_10_price` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '转盘抽10次的金币价格',
  `turntable_100_price` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '转盘抽100次的金币价格',
  `turntable_status` int NULL DEFAULT 0 COMMENT '转盘状态:0关闭,1开启',
  `is_all_floating_screen` tinyint(1) NULL DEFAULT 0 COMMENT '全服飘屏:0关,1开',
  `is_open_gf_wx_pay` tinyint(1) NULL DEFAULT 0 COMMENT '官方微信支付状态:0关闭,1开启',
  `is_open_gf_zfb_pay` tinyint(1) NULL DEFAULT 0 COMMENT '官方支付宝支付状态:0关闭,1开启',
  `is_open_sd_wx_pay` tinyint(1) NULL DEFAULT 0 COMMENT '精秀微信支付状态:0关闭,1开启',
  `is_open_sd_zfb_pay` tinyint(1) NULL DEFAULT 0 COMMENT '精秀支付宝支付状态:0关闭,1开启',
  `zfb_tx_config_id` bigint NULL DEFAULT 0 COMMENT '启用的提现打款支付宝id',
  `mzls_jc_type` int NULL DEFAULT 2 COMMENT '猫抓老鼠奖池类型:1数量公池,2概率池',
  `bx_min_kzl` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '宝箱最小控制率',
  `bx_max_kzl` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '宝箱最大控制率',
  `mzls_min_kzl` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '猫抓老鼠最小控制率',
  `mzls_max_kzl` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '猫抓老鼠最大控制率',
  `bx_slc_bl` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '宝箱数量公池爆率',
  `mzls_slc_bl` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '猫抓老鼠数量公池爆率',
  `skill_price_scale` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '技能收益比例',
  `bx_jc_type` int NULL DEFAULT 2 COMMENT '宝箱奖池类型:1数量公池,2概率池',
  `sum_put_gl` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '猫抓老鼠-概率公池-总投入金额',
  `sum_out_gl` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '猫抓老鼠-概率公池-总产出金额',
  `sum_profit_gl` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '猫抓老鼠-概率公池-盈利金额',
  `extracted_num_gl` bigint NULL DEFAULT 0 COMMENT '猫抓老鼠-概率公池-已抽取轮次',
  `mzls_bl_gl` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '猫抓老鼠-概率公池爆率',
  `min_kzl` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '猫爪老鼠-最小控制率',
  `max_kzl` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '猫爪老鼠-最大控制率',
  `game_show_amount` decimal(10, 4) NULL DEFAULT 0.0000 COMMENT '开蛋金额',
  `chat_room_gift_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '房间礼物飘屏价格',
  `full_server_floating_screen_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '全服飘屏价格',
  `home_gift_top_line` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '首页头条礼物显示钻石线',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者账号',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者账号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'app配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_content_check
-- ----------------------------
DROP TABLE IF EXISTS `app_content_check`;
CREATE TABLE `app_content_check`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `type` int NOT NULL COMMENT '审核类型 1.文本 2.图片 3.音频 4.视频 5.音频流 6.视频流',
  `scene` int NOT NULL COMMENT '场景 1.私聊 2.广场 3.认证 4.音乐 5.个人资料',
  `category` int NULL DEFAULT NULL COMMENT '类别 1.头像 2.相册 3.昵称 4.个性签名 场景为5时使用',
  `send_user_id` bigint NOT NULL COMMENT '发送人id',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '发送内容',
  `biz_id` bigint NULL DEFAULT NULL COMMENT '关联业务表id',
  `task_id` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '腾讯云任务id',
  `tencent_check` tinyint NULL DEFAULT NULL COMMENT '腾讯云审核结果 0.否 1.是(通过)',
  `tencent_label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '腾讯云标签',
  `tencent_suggestion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '腾讯云建议',
  `manual_check` tinyint NULL DEFAULT NULL COMMENT '人工审核结果 0.否 1.是(通过)',
  `check_user_id` bigint NULL DEFAULT NULL COMMENT '审核人id',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0未删除1已删除',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `type`(`type` ASC) USING BTREE,
  INDEX `scene`(`scene` ASC) USING BTREE,
  INDEX `send_user_id`(`send_user_id` ASC) USING BTREE,
  INDEX `biz_id`(`biz_id` ASC) USING BTREE,
  INDEX `tencent_check`(`tencent_check` ASC) USING BTREE,
  INDEX `tencent_suggestion`(`tencent_suggestion` ASC) USING BTREE,
  INDEX `manual_check`(`manual_check` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 58402 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '内容审核表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_context_owner
-- ----------------------------
DROP TABLE IF EXISTS `app_context_owner`;
CREATE TABLE `app_context_owner`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `chat_room_id` bigint NULL DEFAULT 0 COMMENT '房间id',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `date_str` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '日期字符串:0000-00-00 00格式',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `chat_room_id`(`chat_room_id` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `date_str`(`date_str` ASC) USING BTREE,
  INDEX `zh_index`(`chat_room_id` ASC, `user_id` ASC, `date_str` ASC) USING BTREE,
  INDEX `z1_index`(`chat_room_id` ASC, `date_str` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '排版表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_dress_up_chest_config
-- ----------------------------
DROP TABLE IF EXISTS `app_dress_up_chest_config`;
CREATE TABLE `app_dress_up_chest_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `ratio` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '中奖比例',
  `chest_id` bigint NULL DEFAULT 0 COMMENT '宝箱id',
  `dress_up_id` bigint NULL DEFAULT 0 COMMENT '绑定的装扮id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `chest_id`(`chest_id` ASC) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `dress_up_id`(`dress_up_id` ASC) USING BTREE,
  INDEX `zh_index`(`is_del` ASC, `chest_id` ASC, `dress_up_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '宝箱装扮配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_dress_up_chest_main_config
-- ----------------------------
DROP TABLE IF EXISTS `app_dress_up_chest_main_config`;
CREATE TABLE `app_dress_up_chest_main_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `chest_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '宝箱名称',
  `price` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '宝箱金币价格',
  `give_gif_img_url` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '宝箱动图',
  `img_url` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '宝箱图片',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `chest_name`(`chest_name` ASC) USING BTREE,
  INDEX `zh_index`(`is_del` ASC, `chest_name` ASC) USING BTREE,
  INDEX `zh2_index`(`is_del` ASC, `price` ASC) USING BTREE,
  INDEX `price`(`price` ASC) USING BTREE,
  INDEX `zh3_index`(`is_del` ASC, `chest_name` ASC, `price` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '装扮宝箱配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_emote
-- ----------------------------
DROP TABLE IF EXISTS `app_emote`;
CREATE TABLE `app_emote`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '表情包地址',
  `order_no` int NULL DEFAULT NULL COMMENT '排序字段',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `deleted` tinyint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 60 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '表情' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_family
-- ----------------------------
DROP TABLE IF EXISTS `app_family`;
CREATE TABLE `app_family`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `user_id` bigint NULL DEFAULT 0 COMMENT '群主用户id',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `people_num` int NULL DEFAULT 0 COMMENT '已有人数',
  `max_people_num` int NULL DEFAULT 0 COMMENT '最大人数',
  `group_number` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '群号',
  `family_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '家族名称',
  `greeting` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '欢迎语',
  `head_portrait` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '家族头像',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `is_del` ASC) USING BTREE,
  INDEX `zh2_index`(`id` ASC, `user_id` ASC, `is_del` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '家族' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_family_apply
-- ----------------------------
DROP TABLE IF EXISTS `app_family_apply`;
CREATE TABLE `app_family_apply`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `initiate_user_id` bigint NULL DEFAULT 0 COMMENT '发起用户id',
  `family_id` bigint NULL DEFAULT 0 COMMENT '家族id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `status` int NULL DEFAULT 0 COMMENT '状态:0待对方同意,1已同意,2已拒绝',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `family_id`(`family_id` ASC) USING BTREE,
  INDEX `status`(`status` ASC) USING BTREE,
  INDEX `zh_index`(`family_id` ASC, `status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '家族进群申请' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_family_member
-- ----------------------------
DROP TABLE IF EXISTS `app_family_member`;
CREATE TABLE `app_family_member`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `family_id` bigint NULL DEFAULT 0 COMMENT '家族id',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `last_dynamic_time` datetime NULL DEFAULT NULL COMMENT '最近活跃时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `family_id`(`family_id` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `zh_index`(`family_id` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '家族成员' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_feedback
-- ----------------------------
DROP TABLE IF EXISTS `app_feedback`;
CREATE TABLE `app_feedback`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `type_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '反馈类型内容',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '反馈内容',
  `img_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '图片,格式由前端控制',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '反馈' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_forbidden_equipment
-- ----------------------------
DROP TABLE IF EXISTS `app_forbidden_equipment`;
CREATE TABLE `app_forbidden_equipment`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `equipment_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备id',
  `equipment_type` tinyint(1) NULL DEFAULT 0 COMMENT '设备类型:1:android,2:ios',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `zh_index`(`equipment_id` ASC, `equipment_type` ASC, `end_time` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '禁用设备' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_front_page
-- ----------------------------
DROP TABLE IF EXISTS `app_front_page`;
CREATE TABLE `app_front_page`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '内容',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `chat_room_id` bigint NULL DEFAULT 0 COMMENT '聊天室id',
  `price` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '价格',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `zh_index`(`is_del` ASC, `user_id` ASC) USING BTREE,
  INDEX `chat_room_id`(`chat_room_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '头条' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_gift
-- ----------------------------
DROP TABLE IF EXISTS `app_gift`;
CREATE TABLE `app_gift`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `gift_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '礼物名称',
  `category_id` bigint NULL DEFAULT 0 COMMENT '分类id',
  `masonry_price` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '使用金币购买的价格',
  `img_url` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '礼物图片',
  `give_gif_img_url` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '送礼的动图',
  `sales` bigint NULL DEFAULT 0 COMMENT '销量',
  `effect_picture_url` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '效果图',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `bind_personal_dressing_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '绑定的个性化商品id集合,如[1,2]',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `luck_flag` tinyint(1) NULL DEFAULT 0 COMMENT '盲盒礼物标识:0不是,1是的',
  `luck_id` int NULL DEFAULT NULL COMMENT '盲盒礼物对应的礼物id',
  `luck_weight` int NULL DEFAULT 0 COMMENT '盲盒礼物抽奖权重',
  `luck_img` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '盲盒礼物宣传图片',
  `rate` decimal(20, 2) NULL DEFAULT NULL COMMENT '爆率',
  `luck_pool_type` tinyint(1) NULL DEFAULT 0 COMMENT '盲盒奖池类型：1-数量池，2-概率池（默认）',
  `luck_num_init` int NULL DEFAULT 10 COMMENT '盲盒数量池初始数量',
  `luck_total_num` int NULL DEFAULT 0 COMMENT '盲盒礼物累计总数量',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_id`(`category_id` ASC) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `gift_name`(`gift_name` ASC) USING BTREE,
  INDEX `zh_index`(`id` ASC, `category_id` ASC, `is_del` ASC) USING BTREE,
  INDEX `zh2_index`(`category_id` ASC, `is_del` ASC, `gift_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1101 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '礼物' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_gift_category
-- ----------------------------
DROP TABLE IF EXISTS `app_gift_category`;
CREATE TABLE `app_gift_category`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类名称',
  `ico_url` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类图标',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `type` tinyint NULL DEFAULT NULL COMMENT '礼物分类类型 1、直刷礼物 2、游戏礼物',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `category_name`(`category_name` ASC) USING BTREE,
  INDEX `is_del`(`deleted` ASC) USING BTREE,
  INDEX `zh_index`(`id` ASC, `deleted` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 78 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '礼物分类' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_gift_income_config
-- ----------------------------
DROP TABLE IF EXISTS `app_gift_income_config`;
CREATE TABLE `app_gift_income_config`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '序号',
  `value` int NULL DEFAULT NULL COMMENT '本月收入金额',
  `ratio` decimal(20, 2) NULL DEFAULT NULL COMMENT '比例',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '每月礼物收入比例区间' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_gold_topup_config
-- ----------------------------
DROP TABLE IF EXISTS `app_gold_topup_config`;
CREATE TABLE `app_gold_topup_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `is_ios` tinyint(1) NULL DEFAULT 0 COMMENT '是否是ios内购:0否,1是',
  `ios_id` bigint NULL DEFAULT 0 COMMENT 'ios内购id:is_ios为1有效',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `price` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '价格',
  `gold_num` bigint NULL DEFAULT 0 COMMENT '金币数量',
  `give_num` bigint NULL DEFAULT 0 COMMENT '赠送数量',
  `video_card_amount` int NULL DEFAULT 0 COMMENT '赠送视频卡数量',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 49 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '金币充值配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_good_number
-- ----------------------------
DROP TABLE IF EXISTS `app_good_number`;
CREATE TABLE `app_good_number`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `good_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '靓号',
  `status` int NULL DEFAULT 0 COMMENT '靓号状态：参考AppGoodNumberStatusTypeEnum枚举',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `masonry_price` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '钻石价格',
  `buy_user_id` bigint NULL DEFAULT 0 COMMENT '购买者用户id,状态为已出售有效',
  `buy_order_id` bigint NULL DEFAULT 0 COMMENT '购买者的订单id,状态为已出售有效',
  `buy_time` datetime NULL DEFAULT NULL COMMENT '购买时间,状态为已出售有效',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `good_number`(`good_number` ASC) USING BTREE,
  INDEX `status`(`status` ASC) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `buy_user_id`(`buy_user_id` ASC) USING BTREE,
  INDEX `buy_order_id`(`buy_order_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '靓号' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_guild
-- ----------------------------
DROP TABLE IF EXISTS `app_guild`;
CREATE TABLE `app_guild`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '会长用户id',
  `avatar` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '公会头像',
  `guild_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '公会昵称',
  `intro` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '公会简介',
  `guild_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '公会ID',
  `guild_commission_scale` decimal(20, 2) NULL DEFAULT 0.65 COMMENT '公会长收益比例',
  `to_be_settled` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '待结算金额',
  `is_top` tinyint(1) NULL DEFAULT 0 COMMENT '是否置顶',
  `sys_user_id` int NULL DEFAULT NULL COMMENT '操作人id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `guild_name`(`guild_name` ASC) USING BTREE,
  INDEX `guild_number`(`guild_number` ASC) USING BTREE,
  INDEX `zh_index`(`is_del` ASC, `user_id` ASC) USING BTREE,
  INDEX `zh2_index`(`is_del` ASC, `user_id` ASC, `guild_name` ASC, `guild_number` ASC) USING BTREE,
  INDEX `zh3_index`(`is_del` ASC, `guild_name` ASC, `guild_number` ASC) USING BTREE,
  INDEX `zh4_index`(`is_del` ASC, `to_be_settled` ASC) USING BTREE,
  INDEX `zh5_index`(`is_del` ASC, `is_top` ASC) USING BTREE,
  INDEX `zh6_index`(`is_del` ASC, `guild_name` ASC, `guild_number` ASC, `is_top` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '公会' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_guild_apply
-- ----------------------------
DROP TABLE IF EXISTS `app_guild_apply`;
CREATE TABLE `app_guild_apply`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `initiate_user_id` bigint NULL DEFAULT 0 COMMENT '发起用户id',
  `guild_id` bigint NULL DEFAULT 0 COMMENT '公会id',
  `status` int NULL DEFAULT 0 COMMENT '状态:0待对方同意,1已同意,2已拒绝',
  `remarks` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '备注',
  `sys_user_id` int NULL DEFAULT NULL COMMENT '经纪人id',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `type` tinyint NULL DEFAULT 1 COMMENT '类型1,主动申请,2会长邀请',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `guild_id`(`guild_id` ASC) USING BTREE,
  INDEX `status`(`status` ASC) USING BTREE,
  INDEX `zh_index`(`guild_id` ASC, `status` ASC) USING BTREE,
  INDEX `zh2_index`(`guild_id` ASC, `status` ASC, `initiate_user_id` ASC) USING BTREE,
  INDEX `zh3_index`(`guild_id` ASC, `initiate_user_id` ASC) USING BTREE,
  INDEX `initiate_user_id`(`initiate_user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 377 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '公会入会申请' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_guild_earnings_records
-- ----------------------------
DROP TABLE IF EXISTS `app_guild_earnings_records`;
CREATE TABLE `app_guild_earnings_records`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `guild_id` bigint NULL DEFAULT 0 COMMENT '公会id',
  `amount` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '金额',
  `trigger_obecjt_id` bigint NULL DEFAULT 0 COMMENT '触发收益的用户id',
  `receive_object_id` bigint NULL DEFAULT 0 COMMENT '接收触发收益的用户id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `guild_id`(`guild_id` ASC) USING BTREE,
  INDEX `create_time`(`create_time` ASC) USING BTREE,
  INDEX `amount`(`amount` ASC) USING BTREE,
  INDEX `zh_index`(`guild_id` ASC, `amount` ASC, `create_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '公会收益记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_guild_member
-- ----------------------------
DROP TABLE IF EXISTS `app_guild_member`;
CREATE TABLE `app_guild_member`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `guild_id` bigint NULL DEFAULT 0 COMMENT '公会id',
  `gift_income_scale` decimal(20, 2) NULL DEFAULT 0.60 COMMENT '礼物收益比例',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `is_admin` tinyint(1) NULL DEFAULT 0 COMMENT '是否是管理员:0否,1是',
  `sys_user_id` int NULL DEFAULT NULL COMMENT '平台操作人id(经纪人id)',
  `type` tinyint NULL DEFAULT NULL COMMENT '主播类型',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `guild_id`(`guild_id` ASC) USING BTREE,
  INDEX `is_admin`(`is_admin` ASC) USING BTREE,
  INDEX `zh_index`(`guild_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `zh2_index`(`user_id` ASC, `guild_id` ASC, `create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 367 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '公会成员' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_guild_return_records
-- ----------------------------
DROP TABLE IF EXISTS `app_guild_return_records`;
CREATE TABLE `app_guild_return_records`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `guild_id` bigint NULL DEFAULT 0 COMMENT '公会id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `status` int NULL DEFAULT 0 COMMENT '状态:0等待中,1已退会',
  `end_time` datetime NULL DEFAULT NULL COMMENT '结束时间',
  `sys_user_id` int NULL DEFAULT NULL COMMENT '操作人id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `status`(`status` ASC) USING BTREE,
  INDEX `zh_index`(`guild_id` ASC, `status` ASC) USING BTREE,
  INDEX `guild_id`(`guild_id` ASC) USING BTREE,
  INDEX `zh2_index`(`guild_id` ASC, `status` ASC, `user_id` ASC) USING BTREE,
  INDEX `zh3_index`(`guild_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `end_time`(`end_time` ASC) USING BTREE,
  INDEX `zh4_index`(`status` ASC, `end_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '公会退会记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_hall_owner_earnings_records
-- ----------------------------
DROP TABLE IF EXISTS `app_hall_owner_earnings_records`;
CREATE TABLE `app_hall_owner_earnings_records`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `amount` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '金额',
  `trigger_obecjt_id` bigint NULL DEFAULT 0 COMMENT '触发收益的用户id',
  `receive_object_id` bigint NULL DEFAULT 0 COMMENT '接收触发收益的用户id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `create_time`(`create_time` ASC) USING BTREE,
  INDEX `amount`(`amount` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `amount` ASC, `create_time` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `zh2_index`(`user_id` ASC, `create_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '厅主收益记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_html
-- ----------------------------
DROP TABLE IF EXISTS `app_html`;
CREATE TABLE `app_html`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标题',
  `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '内容,富文本格式',
  `type` int NULL DEFAULT 0 COMMENT '类型:参考java类中枚举：HtmlTypeEnums',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否，1是',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '静态页面表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_ip_banned
-- ----------------------------
DROP TABLE IF EXISTS `app_ip_banned`;
CREATE TABLE `app_ip_banned`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'ip地址',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ip_address`(`ip_address` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'Ip封禁' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_mzls_bfjl
-- ----------------------------
DROP TABLE IF EXISTS `app_mzls_bfjl`;
CREATE TABLE `app_mzls_bfjl`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `jjc_id` bigint NULL DEFAULT 0 COMMENT '进阶池id',
  `gift_id` bigint NULL DEFAULT NULL COMMENT '补发的礼物id',
  `gift_num` bigint NULL DEFAULT 0 COMMENT '补发礼物的数量',
  `gift_price` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '补发的礼物单价',
  `sum_gift_price` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '本次补发总价',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `is_sub_kc` tinyint(1) NULL DEFAULT 0 COMMENT '核销状态:0未,1已核销',
  `sub_num` bigint NULL DEFAULT 0 COMMENT '剩余核销数量',
  `sub_time` datetime NULL DEFAULT NULL COMMENT '最近核销时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sub_num`(`sub_num` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `gift_id`(`gift_id` ASC) USING BTREE,
  INDEX `jjc_id`(`jjc_id` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `sub_num` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '猫抓老鼠补发记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_mzls_user_tj
-- ----------------------------
DROP TABLE IF EXISTS `app_mzls_user_tj`;
CREATE TABLE `app_mzls_user_tj`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `jjc_id` bigint NULL DEFAULT 0 COMMENT '进阶池id',
  `sr_sum` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '总收入',
  `zc_sum` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '总支出',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `game_id` bigint NOT NULL,
  `device_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户当前设备设备类型:1:android,2:ios',
  `user_login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户登录ip',
  `type` tinyint NULL DEFAULT 0 COMMENT '1:数量池,2概率池',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `jjc_id`(`jjc_id` ASC) USING BTREE,
  INDEX `create_time`(`create_time` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `jjc_id` ASC, `create_time` ASC) USING BTREE,
  INDEX `z2_index`(`user_id` ASC, `jjc_id` ASC) USING BTREE,
  INDEX `z3_index`(`user_id` ASC, `create_time` ASC) USING BTREE,
  INDEX `z4_index`(`jjc_id` ASC, `create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '猫抓老鼠用户开奖统计' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_order
-- ----------------------------
DROP TABLE IF EXISTS `app_order`;
CREATE TABLE `app_order`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_no` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订单编号',
  `order_status` int NULL DEFAULT 0 COMMENT '订单状态:0待支付,1已完成',
  `goods_id` bigint NULL DEFAULT 0 COMMENT '购买的原商品id',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `order_price` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '订单金额',
  `pay_channel_type` int NULL DEFAULT 0 COMMENT '支付方式:参考java枚举类AppPayChannelTypeEnum',
  `goods_info_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '购买的商品详细信息,json格式',
  `order_type` int NULL DEFAULT 0 COMMENT '订单类型:参考java枚举类AppOrderTypeEnums',
  `buy_num` bigint NULL DEFAULT 0 COMMENT '购买数量',
  `pay_time` datetime NULL DEFAULT NULL COMMENT '支付时间,订单状态为1有效',
  `pay_end_time` datetime NULL DEFAULT NULL COMMENT '支付结束时间',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_status`(`order_status` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `order_type`(`order_type` ASC) USING BTREE,
  INDEX `z_index`(`order_status` ASC, `user_id` ASC, `is_del` ASC, `order_type` ASC) USING BTREE,
  INDEX `z2_index`(`order_no` ASC, `is_del` ASC) USING BTREE,
  INDEX `order_no`(`order_no` ASC) USING BTREE,
  INDEX `create_time`(`create_time` ASC) USING BTREE,
  INDEX `order_price`(`order_price` ASC) USING BTREE,
  INDEX `z1_index`(`is_del` ASC, `order_status` ASC, `order_type` ASC, `create_time` ASC, `order_price` ASC) USING BTREE,
  INDEX `z3_index`(`is_del` ASC, `order_status` ASC, `order_type` ASC, `create_time` ASC, `order_price` ASC, `user_id` ASC) USING BTREE,
  INDEX `z4_index`(`order_status` ASC, `order_type` ASC, `is_del` ASC, `create_time` ASC, `order_price` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2964 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'APP订单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_personal_dressing
-- ----------------------------
DROP TABLE IF EXISTS `app_personal_dressing`;
CREATE TABLE `app_personal_dressing`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '名称',
  `img_url` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图片地址',
  `gif_url` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '动图地址',
  `valid_days` int NULL DEFAULT 0 COMMENT '有效期天数',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `color_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '颜色值,分类为彩色昵称有效',
  `category_id` bigint NULL DEFAULT 0 COMMENT '分类id:参考java枚举类AppPersonalDressingCategoryEnums',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `gold_price` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '金币价格',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `category_id`(`category_id` ASC) USING BTREE,
  INDEX `zh1_index`(`id` ASC, `category_id` ASC) USING BTREE,
  INDEX `gold_price`(`gold_price` ASC) USING BTREE,
  INDEX `zh2_index`(`is_del` ASC, `id` ASC, `category_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 244 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '个性装扮' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_points_buy_gold_config
-- ----------------------------
DROP TABLE IF EXISTS `app_points_buy_gold_config`;
CREATE TABLE `app_points_buy_gold_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `points_price` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '积分价格',
  `gold_num` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '金币数量',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '积分兑换金币套餐' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_private_letter_list
-- ----------------------------
DROP TABLE IF EXISTS `app_private_letter_list`;
CREATE TABLE `app_private_letter_list`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '消息触发用户id',
  `receive_user_id` bigint NULL DEFAULT 0 COMMENT '接收人id',
  `is_family` tinyint(1) NULL DEFAULT 0 COMMENT '是否是家族;0否,1是',
  `family_id` bigint NULL DEFAULT 0 COMMENT '家族id,is_family为1时有效',
  `is_temporarily` tinyint(1) NULL DEFAULT 0 COMMENT '是否是临时消息:0否,1是',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_top` tinyint(1) NULL DEFAULT 0 COMMENT '是否置顶',
  `remark_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `receive_user_id`(`receive_user_id` ASC) USING BTREE,
  INDEX `is_family`(`is_family` ASC) USING BTREE,
  INDEX `family_id`(`family_id` ASC) USING BTREE,
  INDEX `is_temporarily`(`is_temporarily` ASC) USING BTREE,
  INDEX `update_time`(`update_time` ASC) USING BTREE,
  INDEX `z1_index`(`user_id` ASC, `receive_user_id` ASC, `is_family` ASC, `is_temporarily` ASC) USING BTREE,
  INDEX `z2_index`(`receive_user_id` ASC, `is_family` ASC, `is_temporarily` ASC) USING BTREE,
  INDEX `zh5_index`(`receive_user_id` ASC, `is_temporarily` ASC, `is_family` ASC, `update_time` ASC) USING BTREE,
  INDEX `zh6_index`(`receive_user_id` ASC, `is_temporarily` ASC, `update_time` ASC) USING BTREE,
  INDEX `zh8_index`(`receive_user_id` ASC, `is_temporarily` ASC, `is_family` ASC, `update_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 42214 CHARACTER SET = utf8mb3 COLLATE = utf8mb3_general_ci COMMENT = '私信列表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_problem
-- ----------------------------
DROP TABLE IF EXISTS `app_problem`;
CREATE TABLE `app_problem`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '内容',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `problem_type` int NULL DEFAULT 0 COMMENT '问题类型:参考AppProblemTypeEnums枚举',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `problem_type`(`problem_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '问题' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_report
-- ----------------------------
DROP TABLE IF EXISTS `app_report`;
CREATE TABLE `app_report`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '序号',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '说明原因',
  `type` tinyint NULL DEFAULT NULL COMMENT '枚举类型,对应字典值:sys_dict:app_report_config',
  `user_id` int NULL DEFAULT NULL COMMENT '举报人id',
  `to_user_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '被举报人id',
  `photos` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '图片数据',
  `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NULL DEFAULT NULL COMMENT '删除标记0有效,1删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '举报信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_room_broadcast
-- ----------------------------
DROP TABLE IF EXISTS `app_room_broadcast`;
CREATE TABLE `app_room_broadcast`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `room_id` bigint NOT NULL COMMENT '房间ID',
  `user_id` bigint NOT NULL COMMENT '发布者用户ID',
  `room_theme` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '房间主题',
  `room_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '房间名称',
  `room_requirements` json NULL COMMENT '房主要求,JSON数组格式',
  `broadcast_cost` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '发布房间费用',
  `start_time` datetime NOT NULL COMMENT '广播开始时间',
  `end_time` datetime NOT NULL COMMENT '广播结束时间',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态:0-已取消,1-生效中,2-已过期',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_room_id`(`room_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_end_time`(`end_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '房间广播消息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_room_lock_records
-- ----------------------------
DROP TABLE IF EXISTS `app_room_lock_records`;
CREATE TABLE `app_room_lock_records`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `chat_room_id` bigint NULL DEFAULT 0 COMMENT '房间id',
  `type` tinyint(1) NULL DEFAULT 0 COMMENT '类型:0锁房,1开房',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `chat_room_id`(`chat_room_id` ASC) USING BTREE,
  INDEX `type`(`type` ASC) USING BTREE,
  INDEX `create_time`(`create_time` ASC) USING BTREE,
  INDEX `zh_index`(`chat_room_id` ASC, `type` ASC) USING BTREE,
  INDEX `zh2_index`(`chat_room_id` ASC, `create_time` ASC) USING BTREE,
  INDEX `zh3_index`(`chat_room_id` ASC, `type` ASC, `create_time` ASC) USING BTREE,
  INDEX `zh4_index`(`chat_room_id` ASC, `create_time` ASC, `type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '锁房记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_sd_pay_config
-- ----------------------------
DROP TABLE IF EXISTS `app_sd_pay_config`;
CREATE TABLE `app_sd_pay_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '名称',
  `is_enable` tinyint(1) NULL DEFAULT 0 COMMENT '是否启用:0否,1是',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `mch_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商户id',
  `mch_key` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '平台公钥',
  `public_key_path` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '商户公钥',
  `private_key_path` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '商户私钥',
  `wx_sub_app_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '微信移动应用appid',
  `wx_gh_rri_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '微信小程序原始id',
  `wx_path_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '拉起拉起小程序页面地址',
  `wx_mini_program_type` int NULL DEFAULT 0 COMMENT '微信小程序版本:0正式版,1开发版,2体验版',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `is_enable`(`is_enable` ASC) USING BTREE,
  INDEX `zh_index`(`is_del` ASC, `is_enable` ASC) USING BTREE,
  INDEX `zh2_index`(`is_del` ASC, `is_enable` ASC) USING BTREE,
  INDEX `name`(`name` ASC) USING BTREE,
  INDEX `zh3_index`(`is_del` ASC, `is_enable` ASC, `name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '精秀支付配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_send_virtual_msg_records
-- ----------------------------
DROP TABLE IF EXISTS `app_send_virtual_msg_records`;
CREATE TABLE `app_send_virtual_msg_records`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '内容',
  `virtual_user_id` bigint NULL DEFAULT 0 COMMENT '虚拟用户id',
  `receive_user_id` bigint NULL DEFAULT 0 COMMENT '接收用户id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `virtual_user_id`(`virtual_user_id` ASC) USING BTREE,
  INDEX `receive_user_id`(`receive_user_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '发送虚拟消息记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_send_virtual_msg_task
-- ----------------------------
DROP TABLE IF EXISTS `app_send_virtual_msg_task`;
CREATE TABLE `app_send_virtual_msg_task`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `goal_user_id` bigint NULL DEFAULT 0 COMMENT '目标用户id',
  `is_pause` tinyint(1) NULL DEFAULT 0 COMMENT '任务是否暂停:0否,1是',
  `next_send_time` datetime NULL DEFAULT NULL COMMENT '下次发送时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `goal_user_id`(`goal_user_id` ASC) USING BTREE,
  INDEX `is_pause`(`is_pause` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3713383 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '发送虚拟消息任务' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_sensitive_lexicon
-- ----------------------------
DROP TABLE IF EXISTS `app_sensitive_lexicon`;
CREATE TABLE `app_sensitive_lexicon`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `content` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '内容',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `content`(`content` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 126 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '敏感词库' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_sign_gold_config
-- ----------------------------
DROP TABLE IF EXISTS `app_sign_gold_config`;
CREATE TABLE `app_sign_gold_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `week_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '星期名称',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `gold_num` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '金币数量',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `week_name`(`week_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '签到金币配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_sign_in_records
-- ----------------------------
DROP TABLE IF EXISTS `app_sign_in_records`;
CREATE TABLE `app_sign_in_records`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `gold` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '获得金币',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `create_time`(`create_time` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `create_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10025 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '签到记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_skill
-- ----------------------------
DROP TABLE IF EXISTS `app_skill`;
CREATE TABLE `app_skill`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '技能名称',
  `ico_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '技能图标',
  `instructions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '技能说明',
  `aptitude` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '资质要求',
  `is_game` tinyint(1) NULL DEFAULT 0 COMMENT '是否是游戏:0否,1是',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `img_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '技能证明参考图',
  `avatar_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像示例图',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '技能' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_skill_evaluate
-- ----------------------------
DROP TABLE IF EXISTS `app_skill_evaluate`;
CREATE TABLE `app_skill_evaluate`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '评价的用户id',
  `order_id` bigint NULL DEFAULT 0 COMMENT '订单id',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '评价内容',
  `user_skill_id` bigint NULL DEFAULT 0 COMMENT '用户的技能id',
  `mark` decimal(10, 1) NULL DEFAULT 5.0 COMMENT '评分',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_skill_id`(`user_skill_id` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `order_id`(`order_id` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `user_skill_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '技能评价' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_skill_order
-- ----------------------------
DROP TABLE IF EXISTS `app_skill_order`;
CREATE TABLE `app_skill_order`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '下单用户',
  `skill_user_id` bigint NULL DEFAULT 0 COMMENT '技能用户id',
  `user_skill_id` bigint NULL DEFAULT 0 COMMENT '用户的技能id',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '技能名称',
  `ico_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '技能图标',
  `is_game` tinyint(1) NULL DEFAULT 0 COMMENT '是否是游戏:0否,1是',
  `charge_type` int NULL DEFAULT -1 COMMENT '用户技能收费类型:0局,1小时',
  `price` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '用户技能价格',
  `num` int NULL DEFAULT 0 COMMENT '数量',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `service_time` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '服务时间',
  `order_on` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订单编号',
  `expired_time` datetime NULL DEFAULT NULL COMMENT '过期时间',
  `order_status` int NULL DEFAULT 0 COMMENT '订单状态',
  `order_price` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '订单价格',
  `service_duration` bigint NULL DEFAULT 0 COMMENT '服务时长,1就是1局或1小时,根据charge_type判断',
  `msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '订单备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `skill_user_id`(`skill_user_id` ASC) USING BTREE,
  INDEX `user_skill_id`(`user_skill_id` ASC) USING BTREE,
  INDEX `order_status`(`order_status` ASC) USING BTREE,
  INDEX `zh_index`(`order_status` ASC, `user_id` ASC, `skill_user_id` ASC) USING BTREE,
  INDEX `z2`(`order_status` ASC, `user_id` ASC) USING BTREE,
  INDEX `z3`(`order_status` ASC, `skill_user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '技能订单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_sys_msg
-- ----------------------------
DROP TABLE IF EXISTS `app_sys_msg`;
CREATE TABLE `app_sys_msg`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '消息内容',
  `title` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '消息标题',
  `is_read` tinyint(1) NULL DEFAULT 0 COMMENT '是否已读:0否,1是',
  `msg_type` int NULL DEFAULT 0 COMMENT '消息类型:参考java枚举类AppSysMsgTypeEnums',
  `object_id` bigint NULL DEFAULT NULL COMMENT '产生消息的id,为0就是空',
  `task_id` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '任务id',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者账号',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者账号',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `task_id`(`task_id` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `is_read`(`is_read` ASC) USING BTREE,
  INDEX `msg_type`(`msg_type` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `is_del` ASC, `is_read` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41749 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'app系统消息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_text_template
-- ----------------------------
DROP TABLE IF EXISTS `app_text_template`;
CREATE TABLE `app_text_template`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `text_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '文本内容',
  `apply_sex` int NULL DEFAULT 0 COMMENT '适用性别:0男,1女,-1代表都适用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '文字消息模板' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_top_up_grade_config
-- ----------------------------
DROP TABLE IF EXISTS `app_top_up_grade_config`;
CREATE TABLE `app_top_up_grade_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '等级名称',
  `ico_url` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '等级图标',
  `grade_size` bigint NULL DEFAULT 0 COMMENT '等级大小',
  `money_value` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '升级所需钱的金额',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `grade_size`(`grade_size` ASC) USING BTREE,
  INDEX `money_value`(`money_value` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '充值等级配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_top_up_rewards
-- ----------------------------
DROP TABLE IF EXISTS `app_top_up_rewards`;
CREATE TABLE `app_top_up_rewards`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `amount` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '充值金额',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `is_enable` tinyint(1) NULL DEFAULT 0 COMMENT '是否启用:0否,1是',
  `color_nick_name_id` bigint NULL DEFAULT 0 COMMENT '绑定的彩色昵称id',
  `mount_id` bigint NULL DEFAULT 0 COMMENT '绑定的坐骑id',
  `chat_frame_id` bigint NULL DEFAULT 0 COMMENT '绑定的气泡框id',
  `mic_frame` bigint NULL DEFAULT 0 COMMENT '绑定的头像框id',
  `pendant_id` bigint NULL DEFAULT 0 COMMENT '绑定的挂件id',
  `famous_brand_id` bigint NULL DEFAULT 0 COMMENT '绑定的名牌id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '首充有礼' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_upgrade
-- ----------------------------
DROP TABLE IF EXISTS `app_upgrade`;
CREATE TABLE `app_upgrade`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `version` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '版本号',
  `build_version` int NULL DEFAULT 0 COMMENT '构建版本号',
  `is_mandatory` tinyint(1) NULL DEFAULT 0 COMMENT '是否强制更新：0否，1是',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：0禁用，1启用',
  `url` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新地址',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备类型：ios或android',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '更新内容',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'App升级表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_user
-- ----------------------------
DROP TABLE IF EXISTS `app_user`;
CREATE TABLE `app_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `nick_name` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '昵称',
  `phone` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
  `gold_balance` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '金币余额',
  `points_balance` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '积分余额',
  `candy_balance` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '糖果余额',
  `channel_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '自身渠道码',
  `up_channel_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '--' COMMENT '上级渠道码',
  `recode_code` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户靓号',
  `token` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'token',
  `password` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '密码',
  `cancellation_time` datetime NULL DEFAULT NULL COMMENT '注销时间,用户状态为3有效',
  `equipment_type` tinyint(1) NULL DEFAULT 0 COMMENT '设备类型:1:android,2:ios',
  `push_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '极光推送标识',
  `equipment_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备名称',
  `last_operating_time` datetime NULL DEFAULT NULL COMMENT '最后操作时间',
  `weixin_openid` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '微信openid',
  `qq_openid` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'QQopenid',
  `ios_openid` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '苹果openid',
  `is_perfect_info` tinyint(1) NULL DEFAULT 0 COMMENT '是否完善基本资料:0否,1是',
  `photo_album` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '相册:json数组格式',
  `birthday` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '生日',
  `age` int NULL DEFAULT -1 COMMENT '年龄,不为-1有效',
  `sex` int NULL DEFAULT -1 COMMENT '性别:0男,1女,不为-1有效',
  `personal_signature` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '个性签名',
  `head_portrait` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像',
  `perfect_info_time` datetime NULL DEFAULT NULL COMMENT '完善基本资料时间:is_perfect_info为1有效',
  `recode_user_id` bigint NULL DEFAULT 0 COMMENT '推荐用户id,大于0有效',
  `education_background` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '学历',
  `constellation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '星座',
  `height` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '身高',
  `weight` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '体重',
  `location` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '所在地',
  `annual_income` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '年收入',
  `purchase_situation` tinyint(1) NULL DEFAULT -1 COMMENT '购房情况：0未购房，1已购房,-1为空',
  `car_purchase_situation` tinyint(1) NULL DEFAULT -1 COMMENT '购车情况：0未购车，1已购车,-1为空',
  `self_introduction` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '自我介绍',
  `label` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '标签',
  `voice_signature` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '语音签名',
  `voice_check_status` int NOT NULL DEFAULT 0 COMMENT '语音认证状态 0=未认证 1=审核中 2=已认证 3=认证未通过',
  `video` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '认证视频',
  `video_check_status` int NOT NULL DEFAULT 0 COMMENT '视频认证状态 0=未认证 1=审核中 2=已认证 3=认证未通过',
  `authentication` int NOT NULL DEFAULT 0 COMMENT '男神/女神认证 0=未认证 1=已认证',
  `authentication_time` datetime NULL DEFAULT NULL COMMENT '男神/女神认证时间',
  `longitude` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '115.596434' COMMENT '经度',
  `latitude` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '33.217462' COMMENT '纬度',
  `province` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '省',
  `city` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '市',
  `area` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '区',
  `is_virtual` tinyint(1) NULL DEFAULT 0 COMMENT '是否是虚拟用户:0否,1是',
  `is_real_person_auth` tinyint(1) NULL DEFAULT 0 COMMENT '是否已真人认证:0否1,是',
  `is_real_name_auth` tinyint(1) NULL DEFAULT 0 COMMENT '是否已实名认证:0否1,是',
  `bind_recode_user_time` datetime NULL DEFAULT NULL COMMENT '绑定推荐用户时间',
  `real_name` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '真实姓名,实名认证后有效',
  `id_number` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '身份证号码,实名认证后有效',
  `is_online` tinyint(1) NULL DEFAULT 0 COMMENT '是否在线:0否,1是',
  `is_good_number` tinyint(1) NULL DEFAULT 0 COMMENT '是否靓号:0否,1是',
  `is_tx` tinyint(1) NULL DEFAULT 0 COMMENT '是否提现',
  `is_vip` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否是会员 0否1是',
  `to_be_settled` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '待结算金额',
  `can_transfer` tinyint(1) NULL DEFAULT 0 COMMENT '能否转赠 0不能 1能',
  `is_free_voice` tinyint(1) NULL DEFAULT 0 COMMENT '是否免费通话1分钟:0可以,1已使用',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人账号',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '最近修改时间',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '最近一次修改人账号',
  `deleted` tinyint NULL DEFAULT NULL,
  `last_login_ip` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '最近一次登录IP地址',
  `user_status` tinyint(1) NULL DEFAULT 1 COMMENT '用户状态:1正常,2禁用,3注销',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最近一次登录时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `is_perfect_info`(`is_perfect_info` ASC) USING BTREE,
  INDEX `id`(`id` ASC) USING BTREE,
  INDEX `user_status`(`user_status` ASC) USING BTREE,
  INDEX `qq_openid`(`qq_openid` ASC) USING BTREE,
  INDEX `ios_openid`(`ios_openid` ASC) USING BTREE,
  INDEX `nick_name`(`nick_name`(768) ASC) USING BTREE,
  INDEX `birthday`(`birthday` ASC) USING BTREE,
  INDEX `age`(`age` ASC) USING BTREE,
  INDEX `sex`(`sex` ASC) USING BTREE,
  INDEX `recode_user_id`(`recode_user_id` ASC) USING BTREE,
  INDEX `purchase_situation`(`purchase_situation` ASC) USING BTREE,
  INDEX `car_purchase_situation`(`car_purchase_situation` ASC) USING BTREE,
  INDEX `is_virtual`(`is_virtual` ASC) USING BTREE,
  INDEX `is_real_person_auth`(`is_real_person_auth` ASC) USING BTREE,
  INDEX `is_real_name_auth`(`is_real_name_auth` ASC) USING BTREE,
  INDEX `weixin_openid`(`weixin_openid` ASC) USING BTREE,
  INDEX `recode_code`(`recode_code` ASC) USING BTREE,
  INDEX `create_time`(`created_time` ASC) USING BTREE,
  INDEX `zh_index`(`user_status` ASC, `is_perfect_info` ASC, `latitude` ASC, `longitude` ASC) USING BTREE,
  INDEX `is_online`(`is_online` ASC) USING BTREE,
  INDEX `is_good_number`(`is_good_number` ASC) USING BTREE,
  INDEX `zh2_index`(`id` ASC, `recode_code` ASC) USING BTREE,
  INDEX `zh3_index`(`id` ASC, `user_status` ASC) USING BTREE,
  INDEX `to_be_settled`(`to_be_settled` ASC) USING BTREE,
  INDEX `z_index`(`recode_user_id` ASC, `created_time` ASC) USING BTREE,
  INDEX `points_balance`(`points_balance` ASC) USING BTREE,
  INDEX `app_user_channel_code_IDX`(`channel_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4403 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'App用户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_user_beauty_set
-- ----------------------------
DROP TABLE IF EXISTS `app_user_beauty_set`;
CREATE TABLE `app_user_beauty_set`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `buffing` bigint NULL DEFAULT 0 COMMENT '磨皮',
  `skin_whitening` bigint NULL DEFAULT 0 COMMENT '美白',
  `macrophthalmia` bigint NULL DEFAULT 0 COMMENT '大眼',
  `facial_slimming` bigint NULL DEFAULT 0 COMMENT '瘦脸',
  `beauty_level` bigint NULL DEFAULT 0 COMMENT '美颜等级',
  `beauty_style` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '美颜风格',
  `rosy_level` bigint NULL DEFAULT 0 COMMENT '红润程度',
  `white_level` bigint NULL DEFAULT 0 COMMENT '美白程度',
  `clarity_enhance` tinyint NULL DEFAULT 0 COMMENT '清晰增强',
  `filter_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '滤镜名称',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 306 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户美颜设置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_user_broadcast_record
-- ----------------------------
DROP TABLE IF EXISTS `app_user_broadcast_record`;
CREATE TABLE `app_user_broadcast_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `broadcast_id` bigint NOT NULL COMMENT '广播消息ID',
  `user_id` bigint NOT NULL COMMENT '接收用户ID',
  `is_read` tinyint NULL DEFAULT 0 COMMENT '是否已读:0-未读,1-已读',
  `read_time` datetime NULL DEFAULT NULL COMMENT '阅读时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_broadcast_user`(`broadcast_id` ASC, `user_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 459 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户广播接收记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_user_charm_grade
-- ----------------------------
DROP TABLE IF EXISTS `app_user_charm_grade`;
CREATE TABLE `app_user_charm_grade`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `current_grade_id` bigint NULL DEFAULT 0 COMMENT '当前等级id',
  `sum_gold_value` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '累计收到金币数量(每次收到的礼物金额除以10后统计)',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `current_grade_id`(`current_grade_id` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `current_grade_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 317 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户魅力等级' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_user_communicate_telephone_config
-- ----------------------------
DROP TABLE IF EXISTS `app_user_communicate_telephone_config`;
CREATE TABLE `app_user_communicate_telephone_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `voice_minutes_gold` decimal(30, 2) NULL DEFAULT 0.00 COMMENT '语音通话每分钟费用',
  `is_enable_voice` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用语音接听:0否,1是',
  `voice_total_time` int NULL DEFAULT 0 COMMENT '语音通话总时长 单位是秒',
  `is_banned_voice` tinyint(1) NULL DEFAULT 0 COMMENT '是否封禁语音通话：0否,1是',
  `video_minutes_gold` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '视频通话每分钟费用',
  `is_enable_video` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用视频接听:0否,1是',
  `video_total_time` int NULL DEFAULT 0 COMMENT '视频通话总时长 单位是秒',
  `is_banned_video` tinyint(1) NULL DEFAULT 0 COMMENT '是否封禁视频通话：0否,1是',
  `send_msg_gold_price` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '发送消息每条价格',
  `is_banned_chat` tinyint(1) NULL DEFAULT 0 COMMENT '是否封禁私聊：0否,1是',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4401 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户通话配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_user_dua
-- ----------------------------
DROP TABLE IF EXISTS `app_user_dua`;
CREATE TABLE `app_user_dua`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'id',
  `date` date NOT NULL COMMENT '统计日期',
  `gender` tinyint NOT NULL COMMENT '性别',
  `count` int NULL DEFAULT NULL COMMENT '用户数',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ids_data_gender`(`date` ASC, `gender` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'APP日活统计' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_user_emote
-- ----------------------------
DROP TABLE IF EXISTS `app_user_emote`;
CREATE TABLE `app_user_emote`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '表情包地址',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `url` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表情' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_user_follow
-- ----------------------------
DROP TABLE IF EXISTS `app_user_follow`;
CREATE TABLE `app_user_follow`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `be_user_id` bigint NULL DEFAULT 0 COMMENT '被关注用户id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `be_user_id`(`be_user_id` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `be_user_id` ASC) USING BTREE,
  INDEX `zh2_index`(`be_user_id` ASC, `user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6744 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户关注' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_user_gift_backpack
-- ----------------------------
DROP TABLE IF EXISTS `app_user_gift_backpack`;
CREATE TABLE `app_user_gift_backpack`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户id',
  `gift_id` bigint NULL DEFAULT 0 COMMENT '礼物id',
  `gift_num` bigint NULL DEFAULT 0 COMMENT '总数量',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `app_user_gift_backpack_pk`(`user_id` ASC, `gift_id` ASC) USING BTREE,
  INDEX `gift_id`(`gift_id` ASC) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `zh2_index`(`deleted` ASC, `gift_id` ASC, `user_id` ASC, `gift_num` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户礼物背包' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_user_gold_bill
-- ----------------------------
DROP TABLE IF EXISTS `app_user_gold_bill`;
CREATE TABLE `app_user_gold_bill`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `to_user_id` bigint NULL DEFAULT NULL COMMENT '接收用户id',
  `bill_type` int NULL DEFAULT 0 COMMENT '账单类型:参考java枚举类AppGoldBillTypeEnums',
  `amount` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '金额',
  `object_id` bigint NULL DEFAULT 0 COMMENT '产生账单的相关id,大于0有效',
  `remarks_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '备注',
  `chat_room_id` bigint NULL DEFAULT 0 COMMENT '房间id',
  `user_contribution_value` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '用户贡献值',
  `to_user_charm_value` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '对方魅力值',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `to_user_charm_value`(`to_user_charm_value` ASC) USING BTREE,
  INDEX `user_contribution_value`(`user_contribution_value` ASC) USING BTREE,
  INDEX `zh_index`(`is_del` ASC, `bill_type` ASC, `amount` ASC, `object_id` ASC, `user_id` ASC, `chat_room_id` ASC, `create_time` ASC, `user_contribution_value` ASC, `to_user_charm_value` ASC) USING BTREE,
  INDEX `zh2_index`(`chat_room_id` ASC, `amount` ASC, `create_time` ASC) USING BTREE,
  INDEX `zh3_index`(`chat_room_id` ASC, `user_contribution_value` ASC, `create_time` ASC) USING BTREE,
  INDEX `zh4_index`(`user_contribution_value` ASC, `chat_room_id` ASC, `create_time` ASC) USING BTREE,
  INDEX `create_time`(`create_time` ASC) USING BTREE,
  INDEX `object_id`(`object_id` ASC) USING BTREE,
  INDEX `amount`(`amount` ASC) USING BTREE,
  INDEX `bill_type`(`bill_type` ASC) USING BTREE,
  INDEX `chat_room_id`(`chat_room_id` ASC) USING BTREE,
  INDEX `z_index`(`bill_type` ASC, `user_id` ASC) USING BTREE,
  INDEX `z2_index`(`bill_type` ASC, `create_time` ASC) USING BTREE,
  INDEX `z3_index`(`bill_type` ASC, `user_id` ASC, `create_time` ASC) USING BTREE,
  INDEX `z4_index`(`user_id` ASC, `create_time` ASC) USING BTREE,
  INDEX `z5_index`(`amount` ASC, `bill_type` ASC, `create_time` ASC) USING BTREE,
  INDEX `z6_index`(`amount` ASC, `create_time` ASC) USING BTREE,
  INDEX `z7_index`(`user_contribution_value` ASC, `create_time` ASC, `chat_room_id` ASC) USING BTREE,
  INDEX `z8_index`(`create_time` ASC, `user_contribution_value` ASC, `chat_room_id` ASC) USING BTREE,
  INDEX `z9_index`(`chat_room_id` ASC, `create_time` ASC, `user_contribution_value` ASC) USING BTREE,
  INDEX `z10_index`(`chat_room_id` ASC, `create_time` ASC) USING BTREE,
  INDEX `z11_index`(`object_id` ASC, `user_id` ASC, `amount` ASC, `user_contribution_value` ASC) USING BTREE,
  INDEX `phb_index`(`create_time` ASC, `to_user_charm_value` ASC) USING BTREE,
  INDEX `phb2_index`(`create_time` ASC, `user_contribution_value` ASC) USING BTREE,
  INDEX `phb3`(`chat_room_id` ASC, `create_time` ASC, `to_user_charm_value` ASC) USING BTREE,
  INDEX `phb4_index`(`chat_room_id` ASC, `create_time` ASC, `user_contribution_value` ASC) USING BTREE,
  INDEX `phb4`(`chat_room_id` ASC, `to_user_charm_value` ASC) USING BTREE,
  INDEX `phb5`(`chat_room_id` ASC, `user_contribution_value` ASC) USING BTREE,
  INDEX `z1`(`user_id` ASC, `bill_type` ASC, `amount` ASC) USING BTREE,
  INDEX `z2`(`user_id` ASC, `bill_type` ASC, `create_time` ASC, `amount` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 47451 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户金币账单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_user_intimacy
-- ----------------------------
DROP TABLE IF EXISTS `app_user_intimacy`;
CREATE TABLE `app_user_intimacy`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `to_user_id` bigint NULL DEFAULT 0 COMMENT '对方用户id',
  `intimacy_value` decimal(20, 1) NULL DEFAULT 0.0 COMMENT '亲密值',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `to_user_id`(`to_user_id` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `to_user_id` ASC) USING BTREE,
  INDEX `zh2_index`(`user_id` ASC, `to_user_id` ASC, `intimacy_value` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13927 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户亲密度' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_user_login_log
-- ----------------------------
DROP TABLE IF EXISTS `app_user_login_log`;
CREATE TABLE `app_user_login_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `login_ip` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '登录ip',
  `login_time` datetime NULL DEFAULT NULL COMMENT '登录时间',
  `device_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户设备',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6445 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户登录日志' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_user_personal_dressing
-- ----------------------------
DROP TABLE IF EXISTS `app_user_personal_dressing`;
CREATE TABLE `app_user_personal_dressing`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `personal_dressing_id` bigint NULL DEFAULT 0 COMMENT '个性装扮id',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `expired_time` datetime NULL DEFAULT NULL COMMENT '过期时间',
  `is_use` tinyint(1) NULL DEFAULT 0 COMMENT '是否使用中:0否,1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `personal_dressing_id`(`personal_dressing_id` ASC) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `expired_time`(`expired_time` ASC) USING BTREE,
  INDEX `zh1_index`(`user_id` ASC, `personal_dressing_id` ASC, `is_del` ASC, `expired_time` ASC) USING BTREE,
  INDEX `zh2_index`(`is_del` ASC, `user_id` ASC, `personal_dressing_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 33 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户个性装扮' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_user_points_bill
-- ----------------------------
DROP TABLE IF EXISTS `app_user_points_bill`;
CREATE TABLE `app_user_points_bill`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `bill_type` int NULL DEFAULT 0 COMMENT '账单类型:参考java枚举类AppPointsBillTypeEnums',
  `gift_income_scale` decimal(20, 2) NULL DEFAULT NULL COMMENT '收到礼物时的礼物提现比例',
  `total_amount` decimal(20, 2) NULL DEFAULT NULL COMMENT '原始数据总金币',
  `amount` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '金额',
  `object_id` bigint NULL DEFAULT 0 COMMENT '产生账单的相关id,大于0有效',
  `remarks_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '备注',
  `chat_room_id` bigint NULL DEFAULT NULL COMMENT '房间id',
  `user_contribution_value` decimal(50, 2) NULL DEFAULT NULL COMMENT '用户贡献值',
  `to_user_charm_value` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '对方魅力值',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `guild_id` int NULL DEFAULT NULL COMMENT '发生收益时所在公会id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `is_del`(`deleted` ASC) USING BTREE,
  INDEX `create_time`(`created_time` ASC) USING BTREE,
  INDEX `bill_type`(`bill_type` ASC) USING BTREE,
  INDEX `amount`(`amount` ASC) USING BTREE,
  INDEX `object_id`(`object_id` ASC) USING BTREE,
  INDEX `z_index`(`bill_type` ASC, `user_id` ASC) USING BTREE,
  INDEX `z2_index`(`bill_type` ASC, `created_time` ASC) USING BTREE,
  INDEX `z3_index`(`bill_type` ASC, `user_id` ASC, `created_time` ASC) USING BTREE,
  INDEX `z4_index`(`user_id` ASC, `created_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 30657 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户积分账单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_user_skill
-- ----------------------------
DROP TABLE IF EXISTS `app_user_skill`;
CREATE TABLE `app_user_skill`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `skill_id` bigint NULL DEFAULT 0 COMMENT '技能id',
  `stats` int NULL DEFAULT 0 COMMENT '状态:0审核中,1审核通过,2审核拒绝',
  `msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '审核备注',
  `skill_certificate` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '技能证明,格式自定义',
  `avatar` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '头像',
  `voice_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '语音介绍',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '服务内容',
  `charge_type` int NULL DEFAULT -1 COMMENT '收费类型:0局,1小时',
  `price` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '价格',
  `game_id` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '游戏id',
  `game_lv` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '游戏等级',
  `tag` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '标签,格式自定义',
  `order_count` bigint NULL DEFAULT 0 COMMENT '接单次数',
  `mark` decimal(10, 1) NULL DEFAULT 5.0 COMMENT '评分',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `skill_id`(`skill_id` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `skill_id` ASC) USING BTREE,
  INDEX `mark`(`mark` ASC) USING BTREE,
  INDEX `order_count`(`order_count` ASC) USING BTREE,
  INDEX `z2_index`(`user_id` ASC, `order_count` ASC) USING BTREE,
  INDEX `stats`(`stats` ASC) USING BTREE,
  INDEX `z3_index`(`stats` ASC, `order_count` ASC) USING BTREE,
  INDEX `charge_type`(`charge_type` ASC) USING BTREE,
  INDEX `z1`(`stats` ASC, `charge_type` ASC, `order_count` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户技能' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_user_top_up_grade
-- ----------------------------
DROP TABLE IF EXISTS `app_user_top_up_grade`;
CREATE TABLE `app_user_top_up_grade`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `current_grade_id` bigint NULL DEFAULT 0 COMMENT '当前等级id',
  `sum_money_value` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '累计充值的钱金额',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `current_grade_id`(`current_grade_id` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `current_grade_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 737 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户充值等级' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_user_video_card
-- ----------------------------
DROP TABLE IF EXISTS `app_user_video_card`;
CREATE TABLE `app_user_video_card`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `amount` int NOT NULL COMMENT '数量',
  `status` int NOT NULL DEFAULT 1 COMMENT '状态 1=正常 2=已用完 3=已过期',
  `expire_time` date NULL DEFAULT NULL COMMENT '到期时间',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id_key`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 58 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户视频卡' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_user_video_card_bill
-- ----------------------------
DROP TABLE IF EXISTS `app_user_video_card_bill`;
CREATE TABLE `app_user_video_card_bill`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `to_user_id` bigint NULL DEFAULT NULL COMMENT '收益用户id 枚举类型为视频卡通话时使用',
  `bill_type` int NULL DEFAULT NULL COMMENT '账单类型 见枚举AppVideoCardBillTypeEnums',
  `biz_id` bigint NULL DEFAULT NULL COMMENT '关联的视频通话记录id',
  `amount` int NULL DEFAULT NULL COMMENT '数量',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id_key`(`user_id` ASC) USING BTREE,
  INDEX `to_user_id_key`(`to_user_id` ASC) USING BTREE,
  INDEX `bill_type_key`(`bill_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 127 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '视频卡账单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_view_user_records
-- ----------------------------
DROP TABLE IF EXISTS `app_view_user_records`;
CREATE TABLE `app_view_user_records`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `view_user_id` bigint NULL DEFAULT 0 COMMENT '查看用户id',
  `be_view_user_id` bigint NULL DEFAULT NULL COMMENT '被查看用户id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `is_read` tinyint(1) NULL DEFAULT 0 COMMENT '是否已读:0否,1是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `view_user_id`(`view_user_id` ASC) USING BTREE,
  INDEX `be_view_user_id`(`be_view_user_id` ASC) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `is_read`(`is_read` ASC) USING BTREE,
  INDEX `z1_index`(`view_user_id` ASC, `be_view_user_id` ASC, `is_del` ASC, `is_read` ASC) USING BTREE,
  INDEX `create_time`(`create_time` ASC) USING BTREE,
  INDEX `z2_index`(`be_view_user_id` ASC, `create_time` ASC) USING BTREE,
  INDEX `z3_index`(`view_user_id` ASC, `create_time` ASC) USING BTREE,
  INDEX `z4_index`(`be_view_user_id` ASC, `is_read` ASC) USING BTREE,
  INDEX `z5_index`(`view_user_id` ASC, `be_view_user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 45363 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '查看用户记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_vip
-- ----------------------------
DROP TABLE IF EXISTS `app_vip`;
CREATE TABLE `app_vip`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `expire_time` datetime NULL DEFAULT NULL COMMENT 'VIP到期时间',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 1-有效 0-无效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '会员表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_vip_chat_record
-- ----------------------------
DROP TABLE IF EXISTS `app_vip_chat_record`;
CREATE TABLE `app_vip_chat_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `send_user_id` bigint NULL DEFAULT NULL COMMENT '发送者id',
  `receive_user_id` bigint NULL DEFAULT NULL COMMENT '接受者id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '发送时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5030 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '搭讪记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_vip_chat_templates
-- ----------------------------
DROP TABLE IF EXISTS `app_vip_chat_templates`;
CREATE TABLE `app_vip_chat_templates`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文案内容',
  `status` int NOT NULL DEFAULT 1 COMMENT '审核状态 1=审核中 2=审核通过 3=审核不通过',
  `is_del` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态 1=已删除 0=未删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 66 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '搭讪文案表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_vip_price
-- ----------------------------
DROP TABLE IF EXISTS `app_vip_price`;
CREATE TABLE `app_vip_price`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `type` int NULL DEFAULT NULL COMMENT '类型 1=充值 2=续费',
  `price` decimal(10, 2) NOT NULL COMMENT '优惠价',
  `original_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '原价',
  `month` int NULL DEFAULT NULL COMMENT '月数',
  `video_card_amount` int NULL DEFAULT 0 COMMENT '赠送视频卡数量',
  `status` tinyint NULL DEFAULT 0 COMMENT '是否删除 0=未删除 1=已删除',
  `is_ios` tinyint NULL DEFAULT NULL COMMENT '是否苹果内购',
  `ios_id` bigint NULL DEFAULT NULL COMMENT 'ios内购id',
  `create_user_id` bigint NULL DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员价格表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_vip_privileges
-- ----------------------------
DROP TABLE IF EXISTS `app_vip_privileges`;
CREATE TABLE `app_vip_privileges`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `privilege_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '特权key',
  `privilege_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '特权名称',
  `privilege_desc_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '特权描述URL',
  `icon_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '特权图标URL',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 1-启用 0-禁用',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序序号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '会员特权表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_vip_privileges_config
-- ----------------------------
DROP TABLE IF EXISTS `app_vip_privileges_config`;
CREATE TABLE `app_vip_privileges_config`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint UNSIGNED NOT NULL COMMENT '用户ID',
  `online_notify` tinyint(1) NOT NULL DEFAULT 1 COMMENT '好友上线通知 1-开启 0-关闭',
  `hide_charm_value` tinyint(1) NOT NULL DEFAULT 1 COMMENT '隐藏魅力值 1-开启 0-关闭',
  `hide_gifts` tinyint(1) NOT NULL DEFAULT 1 COMMENT '隐藏收到礼物 1-开启 0-关闭',
  `hide_in_ranking` tinyint(1) NOT NULL DEFAULT 1 COMMENT '在榜单中隐身 1-开启 0-关闭',
  `hide_visit` tinyint(1) NOT NULL DEFAULT 1 COMMENT '隐藏访问足迹 1-开启 0-关闭',
  `hide_wealth_value` tinyint(1) NOT NULL DEFAULT 1 COMMENT '隐藏财富值 1-开启 0-关闭',
  `hide_wealth` tinyint(1) NOT NULL DEFAULT 1 COMMENT '隐藏财富等级 1-开启 0-关闭',
  `hide_charm` tinyint(1) NOT NULL DEFAULT 1 COMMENT '隐藏魅力等级 1-开启 0-关闭',
  `hide_sent_gifts` tinyint(1) NOT NULL DEFAULT 1 COMMENT '隐藏赠送礼物 1-开启 0-关闭',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '会员权限设置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_vip_record
-- ----------------------------
DROP TABLE IF EXISTS `app_vip_record`;
CREATE TABLE `app_vip_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `type` int NOT NULL COMMENT '类型 1=充值 2=续费',
  `price` decimal(10, 2) NULL DEFAULT NULL COMMENT '付款金额',
  `month` int NULL DEFAULT NULL COMMENT '月份',
  `is_ios` tinyint NULL DEFAULT NULL COMMENT '设备类型 0=安卓 1=苹果',
  `order_id` bigint NULL DEFAULT NULL COMMENT '关联的订单id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id_key`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员充值记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_withdraw_records
-- ----------------------------
DROP TABLE IF EXISTS `app_withdraw_records`;
CREATE TABLE `app_withdraw_records`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `withdraw_points` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '提现积分',
  `service_charge` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '手续费',
  `actual_amount` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '实际到账金额',
  `one_points_eq_rmb` decimal(20, 2) NULL DEFAULT 0.00 COMMENT '一积分等于多少人民币',
  `alipay_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '支付宝真实姓名',
  `alipay_account` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '支付宝账号',
  `alipay_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '支付宝返回结果',
  `status` int NULL DEFAULT 0 COMMENT '状态:0待审核,1审核通过,2审核拒绝',
  `refuse_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '被拒原因,状态为2有效',
  `withdraw_amount` decimal(20, 2) NULL DEFAULT NULL COMMENT '实际提现金额',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `status`(`status` ASC) USING BTREE,
  INDEX `create_time`(`create_time` ASC) USING BTREE,
  INDEX `z_index`(`status` ASC, `create_time` ASC) USING BTREE,
  INDEX `actual_amount`(`actual_amount` ASC) USING BTREE,
  INDEX `z2_index`(`status` ASC, `create_time` ASC, `actual_amount` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 240 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '提现记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for app_wx_pay_config
-- ----------------------------
DROP TABLE IF EXISTS `app_wx_pay_config`;
CREATE TABLE `app_wx_pay_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `is_enable` tinyint(1) NULL DEFAULT 0 COMMENT '是否启用:0否,1是',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `app_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'appId',
  `mch_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '微信支付商户id',
  `mch_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '微信支付商户key',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '名称',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `app_id`(`app_id` ASC) USING BTREE,
  INDEX `is_del`(`is_del` ASC) USING BTREE,
  INDEX `is_enable`(`is_enable` ASC) USING BTREE,
  INDEX `zh_index`(`is_del` ASC, `is_enable` ASC, `app_id` ASC) USING BTREE,
  INDEX `zh2_index`(`is_del` ASC, `is_enable` ASC) USING BTREE,
  INDEX `name`(`name` ASC) USING BTREE,
  INDEX `zh3_index`(`is_del` ASC, `is_enable` ASC, `app_id` ASC, `name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '微信支付配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for candy_exchange_record
-- ----------------------------
DROP TABLE IF EXISTS `candy_exchange_record`;
CREATE TABLE `candy_exchange_record`  (
  `exchange_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户ID',
  `item_id` bigint NULL DEFAULT 0 COMMENT '商品ID',
  `gold_cost` int NULL DEFAULT 0 COMMENT '消耗金币',
  `candy_cost` int NULL DEFAULT 0 COMMENT '消耗糖果',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`exchange_id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `item_id`(`item_id` ASC) USING BTREE,
  INDEX `create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '糖果商品兑换记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for candy_grab_activity
-- ----------------------------
DROP TABLE IF EXISTS `candy_grab_activity`;
CREATE TABLE `candy_grab_activity`  (
  `activity_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `config_id` bigint NOT NULL DEFAULT 0 COMMENT '配置ID',
  `period_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '期号',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态(0:进行中, 1:已开奖)',
  `total_candy` int NULL DEFAULT 1000 COMMENT '总糖果数量',
  `current_participants` int NULL DEFAULT 0 COMMENT '当前参与人数',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `draw_time` datetime NULL DEFAULT NULL COMMENT '开奖时间',
  PRIMARY KEY (`activity_id`) USING BTREE,
  INDEX `config_id`(`config_id` ASC) USING BTREE,
  INDEX `period_no`(`period_no` ASC) USING BTREE,
  INDEX `status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '抢糖果活动表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for candy_grab_config
-- ----------------------------
DROP TABLE IF EXISTS `candy_grab_config`;
CREATE TABLE `candy_grab_config`  (
  `config_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `gold_amount` int NOT NULL DEFAULT 0 COMMENT '投入金币数量',
  `candy_ratio` int NOT NULL DEFAULT 10 COMMENT '兑换糖果比例(如10表示1金币兑换10糖果)',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态(0:禁用, 1:启用)',
  `participants` int NULL DEFAULT 5 COMMENT '开奖人数',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`config_id`) USING BTREE,
  INDEX `status`(`status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '抢糖果配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for candy_grab_record
-- ----------------------------
DROP TABLE IF EXISTS `candy_grab_record`;
CREATE TABLE `candy_grab_record`  (
  `grab_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户ID',
  `activity_id` bigint NULL DEFAULT 0 COMMENT '活动ID',
  `period_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '期号',
  `candy_cost` int NULL DEFAULT 1000 COMMENT '消耗糖果数量',
  `gold_cost` int NULL DEFAULT 0 COMMENT '消耗金币数量',
  `is_gold_exchange` tinyint(1) NULL DEFAULT 0 COMMENT '是否使用金币兑换(0:否, 1:是)',
  `result_candy` int NULL DEFAULT 0 COMMENT '获得糖果数量',
  `bonus_gold` int NULL DEFAULT 0 COMMENT '奖励金币数量',
  `is_min_winner` tinyint(1) NULL DEFAULT 0 COMMENT '是否为最小获奖者(0:否, 1:是)',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '状态(0:已参与, 1:已开奖)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`grab_id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `activity_id`(`activity_id` ASC) USING BTREE,
  INDEX `period_no`(`period_no` ASC) USING BTREE,
  INDEX `status`(`status` ASC) USING BTREE,
  INDEX `create_time`(`create_time` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `period_no` ASC, `status` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '抢糖果记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for candy_ranking
-- ----------------------------
DROP TABLE IF EXISTS `candy_ranking`;
CREATE TABLE `candy_ranking`  (
  `ranking_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户ID',
  `month` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '月份(格式:YYYYMM)',
  `total_candy` int NULL DEFAULT 0 COMMENT '本月获得糖果总数',
  `ranking` int NULL DEFAULT 0 COMMENT '排名',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`ranking_id`) USING BTREE,
  INDEX `user_id`(`user_id` ASC) USING BTREE,
  INDEX `month`(`month` ASC) USING BTREE,
  INDEX `ranking`(`ranking` ASC) USING BTREE,
  INDEX `total_candy`(`total_candy` ASC) USING BTREE,
  INDEX `zh_index`(`user_id` ASC, `month` ASC) USING BTREE,
  INDEX `zh2_index`(`month` ASC, `ranking` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '糖果排行榜表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for candy_shop_item
-- ----------------------------
DROP TABLE IF EXISTS `candy_shop_item`;
CREATE TABLE `candy_shop_item`  (
  `item_id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `gift_id` bigint NULL DEFAULT 0 COMMENT '礼物ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商品名称',
  `image_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商品图片',
  `gold_price` int NULL DEFAULT 0 COMMENT '商品价格(金币)',
  `candy_cost` int NULL DEFAULT 0 COMMENT '消耗糖果数量',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '商品描述',
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '状态(0:下架, 1:上架)',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del` tinyint(1) NULL DEFAULT 0 COMMENT '软删除(0:未删除,1:删除)',
  PRIMARY KEY (`item_id`) USING BTREE,
  INDEX `status`(`status` ASC) USING BTREE,
  INDEX `gold_price`(`gold_price` ASC) USING BTREE,
  INDEX `candy_cost`(`candy_cost` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '糖果商城商品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for channel_commission_record
-- ----------------------------
DROP TABLE IF EXISTS `channel_commission_record`;
CREATE TABLE `channel_commission_record`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '序号',
  `channel_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '受益渠道',
  `recharge_user_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '充值人渠道码',
  `user_id` int NULL DEFAULT NULL COMMENT '充值人用户id用户id',
  `order_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '充值单号或赠送礼物单号',
  `order_price` decimal(20, 2) NULL DEFAULT NULL COMMENT '订单金额,充值是元,赠送消费礼物是钻石',
  `ratio` float NULL DEFAULT NULL COMMENT '分佣比例',
  `money` decimal(20, 2) NULL DEFAULT NULL COMMENT '佣金',
  `type` tinyint(1) NULL DEFAULT NULL COMMENT '分佣提成类型:1现金,2积分',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `flag` tinyint NULL DEFAULT 1 COMMENT '标志:1充值,2收礼物分佣',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `channel_commission_record_channel_code_IDX`(`channel_code` ASC, `recharge_user_code` ASC, `order_no` ASC) USING BTREE,
  INDEX `channel_commission_record_recharge_user_code_IDX`(`recharge_user_code` ASC) USING BTREE,
  INDEX `channel_commission_record_order_no_IDX`(`order_no` ASC) USING BTREE,
  INDEX `channel_commission_record_type_IDX`(`type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14807 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '渠道佣金记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for channel_user_relation
-- ----------------------------
DROP TABLE IF EXISTS `channel_user_relation`;
CREATE TABLE `channel_user_relation`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `channel_id` bigint NOT NULL COMMENT '渠道ID',
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` int NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 694 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '渠道与用户关系表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for game_config
-- ----------------------------
DROP TABLE IF EXISTS `game_config`;
CREATE TABLE `game_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '游戏名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '游戏介绍',
  `icon` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '游戏图标',
  `image` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '背景图片',
  `once_price` decimal(10, 4) NOT NULL COMMENT '单次价格（钻石）',
  `status` tinyint NULL DEFAULT NULL COMMENT '开启状态 1开启 0关闭',
  `type` tinyint NULL DEFAULT NULL COMMENT '1、数量公池 2、概率公池',
  `order_no` int NULL DEFAULT NULL COMMENT '排序字段',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint NOT NULL COMMENT '逻辑删除',
  `line` decimal(10, 4) NULL DEFAULT NULL COMMENT '蓄水池开奖线',
  `deduction_ratio` decimal(10, 4) NULL DEFAULT NULL COMMENT '蓄水池每次扣除比例',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '游戏配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for game_impoundment_pool
-- ----------------------------
DROP TABLE IF EXISTS `game_impoundment_pool`;
CREATE TABLE `game_impoundment_pool`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '序号',
  `game_id` int NULL DEFAULT NULL COMMENT '游戏id',
  `gift_id` int NULL DEFAULT NULL COMMENT '奖品id',
  `weight` int NULL DEFAULT NULL COMMENT '抽奖权重',
  `gift_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '礼物名称',
  `masonry_price` decimal(20, 2) NULL DEFAULT NULL COMMENT '礼物价格',
  `pool_id` int NULL DEFAULT NULL COMMENT '进阶池id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '蓄水池奖品配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for game_pool_gift_relation
-- ----------------------------
DROP TABLE IF EXISTS `game_pool_gift_relation`;
CREATE TABLE `game_pool_gift_relation`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `num` int NULL DEFAULT 0 COMMENT '投放数量',
  `gl` int NULL DEFAULT 0 COMMENT '概率权重(概率池)',
  `bind_gift_id` bigint NULL DEFAULT 0 COMMENT '绑定的礼物id',
  `pool_id` bigint NULL DEFAULT 0 COMMENT '绑定的进阶池id',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新者',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1172 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '游戏奖池礼物配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for game_record
-- ----------------------------
DROP TABLE IF EXISTS `game_record`;
CREATE TABLE `game_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `pool_id` bigint NULL DEFAULT NULL COMMENT '奖池id',
  `user_id` bigint NULL DEFAULT NULL COMMENT '用户id',
  `get_num` int NULL DEFAULT NULL COMMENT '单次抓取次数',
  `gift_info` json NULL COMMENT '礼物信息，是json数组的形式，\n[   {     \"giftId\": 1,     \"giftName\": \"大礼包\",     \"giftUnitAmount\": 123,     \"giftNum\": 1,     \"giftTotalAmount\": 123   } ]',
  `put_amount` decimal(20, 4) NULL DEFAULT NULL COMMENT '本次投入金额',
  `get_amount` decimal(20, 4) NULL DEFAULT NULL COMMENT '产出金额',
  `chat_room_id` bigint NULL DEFAULT NULL COMMENT '房间id',
  `round_num` bigint NULL DEFAULT NULL COMMENT '数量公池-已抽取轮次',
  `created_time` datetime NULL DEFAULT NULL,
  `updated_time` datetime NULL DEFAULT NULL,
  `deleted` tinyint NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 109 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '游戏记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for game_reimbursement
-- ----------------------------
DROP TABLE IF EXISTS `game_reimbursement`;
CREATE TABLE `game_reimbursement`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pool_id` bigint NULL DEFAULT 0 COMMENT '进阶池id',
  `gift_id` bigint NULL DEFAULT NULL COMMENT '补发的礼物id',
  `user_id` bigint NULL DEFAULT 0 COMMENT '用户id',
  `is_sub_kc` tinyint(1) NULL DEFAULT 0 COMMENT '核销状态: 1、已发放未领取 2、已领取未核销 3、已发放已核销',
  `game_id` bigint NOT NULL,
  `sub_time` datetime NULL DEFAULT NULL COMMENT '最近核销时间',
  `deleted` tinyint NOT NULL COMMENT '逻辑删除',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建者',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '游戏补发补发记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for game_upgrade_pool
-- ----------------------------
DROP TABLE IF EXISTS `game_upgrade_pool`;
CREATE TABLE `game_upgrade_pool`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '名称',
  `min_amount` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '最小金额',
  `max_amount` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '最大金额',
  `sum_put` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '数量公池-总投入金额',
  `sum_out` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '数量公池-总产出金额',
  `sum_profit` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '数量公池-盈利金额',
  `extracted_num` bigint NULL DEFAULT 0 COMMENT '数量公池-已抽取轮次',
  `sum_put_gl` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '概率公池-总投入金额',
  `sum_out_gl` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '概率公池-总产出金额',
  `sum_profit_gl` decimal(50, 2) NULL DEFAULT 0.00 COMMENT '概率公池-盈利金额',
  `sum_turn_gl` bigint NULL DEFAULT 0 COMMENT '概率公池-总轮次',
  `extracted_num_gl` bigint NULL DEFAULT 0 COMMENT '概率公池-已抽取轮次',
  `bottom_line` int NULL DEFAULT NULL COMMENT '概率池保底线金额',
  `bottom_ratio` decimal(20, 2) NULL DEFAULT NULL COMMENT '保底比率=产出/投入',
  `top_line` int NULL DEFAULT NULL COMMENT '概率池保顶线金额',
  `top_ratio` decimal(20, 2) NULL DEFAULT NULL COMMENT '上限比率',
  `min_kzl` decimal(20, 2) NULL DEFAULT -1.00 COMMENT '最小控制率',
  `max_kzl` decimal(20, 2) NULL DEFAULT -1.00 COMMENT '最大控制率',
  `slc_bl` decimal(20, 2) NULL DEFAULT 1.00 COMMENT '数量公池爆率',
  `mzls_jc_type` int NULL DEFAULT 2 COMMENT '奖池类型:1数量公池,2概率池',
  `game_id` bigint NOT NULL COMMENT '关联app_game',
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NULL DEFAULT 0 COMMENT '删除标记0有效,1删除',
  `line` decimal(10, 4) NULL DEFAULT NULL COMMENT '蓄水池开奖线',
  `deduction_ratio` decimal(10, 4) NULL DEFAULT NULL COMMENT '蓄水池每次扣除比例',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `min_amount`(`min_amount` ASC) USING BTREE,
  INDEX `max_amount`(`max_amount` ASC) USING BTREE,
  INDEX `z2_index`(`min_amount` ASC, `max_amount` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 435 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '猫抓老鼠进阶池配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for game_upgrade_pool_range
-- ----------------------------
DROP TABLE IF EXISTS `game_upgrade_pool_range`;
CREATE TABLE `game_upgrade_pool_range`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '序号',
  `game_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联概率池',
  `bottom_line` int NULL DEFAULT NULL COMMENT '底线',
  `ratio` decimal(20, 2) NULL DEFAULT NULL COMMENT '底线比率',
  `top_line` int NULL DEFAULT NULL COMMENT '顶线',
  `top_ratio` decimal(20, 2) NULL DEFAULT NULL COMMENT '上限比率',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '概率池区间' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gen_table
-- ----------------------------
DROP TABLE IF EXISTS `gen_table`;
CREATE TABLE `gen_table`  (
  `table_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '表描述',
  `sub_table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联子表的表名',
  `sub_table_fk_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '子表关联的外键名',
  `class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '实体类名称',
  `tpl_category` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
  `package_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '生成包路径',
  `module_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '生成模块名',
  `business_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '生成业务名',
  `function_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '生成功能名',
  `function_author` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '生成功能作者',
  `gen_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  `gen_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
  `options` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '其它生成选项',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`table_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 155 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '代码生成业务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for gen_table_column
-- ----------------------------
DROP TABLE IF EXISTS `gen_table_column`;
CREATE TABLE `gen_table_column`  (
  `column_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '归属表编号',
  `column_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '列名称',
  `column_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '列描述',
  `column_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '列类型',
  `java_type` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'JAVA类型',
  `java_field` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'JAVA字段名',
  `is_pk` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '是否主键（1是）',
  `is_increment` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '是否自增（1是）',
  `is_required` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '是否必填（1是）',
  `is_insert` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '是否为插入字段（1是）',
  `is_edit` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '是否编辑字段（1是）',
  `is_list` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '是否列表字段（1是）',
  `is_query` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '是否查询字段（1是）',
  `query_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  `html_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `dict_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '字典类型',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`column_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2013 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '代码生成业务表字段' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_anchor_commission
-- ----------------------------
DROP TABLE IF EXISTS `sys_anchor_commission`;
CREATE TABLE `sys_anchor_commission`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '主播id',
  `nick_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主播名称',
  `old_commission_rate` decimal(5, 2) NOT NULL COMMENT '修改前的自提比例',
  `new_commission_rate` decimal(5, 2) NOT NULL COMMENT '修改后的自提比例',
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作人',
  `created_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `guild_id` int NULL DEFAULT NULL COMMENT '公会Id',
  `type` int NULL DEFAULT NULL COMMENT '主播类型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 145 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '主播自提比例修改记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_channel
-- ----------------------------
DROP TABLE IF EXISTS `sys_channel`;
CREATE TABLE `sys_channel`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '渠道ID',
  `channel_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '渠道名称',
  `invite_code` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邀请码',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '渠道码',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父渠道ID',
  `created_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `updated_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `deleted` int NULL DEFAULT 0,
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `chat_room_id` int NULL DEFAULT NULL COMMENT '推广房间id',
  `sys_user_id` int NULL DEFAULT NULL COMMENT '系统用户id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sys_channel_code_IDX`(`code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 90 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '渠道权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` int NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`config_id`) USING BTREE,
  INDEX `sys_config_config_key_IDX`(`config_key` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '参数配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '部门名称',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 96 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '字典键值',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图片',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 179 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '字典数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '字典类型表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_guild_license
-- ----------------------------
DROP TABLE IF EXISTS `sys_guild_license`;
CREATE TABLE `sys_guild_license`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '主播id',
  `nick_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主播名称',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `guild_id` int NULL DEFAULT NULL COMMENT '公会Id',
  `old_license_id` int NULL DEFAULT NULL COMMENT '原绑定房间靓号',
  `new_license_id` int NULL DEFAULT NULL COMMENT '新绑定房间靓号',
  `status` int NULL DEFAULT NULL COMMENT '房间状态',
  `license_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '牌照名称',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `deleted` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '公会牌照变更记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '定时任务调度表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
  `job_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '日志信息',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '异常信息',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '定时任务调度日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`  (
  `info_id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '操作系统',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '提示消息',
  `login_time` datetime NULL DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 360 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统访问记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '路由参数',
  `is_frame` int NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int NULL DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2473 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '菜单权限表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` int NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob NULL COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '通知公告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `oper_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '模块标题',
  `business_type` int NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` int NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '返回参数',
  `status` int NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  PRIMARY KEY (`oper_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 386 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '操作日志记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `post_id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '岗位信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 128 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '角色信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '角色和部门关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint NULL DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `second_password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '二级密码',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 109 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user_closure
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_closure`;
CREATE TABLE `sys_user_closure`  (
  `ancestor` int NULL DEFAULT NULL COMMENT '上级id',
  `descendant` int NULL DEFAULT NULL COMMENT '下级id',
  `depth` int NULL DEFAULT NULL COMMENT '层级差',
  INDEX `sys_user_closure_descendant_IDX`(`descendant` ASC) USING BTREE,
  INDEX `sys_user_closure_ancestor_IDX`(`ancestor` ASC, `depth` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户闭包表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `post_id` bigint NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户和角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user_whitelist
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_whitelist`;
CREATE TABLE `sys_user_whitelist`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户ip',
  `Expiration_time` datetime NULL DEFAULT NULL COMMENT '过期时间',
  `deleted` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除:0否,1是',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户ip白名单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for user_give_gift
-- ----------------------------
DROP TABLE IF EXISTS `user_give_gift`;
CREATE TABLE `user_give_gift`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `user_id` bigint NOT NULL COMMENT '赠送用户的id',
  `to_user_id` bigint NOT NULL COMMENT '接收礼物的用户id',
  `hall_owner_user_id` bigint NULL DEFAULT NULL COMMENT '厅主用户id',
  `gift_id` bigint NOT NULL COMMENT '礼物id',
  `gift_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '礼物名称',
  `gift_type` tinyint NULL DEFAULT NULL COMMENT '1、直刷礼物 2、游戏礼物',
  `gift_gold_unit_amount` decimal(20, 4) NULL DEFAULT NULL COMMENT '金币金额',
  `gift_num` int NULL DEFAULT NULL COMMENT '礼物数量',
  `luck_gift_id` int NULL DEFAULT NULL COMMENT '盲盒礼物id',
  `luck_gift_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '盲盒礼物名称',
  `gift_gold_total_amount` decimal(20, 4) NULL DEFAULT NULL COMMENT '总计金额',
  `receive_point_total_amount` decimal(20, 4) NULL DEFAULT NULL COMMENT '接收者积分到账金额',
  `hall_point_total_amount` decimal(20, 4) NULL DEFAULT NULL COMMENT '厅主收益积分金额',
  `profit_amount` decimal(20, 4) NULL DEFAULT NULL COMMENT '平台积分利润/盈亏',
  `chat_room_id` bigint NULL DEFAULT NULL COMMENT '聊天室id',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL,
  `deleted` tinyint NULL DEFAULT NULL,
  `guild_id` int NULL DEFAULT NULL COMMENT '发生收益时所在公会id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7991 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户赠送礼物表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
