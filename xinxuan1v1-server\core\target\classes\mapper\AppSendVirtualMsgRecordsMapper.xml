<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppSendVirtualMsgRecordsMapper">

    <resultMap type="AppSendVirtualMsgRecords" id="AppSendVirtualMsgRecordsResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="content" column="content"/>
        <result property="virtualUserId" column="virtual_user_id"/>
        <result property="receiveUserId" column="receive_user_id"/>
    </resultMap>

    <sql id="selectAppSendVirtualMsgRecordsVo">
        select id, create_time, update_time, content, virtual_user_id, receive_user_id from app_send_virtual_msg_records
    </sql>

    <select id="selectAppSendVirtualMsgRecordsList" parameterType="AppSendVirtualMsgRecords"
            resultMap="AppSendVirtualMsgRecordsResult">
        <include refid="selectAppSendVirtualMsgRecordsVo"/>
        <where>
            <if test="content != null  and content != ''">and content = #{content}</if>
            <if test="virtualUserId != null ">and virtual_user_id = #{virtualUserId}</if>
            <if test="receiveUserId != null ">and receive_user_id = #{receiveUserId}</if>
        </where>
    </select>

    <select id="selectAppSendVirtualMsgRecordsById" parameterType="Long" resultMap="AppSendVirtualMsgRecordsResult">
        <include refid="selectAppSendVirtualMsgRecordsVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppSendVirtualMsgRecords" parameterType="AppSendVirtualMsgRecords" useGeneratedKeys="true"
            keyProperty="id">
        insert into app_send_virtual_msg_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="content != null">content,</if>
            <if test="virtualUserId != null">virtual_user_id,</if>
            <if test="receiveUserId != null">receive_user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="content != null">#{content},</if>
            <if test="virtualUserId != null">#{virtualUserId},</if>
            <if test="receiveUserId != null">#{receiveUserId},</if>
        </trim>
    </insert>

    <update id="updateAppSendVirtualMsgRecords" parameterType="AppSendVirtualMsgRecords">
        update app_send_virtual_msg_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="content != null">content = #{content},</if>
            <if test="virtualUserId != null">virtual_user_id = #{virtualUserId},</if>
            <if test="receiveUserId != null">receive_user_id = #{receiveUserId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppSendVirtualMsgRecordsById" parameterType="Long">
        delete from app_send_virtual_msg_records where id = #{id}
    </delete>

    <delete id="deleteAppSendVirtualMsgRecordsByIds" parameterType="String">
        delete from app_send_virtual_msg_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>