<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppUserVideoCardBillMapper">


    <select id="getVideoCardUseDetail" resultType="com.hzy.core.entity.AppUserVideoCardBill">
        select * from app_user_video_card_bill
        where user_id = #{userId}
        order by id desc
    </select>

    <select id="getVideoCardBill" resultType="com.hzy.core.entity.AppUserVideoCardBill">
        select vcb.*, au1.nick_name as nickname, au1.recode_code as recodeCode, au2.nick_name as toNickname, au2.recode_code as toRecodeCode
        from app_user_video_card_bill vcb
        left join app_user au1 on vcb.user_id = au1.id
        left join app_user au2 on vcb.to_user_id = au2.id
        <where>
            <if test="nickname != null and nickname != ''">
                au1.nick_name = #{nickname}
            </if>
            <if test="recodeCode != null and recodeCode != ''">
                au1.recode_code = #{recodeCode}
            </if>
            <if test="toNickname != null and toNickname != ''">
                au2.nick_name = #{toNickname}
            </if>
            <if test="toRecodeCode != null and toRecodeCode != ''">
                au2.recode_code = #{toRecodeCode}
            </if>
            <if test="billType != null">
                and vcb.bill_type = #{billType}
            </if>
        </where>
        order by id desc
    </select>

    <select id="getVideoCardBillTotalAmount" resultType="java.lang.Integer">
        select IFNULL(SUM(vcb.amount), 0)
        from app_user_video_card_bill vcb
        left join app_user au1 on vcb.user_id = au1.id
        left join app_user au2 on vcb.to_user_id = au2.id
        <where>
            <if test="nickname != null and nickname != ''">
                au1.nick_name = #{nickname}
            </if>
            <if test="recodeCode != null and recodeCode != ''">
                au1.recode_code = #{recodeCode}
            </if>
            <if test="toNickname != null and toNickname != ''">
                au2.nick_name = #{toNickname}
            </if>
            <if test="toRecodeCode != null and toRecodeCode != ''">
                au2.recode_code = #{toRecodeCode}
            </if>
            <if test="billType != null">
                and vcb.bill_type = #{billType}
            </if>
        </where>
    </select>
</mapper>