<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppBannedRecordMapper">


    <select id="getLastUserBannedRecord" resultType="com.hzy.core.entity.AppBannedRecord">
        select * from app_banned_record
        where user_id = #{userId}
        order by id desc
        limit 1
    </select>

    <select id="getUserBannedRecordList" resultType="com.hzy.core.entity.AppBannedRecord">
        select *, au.nick_name as userName, au.recode_code as recodeCode, su.nick_name as createUserName
        from app_banned_record abr
        left join app_user au on abr.user_id = au.id
        left join sys_user su on abr.create_user_id = su.user_id
        <where>
            <if test="userId != null">
                and abr.user_id = #{userId}
            </if>
            <if test="type != null">
                and abr.type = #{type}
            </if>
            <if test="reason != null and reason != ''">
                and abr.reason = #{reason}
            </if>
        </where>
        order by abr.id desc
    </select>
</mapper>