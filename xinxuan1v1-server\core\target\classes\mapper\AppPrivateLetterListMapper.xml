<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppPrivateLetterListMapper">

    <resultMap type="AppPrivateLetterList" id="AppPrivateLetterListResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="receiveUserId" column="receive_user_id"/>
        <result property="isTemporarily" column="is_temporarily"/>
        <result property="isFamily" column="is_family"/>
        <result property="familyId" column="family_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="isTop" column="is_top"/>
    </resultMap>

    <sql id="selectAppPrivateLetterListVo">
        select id, user_id, receive_user_id, is_temporarily, is_family, family_id, update_time, is_top, remark_name
        from app_private_letter_list
    </sql>

    <select id="selectAppPrivateLetterListList" parameterType="AppPrivateLetterList"
            resultMap="AppPrivateLetterListResult">
        <include refid="selectAppPrivateLetterListVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="receiveUserId != null ">and receive_user_id = #{receiveUserId}</if>
            <if test="isTemporarily != null ">and is_temporarily = #{isTemporarily}</if>
            <if test="isFamily != null ">and is_family = #{isFamily}</if>
            <if test="familyId != null ">and family_id = #{familyId}</if>
        </where>
    </select>

    <select id="selectAppPrivateLetterListById" parameterType="Long" resultMap="AppPrivateLetterListResult">
        <include refid="selectAppPrivateLetterListVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppPrivateLetterList" parameterType="AppPrivateLetterList" useGeneratedKeys="true"
            keyProperty="id">
        insert into app_private_letter_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="receiveUserId != null">receive_user_id,</if>
            <if test="isTemporarily != null">is_temporarily,</if>
            <if test="isFamily != null">is_family,</if>
            <if test="familyId != null">family_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isTop != null">is_top,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="receiveUserId != null">#{receiveUserId},</if>
            <if test="isTemporarily != null">#{isTemporarily},</if>
            <if test="isFamily != null">#{isFamily},</if>
            <if test="familyId != null">#{familyId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isTop != null">#{isTop},</if>
        </trim>
    </insert>

    <update id="updateAppPrivateLetterList" parameterType="AppPrivateLetterList">
        update app_private_letter_list
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="receiveUserId != null">receive_user_id = #{receiveUserId},</if>
            <if test="isTemporarily != null">is_temporarily = #{isTemporarily},</if>
            <if test="isFamily != null">is_family = #{isFamily},</if>
            <if test="familyId != null">family_id = #{familyId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isTop != null">is_top = #{isTop},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppPrivateLetterListById" parameterType="Long">
        delete
        from app_private_letter_list
        where id = #{id}
    </delete>

    <delete id="deleteAppPrivateLetterListByIds" parameterType="String">
        delete from app_private_letter_list where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="getUserPrivateLetterList"
            resultType="com.hzy.core.model.vo.app.AppPrivateLetterListVo">
        select pl.id as privateLetterId,
        pl.user_id as receiveUserId,
        pl.family_id as familyId,
        pl.is_family as isFamily,
        pl.is_top as isTop,
        pl.remark_name as remarkName,
        pl.update_time as updateTime
        from
        app_private_letter_list
        pl
        where pl.receive_user_id=#{userId}
        and pl.is_temporarily=false
        order by pl.is_top desc, pl.update_time desc
    </select>

    <delete id="deletePrivateLetter" parameterType="java.lang.Long">
        delete
        from app_private_letter_list
        where id = #{privateLetterId}
        and receive_user_id = #{userId}
    </delete>

    <delete id="deletePrivateLetterByUserIdAndReceiveUserId" parameterType="java.lang.Long">
        delete
        from app_private_letter_list
        where user_id = #{userId}
        and receive_user_id = #{receiveUserId}
        and is_family = false
    </delete>

    <select id="isExist" parameterType="java.lang.Long" resultType="java.lang.Boolean">
        select if(count(*) > 0, 1, 0)
        from app_private_letter_list
        where user_id = #{userId}
        and receive_user_id = #{receiveUserId}
        and is_family = false
    </select>


    <select id="selectAppPrivateLetterListByUserIdAndReceiveUserId" parameterType="java.lang.Long"
            resultMap="AppPrivateLetterListResult">
        <include refid="selectAppPrivateLetterListVo"/>
        where user_id = #{userId}
        and receive_user_id = #{receiveUserId}
        and is_family=false
    </select>

    <select id="isExistFamily" parameterType="java.lang.Long" resultType="java.lang.Boolean">
        select if(count(*) > 0, 1, 0)
        from app_private_letter_list
        where receive_user_id = #{receiveUserId}
        and family_id = #{familyId}
        and is_family = true
    </select>


    <delete id="deletePrivateLetterByUserIdAndFamilyId" parameterType="java.lang.Long">
        delete
        from app_private_letter_list
        where family_id = #{familyId}
        and receive_user_id = #{receiveUserId}
    </delete>

    <delete id="deletePrivateLetterByFamilyId" parameterType="java.lang.Long">
        delete
        from app_private_letter_list
        where family_id = #{familyId}
    </delete>

    <select id="getTemporarilyPrivateLetterUnreadCount" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select sum(
        (select count(*)
        from app_chatting_records c1
        where c1.receive_user_id = pl.receive_user_id
        and c1.send_user_id = pl.user_id
        and c1.is_read = 0
        and c1.is_family = false))
        from app_private_letter_list pl
        LEFT JOIN app_user receive_user
        on (receive_user.id = pl.user_id)
        where pl.receive_user_id = #{userId}
        and pl.is_temporarily = true
        and pl.is_family = false
        and receive_user.user_status = true
    </select>


    <select id="getChatUnreadCount" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select
        sum(if(pl.is_family=false,(select
        count(*)
        from app_chatting_records c1
        where c1.receive_user_id = pl.receive_user_id and c1.send_user_id =pl.user_id and c1.is_read=0 and
        c1.is_family=false
        ) ,(select count(*)
        from app_chatting_records c1
        where c1.family_id=pl.family_id
        and c1.is_read=0
        and c1.receive_user_id=pl.receive_user_id
        and c1.is_family=true)))
        from
        app_private_letter_list
        pl
        LEFT JOIN app_user receive_user
        on (receive_user.id = pl.user_id)
        LEFT JOIN app_family f
        on (f.id = pl.family_id)
        where pl.receive_user_id=#{userId}
        and pl.is_temporarily=false
    </select>

    <select id="getFamilyByReceiveUserIdAndFamilyId" parameterType="java.lang.Long"
            resultMap="AppPrivateLetterListResult">
        <include refid="selectAppPrivateLetterListVo"/>
        where receive_user_id = #{receiveUserId}
        and family_id = #{familyId}
        and is_family = true
        order by id desc
    </select>

    <!-- updateAppPrivateLetterListDate --> 

    <update id="updateAppPrivateLetterListDate">
        update app_private_letter_list
        <trim prefix="SET" suffixOverrides=",">
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where user_id = #{userId}
        and receive_user_id = #{receiveUserId}
    </update>

    <update id="updateTopStatus">
        update app_private_letter_list
        set is_top = #{isTop}
        where id = #{privateLetterId}
    </update>

    <select id="countPrivateMessagesBetweenUsers" resultType="java.lang.Integer">
        select count(*)
        from app_chatting_records 
        where send_user_id = #{fromUserId}
        and receive_user_id = #{toUserId}
        and is_family = false
    </select>

    <!-- 根据用户ID删除所有相关的私信关系 -->
    <delete id="deleteByUserId">
        DELETE FROM app_private_letter_list 
        WHERE user_id = #{userId} OR receive_user_id = #{userId}
    </delete>
</mapper>