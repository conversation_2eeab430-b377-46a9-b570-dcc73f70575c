<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppCompleteTaskRecordMapper">

    <resultMap type="AppCompleteTaskRecord" id="AppCompleteTaskRecordResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="taskType" column="task_type"/>
        <result property="points" column="points"/>
    </resultMap>

    <sql id="selectAppCompleteTaskRecordVo">
        select id, create_time, update_time, user_id, task_type, points
        from app_complete_task_record
    </sql>

    <select id="selectAppCompleteTaskRecordList" parameterType="AppCompleteTaskRecord"
            resultMap="AppCompleteTaskRecordResult">
        <include refid="selectAppCompleteTaskRecordVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="taskType != null ">and task_type = #{taskType}</if>
            <if test="points != null ">and points = #{points}</if>
        </where>
    </select>

    <select id="selectAppCompleteTaskRecordById" parameterType="Long" resultMap="AppCompleteTaskRecordResult">
        <include refid="selectAppCompleteTaskRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppCompleteTaskRecord" parameterType="AppCompleteTaskRecord" useGeneratedKeys="true"
            keyProperty="id">
        insert into app_complete_task_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="taskType != null">task_type,</if>
            <if test="points != null">points,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="taskType != null">#{taskType},</if>
            <if test="points != null">#{points},</if>
        </trim>
    </insert>

    <update id="updateAppCompleteTaskRecord" parameterType="AppCompleteTaskRecord">
        update app_complete_task_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="taskType != null">task_type = #{taskType},</if>
            <if test="points != null">points = #{points},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppCompleteTaskRecordById" parameterType="Long">
        delete
        from app_complete_task_record
        where id = #{id}
    </delete>

    <delete id="deleteAppCompleteTaskRecordByIds" parameterType="String">
        delete from app_complete_task_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="isCompleteByTaskTypeAndUserId" resultType="java.lang.Integer">
        select if(count(*)>0,1,0)
        from app_complete_task_record
        where user_id = #{userId}
        and task_type = #{taskType}
        <if test="!isBeginnerTask">
            and to_days(create_time) = to_days(now())
        </if>
    </select>
</mapper>