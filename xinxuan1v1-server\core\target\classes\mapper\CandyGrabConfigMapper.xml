<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.candy.CandyGrabConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hzy.core.entity.candy.CandyGrabConfig">
        <id column="config_id" property="configId" />
        <result column="gold_amount" property="goldAmount" />
        <result column="candy_ratio" property="candyRatio" />
        <result column="status" property="status" />
        <result column="participants" property="participants" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        config_id, gold_amount, candy_ratio, status, participants, create_time, update_time
    </sql>

</mapper>
