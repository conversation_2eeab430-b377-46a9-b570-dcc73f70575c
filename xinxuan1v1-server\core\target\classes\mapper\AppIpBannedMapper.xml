<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppIpBannedMapper">

    <resultMap type="AppIpBanned" id="AppIpBannedResult">
        <result property="id" column="id"/>
        <result property="ipAddress" column="ip_address"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="selectAppIpBannedVo">
        select id, ip_address, create_time, update_time, create_by, update_by from app_ip_banned
    </sql>

    <select id="selectAppIpBannedList" parameterType="AppIpBanned" resultMap="AppIpBannedResult">
        <include refid="selectAppIpBannedVo"/>
        <where>
            <if test="ipAddress != null  and ipAddress != ''">and ip_address like concat('%', #{ipAddress}, '%')</if>
        </where>
        order by id desc
    </select>

    <select id="selectAppIpBannedById" parameterType="Long" resultMap="AppIpBannedResult">
        <include refid="selectAppIpBannedVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppIpBanned" parameterType="AppIpBanned" useGeneratedKeys="true" keyProperty="id">
        insert into app_ip_banned
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ipAddress != null">ip_address,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ipAddress != null">#{ipAddress},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateAppIpBanned" parameterType="AppIpBanned">
        update app_ip_banned
        <trim prefix="SET" suffixOverrides=",">
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppIpBannedById" parameterType="Long">
        delete from app_ip_banned where id = #{id}
    </delete>

    <delete id="deleteAppIpBannedByIds" parameterType="String">
        delete from app_ip_banned where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAppIpBannedByIp">
        delete from app_ip_banned where ip_address=#{ip}
    </delete>

    <select id="isIpBanned" resultType="java.lang.Integer" parameterType="java.lang.String">
        select 1 from app_ip_banned
        where ip_address=#{ip}
        limit 0,1
    </select>
</mapper>