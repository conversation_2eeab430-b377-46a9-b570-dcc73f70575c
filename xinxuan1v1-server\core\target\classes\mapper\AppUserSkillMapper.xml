<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppUserSkillMapper">

    <resultMap type="AppUserSkill" id="AppUserSkillResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="skillId" column="skill_id"/>
        <result property="stats" column="stats"/>
        <result property="msg" column="msg"/>
        <result property="skillCertificate" column="skill_certificate"/>
        <result property="avatar" column="avatar"/>
        <result property="voiceContent" column="voice_content"/>
        <result property="content" column="content"/>
        <result property="chargeType" column="charge_type"/>
        <result property="price" column="price"/>
        <result property="gameId" column="game_id"/>
        <result property="gameLv" column="game_lv"/>
        <result property="tag" column="tag"/>
        <result property="orderCount" column="order_count"/>
        <result property="mark" column="mark"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectAppUserSkillVo">
        select id, user_id, skill_id, stats, msg, skill_certificate, avatar, voice_content, content, charge_type, price,
        game_id, game_lv, tag, order_count, mark,create_time from app_user_skill
    </sql>

    <select id="selectAppUserSkillList" parameterType="AppUserSkill" resultMap="AppUserSkillResult">
        select usk.id, usk.user_id, usk.skill_id, usk.stats, usk.msg, usk.skill_certificate, usk.avatar,
        usk.voice_content, usk.content, usk.charge_type, usk.price,
        usk.game_id, usk.game_lv, usk.tag, usk.order_count, usk.mark,
        usk.create_time,
        sk.name as skillName,
        u.nick_name as nickName,
        u.phone as phone,
        u.sex as sex,
        u.head_portrait as headPortrait,
        u.recode_code as recodeCode
        from app_user_skill usk
        LEFT JOIN app_skill sk on(sk.id=usk.skill_id)
        LEFT JOIN app_user u on(u.id=usk.user_id)
        where 1=1
        <if test="skillName!=null and skillName!=''">
            and sk.name like concat('%', #{skillName}, '%')
        </if>
        <if test="recodeCode!=null and recodeCode!=''">
            and u.recode_code like concat('%', #{recodeCode}, '%')
        </if>
        <if test="phone!=null and phone!=''">
            and u.phone like concat('%', #{phone}, '%')
        </if>
        <if test="nickName!=null and nickName!=''">
            and u.nick_name like concat('%', #{nickName}, '%')
        </if>
        <if test="userId != null ">and usk.user_id = #{userId}</if>
        <if test="skillId != null ">and usk.skill_id = #{skillId}</if>
        <if test="stats != null ">and usk.stats = #{stats}</if>
        order by usk.id desc
    </select>

    <select id="selectAppUserSkillById" parameterType="Long" resultMap="AppUserSkillResult">
        <include refid="selectAppUserSkillVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppUserSkill" parameterType="AppUserSkill" useGeneratedKeys="true" keyProperty="id">
        insert into app_user_skill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="skillId != null">skill_id,</if>
            <if test="stats != null">stats,</if>
            <if test="msg != null">msg,</if>
            <if test="skillCertificate != null">skill_certificate,</if>
            <if test="avatar != null">avatar,</if>
            <if test="voiceContent != null">voice_content,</if>
            <if test="content != null">content,</if>
            <if test="chargeType != null">charge_type,</if>
            <if test="price != null">price,</if>
            <if test="gameId != null">game_id,</if>
            <if test="gameLv != null">game_lv,</if>
            <if test="tag != null">tag,</if>
            <if test="orderCount != null">order_count,</if>
            <if test="mark != null">mark,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="skillId != null">#{skillId},</if>
            <if test="stats != null">#{stats},</if>
            <if test="msg != null">#{msg},</if>
            <if test="skillCertificate != null">#{skillCertificate},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="voiceContent != null">#{voiceContent},</if>
            <if test="content != null">#{content},</if>
            <if test="chargeType != null">#{chargeType},</if>
            <if test="price != null">#{price},</if>
            <if test="gameId != null">#{gameId},</if>
            <if test="gameLv != null">#{gameLv},</if>
            <if test="tag != null">#{tag},</if>
            <if test="orderCount != null">#{orderCount},</if>
            <if test="mark != null">#{mark},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateAppUserSkill" parameterType="AppUserSkill">
        update app_user_skill
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="skillId != null">skill_id = #{skillId},</if>
            <if test="stats != null">stats = #{stats},</if>
            <if test="msg != null">msg = #{msg},</if>
            <if test="skillCertificate != null">skill_certificate = #{skillCertificate},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="voiceContent != null">voice_content = #{voiceContent},</if>
            <if test="content != null">content = #{content},</if>
            <if test="chargeType != null">charge_type = #{chargeType},</if>
            <if test="price != null">price = #{price},</if>
            <if test="gameId != null">game_id = #{gameId},</if>
            <if test="gameLv != null">game_lv = #{gameLv},</if>
            <if test="tag != null">tag = #{tag},</if>
            <if test="orderCount != null">order_count = #{orderCount},</if>
            <if test="mark != null">mark = #{mark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppUserSkillById" parameterType="Long">
        delete from app_user_skill where id = #{id}
    </delete>

    <delete id="deleteAppUserSkillByIds" parameterType="String">
        delete from app_user_skill where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getUserSkillList" resultType="com.hzy.core.model.vo.app.AppUserSkillVo">
        select uk.id as userSkillId, uk.user_id as userId, uk.stats as status, uk.msg, uk.skill_certificate as
        skillCertificate,
        uk.avatar, uk.voice_content as voiceContent,
        uk.create_time as createTime,
        uk.content, uk.charge_type as chargeType, uk.price,
        uk.game_id as gameId, uk.game_lv as gameLv, uk.tag, uk.order_count as orderCount, uk.mark,
        sk.id as skillId, sk.name, sk.ico_url as icoUrl, sk.instructions, sk.aptitude, sk.is_game as isGame,
        uk.user_id as userId
        from app_user_skill uk
        LEFT JOIN app_skill sk on(sk.id=uk.skill_id)
        where uk.user_id=#{userId}
        <if test="stats!=null">
            and uk.stats=#{stats}
        </if>
        <if test="stats==null">
            and uk.stats BETWEEN 0 AND 1
        </if>
        order by uk.id desc
    </select>

    <select id="getUserSkillDetails" resultType="com.hzy.core.model.vo.app.AppUserSkillVo"
            parameterType="java.lang.Long">
        select uk.id as userSkillId, uk.user_id as userId, uk.stats as status, uk.msg, uk.skill_certificate as
        skillCertificate,
        sk.img_url as imgUrl,
        sk.avatar_url as avatarUrl,
        uk.avatar, uk.voice_content as voiceContent,
        uk.content, uk.charge_type as chargeType, uk.price,
        uk.game_id as gameId, uk.game_lv as gameLv, uk.tag, uk.order_count as orderCount, uk.mark,
        sk.id as skillId, sk.name, sk.ico_url as icoUrl, sk.instructions, sk.aptitude, sk.is_game as isGame
        from app_user_skill uk
        LEFT JOIN app_skill sk on(sk.id=uk.skill_id)
        where uk.id=#{userSkillId}
    </select>

    <select id="getStatusBySkillIdAndUserId" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select `stats` from app_user_skill
        where user_id=#{userId}
        and skill_id=#{skillId}
        order by id desc limit 0,1
    </select>


    <select id="getMahoganyList" resultType="com.hzy.core.model.vo.app.AppMahoganyVo">
        select uk.id as userSkillId, uk.user_id as userId, uk.stats as status, uk.msg, uk.skill_certificate as
        skillCertificate,
        uk.avatar, uk.voice_content as voiceContent,
        uk.content, uk.charge_type as chargeType, uk.price,
        uk.game_id as gameId, uk.game_lv as gameLv, uk.tag, uk.order_count as orderCount, uk.mark,
        sk.id as skillId, sk.name, sk.ico_url as icoUrl, sk.instructions, sk.aptitude, sk.is_game as isGame
        from app_user_skill uk
        LEFT JOIN app_skill sk on(sk.id=uk.skill_id)
        LEFT JOIN app_user u on(u.id=uk.user_id)
        where uk.stats=1
        and uk.charge_type BETWEEN 0 and 1
        <if test="sex!=null">
            and u.sex = #{sex}
        </if>
        <if test="minAge!=null">
            and u.age >=#{minAge}
        </if>
        <if test="maxAge!=null">
            and u.age &lt;= #{maxAge}
        </if>
        <if test="skillId!=null">
            and sk.id = #{skillId}
        </if>
        order by uk.order_count desc, uk.id desc
    </select>


    <select id="getInfoBySkillIdAndUserId" parameterType="java.lang.Long" resultMap="AppUserSkillResult">
        <include refid="selectAppUserSkillVo"/>
        where user_id=#{userId}
        and skill_id=#{skillId}
        order by id desc limit 0,1
    </select>

    <select id="getUserSkillDetailsBySkillId" resultType="com.hzy.core.model.vo.app.AppUserSkillVo">
        select uk.id as userSkillId, uk.user_id as userId, uk.stats as status, uk.msg, uk.skill_certificate as
        skillCertificate,
        sk.img_url as imgUrl,
        sk.avatar_url as avatarUrl,
        uk.avatar, uk.voice_content as voiceContent,
        uk.content, uk.charge_type as chargeType, uk.price,
        uk.game_id as gameId, uk.game_lv as gameLv, uk.tag, uk.order_count as orderCount, uk.mark,
        sk.id as skillId, sk.name, sk.ico_url as icoUrl, sk.instructions, sk.aptitude, sk.is_game as isGame
        from app_user_skill uk
        LEFT JOIN app_skill sk on(sk.id=uk.skill_id)
        where uk.user_id=#{userId} and uk.skill_id=#{skillId}
        order by uk.id desc limit 0,1
    </select>
</mapper>