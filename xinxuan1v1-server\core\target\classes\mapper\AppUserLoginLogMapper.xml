<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppUserLoginLogMapper">

    <resultMap type="AppUserLoginLog" id="AppUserLoginLogResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginTime" column="login_time"/>
        <result property="deviceId" column="device_id"/>
    </resultMap>

    <sql id="selectAppUserLoginLogVo">
        select id, user_id, login_ip, login_time , device_id from app_user_login_log
    </sql>
    <insert id="insertAppUserLoginLog" parameterType="com.hzy.core.entity.AppUserLoginLog">

        insert into app_user_login_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="loginIp != null">login_ip,</if>
            <if test="loginTime != null">login_time,</if>
            <if test="deviceId != null">device_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="loginIp != null">#{loginIp},</if>
            <if test="loginTime != null">#{loginTime},</if>
            <if test="deviceId != null">#{deviceId},</if>
        </trim>
    </insert>

    <select id="selectAppUserLoginLogList" parameterType="com.hzy.core.entity.AppUserLoginLog"
            resultMap="AppUserLoginLogResult">
        <include refid="selectAppUserLoginLogVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
        </where>
        order by login_time desc
    </select>

    <select id="selectAppUserLoginLogById" parameterType="long" resultMap="AppUserLoginLogResult">
        <include refid="selectAppUserLoginLogVo"/>
        where id = #{id}
    </select>


    <update id="updateAppUserLoginLog" parameterType="com.hzy.core.entity.AppUserLoginLog">
        update app_user_login_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="loginIp != null">login_ip = #{loginIp},</if>
            <if test="loginTime != null">login_time = #{loginTime},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppUserLoginLogById" parameterType="Long">
        delete from app_user_login_log where id = #{id}
    </delete>

    <delete id="deleteAppUserLoginLogByIds" parameterType="String">
        delete from app_user_login_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>