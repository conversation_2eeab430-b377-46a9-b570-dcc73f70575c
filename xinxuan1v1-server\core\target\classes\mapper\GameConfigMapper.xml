<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.GameConfigMapper">

    <select id="selectGameEntityPoolId" resultType="com.hzy.core.entity.GameConfigEntity">
        select gc.id, gc.once_price
        from game_config gc
        left join game_upgrade_pool gup on gc.id = gup.game_id
        where gup.id = #{poolId}
    </select>
</mapper>