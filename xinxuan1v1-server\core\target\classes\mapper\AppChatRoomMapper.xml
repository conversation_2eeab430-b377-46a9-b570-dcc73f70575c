<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppChatRoomMapper">

    <resultMap type="AppChatRoom" id="AppChatRoomResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="startTime" column="start_time"/>
        <result property="lastEndTime" column="last_end_time"/>
        <result property="lastStartTime" column="last_start_time"/>
        <result property="status" column="status"/>
        <result property="name" column="name"/>
        <result property="greetingText" column="greeting_text"/>
        <result property="noticeText" column="notice_text"/>
        <result property="avatarUrl" column="avatar_url"/>
        <result property="backgroundUrl" column="background_url"/>
        <result property="thirdPushFlow" column="third_push_flow"/>
        <result property="thirdId" column="third_id"/>
        <result property="type" column="type"/>
        <result property="isDel" column="is_del"/>
        <result property="onlineUserCount" column="online_user_count"/>
        <result property="wheatServingType" column="wheat_serving_type"/>
        <result property="isBlanking" column="is_blanking"/>
        <result property="wheatBit" column="wheat_bit"/>
        <result property="useMusicJson" column="use_music_json"/>
        <result property="isHideGiftGold" column="is_hide_gift_gold"/>
        <result property="password" column="password"/>
        <result property="fatherId" column="father_id"/>
        <result property="fatherId" column="father_id"/>
        <result property="labelJson" column="label_json"/>
        <result property="motorcadeType" column="motorcade_type"/>
        <result property="isHot" column="is_hot"/>
        <result property="isTop" column="is_top"/>
        <result property="guildId" column="guild_id"/>
        <result property="hallOwnerUserId" column="hall_owner_user_id"/>
        <result property="heat" column="heat"/>
        <result property="hallOwnerCommissionScale" column="hall_owner_commission_scale"/>
        <result property="giftIncomeScale" column="gift_income_scale"/>
        <result property="licenseName" column="license_name"/>
    </resultMap>

    <sql id="selectAppChatRoomVo">
        select id,
        create_time,
        update_time,
        user_id,
        start_time,
        last_end_time,
        last_start_time,
        `status`,
        `name`,
        greeting_text,
        notice_text,
        avatar_url,
        background_url,
        third_push_flow,
        third_id,
        `type`,
        is_del,
        online_user_count,
        wheat_serving_type,
        is_blanking,
        wheat_bit,
        use_music_json,
        is_hide_gift_gold,
        `password`,
        father_id,
        label_json,
        motorcade_type,
        is_hot,
        guild_id,
        hall_owner_user_id,
        heat,
        hall_owner_commission_scale,
        gift_income_scale
        from app_chat_room
    </sql>

    <select id="selectAppChatRoomList" parameterType="AppChatRoom" resultMap="AppChatRoomResult">
        <include refid="selectAppChatRoomVo"/>
        where is_del =false
        <if test="userId != null ">and user_id = #{userId}</if>
        <if test="startTime != null ">and start_time = #{startTime}</if>
        <if test="lastEndTime != null ">and last_end_time = #{lastEndTime}</if>
        <if test="lastStartTime != null ">and last_start_time = #{lastStartTime}</if>
        <if test="status != null ">and `status` = #{status}</if>
        <if test="name != null  and name != ''">and `name` like concat('%', #{name}, '%')</if>
        <if test="greetingText != null  and greetingText != ''">and greeting_text = #{greetingText}</if>
        <if test="noticeText != null  and noticeText != ''">and notice_text = #{noticeText}</if>
        <if test="avatarUrl != null  and avatarUrl != ''">and avatar_url = #{avatarUrl}</if>
        <if test="backgroundUrl != null  and backgroundUrl != ''">and background_url = #{backgroundUrl}</if>
        <if test="thirdPushFlow != null  and thirdPushFlow != ''">and third_push_flow = #{thirdPushFlow}</if>
        <if test="thirdId != null  and thirdId != ''">and third_id = #{thirdId}</if>
        <if test="type != null ">and `type` = #{type}</if>
        <if test="onlineUserCount != null ">and online_user_count = #{onlineUserCount}</if>
        <if test="wheatServingType != null ">and wheat_serving_type = #{wheatServingType}</if>
        <if test="isBlanking != null ">and is_blanking = #{isBlanking}</if>
        <if test="wheatBit != null ">and wheat_bit = #{wheatBit}</if>
        <if test="isHideGiftGold != null ">and is_hide_gift_gold = #{isHideGiftGold}</if>
        <if test="fatherId != null ">and father_id = #{fatherId}</if>
        <if test="motorcadeType != null ">and motorcade_type = #{motorcadeType}</if>
        <if test="isHot != null ">and is_hot = #{isHot}</if>
        <if test="guildId != null ">and guild_id = #{guildId}</if>
        <if test="hallOwnerUserId != null ">and hall_owner_user_id = #{hallOwnerUserId}</if>
        <if test="heat != null ">and heat = #{heat}</if>
        order by id desc
    </select>

    <select id="selectChatRoomListByType" resultType="java.lang.Long">
        SELECT cr.id AS chatRoomId FROM app_chat_room cr
                          LEFT JOIN app_chat_room_category crc ON crc.id = cr.type
                          LEFT JOIN app_user u ON u.id= cr.hall_owner_user_id
        where cr.status = 1 AND (cr.password IS NULL OR cr.password = '')
    </select>


    <select id="selectAppChatRoomById1" parameterType="Long" resultType="AppChatRoom">
        select cr.id AS id,
        gm.guild_id AS guildId
        FROM app_chat_room cr
        INNER JOIN app_guild_member gm ON cr.hall_owner_user_id = gm.user_id
        where cr.is_del = false AND cr.id = #{chatRoomId}
        and is_del =false
    </select>

    <select id="getChatRoomById" parameterType="Long" resultMap="AppChatRoomResult">
        <include refid="selectAppChatRoomVo"/>
        where id = #{id}
    </select>

    <select id="selectAppChatRoomByThirdId" parameterType="java.lang.String" resultMap="AppChatRoomResult">
        <include refid="selectAppChatRoomVo"/>
        where third_id = #{thirdId} and is_del =false
        order by id desc limit 0,1
    </select>

    <insert id="insertAppChatRoom" parameterType="AppChatRoom" useGeneratedKeys="true" keyProperty="id">
        insert into app_chat_room
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="lastEndTime != null">last_end_time,</if>
            <if test="lastStartTime != null">last_start_time,</if>
            <if test="status != null">status,</if>
            <if test="name != null">name,</if>
            <if test="greetingText != null">greeting_text,</if>
            <if test="noticeText != null">notice_text,</if>
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="backgroundUrl != null">background_url,</if>
            <if test="thirdPushFlow != null">third_push_flow,</if>
            <if test="thirdId != null">third_id,</if>
            <if test="type != null">type,</if>
            <if test="isDel != null">is_del,</if>
            <if test="onlineUserCount != null">online_user_count,</if>
            <if test="wheatServingType != null">wheat_serving_type,</if>
            <if test="isBlanking != null">is_blanking,</if>
            <if test="wheatBit != null">wheat_bit,</if>
            <if test="useMusicJson != null">use_music_json,</if>
            <if test="isHideGiftGold != null">is_hide_gift_gold,</if>
            <if test="password != null">password,</if>
            <if test="fatherId != null">father_id,</if>
            <if test="labelJson != null">label_json,</if>
            <if test="motorcadeType != null">motorcade_type,</if>
            <if test="isHot != null">is_hot,</if>
            <if test="guildId != null">guild_id,</if>
            <if test="hallOwnerUserId != null">hall_owner_user_id,</if>
            <if test="heat != null">heat,</if>
            <if test="hallOwnerCommissionScale != null">hall_owner_commission_scale,</if>
            <if test="giftIncomeScale != null">gift_income_scale,</if>
            <if test="licenseName != null">license_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="lastEndTime != null">#{lastEndTime},</if>
            <if test="lastStartTime != null">#{lastStartTime},</if>
            <if test="status != null">#{status},</if>
            <if test="name != null">#{name},</if>
            <if test="greetingText != null">#{greetingText},</if>
            <if test="noticeText != null">#{noticeText},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="backgroundUrl != null">#{backgroundUrl},</if>
            <if test="thirdPushFlow != null">#{thirdPushFlow},</if>
            <if test="thirdId != null">#{thirdId},</if>
            <if test="type != null">#{type},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="onlineUserCount != null">#{onlineUserCount},</if>
            <if test="wheatServingType != null">#{wheatServingType},</if>
            <if test="isBlanking != null">#{isBlanking},</if>
            <if test="wheatBit != null">#{wheatBit},</if>
            <if test="useMusicJson != null">#{useMusicJson},</if>
            <if test="isHideGiftGold != null">#{isHideGiftGold},</if>
            <if test="password != null">#{password},</if>
            <if test="fatherId != null">#{fatherId},</if>
            <if test="labelJson != null">#{labelJson},</if>
            <if test="motorcadeType != null">#{motorcadeType},</if>
            <if test="isHot != null">#{isHot},</if>
            <if test="guildId != null">#{guildId},</if>
            <if test="hallOwnerUserId != null">#{hallOwnerUserId},</if>
            <if test="heat != null">#{heat},</if>
            <if test="hallOwnerCommissionScale != null">#{hallOwnerCommissionScale},</if>
            <if test="giftIncomeScale != null">#{giftIncomeScale},</if>
            <if test="licenseName != null">#{licenseName},</if>
        </trim>
    </insert>

    <update id="updateAppChatRoom" parameterType="AppChatRoom">
        update app_chat_room
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="lastEndTime != null">last_end_time = #{lastEndTime},</if>
            <if test="lastStartTime != null">last_start_time = #{lastStartTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="name != null">name = #{name},</if>
            <if test="greetingText != null">greeting_text = #{greetingText},</if>
            <if test="noticeText != null">notice_text = #{noticeText},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="backgroundUrl != null">background_url = #{backgroundUrl},</if>
            <if test="thirdPushFlow != null">third_push_flow = #{thirdPushFlow},</if>
            <if test="thirdId != null">third_id = #{thirdId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="onlineUserCount != null">online_user_count = #{onlineUserCount},</if>
            <if test="wheatServingType != null">wheat_serving_type = #{wheatServingType},</if>
            <if test="isBlanking != null">is_blanking = #{isBlanking},</if>
            <if test="wheatBit != null">wheat_bit = #{wheatBit},</if>
            <if test="useMusicJson != null">use_music_json = #{useMusicJson},</if>
            <if test="isHideGiftGold != null">is_hide_gift_gold = #{isHideGiftGold},</if>
            <if test="password != null">password = #{password},</if>
            <if test="fatherId != null">father_id = #{fatherId},</if>
            <if test="labelJson != null">label_json = #{labelJson},</if>
            <if test="motorcadeType != null">motorcade_type = #{motorcadeType},</if>
            <if test="isHot != null">is_hot = #{isHot},</if>
            <if test="guildId != null">guild_id = #{guildId},</if>
            <if test="hallOwnerUserId != null">hall_owner_user_id = #{hallOwnerUserId},</if>
            <if test="heat != null">heat = #{heat},</if>
            <if test="hallOwnerCommissionScale != null">hall_owner_commission_scale = #{hallOwnerCommissionScale},</if>
            <if test="giftIncomeScale != null">gift_income_scale = #{giftIncomeScale},</if>
            <if test="isTop != null">is_top = #{isTop},</if>
            <if test="licenseName != null">license_name=#{licenseName},</if>

        </trim>
        where id = #{id}
    </update>


    <delete id="deleteAppChatRoomById" parameterType="Long">
        delete
        from app_chat_room
        where id = #{id}
    </delete>

    <delete id="deleteAppChatRoomByIds" parameterType="String">
        delete from app_chat_room where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectAppChatRoomByUserId" parameterType="Long" resultMap="AppChatRoomResult">
        <include refid="selectAppChatRoomVo"/>
        where user_id = #{id} and is_del =false
        order by id desc limit 0,1
    </select>

    <update id="addOnlineUserCount" parameterType="java.lang.Long">
        update app_chat_room
        set online_user_count=online_user_count + 1
        where id = #{id}
        and `status` = true
        and is_del = false
        and user_id!=#{userId}
    </update>

    <update id="subtractOnlineUserCount" parameterType="java.lang.Long">
        update app_chat_room
        set online_user_count=online_user_count - 1
        where id = #{id}
        and online_user_count >= 1
        and user_id!=#{userId}
    </update>

    <select id="getAppChatRoomList" resultType="com.hzy.core.model.vo.app.AppChatRoomListVo">
        select cr.id as chatRoomId,
        cr.start_time as startTime,
        cr.name,
        crc.category_name as categoryName,
        if(cr.type=5,cr.user_id,cr.hall_owner_user_id) as createUserId,
        cr.password as password,
        cr.greeting_text as greetingText,
        cr.notice_text as noticeText,
        cr.avatar_url as avatarUrl,
        cr.type as chatRoomType,
        cr.third_id as thirdId,
        cr.status as `status`,
        cr.heat,
        cr.motorcade_type as motorcadeType,
        cr.father_id as fatherId,
        cr.label_json as labelJson,
        cr.background_url as backgroundUrl,
        cr.wheat_serving_type as wheatServingType,
        cr.is_hide_gift_gold as isHideGiftGold,
        cr.is_hot as isHot,
        cr.is_top as isTop,
        cr.guild_id as guildId,
        cr.hall_owner_user_id,
        cr.hall_owner_commission_scale as hallOwnerCommissionScale,
        cr.gift_income_scale as giftIncomeScale,
        cr.license_name as licenseName,
        <if test="userId!=null">
            acru.is_follow as isFollow,
        </if>
        (select count(*)
        from app_chat_room_user cru
        where cru.is_del = false
        and cru.chat_room_id = cr.id
        and cru.is_join = true) as onlineUserCount
        from app_chat_room cr
        left join app_chat_room_category crc ON cr.type = crc.id
        <if test="userId!=null">
          left join app_chat_room_user acru ON cr.id = acru.chat_room_id AND acru.user_id = #{userId}

        </if>
        where cr.is_del = false
        and 1=if(cr.type=4,0,1)
        <if test="queryType!=null and queryType!=-1 and queryType!=6">
            and cr.type =#{queryType}
        </if>
        <if test="createUserId!=null and createUserId!=''">
            and (if(cr.type=5, cr.user_id, cr.hall_owner_user_id) = #{createUserId})
        </if>
        <if test="status!=null">
            and cr.status =#{status}
        </if>
        <if test="admin==1">
            <if test="isHot!=null">
                and cr.is_hot =#{isHot}
            </if>
            <if test="isTop!=null">
                and cr.is_top =#{isTop}
            </if>
        </if>
        <if test="admin==0">
            <if test="queryType!=null and queryType==6">
                and cr.type in(1,2,3,5,7,8,10)
            </if>
            <if test="queryType!=null and queryType==-1">
                -- and cr.is_hot =1
            </if>
            <if test="keyWord==null or keyWord==''">

            </if>
        </if>
        <if test="keyWord!=null and keyWord!=''">
            and (cr.third_id like concat('%', #{keyWord}, '%') or cr.name like concat('%', #{keyWord}, '%'))
        </if>

        <if test="admin!=null and admin==1 and queryType!=5">
            and cr.type!=5
        </if>
        <if test="admin==0">
            <if test="queryType!=null and queryType==5">
                order by if(cr.password='' or cr.password is null,1,0) desc, onlineUserCount desc,cr.id desc
            </if>
            <if test="queryType==null or queryType!=5">
                <if test="keyWord==null or keyWord==''">
                    order by cr.is_top DESC, if(cr.password='' or cr.password is null,1,0) desc, heat
                    desc,onlineUserCount desc,cr.id desc
                </if>
                <if test="keyWord!=null and keyWord!=''">
                    order by heat desc,onlineUserCount desc,cr.id desc
                </if>

            </if>
        </if>
        <if test="admin==1">
            order by if(cr.password='' or cr.password is null,1,0) desc, heat desc,onlineUserCount desc,cr.id desc
        </if>

    </select>
    <select id="getAppChatRoomList2" resultType="com.hzy.core.model.vo.app.AppChatRoomListVo">
        select cr.id as chatRoomId,
        cr.start_time as startTime,
        cr.name,
        crc.category_name as categoryName,
        if(cr.type=5,cr.user_id,cr.hall_owner_user_id) as createUserId,
        cr.password as password,
        cr.greeting_text as greetingText,
        cr.notice_text as noticeText,
        cr.avatar_url as avatarUrl,
        cr.type as chatRoomType,
        cr.third_id as thirdId,
        cr.status as `status`,
        cr.heat,
        cr.motorcade_type as motorcadeType,
        cr.father_id as fatherId,
        cr.label_json as labelJson,
        cr.background_url as backgroundUrl,
        cr.wheat_serving_type as wheatServingType,
        cr.is_hide_gift_gold as isHideGiftGold,
        cr.is_hot as isHot,
        cr.is_top as isTop,
        cr.guild_id as guildId,
        cr.hall_owner_user_id,
        cr.hall_owner_commission_scale as hallOwnerCommissionScale,
        cr.gift_income_scale as giftIncomeScale,
        cr.license_name as licenseName,
        <if test="userId!=null">
            acru.is_follow as isFollow,
        </if>
        (select count(*)
        from app_chat_room_user cru
        where cru.is_del = false
        and cru.chat_room_id = cr.id
        and cru.is_join = true) as onlineUserCount
        from app_chat_room cr
        left join app_chat_room_category crc ON cr.type = crc.id
        <if test="userId!=null">
           right join app_chat_room_user acru ON cr.id = acru.chat_room_id AND acru.user_id = #{userId}
              and acru.is_follow = 1
        </if>
        where cr.is_del = false
        and 1=if(cr.type=4,0,1)
        <if test="queryType!=null and queryType!=-1 and queryType!=6">
            and cr.type =#{queryType}
        </if>
        <if test="createUserId!=null and createUserId!=''">
            and (if(cr.type=5, cr.user_id, cr.hall_owner_user_id) = #{createUserId})
        </if>
        <if test="status!=null">
            and cr.status =#{status}
        </if>
        <if test="admin==1">
            <if test="isHot!=null">
                and cr.is_hot =#{isHot}
            </if>
            <if test="isTop!=null">
                and cr.is_top =#{isTop}
            </if>
        </if>
        <if test="admin==0">
            <if test="queryType!=null and queryType==6">
                and cr.type in(1,2,3,5,7,8,10)
            </if>
            <if test="queryType!=null and queryType==-1">
                and cr.is_hot =1
            </if>
            <if test="keyWord==null or keyWord==''">

            </if>
        </if>
        <if test="keyWord!=null and keyWord!=''">
            and (cr.third_id like concat('%', #{keyWord}, '%') or cr.name like concat('%', #{keyWord}, '%'))
        </if>

        <if test="admin!=null and admin==1 and queryType!=5">
            and cr.type!=5
        </if>
        <if test="admin==0">
            <if test="queryType!=null and queryType==5">
                order by if(cr.password='' or cr.password is null,1,0) desc, onlineUserCount desc,cr.id desc
            </if>
            <if test="queryType==null or queryType!=5">
                <if test="keyWord==null or keyWord==''">
                    order by cr.is_top DESC, if(cr.password='' or cr.password is null,1,0) desc, heat
                    desc,onlineUserCount desc,cr.id desc
                </if>
                <if test="keyWord!=null and keyWord!=''">
                    order by heat desc,onlineUserCount desc,cr.id desc
                </if>

            </if>
        </if>
        <if test="admin==1">
            order by if(cr.password='' or cr.password is null,1,0) desc, heat desc,onlineUserCount desc,cr.id desc
        </if>

    </select>


    <select id="getAppChatRoomList1" resultType="com.hzy.core.model.vo.app.AppChatRoomListVo">
        select cr.id as chatRoomId,
        cr.start_time as startTime,
        cr.name,
        cr.type as type,
        crc.category_name as categoryName,
        if(cr.type=5,cr.user_id,cr.hall_owner_user_id) as createUserId,
        cr.password as password,
        cr.greeting_text as greetingText,
        cr.notice_text as noticeText,
        cr.avatar_url as avatarUrl,
        cr.type as chatRoomType,
        cr.third_id as thirdId,
        cr.status as `status`,
        cr.heat,
        cr.motorcade_type as motorcadeType,
        cr.father_id as fatherId,
        cr.label_json as labelJson,
        cr.background_url as backgroundUrl,
        cr.wheat_serving_type as wheatServingType,
        cr.is_hide_gift_gold as isHideGiftGold,
        cr.is_hot as isHot,
        cr.guild_id as guildId,
        cr.hall_owner_user_id,
        cr.hall_owner_commission_scale as hallOwnerCommissionScale,
        cr.gift_income_scale as giftIncomeScale,
        (select count(*)
        from app_chat_room_user cru
        where cru.is_del = false
        and cru.chat_room_id = cr.id
        and cru.is_join = true) as onlineUserCount
        from app_chat_room cr
        left join app_chat_room_category crc ON crc.id = cr.type
        where cr.is_del = false
        -- and 1=if(cr.type=4,0,1)
        and 1 = 1
        <if test="queryType!=null">
            and cr.type =#{queryType}
        </if>
        <if test="status!=null">
            and cr.status =#{status}
        </if>

        <if test="admin==1">
            <if test="isHot!=null">
                and cr.is_hot =#{isHot}
            </if>
        </if>
        <if test="admin==0">
            <if test="queryType!=null and queryType==6">
                and cr.type in(1,2,3,7,8)
            </if>
            <if test="queryType!=null and queryType==-1">
                and cr.is_hot =1
            </if>
            <if test="keyWord==null or keyWord==''">

            </if>
        </if>
        <if test="keyWord!=null and keyWord!=''">
            and (cr.third_id like concat('%', #{keyWord}, '%') or cr.name like concat('%', #{keyWord}, '%'))
        </if>

        <if test="admin!=null and admin==1 and queryType!=5">
            and cr.type!=5
        </if>
        <if test="admin==0">
            <if test="queryType!=null and queryType==5">
                order by if(cr.password='' or cr.password is null,1,0) desc, onlineUserCount desc,cr.id desc
            </if>
            <if test="queryType==null or queryType!=5">
                <if test="keyWord==null or keyWord==''">
                    order by if(cr.password='' or cr.password is null,1,0) desc, heat desc,onlineUserCount desc,cr.id
                    desc
                </if>
                <if test="keyWord!=null and keyWord!=''">
                    order by heat desc,onlineUserCount desc,cr.id desc
                </if>

            </if>
        </if>
        <if test="admin==1">
            order by if(cr.password='' or cr.password is null,1,0) desc, heat desc,onlineUserCount desc,cr.id desc
        </if>

    </select>

    <select id="getOnlineUserCount" resultType="java.lang.Long" parameterType="java.lang.Long">
        select count(*)
        from app_chat_room_user cru
        where cru.chat_room_id = #{id}
        and cru.is_join = true
    </select>


    <select id="getAppChatRoomDetails" parameterType="java.lang.Long"
            resultType="com.hzy.core.model.vo.app.AppChatRoomListVo">
        select cr.id as chatRoomId,
        cr.start_time as startTime,
        cr.name,
        cr.user_id as createUserId,
        cr.heat,
        cr.password as password,
        cr.greeting_text as greetingText,
        cr.notice_text as noticeText,
        cr.motorcade_type as motorcadeType,
        cr.avatar_url as avatarUrl,
        cr.type as chatRoomType,
        cr.third_id as thirdId,
        cr.status as `status`,
        cr.father_id as fatherId,
        cr.label_json as labelJson,
        cr.background_url as backgroundUrl,
        cr.guild_id as guildId,
        cr.hall_owner_user_id,
        cr.wheat_serving_type as wheatServingType,
        cr.is_hide_gift_gold as isHideGiftGold,
        (select count(*)
        from app_chat_room_user cru
        where cru.is_del = false
        and cru.chat_room_id = cr.id
        and cru.is_join = true) as onlineUserCount
        from app_chat_room cr
        where cr.id=#{id}
        and cr.is_del = false
    </select>


    <select id="getChatRoomMotorcadeList" resultType="com.hzy.core.model.vo.app.AppChatRoomListVo">
        select cr.id as chatRoomId,
        cr.start_time as startTime,
        cr.name,
        null as heat,
        cr.user_id as createUserId,
        cr.password as password,
        cr.greeting_text as greetingText,
        cr.notice_text as noticeText,
        cr.avatar_url as avatarUrl,
        cr.type as chatRoomType,
        cr.third_id as thirdId,
        cr.status as `status`,
        cr.father_id as fatherId,
        cr.label_json as labelJson,
        cr.background_url as backgroundUrl,
        cr.wheat_serving_type as wheatServingType,
        cr.motorcade_type as motorcadeType,
        cr.is_hide_gift_gold as isHideGiftGold,
        cr.guild_id as guildId,
        cr.hall_owner_user_id,
        (select count(*)
        from app_chat_room_user cru
        where cru.is_del = false
        and cru.chat_room_id = cr.id
        and cru.is_join = true) as onlineUserCount
        from app_chat_room cr
        where cr.is_del = false
        and cr.type=4
        and cr.status !=2
        and cr.father_id=#{chatRoomId}
        <if test="keyWord!=null and keyWord!=''">
            and (cr.third_id like concat('%', #{keyWord}, '%') or cr.name like concat('%', #{keyWord}, '%'))
        </if>
        order by onlineUserCount desc, cr.id desc
    </select>


    <select id="getUserMotorcade" parameterType="Long" resultMap="AppChatRoomResult">
        <include refid="selectAppChatRoomVo"/>
        where user_id = #{userId} and is_del =false
        and type=4
        and father_id=#{chatRoomId}
        order by id desc limit 0,1
    </select>

    <select id="getUserPersonageChatRoomInfo" parameterType="Long" resultMap="AppChatRoomResult">
        <include refid="selectAppChatRoomVo"/>
        where user_id = #{id} and is_del =false and `type`=5
        order by id desc limit 0,1
    </select>


    <select id="getWeeksStarChatRoomInfo" parameterType="java.lang.Long"
            resultType="com.hzy.core.model.vo.app.AppChatRoomListVo">
        select cr.id as chatRoomId,
        cr.start_time as startTime,
        cr.name,
        cr.heat,
        if(cr.type=5,cr.user_id,cr.hall_owner_user_id) as createUserId,
        cr.password as password,
        cr.greeting_text as greetingText,
        cr.notice_text as noticeText,
        cr.motorcade_type as motorcadeType,
        cr.avatar_url as avatarUrl,
        cr.type as chatRoomType,
        cr.third_id as thirdId,
        cr.status as `status`,
        cr.father_id as fatherId,
        cr.label_json as labelJson,
        cr.background_url as backgroundUrl,
        cr.guild_id as guildId,
        cr.hall_owner_user_id,
        cr.wheat_serving_type as wheatServingType,
        cr.is_hide_gift_gold as isHideGiftGold,
        ifnull(sl.zsl,0) as chatRoomSumAmount
        from app_chat_room cr
        LEFT JOIN (select ifnull(sum(bill.user_contribution_value),0) as zsl,bill.chat_room_id from app_user_gold_bill
        bill where bill.create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
        and bill.create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        and 1=if(bill.user_contribution_value >0,1,0)
        GROUP BY bill.chat_room_id
        ) sl on (sl.chat_room_id=cr.id)
        where cr.is_del = false
        <if test="type!=null">
            and cr.type=#{type}
        </if>
        and 1=if(cr.type=4,0,if(cr.type=5,0,1))
        order by sl.zsl desc limit 0,1
    </select>


    <select id="getToDayStarChatRoomList" parameterType="java.lang.Long"
            resultType="com.hzy.core.model.vo.app.AppChatRoomListVo">
        select cr.id as chatRoomId,
        cr.start_time as startTime,
        cr.name,
        cr.heat,
        if(cr.type=5,cr.user_id,cr.hall_owner_user_id) as createUserId,
        cr.password as password,
        cr.greeting_text as greetingText,
        cr.notice_text as noticeText,
        cr.motorcade_type as motorcadeType,
        cr.avatar_url as avatarUrl,
        cr.type as chatRoomType,
        cr.third_id as thirdId,
        cr.status as `status`,
        cr.father_id as fatherId,
        cr.label_json as labelJson,
        cr.background_url as backgroundUrl,
        cr.guild_id as guildId,
        cr.hall_owner_user_id,
        cr.wheat_serving_type as wheatServingType,
        ifnull(sl.zsl,0) as chatRoomSumAmount
        from app_chat_room cr
        LEFT JOIN (select ifnull(sum(bill.user_contribution_value),0) as zsl,bill.chat_room_id from app_user_gold_bill
        bill where
        bill.create_time >= CURDATE() AND bill.create_time &lt; CURDATE() + INTERVAL 1 DAY
        and 1=if(bill.user_contribution_value >0,1,0)
        GROUP BY bill.chat_room_id
        ) sl on (sl.chat_room_id=cr.id)
        where cr.is_del = false
        <if test="type!=null">
            and cr.type=#{type}
        </if>
        and 1=if(cr.type=4,0,if(cr.type=5,0,1))
        order by sl.zsl desc limit 0,2
    </select>


    <update id="clearGuildBindChatRoom" parameterType="java.lang.Long">
        update app_chat_room set guild_id=0
        where guild_id=#{guildId}
    </update>


    <!--<select id="getGuildChatRoomList" resultType="com.hzy.core.model.vo.app.AppChatRoomListVo">
        select cr.id as chatRoomId,
        cr.start_time as startTime,
        cr.name,
        cr.heat,
        cr.user_id as createUserId,
        cr.password as password,
        cr.greeting_text as greetingText,
        cr.notice_text as noticeText,
        cr.avatar_url as avatarUrl,
        cr.type as chatRoomType,
        cr.third_id as thirdId,
        cr.status as `status`,
        cr.motorcade_type as motorcadeType,
        cr.father_id as fatherId,
        cr.label_json as labelJson,
        cr.background_url as backgroundUrl,
        cr.wheat_serving_type as wheatServingType,
        cr.is_hide_gift_gold as isHideGiftGold,
        cr.is_hot as isHot,
        cr.guild_id as guildId,
        cr.hall_owner_user_id,
        (select count(*)
        from app_chat_room_user cru
        where cru.is_del = false
        and cru.chat_room_id = cr.id
        and cru.is_join = true) as onlineUserCount
        from app_chat_room cr
        where cr.is_del = false
        <if test="status!=null">
            and cr.status =#{status}
        </if>
        <if test="type!=null">
            and cr.type =#{type}
        </if>
        <if test="chatRoomId!=null">
            and cr.id =#{chatRoomId}
        </if>
        <if test="keyWord!=null and keyWord!=''">
            and (cr.third_id like concat('%', #{keyWord}, '%') or cr.name like concat('%', #{keyWord}, '%'))
        </if>
        and cr.guild_id=#{guildId}
        <if test="isAdminQuery==1">
            order by cr.id desc
        </if>
        <if test="isAdminQuery==0">
            order by onlineUserCount desc, cr.id desc
        </if>
    </select>-->


    <select id="getGuildChatRoomList" resultType="com.hzy.core.model.vo.app.AppChatRoomListVo">
        select cr.id as chatRoomId,
        cr.start_time as startTime,
        cr.name,
        cr.heat,
        cr.user_id as createUserId,
        cr.password as password,
        cr.greeting_text as greetingText,
        cr.notice_text as noticeText,
        cr.avatar_url as avatarUrl,
        cr.type as chatRoomType,
        cr.third_id as thirdId,
        cr.status as `status`,
        cr.motorcade_type as motorcadeType,
        cr.father_id as fatherId,
        cr.label_json as labelJson,
        cr.background_url as backgroundUrl,
        cr.wheat_serving_type as wheatServingType,
        cr.is_hide_gift_gold as isHideGiftGold,
        cr.is_hot as isHot,
        cr.guild_id as guildId,
        cr.hall_owner_user_id,
        gm.sys_user_id as brokerId,
        su.nick_name as brokerName,
        cr.license_name as licenseName,
        (select count(*)
        from app_chat_room_user cru
        where cru.is_del = false
        and cru.chat_room_id = cr.id
        and cru.is_join = true) as onlineUserCount
        from app_chat_room cr
        INNER JOIN app_guild_member gm ON cr.hall_owner_user_id = gm.user_id
        join app_guild ag on ag.user_id = gm.user_id
        join sys_user su on su.user_id = ag.sys_user_id
        where cr.is_del = false AND gm.guild_id=#{guildId}
          <if test="brokerId!=null">
              and gm.sys_user_id=#{brokerId}
          </if>
        <if test="status!=null">
            and cr.status =#{status}
        </if>
        <if test="type==5">
            and cr.type IN (5)
        </if>
        <if test="type==4">
            and cr.type NOT IN (5)
        </if>
        <if test="chatRoomId!=null">
            and cr.id =#{chatRoomId}
        </if>
        <if test="keyWord!=null and keyWord!=''">
            and (cr.third_id like concat('%', #{keyWord}, '%') or cr.name like concat('%', #{keyWord}, '%'))
        </if>

        <if test="isAdminQuery==1">
            order by cr.id desc
        </if>
        <if test="isAdminQuery==0">
            order by onlineUserCount desc, cr.id desc
        </if>

    </select>

    <!--<select id="getGuildGoodChatRoomList" resultType="com.hzy.core.model.vo.app.AppChatRoomListVo">
        select cr.id as chatRoomId,
        cr.start_time as startTime,
        cr.name,
        cr.user_id as createUserId,
        cr.password as password,
        cr.heat,
        cr.greeting_text as greetingText,
        cr.notice_text as noticeText,
        cr.avatar_url as avatarUrl,
        cr.type as chatRoomType,
        cr.third_id as thirdId,
        cr.status as `status`,
        cr.motorcade_type as motorcadeType,
        cr.father_id as fatherId,
        cr.label_json as labelJson,
        cr.background_url as backgroundUrl,
        cr.wheat_serving_type as wheatServingType,
        cr.is_hide_gift_gold as isHideGiftGold,
        cr.is_hot as isHot,
        cr.guild_id as guildId,
        cr.hall_owner_user_id,
        ifnull(t.zx,0) as chatRoomSumAmount,
        (select count(*)
        from app_chat_room_user cru
        where cru.chat_room_id = cr.id
        and cru.is_join = true) as onlineUserCount
        from app_chat_room cr
        LEFT JOIN (select ifnull(sum(bill.user_contribution_value),0) as zx,bill.chat_room_id from app_user_gold_bill bill where
        bill.create_time BETWEEN DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') AND LAST_DAY(CURRENT_DATE)
        and 1=if(bill.user_contribution_value >0,1,0)
        group by bill.chat_room_id) t on(t.chat_room_id=cr.id)
        where  cr.status=1
        and 1=if(cr.is_del=1,0,1)
        and 1=if(cr.guild_id !=0,1,0)
        order by chatRoomSumAmount desc,onlineUserCount desc, cr.id desc
    </select>-->

    <select id="getGuildGoodChatRoomList" resultType="com.hzy.core.model.vo.app.AppChatRoomListVo">
        select cr.id as chatRoomId,
        cr.start_time as startTime,
        cr.name,
        cr.user_id as createUserId,
        cr.password as password,
        cr.heat,
        cr.greeting_text as greetingText,
        cr.notice_text as noticeText,
        cr.avatar_url as avatarUrl,
        cr.type as chatRoomType,
        cr.third_id as thirdId,
        cr.status as `status`,
        cr.motorcade_type as motorcadeType,
        cr.father_id as fatherId,
        cr.label_json as labelJson,
        cr.background_url as backgroundUrl,
        cr.wheat_serving_type as wheatServingType,
        cr.is_hide_gift_gold as isHideGiftGold,
        cr.is_hot as isHot,
        cr.guild_id as guildId,
        cr.hall_owner_user_id,
        ifnull(t.zx,0) as chatRoomSumAmount,
        (select count(*)
        from app_chat_room_user cru
        where cru.chat_room_id = cr.id
        and cru.is_join = true) as onlineUserCount

        from app_chat_room cr
        LEFT JOIN (select ifnull(sum(bill.gift_gold_total_amount),0) as zx,bill.chat_room_id from user_give_gift bill
        where
        bill.created_time BETWEEN DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') AND LAST_DAY(CURRENT_DATE)
        and 1=if(bill.gift_gold_total_amount >0,1,0)
        group by bill.chat_room_id) t on(t.chat_room_id=cr.id)
        where cr.status=1
        and 1=if(cr.is_del=1,0,1)

        order by chatRoomSumAmount desc,onlineUserCount desc, cr.id desc
    </select>
    <update id="addHeat" parameterType="java.lang.Long">
        update app_chat_room set heat=heat+#{heat}
        where id=#{id}
    </update>

    <update id="subHeat" parameterType="java.lang.Long">
        update app_chat_room set heat=heat-#{heat}
        where id=#{id} and (heat-#{heat})>=500
    </update>

    <update id="subAllHeat">
        update app_chat_room set heat=if((heat-2000)>=500,(heat-2000),500)
    </update>

    <select id="getType" resultType="java.lang.Long" parameterType="java.lang.Long">
        select `type` from app_chat_room where id=#{id}
    </select>


    <select id="isThirdIdExist" resultType="java.lang.Integer" parameterType="java.lang.String">
        select 1 from app_chat_room where is_del=false and third_id=#{thirdId}
        order by id desc
        limit 0,1
    </select>
    <select id="getAppMyChatRoomList" resultType="com.hzy.core.entity.AppChatRoom"
            parameterType="java.lang.Long">
        select * from app_chat_room where user_id=#{userId}
    </select>
    <select id="selectAppChatRoomById" resultType="com.hzy.core.entity.AppChatRoom">
        select * from app_chat_room   where id = #{id} and is_del =false
    </select>


    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            <choose>
                <when test="item.heat != null and item.heat > 0">
                    UPDATE app_chat_room
                    SET heat = #{item.heat}
                    WHERE id = #{item.chatRoomId}
                </when>
                <otherwise>
                    UPDATE app_chat_room
                    SET heat = heat
                    WHERE id = #{item.chatRoomId}
                </otherwise>
            </choose>
        </foreach>
    </update>

    <select id="getHeat" resultType="java.lang.Long" parameterType="java.lang.Long">
        select heat from app_chat_room where id=#{chatRoomId}
    </select>
</mapper>
