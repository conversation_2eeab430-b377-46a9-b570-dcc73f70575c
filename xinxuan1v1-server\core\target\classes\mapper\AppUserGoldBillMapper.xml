<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppUserGoldBillMapper">

    <resultMap type="AppUserGoldBill" id="AppUserGoldBillResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="toUserId" column="to_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="billType" column="bill_type"/>
        <result property="amount" column="amount"/>
        <result property="objectId" column="object_id"/>
        <result property="isDel" column="is_del"/>
        <result property="remarksMsg" column="remarks_msg"/>
        <result property="chatRoomId" column="chat_room_id"/>
        <result property="userContributionValue" column="user_contribution_value"/>
        <result property="toUserCharmValue" column="to_user_charm_value"/>
    </resultMap>

    <sql id="selectAppUserGoldBillVo">
        select id,
        user_id,
        to_user_id,
        create_time,
        update_time,
        create_by,
        update_by,
        bill_type,
        amount,
        object_id,
        is_del,
        remarks_msg,
        chat_room_id,
        user_contribution_value,
        to_user_charm_value
        from app_user_gold_bill
    </sql>

    <insert id="insertAppUserGoldBill" parameterType="AppUserGoldBill" useGeneratedKeys="true" keyProperty="id">
        insert into app_user_gold_bill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="toUserId != null">to_user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="billType != null">bill_type,</if>
            <if test="amount != null">amount,</if>
            <if test="objectId != null">object_id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="remarksMsg != null">remarks_msg,</if>
            <if test="chatRoomId != null">chat_room_id,</if>
            <if test="userContributionValue != null">user_contribution_value,</if>
            <if test="toUserCharmValue != null">to_user_charm_value,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="toUserId != null">#{toUserId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="billType != null">#{billType},</if>
            <if test="amount != null">#{amount},</if>
            <if test="objectId != null">#{objectId},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="remarksMsg != null">#{remarksMsg},</if>
            <if test="chatRoomId != null">#{chatRoomId},</if>
            <if test="userContributionValue != null">#{userContributionValue},</if>
            <if test="toUserCharmValue != null">#{toUserCharmValue},</if>
        </trim>
    </insert>

    <update id="updateAppUserGoldBill" parameterType="AppUserGoldBill">
        update app_user_gold_bill
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="toUserId != null">to_user_id = #{toUserId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="billType != null">bill_type = #{billType},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="objectId != null">object_id = #{objectId},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="remarksMsg != null">remarks_msg = #{remarksMsg},</if>
            <if test="chatRoomId != null">chat_room_id = #{chatRoomId},</if>
            <if test="userContributionValue != null">user_contribution_value = #{userContributionValue},</if>
            <if test="toUserCharmValue != null">to_user_charm_value = #{toUserCharmValue},</if>
        </trim>
        where id = #{id}
    </update>
    <select id="getUserGoldBillList" resultType="com.hzy.core.model.vo.app.AppUserGoldBillVo">
        select b.id as goldBillId,
        b.create_time as createTime,
        b.object_id as objectId,
        b.bill_type as billType,
        b.user_id as userId,
        b.amount,
        b.remarks_msg as remarksMsg,
        acr.name as chatRoomName,
        b.chat_room_id as chatRoomId
        from app_user_gold_bill b
        LEFT join app_chat_room acr on acr.id=b.chat_room_id
        where 1=(if(b.bill_type=21,0,1))
        <if test="queryType!=null">
            and b.bill_type=#{queryType}
        </if>
        <if test="queryUserId!=null">
            and b.user_id=#{queryUserId}
        </if>
        <if test="userId!=null">
            and b.user_id=#{userId}
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(b.create_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(b.create_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        order by b.id desc
    </select>

    <select id="getUserGoldBillTotal" resultType="com.hzy.core.model.vo.app.AppUserGoldBillTotalVo">
        select
        IFNULL(sum(b.amount),0) as amountTotal
        from app_user_gold_bill b
        left join app_user au on b.user_id = au.id
        where 1=(if(b.bill_type=21,0,1))
        <if test="queryType!=null">
            and b.bill_type=#{queryType}
        </if>
        <!--<if test="queryUserId!=null">
            and b.user_id=#{queryUserId}
        </if>-->
        <if test="userId!=null">
            and b.user_id=#{userId}
        </if>
        <if test="recodeCode != null and recodeCode != ''">
            and au.recode_code = #{recodeCode}
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(b.create_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(b.create_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
    </select>

    <select id="getChatRoomGoldSumNum" resultType="java.math.BigDecimal">
        select
        <if test="queryType==2">
            ifnull(sum(bill.user_contribution_value),0)
        </if>
        <if test="queryType==3">
            ifnull(sum(bill.to_user_charm_value),0)
        </if>
        <if test="queryType==4">
            ifnull(sum(bill.user_contribution_value),0)

        </if>
        <if test="queryType==5">
            ifnull(sum(bill.to_user_charm_value),0)
        </if>
        from app_user_gold_bill bill
        where chat_room_id=#{chatRoomId}
        and (bill.user_contribution_value >0 or bill.to_user_charm_value>0)

        <if test="queryType==2">
            and bill.create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
            <if test="queryUserId!=null">
                and bill.user_id=#{queryUserId}
            </if>
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.create_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
            <if test="queryUserId!=null">
                and bill.user_id=#{queryUserId}
            </if>
        </if>
        <if test="queryType==4">
            and bill.create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
            <if test="queryUserId!=null">
                and bill.object_id=#{queryUserId}
            </if>
        </if>
        <if test="queryType==5">
            and DATE_FORMAT(bill.create_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
            <if test="queryUserId!=null">
                and bill.object_id=#{queryUserId}
            </if>
        </if>

    </select>


    <select id="getChatRoomGoldSumNumByTime" resultType="java.math.BigDecimal">
        select
        <if test="queryType==1">
            ifnull(sum(bill.user_contribution_value),0)
        </if>
        <if test="queryType==2">
            ifnull(sum(bill.to_user_charm_value),0)
        </if>
        from app_user_gold_bill bill
        where chat_room_id=#{chatRoomId}
        <if test="queryType==1">
            and bill.user_contribution_value >0
            <if test="queryUserId!=null">
                and bill.user_id=#{queryUserId}
            </if>
        </if>
        <if test="queryType==2">
            and bill.to_user_charm_value >0
            <if test="queryUserId!=null">
                and bill.object_id=#{queryUserId}
            </if>
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(bill.create_time,'%y%m%d%H%i%s') &gt;= date_format(#{queryBeginTime},'%y%m%d%H%i%s')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(bill.create_time,'%y%m%d%H%i%s') &lt;= date_format(#{queryEndTime},'%y%m%d%H%i%s')
        </if>
    </select>

    <select id="getBillSumAmonut" resultType="com.hzy.core.model.vo.app.AppUserGoldBillTotalVo">
        select ifnull(sum(amount),0) as amountTotal where is_del = 0
        <if test="userId != null">
            and user_id = #{userId}
        </if>
        <if test="chatRoomId != null">
            and chat_room_id = #{chatRoomId}
        </if>
        <if test="billType != null">
            and bill_type = #{billType}
        </if>
    </select>

    <select id="getBilllist" resultType="list">

    </select>

    <select id="getCfTopThree" resultType="com.hzy.core.model.vo.app.AppUserGoldBillGetTopThreeVo">
        SELECT user_id,
               nick_name,
               head_portrait,
               SUM(amount) AS total_amount
        FROM app_user_gold_bill ab
                 left join app_user au on au.id = ab.user_id
        WHERE
            DATE (create_time) = CURDATE()
        GROUP BY
            user_id
        ORDER BY
            total_amount ASC
            LIMIT 3;
    </select>

    <select id="getMlTopThree" resultType="com.hzy.core.model.vo.app.AppUserGoldBillGetTopThreeVo">
        SELECT user_id,
               nick_name,
               head_portrait,
               SUM(amount) AS total_amount
        FROM app_user_points_bill ab
                 left join app_user au on au.id = ab.user_id
        WHERE
            DATE (ab.created_time) = CURDATE()
        GROUP BY
            user_id
        ORDER BY
            total_amount DESC
            LIMIT 3;
    </select>
</mapper>