<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppSdPayConfigMapper">

    <resultMap type="AppSdPayConfig" id="AppSdPayConfigResult">
        <result property="id" column="id"/>
        <result property="isDel" column="is_del"/>
        <result property="name" column="name"/>
        <result property="isEnable" column="is_enable"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="mchId" column="mch_id"/>
        <result property="mchKey" column="mch_key"/>
        <result property="publicKeyPath" column="public_key_path"/>
        <result property="privateKeyPath" column="private_key_path"/>
        <result property="wxSubAppId" column="wx_sub_app_id"/>
        <result property="wxGhRriId" column="wx_gh_rri_id"/>
        <result property="wxPathUrl" column="wx_path_url"/>
        <result property="wxMiniProgramType" column="wx_mini_program_type"/>
    </resultMap>

    <resultMap type="com.hzy.core.model.vo.app.AppSdPayConfigVo" id="AppSdPayConfigVoResult">
        <result property="id" column="id"/>
        <result property="isDel" column="is_del"/>
        <result property="name" column="name"/>
        <result property="isEnable" column="is_enable"/>
        <result property="mchId" column="mch_id"/>
        <result property="mchKey" column="mch_key"/>
        <result property="publicKeyPath" column="public_key_path"/>
        <result property="privateKeyPath" column="private_key_path"/>
        <result property="wxSubAppId" column="wx_sub_app_id"/>
        <result property="wxGhRriId" column="wx_gh_rri_id"/>
        <result property="wxPathUrl" column="wx_path_url"/>
        <result property="wxMiniProgramType" column="wx_mini_program_type"/>
    </resultMap>

    <select id="getEnableSdPayConfig" resultMap="AppSdPayConfigVoResult">
        select id, is_del, name, is_enable,mch_id, mch_key,
        public_key_path, private_key_path, wx_sub_app_id, wx_gh_rri_id, wx_path_url, wx_mini_program_type from
        app_sd_pay_config
        where is_del=false and is_enable=true
        order by id desc limit 0,1
    </select>

    <select id="isHaveEnableSdPayConfig" resultType="java.lang.Integer">
        select 1 from
        app_sd_pay_config
        where is_del=false and is_enable=true
        order by id desc limit 0,1
    </select>

    <sql id="selectAppSdPayConfigVo">
        select id, is_del, name, is_enable, create_time, update_time, create_by, update_by, mch_id, mch_key,
        public_key_path, private_key_path, wx_sub_app_id, wx_gh_rri_id, wx_path_url, wx_mini_program_type from
        app_sd_pay_config
    </sql>


    <select id="selectAppSdPayConfigList" parameterType="AppSdPayConfig" resultMap="AppSdPayConfigResult">
        <include refid="selectAppSdPayConfigVo"/>
        where is_del=false
        <if test="isEnable != null ">and is_enable = #{isEnable}</if>
        <if test="mchId != null  and mchId != ''">and mch_id like concat('%', #{mchId}, '%')</if>
        <if test="name != null  and name != ''">and `name` like concat('%', #{name}, '%')</if>
        order by is_enable desc, id desc
    </select>

    <select id="selectAppSdPayConfigById" parameterType="Long" resultMap="AppSdPayConfigResult">
        <include refid="selectAppSdPayConfigVo"/>
        where id = #{id} and is_del=false
    </select>

    <select id="getAppSdPayConfigInfo" parameterType="Long" resultMap="AppSdPayConfigResult">
        <include refid="selectAppSdPayConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppSdPayConfig" parameterType="AppSdPayConfig" useGeneratedKeys="true" keyProperty="id">
        insert into app_sd_pay_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDel != null">is_del,</if>
            <if test="name != null">name,</if>
            <if test="isEnable != null">is_enable,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="mchId != null">mch_id,</if>
            <if test="mchKey != null">mch_key,</if>
            <if test="publicKeyPath != null">public_key_path,</if>
            <if test="privateKeyPath != null">private_key_path,</if>
            <if test="wxSubAppId != null">wx_sub_app_id,</if>
            <if test="wxGhRriId != null">wx_gh_rri_id,</if>
            <if test="wxPathUrl != null">wx_path_url,</if>
            <if test="wxMiniProgramType != null">wx_mini_program_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDel != null">#{isDel},</if>
            <if test="name != null">#{name},</if>
            <if test="isEnable != null">#{isEnable},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="mchId != null">#{mchId},</if>
            <if test="mchKey != null">#{mchKey},</if>
            <if test="publicKeyPath != null">#{publicKeyPath},</if>
            <if test="privateKeyPath != null">#{privateKeyPath},</if>
            <if test="wxSubAppId != null">#{wxSubAppId},</if>
            <if test="wxGhRriId != null">#{wxGhRriId},</if>
            <if test="wxPathUrl != null">#{wxPathUrl},</if>
            <if test="wxMiniProgramType != null">#{wxMiniProgramType},</if>
        </trim>
    </insert>

    <update id="updateAppSdPayConfig" parameterType="AppSdPayConfig">
        update app_sd_pay_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="name != null">name = #{name},</if>
            <if test="isEnable != null">is_enable = #{isEnable},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="mchId != null">mch_id = #{mchId},</if>
            <if test="mchKey != null">mch_key = #{mchKey},</if>
            <if test="publicKeyPath != null">public_key_path = #{publicKeyPath},</if>
            <if test="privateKeyPath != null">private_key_path = #{privateKeyPath},</if>
            <if test="wxSubAppId != null">wx_sub_app_id = #{wxSubAppId},</if>
            <if test="wxGhRriId != null">wx_gh_rri_id = #{wxGhRriId},</if>
            <if test="wxPathUrl != null">wx_path_url = #{wxPathUrl},</if>
            <if test="wxMiniProgramType != null">wx_mini_program_type = #{wxMiniProgramType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppSdPayConfigById">
        update app_sd_pay_config set is_del=true,update_by=#{updateBy},update_time=now() where id = #{id}
    </delete>

    <delete id="deleteAppSdPayConfigByIds" parameterType="String">
        delete from app_sd_pay_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="forbiddenAllSdPayConfig">
        update app_sd_pay_config set is_enable=0,update_by=#{updateBy},update_time=now()
    </update>
</mapper>