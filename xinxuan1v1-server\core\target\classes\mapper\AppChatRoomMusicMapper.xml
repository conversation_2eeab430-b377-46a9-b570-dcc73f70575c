<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppChatRoomMusicMapper">

    <select id="getChatRoomMusicPage" resultType="com.hzy.core.entity.AppChatRoomMusic">
        select acrm.id, acrm.room_id, acrm.music_url, acrm.music_name, acrm.singer, acrm.create_time,
               au.nick_name as create_user_name, su.nick_name as sys_user_name
        from app_chat_room_music acrm
        left join app_user au on acrm.create_user_id = au.id
        left join sys_user su on acrm.sys_user_id = su.user_id
        where acrm.room_id = #{roomId}
        and acrm.is_del = false
        and acrm.check_status = 2
    </select>

    <select id="selectAppChatRoomMusicList" resultType="com.hzy.core.entity.AppChatRoomMusic">
        select acrm.id, acrm.room_id, acrm.music_url, acrm.music_name, acrm.singer, acrm.create_time,
        acrm.check_status, au.nick_name as create_user_name, su.nick_name as sys_user_name,
        acr.name as room_name, acr.third_id as room_code
        from app_chat_room_music acrm
        left join app_user au on acrm.create_user_id = au.id
        left join sys_user su on acrm.sys_user_id = su.user_id
        left join app_chat_room acr on acrm.room_id = acr.id
        <where>
            <if test="query.roomCode != null and query.roomCode != ''">
                and acr.third_id = #{query.roomCode}
            </if>
            <if test="query.singer != null and query.singer != ''">
                and acrm.singer = #{query.singer}
            </if>
            <if test="query.musicName != null and query.musicName != ''">
                and acrm.music_name = #{query.musicName}
            </if>
            <if test="query.roomName != null and query.roomName != ''">
                and acr.name = #{query.roomName}
            </if>
            <if test="query.checkStatus != null">
                and acrm.check_status = #{query.checkStatus}
            </if>
            <if test="query.startTime != null and query.startTime != ''">
                and str_to_date(acrm.create_time, '%Y-%m-%d %H:%i:%s') &gt;= str_to_date(#{query.startTime}, '%Y-%m-%d %H:%i:%s')
            </if>
            <if test="query.endTime != null and query.endTime != ''">
                and str_to_date(acrm.create_time, '%Y-%m-%d %H:%i:%s') &lt;= str_to_date(#{query.endTime}, '%Y-%m-%d %H:%i:%s')
            </if>
            and acrm.is_del = false
        </where>
    </select>

    <select id="getChatRoomMusicCount" resultType="java.lang.Integer">
        select count(id)
        from app_chat_room_music
        where room_id = #{roomId}
        and is_del = false
        and check_status != 3
    </select>

    <update id="deleteChatRoomMusic">
        update app_chat_room_music
        set is_del = true
        where id = #{id}
    </update>
</mapper>