package com.hzy.core.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzy.core.entity.AppConfig;
import com.hzy.core.entity.AppUserCommunicateTelephoneConfig;
import com.hzy.core.entity.SysDictData;
import com.hzy.core.enums.WhetherTypeEnum;
import com.hzy.core.mapper.AppConfigMapper;
import com.hzy.core.mapper.AppCommunicateTelephoneRecordsMapper;
import com.hzy.core.mapper.AppUserCommunicateTelephoneConfigMapper;
import com.hzy.core.model.vo.AjaxResult;
import com.hzy.core.model.vo.app.AppUpdUserCommunicateTelephoneConfigVo;
import com.hzy.core.utils.DateUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 用户通话配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@Service
public class AppUserCommunicateTelephoneConfigService extends ServiceImpl<AppUserCommunicateTelephoneConfigMapper, AppUserCommunicateTelephoneConfig> {
    @Autowired
    private AppUserCommunicateTelephoneConfigMapper appUserCommunicateTelephoneConfigMapper;
    @Resource
    private AppConfigMapper appConfigMapper;
    @Resource
    private AppCommunicateTelephoneRecordsMapper appCommunicateTelephoneRecordsMapper;
    @Resource
    private SysDictTypeService sysDictTypeService;
    @Resource
    private RedissonClient redisson;

    /**
     * 根据用户id获取用户通信电话配置
     *
     * @param id id
     * @return {@link AppUserCommunicateTelephoneConfig }
     */
    public AppUserCommunicateTelephoneConfig getUserCommunicateTelephoneConfigByUserId(Long id) {
        return appUserCommunicateTelephoneConfigMapper.getUserCommunicateTelephoneConfigByUserId(id);
    }

    /**
     * 更新配置
     * <AUTHOR>
     * @date 2025/4/7 下午2:05
     */
    public int update(AppUserCommunicateTelephoneConfig config) {
       return appUserCommunicateTelephoneConfigMapper.updateAppUserCommunicateTelephoneConfig(config);
    }

    /**
     * 新增用户通话配置
     *
     * @param appUserCommunicateTelephoneConfig 用户通话配置
     * @return 结果
     */
    public int insertAppUserCommunicateTelephoneConfig(AppUserCommunicateTelephoneConfig appUserCommunicateTelephoneConfig) {
        appUserCommunicateTelephoneConfig.setCreateTime(DateUtils.getNowDate());
        return appUserCommunicateTelephoneConfigMapper.insertAppUserCommunicateTelephoneConfig(appUserCommunicateTelephoneConfig);
    }

    /**
     * 获取用户通话配置-直接请求会给未配置通话金额的用户设置默认值
     *
     * @param viewUserId 被查看的用户id,不传就是查当前用户
     * @param userId     当前登录用户id
     * @return
     */
    public AjaxResult getUserCommunicateTelephoneConfig(Long viewUserId, Long userId) {
        if (null == viewUserId || viewUserId.intValue() < 1) {
            // 被查看的用户id,为空就用当前用户id
            viewUserId = userId;
        }

        AppUserCommunicateTelephoneConfig communicateTelephoneConfig = this.getUserCommunicateTelephoneConfigByUserId(viewUserId);

        if (communicateTelephoneConfig == null) {
            communicateTelephoneConfig = new AppUserCommunicateTelephoneConfig();
            communicateTelephoneConfig.setUserId(viewUserId);
            communicateTelephoneConfig.setVideoMinutesGold(appConfigMapper.getAppConfig().getVideoMinutesGold());
            communicateTelephoneConfig.setVoiceMinutesGold(appConfigMapper.getAppConfig().getVoiceMinutesGold());
            communicateTelephoneConfig.setSendMsgGoldPrice(appConfigMapper.getAppConfig().getSendMsgGoldPrice());
            this.insertAppUserCommunicateTelephoneConfig(communicateTelephoneConfig);
            communicateTelephoneConfig = this.getUserCommunicateTelephoneConfigByUserId(viewUserId);
        }

        return AjaxResult.success(communicateTelephoneConfig);
    }

    /**
     * 修改用户通话配置 - 核心业务逻辑（共享方法）
     *
     * @param vo     通话配置信息
     * @param userId 用户ID
     * @return 修改结果
     */
    public AjaxResult updUserCommunicateTelephoneConfig(AppUpdUserCommunicateTelephoneConfigVo vo, Long userId) {
        return updUserCommunicateTelephoneConfig(vo, userId, true);
    }

    /**
     * 修改用户通话配置 - 核心业务逻辑（共享方法）
     *
     * @param vo           通话配置信息
     * @param userId       用户ID
     * @param skipValidation 是否跳过价格验证（管理员后台使用）
     * @return 修改结果
     */
    public AjaxResult updUserCommunicateTelephoneConfig(AppUpdUserCommunicateTelephoneConfigVo vo, Long userId, boolean skipValidation) {
        RLock lock = redisson.getLock("app:updUserCommunicateTelephoneConfig:" + userId);
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        
        try {
            lock.lock(60, TimeUnit.SECONDS);
            
            // 参数验证
            if (vo.getIsEnableVideo() == null || WhetherTypeEnum.getEnum(vo.getIsEnableVideo()) == null) {
                return AjaxResult.error("是否启用视频接听参数异常");
            }
            if (vo.getIsEnableVoice() == null || WhetherTypeEnum.getEnum(vo.getIsEnableVoice()) == null) {
                return AjaxResult.error("是否启用语音接听参数异常");
            }
            if (vo.getVoiceMinutesGold() == null || vo.getVoiceMinutesGold().compareTo(BigDecimal.ZERO) < 0) {
                return AjaxResult.error("语音通话每分钟费用不能为空或负数");
            }
            if (vo.getVideoMinutesGold() == null || vo.getVideoMinutesGold().compareTo(BigDecimal.ZERO) < 0) {
                return AjaxResult.error("视频通话每分钟费用不能为空或负数");
            }
            if (vo.getSendMsgGoldPrice() == null || vo.getSendMsgGoldPrice().compareTo(BigDecimal.ZERO) < 0) {
                return AjaxResult.error("发送消息每条价格不能为空或负数");
            }

            // 判断当前用户是否有进行中的通话
            if (appCommunicateTelephoneRecordsMapper.isBusy(userId)) {
                return AjaxResult.error("当前有进行中的通话,无法修改");
            }

            // 获取当前用户通话配置
            AppUserCommunicateTelephoneConfig communicateTelephoneConfig = 
                appUserCommunicateTelephoneConfigMapper.getUserCommunicateTelephoneConfigByUserId(userId);

            if (communicateTelephoneConfig == null) {
                // 如果用户没有配置，使用系统默认配置创建新记录
                AppConfig appConfig = appConfigMapper.getAppConfig();
                communicateTelephoneConfig = new AppUserCommunicateTelephoneConfig();
                communicateTelephoneConfig.setUserId(userId);
                communicateTelephoneConfig.setVideoMinutesGold(appConfig.getVideoMinutesGold());
                communicateTelephoneConfig.setVoiceMinutesGold(appConfig.getVoiceMinutesGold());
                communicateTelephoneConfig.setSendMsgGoldPrice(appConfig.getSendMsgGoldPrice());
                appUserCommunicateTelephoneConfigMapper.insertAppUserCommunicateTelephoneConfig(communicateTelephoneConfig);
                
                // 重新获取配置
                communicateTelephoneConfig = appUserCommunicateTelephoneConfigMapper.getUserCommunicateTelephoneConfigByUserId(userId);
                if (communicateTelephoneConfig == null) {
                    return AjaxResult.error("创建用户通话配置失败");
                }
            }

            // 价格验证（管理员后台可跳过）
            if (!skipValidation) {
                // 语音价格验证
                Integer voiceTotalTime = communicateTelephoneConfig.getVoiceTotalTime();
                List<SysDictData> voicePriceList = sysDictTypeService.selectDictDataByType("app_voice_gold");
                if (!CollectionUtils.isEmpty(voicePriceList)) {
                    BigDecimal maxVoiceGold = voicePriceList.stream()
                        .filter(sysDictData -> voiceTotalTime >= Integer.parseInt(sysDictData.getRemark()) * 60)
                        .map(sysDictData -> new BigDecimal(sysDictData.getDictValue()))
                        .max(BigDecimal::compareTo)
                        .orElse(BigDecimal.ZERO);

                    if (vo.getVoiceMinutesGold().compareTo(maxVoiceGold) > 0) {
                        return AjaxResult.error("语音价格不满足消费时长");
                    }
                }

                // 视频价格验证
                List<SysDictData> videoPriceList = sysDictTypeService.selectDictDataByType("app_video_gold");
                if (!CollectionUtils.isEmpty(videoPriceList)) {
                    Integer videoTotalTime = communicateTelephoneConfig.getVideoTotalTime();
                    BigDecimal maxVideoGold = videoPriceList.stream()
                        .filter(sysDictData -> videoTotalTime >= Integer.parseInt(sysDictData.getRemark()) * 60)
                        .map(sysDictData -> new BigDecimal(sysDictData.getDictValue()))
                        .max(BigDecimal::compareTo)
                        .orElse(BigDecimal.ZERO);

                    if (vo.getVideoMinutesGold().compareTo(maxVideoGold) > 0) {
                        return AjaxResult.error("视频价格不满足消费时长");
                    }
                }
            }

            // 更新配置
            AppUserCommunicateTelephoneConfig communicateTelephoneConfigUpd = new AppUserCommunicateTelephoneConfig();
            communicateTelephoneConfigUpd.setId(communicateTelephoneConfig.getId());
            communicateTelephoneConfigUpd.setIsEnableVoice(vo.getIsEnableVoice());
            communicateTelephoneConfigUpd.setVoiceMinutesGold(vo.getVoiceMinutesGold());
            communicateTelephoneConfigUpd.setIsEnableVideo(vo.getIsEnableVideo());
            communicateTelephoneConfigUpd.setVideoMinutesGold(vo.getVideoMinutesGold());
            communicateTelephoneConfigUpd.setSendMsgGoldPrice(vo.getSendMsgGoldPrice());
            communicateTelephoneConfigUpd.setUpdateTime(new Date());

            int result = appUserCommunicateTelephoneConfigMapper.updateAppUserCommunicateTelephoneConfig(communicateTelephoneConfigUpd);
            if (result > 0) {
                return AjaxResult.success("修改成功");
            } else {
                return AjaxResult.error("修改失败");
            }
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

}
