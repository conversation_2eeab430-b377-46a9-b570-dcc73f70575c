package com.hzy.core.utils;

// 移除Jackson依赖，使用简单JSON解析
import java.io.*;
import java.math.BigDecimal;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 37pay支付工具类
 * 提供完整的37pay支付接口封装
 * 
 * 使用示例：
 * 
 * 1. 创建支付配置
 * PayConfig config = new PayConfig("YOUR_PID", "YOUR_KEY", "notify_url", "return_url");
 * 
 * 2. 创建支付请求
 * PayRequest request = new PayRequest("ORDER123", new BigDecimal("100.00"), "商品名称", PayType.ALIPAY);
 * 
 * 3. 发起支付
 * String payUrl = ThirtySevenPayUtils.createPayUrl(config, request);
 * PayResponse response = ThirtySevenPayUtils.apiPay(config, request);
 * 
 * 4. 查询订单
 * QueryRequest queryRequest = new QueryRequest("ORDER123");
 * QueryResponse queryResponse = ThirtySevenPayUtils.queryOrder(config, queryRequest);
 * 
 * 5. 验证回调
 * boolean isValid = ThirtySevenPayUtils.verifyNotify(callbackParams, config.getKey());
 * 
 * <AUTHOR>
 */
public class ThirtySevenPayUtils {
    
    /**
     * 37pay配置类
     */
    public static class PayConfig {
        private String baseUrl = "https://37py.baifeng88.com";
        private String pid; // 商户ID
        private String key; // 商户密钥
        private String notifyUrl; // 异步通知地址
        private String returnUrl; // 同步跳转地址
        
        public PayConfig(String pid, String key) {
            this.pid = pid;
            this.key = key;
        }
        
        public PayConfig(String pid, String key, String notifyUrl, String returnUrl) {
            this.pid = pid;
            this.key = key;
            this.notifyUrl = notifyUrl;
            this.returnUrl = returnUrl;
        }
        
        // Getters and Setters
        public String getBaseUrl() { return baseUrl; }
        public void setBaseUrl(String baseUrl) { this.baseUrl = baseUrl; }
        public String getPid() { return pid; }
        public void setPid(String pid) { this.pid = pid; }
        public String getKey() { return key; }
        public void setKey(String key) { this.key = key; }
        public String getNotifyUrl() { return notifyUrl; }
        public void setNotifyUrl(String notifyUrl) { this.notifyUrl = notifyUrl; }
        public String getReturnUrl() { return returnUrl; }
        public void setReturnUrl(String returnUrl) { this.returnUrl = returnUrl; }
    }
    
    /**
     * 支付类型枚举
     */
    public enum PayType {
        ALIPAY("alipay", "支付宝"),
        WXPAY("wxpay", "微信支付"),
        QQPAY("qqpay", "QQ钱包");
        
        private final String code;
        private final String name;
        
        PayType(String code, String name) {
            this.code = code;
            this.name = name;
        }
        
        public String getCode() { return code; }
        public String getName() { return name; }
    }
    
    /**
     * 支付请求参数
     */
    public static class PayRequest {
        private String outTradeNo; // 商户订单号
        private BigDecimal money; // 支付金额
        private String name; // 商品名称
        private PayType type; // 支付类型
        private String siteName; // 网站名称
        private String clientIp; // 客户端IP
        
        public PayRequest(String outTradeNo, BigDecimal money, String name, PayType type) {
            this.outTradeNo = outTradeNo;
            this.money = money;
            this.name = name;
            this.type = type;
        }
        
        // Getters and Setters
        public String getOutTradeNo() { return outTradeNo; }
        public void setOutTradeNo(String outTradeNo) { this.outTradeNo = outTradeNo; }
        public BigDecimal getMoney() { return money; }
        public void setMoney(BigDecimal money) { this.money = money; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public PayType getType() { return type; }
        public void setType(PayType type) { this.type = type; }
        public String getSiteName() { return siteName; }
        public void setSiteName(String siteName) { this.siteName = siteName; }
        public String getClientIp() { return clientIp; }
        public void setClientIp(String clientIp) { this.clientIp = clientIp; }
    }
    
    /**
     * 支付响应结果
     */
    public static class PayResponse {
        private boolean success;
        private String message;
        private String payUrl; // 支付URL
        private String qrCode; // 二维码内容
        private String tradeNo; // 平台订单号
        private Map<String, Object> data; // 原始响应数据
        
        public PayResponse(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public String getPayUrl() { return payUrl; }
        public void setPayUrl(String payUrl) { this.payUrl = payUrl; }
        public String getQrCode() { return qrCode; }
        public void setQrCode(String qrCode) { this.qrCode = qrCode; }
        public String getTradeNo() { return tradeNo; }
        public void setTradeNo(String tradeNo) { this.tradeNo = tradeNo; }
        public Map<String, Object> getData() { return data; }
        public void setData(Map<String, Object> data) { this.data = data; }
    }
    
    /**
     * 查询订单请求参数
     */
    public static class QueryRequest {
        private String outTradeNo; // 商户订单号
        private String tradeNo; // 平台订单号
        
        public QueryRequest(String outTradeNo) {
            this.outTradeNo = outTradeNo;
        }
        
        public QueryRequest(String outTradeNo, String tradeNo) {
            this.outTradeNo = outTradeNo;
            this.tradeNo = tradeNo;
        }
        
        // Getters and Setters
        public String getOutTradeNo() { return outTradeNo; }
        public void setOutTradeNo(String outTradeNo) { this.outTradeNo = outTradeNo; }
        public String getTradeNo() { return tradeNo; }
        public void setTradeNo(String tradeNo) { this.tradeNo = tradeNo; }
    }
    
    /**
     * 订单查询响应
     */
    public static class QueryResponse {
        private boolean success;
        private String message;
        private String status; // 订单状态
        private String outTradeNo; // 商户订单号
        private String tradeNo; // 平台订单号
        private BigDecimal money; // 订单金额
        private String payTime; // 支付时间
        private Map<String, Object> data; // 原始响应数据
        
        public QueryResponse(boolean success, String message) {
            this.success = success;
            this.message = message;
        }
        
        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public String getOutTradeNo() { return outTradeNo; }
        public void setOutTradeNo(String outTradeNo) { this.outTradeNo = outTradeNo; }
        public String getTradeNo() { return tradeNo; }
        public void setTradeNo(String tradeNo) { this.tradeNo = tradeNo; }
        public BigDecimal getMoney() { return money; }
        public void setMoney(BigDecimal money) { this.money = money; }
        public String getPayTime() { return payTime; }
        public void setPayTime(String payTime) { this.payTime = payTime; }
        public Map<String, Object> getData() { return data; }
        public void setData(Map<String, Object> data) { this.data = data; }
    }
    
    /**
     * 页面跳转支付 - 生成支付表单HTML
     * 
     * @param config 支付配置
     * @param request 支付请求参数
     * @return 支付表单HTML
     */
    public static String createPayForm(PayConfig config, PayRequest request) {
        try {
            Map<String, String> params = buildPayParams(config, request);
            String signature = generateSignature(params, config.getKey());
            params.put("sign", signature);
            
            StringBuilder form = new StringBuilder();
            form.append("<form id='payForm' action='").append(config.getBaseUrl()).append("/submit.php' method='post'>");
            
            for (Map.Entry<String, String> entry : params.entrySet()) {
                form.append("<input type='hidden' name='").append(entry.getKey())
                    .append("' value='").append(entry.getValue()).append("'/>");
            }
            
            form.append("<input type='submit' value='立即支付' style='display:none;'/>");
            form.append("</form>");
            form.append("<script>document.getElementById('payForm').submit();</script>");
            
            return form.toString();
        } catch (Exception e) {
            System.err.println("生成支付表单失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 生成支付URL
     * 
     * @param config 支付配置
     * @param request 支付请求参数
     * @return 支付URL
     */
    public static String createPayUrl(PayConfig config, PayRequest request) {
        try {
            Map<String, String> params = buildPayParams(config, request);
            String signature = generateSignature(params, config.getKey());
            params.put("sign", signature);
            
            StringBuilder url = new StringBuilder();
            url.append(config.getBaseUrl()).append("/submit.php?");
            
            for (Map.Entry<String, String> entry : params.entrySet()) {
                url.append(entry.getKey()).append("=")
                   .append(URLEncoder.encode(entry.getValue(), "UTF-8")).append("&");
            }
            
            return url.substring(0, url.length() - 1);
        } catch (Exception e) {
            System.err.println("生成支付URL失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * API接口支付 - 获取支付二维码或跳转链接
     * 
     * @param config 支付配置
     * @param request 支付请求参数
     * @return 支付响应
     */
    public static PayResponse apiPay(PayConfig config, PayRequest request) {
        try {
            Map<String, String> params = buildPayParams(config, request);
            String signature = generateSignature(params, config.getKey());
            params.put("sign", signature);
            
            String postData = buildPostData(params);
            String response = sendPost(config.getBaseUrl() + "/mapi.php", postData);
            
            System.out.println("37pay API响应: " + response);
            
            return parsePayResponse(response);
        } catch (Exception e) {
            System.err.println("API支付请求失败: " + e.getMessage());
            return new PayResponse(false, "支付请求失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询订单状态
     * 
     * @param config 支付配置
     * @param request 查询请求参数
     * @return 查询响应
     */
    public static QueryResponse queryOrder(PayConfig config, QueryRequest request) {
        try {
            Map<String, String> params = new TreeMap<>();
            params.put("act", "order");
            params.put("pid", config.getPid());
            
            if (request.getOutTradeNo() != null && !request.getOutTradeNo().isEmpty()) {
                params.put("out_trade_no", request.getOutTradeNo());
            }
            if (request.getTradeNo() != null && !request.getTradeNo().isEmpty()) {
                params.put("trade_no", request.getTradeNo());
            }
            
            String signature = generateSignature(params, config.getKey());
            params.put("sign", signature);
            
            String postData = buildPostData(params);
            String response = sendPost(config.getBaseUrl() + "/api.php", postData);
            
            System.out.println("37pay查询响应: " + response);
            
            return parseQueryResponse(response);
        } catch (Exception e) {
            System.err.println("查询订单失败: " + e.getMessage());
            return new QueryResponse(false, "查询订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证回调签名
     * 
     * @param params 回调参数
     * @param key 商户密钥
     * @return 验证结果
     */
    public static boolean verifyNotify(Map<String, String> params, String key) {
        try {
            String sign = params.get("sign");
            if (sign == null || sign.isEmpty()) {
                return false;
            }
            
            Map<String, String> sortedParams = new TreeMap<>(params);
            sortedParams.remove("sign");
            
            String expectedSign = generateSignature(sortedParams, key);
            return sign.equals(expectedSign);
        } catch (Exception e) {
            System.err.println("验证回调签名失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 构建支付参数
     */
    private static Map<String, String> buildPayParams(PayConfig config, PayRequest request) {
        Map<String, String> params = new TreeMap<>();
        params.put("pid", config.getPid());
        params.put("type", request.getType().getCode());
        params.put("out_trade_no", request.getOutTradeNo());
        params.put("notify_url", config.getNotifyUrl());
        params.put("return_url", config.getReturnUrl());
        params.put("name", request.getName());
        params.put("money", request.getMoney().toString());
        
        if (request.getSiteName() != null && !request.getSiteName().isEmpty()) {
            params.put("sitename", request.getSiteName());
        }
        if (request.getClientIp() != null && !request.getClientIp().isEmpty()) {
            params.put("clientip", request.getClientIp());
        }
        
        return params;
    }
    
    /**
     * 生成签名
     */
    private static String generateSignature(Map<String, String> params, String key) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (entry.getValue() != null && !entry.getValue().isEmpty()) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
        }
        sb.append(key);
        
        String signStr = sb.toString();
        System.out.println("签名原文: " + signStr);
        
        return md5Hash(signStr);
    }
    
    /**
     * 构建POST数据
     */
    private static String buildPostData(Map<String, String> params) throws Exception {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(entry.getKey()).append("=").append(URLEncoder.encode(entry.getValue(), "UTF-8"));
        }
        return sb.toString();
    }
    
    /**
     * 解析支付响应
     */
    private static PayResponse parsePayResponse(String response) {
        try {
            Map<String, Object> data = parseJsonToMap(response);
            
            if (data == null) {
                return new PayResponse(false, "响应解析失败");
            }
            
            Integer code = parseIntegerFromMap(data, "code");
            String msg = (String) data.get("msg");
            
            PayResponse payResponse = new PayResponse(code != null && code == 1, msg);
            payResponse.setData(data);
            
            if (payResponse.isSuccess()) {
                payResponse.setPayUrl((String) data.get("payurl"));
                payResponse.setQrCode((String) data.get("qrcode"));
                payResponse.setTradeNo((String) data.get("trade_no"));
            }
            
            return payResponse;
        } catch (Exception e) {
            System.err.println("解析支付响应失败: " + e.getMessage());
            return new PayResponse(false, "响应解析失败: " + e.getMessage());
        }
    }
    
    /**
     * 解析查询响应
     */
    private static QueryResponse parseQueryResponse(String response) {
        try {
            Map<String, Object> data = parseJsonToMap(response);
            
            if (data == null) {
                return new QueryResponse(false, "响应解析失败");
            }
            
            Integer code = parseIntegerFromMap(data, "code");
            String msg = (String) data.get("msg");
            
            QueryResponse queryResponse = new QueryResponse(code != null && code == 1, msg);
            queryResponse.setData(data);
            
            if (queryResponse.isSuccess()) {
                queryResponse.setStatus((String) data.get("status"));
                queryResponse.setOutTradeNo((String) data.get("out_trade_no"));
                queryResponse.setTradeNo((String) data.get("trade_no"));
                
                Object moneyObj = data.get("money");
                if (moneyObj != null) {
                    queryResponse.setMoney(new BigDecimal(moneyObj.toString()));
                }
                
                queryResponse.setPayTime((String) data.get("pay_time"));
            }
            
            return queryResponse;
        } catch (Exception e) {
            System.err.println("解析查询响应失败: " + e.getMessage());
            return new QueryResponse(false, "响应解析失败: " + e.getMessage());
        }
    }
    
    /**
     * MD5加密
     */
    private static String md5Hash(String input) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] bytes = md5.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            System.err.println("MD5加密失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * HTTP POST请求
     */
    private static String sendPost(String url, String params) throws Exception {
        URL realUrl = new URL(url);
        URLConnection conn = realUrl.openConnection();
        conn.setRequestProperty("accept", "*/*");
        conn.setRequestProperty("connection", "Keep-Alive");
        conn.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
        conn.setRequestProperty("Accept-Charset", "utf-8");
        conn.setRequestProperty("contentType", "utf-8");
        conn.setDoOutput(true);
        conn.setDoInput(true);
        
        try (PrintWriter out = new PrintWriter(conn.getOutputStream())) {
            out.print(params);
            out.flush();
        }
        
        StringBuilder result = new StringBuilder();
        try (BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
        }
        
        return result.toString();
    }
    
    /**
     * 生成商户订单号
     */
    public static String generateOutTradeNo() {
        return "PAY" + System.currentTimeMillis() + (int)(Math.random() * 1000);
    }
    
    /**
     * 获取当前时间字符串
     */
    public static String getCurrentTime() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * 简单JSON解析为Map
     */
    private static Map<String, Object> parseJsonToMap(String json) {
        Map<String, Object> map = new HashMap<>();
        try {
            if (json == null || json.trim().isEmpty()) {
                return map;
            }
            
            // 移除前后空白和大括号
            String content = json.trim();
            if (content.startsWith("{") && content.endsWith("}")) {
                content = content.substring(1, content.length() - 1);
            }
            
            // 简单解析key:value对
            String[] pairs = content.split(",");
            for (String pair : pairs) {
                String[] keyValue = pair.split(":", 2);
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim().replaceAll("\"", "");
                    String value = keyValue[1].trim().replaceAll("\"", "");
                    
                    // 尝试解析为数字
                    try {
                        if (value.matches("^-?\\d+$")) {
                            map.put(key, Integer.parseInt(value));
                        } else if (value.matches("^-?\\d+\\.\\d+$")) {
                            map.put(key, Double.parseDouble(value));
                        } else {
                            map.put(key, value);
                        }
                    } catch (NumberFormatException e) {
                        map.put(key, value);
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("JSON解析失败: " + e.getMessage());
        }
        return map;
    }
    
    /**
     * 从Map中解析Integer
     */
    private static Integer parseIntegerFromMap(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
}