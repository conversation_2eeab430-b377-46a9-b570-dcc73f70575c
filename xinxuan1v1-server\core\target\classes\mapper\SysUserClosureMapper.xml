<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.SysUserClosureMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hzy.core.entity.SysUserClosure">
        <result column="ancestor" property="ancestor"/>
        <result column="descendant" property="descendant"/>
        <result column="depth" property="depth"/>
    </resultMap>

    <insert id="insertUserClosure">
        INSERT INTO sys_user_closure (ancestor, descendant, depth)
        SELECT c.ancestor, #{newUserId}, c.depth + 1
        FROM sys_user_closure c
        WHERE c.descendant = #{parentUserId}
        UNION ALL
        SELECT #{newUserId}, #{newUserId}, 0
    </insert>


    <select id="selectAllAncestors" resultType="java.lang.Long">
        SELECT ancestor
        FROM sys_user_closure
        WHERE descendant = #{userId}
          AND depth &gt; 0
        ORDER BY depth ASC
    </select>

    <select id="selectAllDescendants" resultType="java.lang.Long">
        SELECT descendant
        FROM sys_user_closure
        WHERE ancestor = #{userId}
          AND depth &gt; 0
        ORDER BY depth ASC
    </select>

    <!-- isLookingUser --> 

    <select id="isLookingUser" resultType="java.lang.Boolean">
        SELECT COUNT(*)
        FROM sys_user_closure
        WHERE ancestor = #{userId}
          AND descendant = #{toUserId}
    </select>
    <select id="selectAllDescendantsGuildId" resultType="java.lang.Long">
        SELECT id 
        FROM app_guild 
        WHERE is_del = false
        AND sys_user_id IN (
            SELECT descendant
            FROM sys_user_closure
            WHERE ancestor = #{sysUserId}
              AND depth &gt;= 0
        )
    </select>

    <!-- selectAllAncestorsByLevel --> 

    <select id="selectAllAncestorsByLevel" resultType="java.lang.Long">
        SELECT ancestor
        FROM sys_user_closure
        WHERE descendant = #{userId}
          AND depth = #{level}
    </select>
    <select id="selectAncestorsName" resultType="java.lang.String">
        SELECT su.nick_name
        FROM sys_user_closure sc
        left join sys_user su on su.user_id = sc.ancestor
        WHERE descendant = #{userId}
          AND depth = 1
    </select>
</mapper>
