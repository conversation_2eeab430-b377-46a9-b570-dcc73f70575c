<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppCommunityMapper">

    <resultMap type="AppCommunity" id="AppCommunityResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="isDel" column="is_del"/>
        <result property="content" column="content"/>
        <result property="updateBy" column="update_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="pictureUrl" column="picture_url"/>
        <result property="pageView" column="page_view"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="area" column="area"/>
        <result property="isSelectPosition" column="is_select_position"/>
        <result property="videoUrl" column="video_url"/>
        <result property="isVideo" column="is_video"/>
        <result property="voiceSignature" column="voice_signature"/>
    </resultMap>

    <sql id="selectAppCommunityVo">
        select id,
        user_id,
        is_del,
        content,
        update_by,
        create_time,
        update_time,
        picture_url,
        page_view,
        longitude,
        latitude,
        province,
        city,
        area,
        is_select_position,
        video_url,
        is_video,
        zone_id,
        topic,
        only_me,
        voice_signature
        from app_community
    </sql>


    <select id="selectAppCommunityList" parameterType="AppCommunity" resultMap="AppCommunityResult">
        <include refid="selectAppCommunityVo"/>
        where is_del=false
        <if test="userId != null ">and user_id = #{userId}</if>
        <if test="content != null  and content != ''">and content = #{content}</if>
        <if test="pictureUrl != null  and pictureUrl != ''">and picture_url = #{pictureUrl}</if>
        <if test="pageView != null ">and page_view = #{pageView}</if>
        <if test="longitude != null  and longitude != ''">and longitude = #{longitude}</if>
        <if test="latitude != null  and latitude != ''">and latitude = #{latitude}</if>
        <if test="province != null  and province != ''">and province = #{province}</if>
        <if test="city != null  and city != ''">and city = #{city}</if>
        <if test="area != null  and area != ''">and area = #{area}</if>
        <if test="isSelectPosition != null ">and is_select_position = #{isSelectPosition}</if>
        <if test="isVideo != null ">and is_video = #{isVideo}</if>
        <if test="zoneId != null ">and zone_id = #{zoneId}</if>
        <if test="topic != null ">and topic = #{topic}</if>
        <if test="voiceSignature != null  and voiceSignature != ''">and voice_signature = #{voiceSignature}</if>
    </select>

    <select id="selectAppCommunityById" parameterType="Long" resultMap="AppCommunityResult">
        <include refid="selectAppCommunityVo"/>
        where id = #{id} and is_del=false
    </select>

    <insert id="insertAppCommunity" parameterType="AppCommunity" useGeneratedKeys="true" keyProperty="id">
        insert into app_community
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="content != null">content,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="pictureUrl != null">picture_url,</if>
            <if test="pageView != null">page_view,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="area != null">area,</if>
            <if test="isSelectPosition != null">is_select_position,</if>
            <if test="videoUrl != null">video_url,</if>
            <if test="isVideo != null">is_video,</if>
            <if test="voiceSignature != null">voice_signature,</if>
            <if test="zoneId != null">zone_id,</if>
            <if test="topic != null">topic,</if>
            <if test="onlyMe != null">only_me,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="content != null">#{content},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="pictureUrl != null">#{pictureUrl},</if>
            <if test="pageView != null">#{pageView},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="area != null">#{area},</if>
            <if test="isSelectPosition != null">#{isSelectPosition},</if>
            <if test="videoUrl != null">#{videoUrl},</if>
            <if test="isVideo != null">#{isVideo},</if>
            <if test="voiceSignature != null">#{voiceSignature},</if>
            <if test="zoneId != null">#{zoneId},</if>
            <if test="topic != null">#{topic},</if>
            <if test="onlyMe != null">#{onlyMe},</if>
        </trim>
    </insert>

    <update id="updateAppCommunity" parameterType="AppCommunity">
        update app_community
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="content != null">content = #{content},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="pictureUrl != null">picture_url = #{pictureUrl},</if>
            <if test="pageView != null">page_view = #{pageView},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="area != null">area = #{area},</if>
            <if test="isSelectPosition != null">is_select_position = #{isSelectPosition},</if>
            <if test="videoUrl != null">video_url = #{videoUrl},</if>
            <if test="isVideo != null">is_video = #{isVideo},</if>
            <if test="zoneId != null">zone_id = #{zoneId},</if>
            <if test="topic != null">topic = #{topic},</if>
            <if test="onlyMe != null">only_me = #{onlyMe},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppCommunityById" parameterType="Long">
        delete
        from app_community
        where id = #{id}
    </delete>
    <update id="deleteAppCommunityByIds" parameterType="java.util.List">
        update app_community AS ac
        set ac.is_del=1
        where id in
        <foreach item="ids" collection="list" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </update>


    <select id="userIsGiveLike" resultType="java.lang.Boolean" parameterType="java.lang.Long">
        select if((count(*)) > 0, 1, 0)
        from app_community_give_like cgl
        where cgl.community_id = #{postId}
        and cgl.user_id = #{userId}
    </select>

    <select id="getPostGiveLikeSumCount" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select count(*)
        from app_community_give_like cgl
        where cgl.community_id = #{postId}
    </select>

    <select id="getCommunityPostList" resultType="com.hzy.core.model.vo.app.AppCommunityPostVo">
        select c.id as postId,
        c.content,
        c.create_time as createTime,
        c.picture_url as pictureUrl,
        u.is_online as isOnline,
        c.voice_signature as voiceSignature,
        if(u.sex=-1,null,u.sex) as userSex,
        ( select count(*) from app_community_comment cm where cm.is_del=false and cm.community_id=c.id) as
        commentSumCount,
        (select count(*) from app_community_give_like cgl where cgl.community_id=c.id) as giveLikeSumCount,
        <if test="null!=userId">
            (select if((count(*))>0,1,0) from app_community_give_like cgl where cgl.community_id=c.id and
            cgl.user_id=#{userId}) as isGiveLike,
            (if(c.user_id=#{userId},1,0)) as isPresentUser,
        </if>
        <if test="null==userId">
            0 as isGiveLike,
            0 as isPresentUser,
        </if>
        u.id as userId,
        u.is_real_person_auth as isRealPersonAuth,
        u.is_real_name_auth as isRealNameAuth,
        if(u.phone!=''and u.phone is not null,1,0) as isPhoneAuth,

        u.nick_name as userNickName,
        u.head_portrait as userHeadPortrait,
        if(u.age=-1,null,u.age) as userAge,
        u.longitude as userLongitude,
        u.latitude as userLatitude,
        u.province as userProvince,
        u.city as userCity,
        u.area as userArea,
        c.longitude as longitude,
        c.latitude as latitude,
        c.province as province,
        c.city as city,
        c.area as area,
        c.is_select_position as isSelectPosition,
        c.page_view as pageView,
        u.phone as userPhone,
        c.video_url as videoUrl,
        c.is_video as isVideo,
        u.location as userLocation,
        c.topic,
        u.created_time as userCreatedTime
        from
        app_community c
        INNER JOIN app_user u on(c.user_id=u.id)
        where c.is_del = false
        <if test="null!=zoneId">
            and c.zone_id = #{zoneId}
        </if>
        <if test="null!=topic and topic != '' ">
            and c.topic like concat('%', #{topic}, '%')
        </if>
        <if test="null!=queryUserId">
            and u.id = #{queryUserId}
        </if>
        <if test="null!=keyword and keyword!=''">
            and c.content like concat('%', #{keyword}, '%')
        </if>
        <if test="null!=userNickName and userNickName!=''">
            and u.nick_name like concat('%', #{userNickName}, '%')
        </if>
        <if test="null!=userPhone and userPhone!=''">
            and u.phone like concat('%', #{userPhone}, '%')
        </if>
        <if test="null != userId">
            and (c.user_id = #{userId} or c.only_me = false)
        </if>
        order by c.id desc
    </select>


    <select id="getFollowUserPostList" resultType="com.hzy.core.model.vo.app.AppCommunityPostVo"
            parameterType="java.lang.Long">
        select c.id as postId,
        c.content,
        c.create_time as createTime,
        c.picture_url as pictureUrl,
        if(u.sex = -1, null, u.sex) as userSex,
        (select count(*) from app_community_comment cm where cm.is_del = false and cm.community_id = c.id) as
        commentSumCount,
        (select count(*)
        from app_community_give_like cgl
        where cgl.community_id = c.id) as giveLikeSumCount,
        (select if((count(*)) > 0, 1, 0)
        from app_community_give_like cgl
        where cgl.community_id = c.id
        and cgl.user_id = #{userId}) as isGiveLike,
        (if(c.user_id = #{userId}, 1, 0)) as isPresentUser,
        u.id as userId,
        u.nick_name as userNickName,
        u.head_portrait as userHeadPortrait,
        if(u.age = -1, null, u.age) as userAge,
        u.is_online as isOnline,
        u.longitude as userLongitude,
        u.latitude as userLatitude,
        u.province as userProvince,
        u.city as userCity,
        u.area as userArea,
        c.longitude as longitude,
        c.latitude as latitude,
        c.province as province,
        c.city as city,
        c.area as area,
        c.is_select_position as isSelectPosition,
        c.page_view as pageView,
        c.video_url as videoUrl,
        c.is_video as isVideo,
        u.is_real_person_auth as isRealPersonAuth,
        u.is_real_name_auth as isRealNameAuth,
        if(u.phone!=''and u.phone is not null, 1, 0) as isPhoneAuth,
        c.voice_signature as voiceSignature,
        u.location as userLocation
        from
        app_community c
        LEFT JOIN app_user u on(c.user_id=u.id)
        where c.is_del = false
        and c.only_me = false
        and u.id in (select f.be_user_id from app_user_follow f where f.user_id = #{userId})
        order by c.id desc
    </select>


    <select id="getUserReleasePostList" resultType="com.hzy.core.model.vo.app.AppCommunityPostVo"
            parameterType="java.lang.Long">
        select c.id as postId,
        c.content,
        c.create_time as createTime,
        c.picture_url as pictureUrl,
        if(u.sex = -1, null, u.sex) as userSex,
        (select count(*) from app_community_comment cm where cm.is_del = false and cm.community_id = c.id) as
        commentSumCount,
        (select count(*)
        from app_community_give_like cgl
        where cgl.community_id = c.id) as giveLikeSumCount,
        (select if((count(*)) > 0, 1, 0)
        from app_community_give_like cgl
        where cgl.community_id = c.id
        and cgl.user_id = #{userId}) as isGiveLike,
        (if(c.user_id = #{userId}, 1, 0)) as isPresentUser,
        u.id as userId,
        u.is_online as isOnline,
        u.nick_name as userNickName,
        u.head_portrait as userHeadPortrait,
        if(u.age = -1, null, u.age) as userAge,
        u.longitude as userLongitude,
        u.latitude as userLatitude,
        u.province as userProvince,
        u.city as userCity,
        u.area as userArea,
        c.longitude as longitude,
        c.latitude as latitude,
        c.province as province,
        c.video_url as videoUrl,
        c.is_video as isVideo,
        c.city as city,
        c.area as area,
        c.is_select_position as isSelectPosition,
        c.page_view as pageView,
        u.is_real_person_auth as isRealPersonAuth,
        u.is_real_name_auth as isRealNameAuth,
        if(u.phone!=''and u.phone is not null, 1, 0) as isPhoneAuth,
        c.voice_signature as voiceSignature,
        u.location as userLocation
        from
        app_community c
        LEFT JOIN app_user u on(c.user_id=u.id)
        where c.is_del = false
        and u.id = #{viewUserId}
        <if test="viewUserId!=userId">
            and c.only_me = false
        </if>
        order by c.id desc
    </select>


    <select id="getCommunityPostDetails" resultType="com.hzy.core.model.vo.app.AppCommunityPostVo"
            parameterType="java.lang.Long">
        select c.id as postId,
        c.content,
        c.create_time as createTime,
        c.picture_url as pictureUrl,
        if(u.sex=-1,null,u.sex) as userSex,
        ( select count(*) from app_community_comment cm where cm.is_del=false and cm.community_id=c.id) as
        commentSumCount,
        (select count(*) from app_community_give_like cgl where cgl.community_id=c.id) as giveLikeSumCount,
        <if test="null!=userId">
            (select if((count(*))>0,1,0) from app_community_give_like cgl where cgl.community_id=c.id and
            cgl.user_id=#{userId}) as isGiveLike,
            (if(c.user_id=#{userId},1,0)) as isPresentUser,
        </if>
        <if test="null==userId">
            0 as isGiveLike,
            0 as isPresentUser,
        </if>
        u.is_online as isOnline,
        u.id as userId,
        u.is_real_person_auth as isRealPersonAuth,
        u.is_real_name_auth as isRealNameAuth,
        c.video_url as videoUrl,
        c.is_video as isVideo,
        if(u.phone!=''and u.phone is not null,1,0) as isPhoneAuth,

        u.nick_name as userNickName,
        u.head_portrait as userHeadPortrait,
        if(u.age=-1,null,u.age) as userAge,
        u.longitude as userLongitude,
        u.latitude as userLatitude,
        u.province as userProvince,
        u.city as userCity,
        u.area as userArea,
        c.longitude as longitude,
        c.latitude as latitude,
        c.province as province,
        c.city as city,
        c.area as area,
        c.is_select_position as isSelectPosition,
        c.page_view as pageView,
        u.location as userLocation
        from
        app_community c
        LEFT JOIN app_user u on(c.user_id=u.id)
        where c.is_del = false
        and c.id = #{postId}

    </select>


    <select id="getUserNewCommunityPostImgList" resultType="com.hzy.core.model.vo.app.AppUserNewCommunityPostImgVo"
            parameterType="java.lang.Long">
        select c.id as postId,
        c.picture_url as pictureUrl,
        u.id as userId
        from
        app_community c
        LEFT JOIN app_user u on(c.user_id=u.id)
        where c.is_del = false
        and u.id = #{userId}
        and c.picture_url is not null
        and c.picture_url !=''
        and c.picture_url !='[]'
        order by c.id desc
        limit 0, 10
    </select>

    <select id="getUserReleasePostCount" resultType="java.lang.Integer"
            parameterType="java.lang.Long">
        select count(*)
        from
        app_community c
        LEFT JOIN app_user u on(c.user_id=u.id)
        where c.is_del = false
        and u.id = #{userId}
    </select>
    
    <!-- 获取用户最新帖子中的前3张图片 -->
    <select id="getUserTopThreePostImages" resultType="java.lang.String"
            parameterType="java.lang.Long">
        SELECT 
            JSON_UNQUOTE(JSON_EXTRACT(
                c.picture_url, 
                CONCAT('$[', n.n, ']')
            )) AS image_url
        FROM 
            app_community c
        JOIN 
            (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2) n
        WHERE 
            c.is_del = false
            AND c.user_id = #{userId}
            AND c.picture_url IS NOT NULL
            AND c.picture_url != ''
            AND c.picture_url != '[]'
            AND JSON_VALID(c.picture_url)
            AND JSON_LENGTH(c.picture_url) > n.n
        ORDER BY 
            c.id DESC
        LIMIT 3
    </select>
    
    <select id="selectAppCommunityByUserId" resultType="com.hzy.core.entity.AppCommunity"
            parameterType="java.lang.Long">
        select *
        from app_community
        where user_id = #{userId}
        and is_del = 0
    </select>

    <select id="getAppCommunityCountByUserId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(*)
        from app_community
        where user_id = #{userId}
    </select>

    <update id="addPageView" parameterType="java.lang.Long">
        update app_community set page_view=page_view+1
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>