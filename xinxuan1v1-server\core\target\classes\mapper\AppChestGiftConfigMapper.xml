<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppChestGiftConfigMapper">

    <resultMap type="AppChestGiftConfig" id="AppChestGiftConfigResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
        <result property="ratio" column="ratio"/>
        <result property="gittId" column="gitt_id"/>
        <result property="bindGiftId" column="bind_gift_id"/>
        <result property="gl" column="gl"/>
    </resultMap>

    <sql id="selectAppChestGiftConfigVo">
        select id,
        create_time,
        update_time,
        create_by,
        update_by,
        is_del,
        ratio,
        gitt_id,
        bind_gift_id,
        gl
        from app_chest_gift_config
    </sql>

    <select id="selectAppChestGiftConfigList" parameterType="AppChestGiftConfig" resultMap="AppChestGiftConfigResult">
        select c.id,
        c.create_time,
        c.update_time,
        c.create_by,
        c.update_by,
        c.is_del,
        c.gl,
        c.ratio,
        c.gitt_id,
        c.bind_gift_id,
        g.gift_name as bxName,
        g.id as bxId
        from app_chest_gift_config c
        LEFT JOIN app_gift g on(g.id=c.gitt_id)
        where c.is_del=false
        <if test="ratio != null ">and c.ratio = #{ratio}</if>
        <if test="gittId != null ">and c.gitt_id = #{gittId}</if>
        <if test="bxId != null ">and g.id = #{bxId}</if>
        <if test="bindGiftId != null ">and c.bind_gift_id = #{bindGiftId}</if>
        order by c.id desc
    </select>

    <select id="getPrizePoolList" resultType="com.hzy.core.model.vo.app.AppGiftVo" parameterType="java.lang.Long">
        select
        gi.id as giftId,
        gi.gift_name giftName,
        gi.masonry_price as masonryPrice,
        gi.img_url as imgUrl,
        gi.give_gif_img_url as giveGifImgUrl,
        gi.effect_picture_url as effectPictureUrl
        from app_chest_gift_config c,
        app_gift gi
        where c.is_del=false
        and c.bind_gift_id=gi.id
        and c.gitt_id=#{giftId}
        GROUP BY c.bind_gift_id
        order by gi.masonry_price asc, gi.id desc
    </select>

    <select id="getChestConfigGiftSumCount" resultType="java.lang.Integer">
        select count(*)
        from app_chest_gift_config
        where is_del=false
        and gitt_id = #{chestGiftId}
    </select>

    <select id="getChestGiftConfigByGiftId" parameterType="Long" resultMap="AppChestGiftConfigResult">
        <include refid="selectAppChestGiftConfigVo"/>
        where is_del=false and gitt_id=#{giftId}
    </select>

    <select id="selectAppChestGiftConfigById" parameterType="Long" resultMap="AppChestGiftConfigResult">
        select c.id,
        c.create_time,
        c.update_time,
        c.create_by,
        c.update_by,
        c.is_del,
        c.ratio,
        c.gl,
        c.gitt_id,
        c.bind_gift_id,
        up.name as jjcName,
        g.gift_name as bxName,
        g.id as bxId
        from app_chest_gift_config c
        LEFT JOIN app_chest_upgrade_config up on(up.id=c.gitt_id)
        LEFT JOIN app_gift g on(g.id=up.chest_gift_id)
        where c.id = #{id} and c.is_del=false
    </select>

    <insert id="insertAppChestGiftConfig" parameterType="AppChestGiftConfig" useGeneratedKeys="true" keyProperty="id">
        insert into app_chest_gift_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
            <if test="ratio != null">ratio,</if>
            <if test="gittId != null">gitt_id,</if>
            <if test="bindGiftId != null">bind_gift_id,</if>
            <if test="gl != null">gl,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="ratio != null">#{ratio},</if>
            <if test="gittId != null">#{gittId},</if>
            <if test="bindGiftId != null">#{bindGiftId},</if>
            <if test="gl != null">#{gl},</if>
        </trim>
    </insert>

    <update id="updateAppChestGiftConfig" parameterType="AppChestGiftConfig">
        update app_chest_gift_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="ratio != null">ratio = #{ratio},</if>
            <if test="gittId != null">gitt_id = #{gittId},</if>
            <if test="bindGiftId != null">bind_gift_id = #{bindGiftId},</if>
            <if test="gl != null">gl = #{gl},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppChestGiftConfigById" parameterType="Long">
        delete
        from app_chest_gift_config
        where id = #{id}
    </delete>

    <delete id="deleteAppChestGiftConfigByIds" parameterType="String">
        delete from app_chest_gift_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>