<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppUserFollowMapper">

    <resultMap type="AppUserFollow" id="AppUserFollowResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="beUserId" column="be_user_id"/>
    </resultMap>

    <sql id="selectAppUserFollowVo">
        select id, create_time, update_time, user_id, be_user_id
        from app_user_follow
    </sql>

    <select id="selectAppUserFollowList" parameterType="AppUserFollow" resultMap="AppUserFollowResult">
        <include refid="selectAppUserFollowVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="beUserId != null ">and be_user_id = #{beUserId}</if>
        </where>
    </select>

    <select id="selectAppUserFollowById" parameterType="Long" resultMap="AppUserFollowResult">
        <include refid="selectAppUserFollowVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppUserFollow" parameterType="AppUserFollow">
        insert into app_user_follow
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="beUserId != null">be_user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="beUserId != null">#{beUserId},</if>
        </trim>
    </insert>

    <update id="updateAppUserFollow" parameterType="AppUserFollow">
        update app_user_follow
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="beUserId != null">be_user_id = #{beUserId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppUserFollowById" parameterType="Long">
        delete
        from app_user_follow
        where id = #{id}
    </delete>

    <delete id="deleteAppUserFollowByIds" parameterType="String">
        delete from app_user_follow where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectAppUserFollowByUserIdAndBeUserId" parameterType="java.lang.Long" resultMap="AppUserFollowResult">
        <include refid="selectAppUserFollowVo"/>
        where user_id = #{userId} and be_user_id=#{beUserId}
        order by id desc limit 0,1
    </select>


    <select id="getUserVermicelliCount" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select count(*)
        from app_user_follow
        where be_user_id = #{userId}
    </select>

    <select id="getUserVermicelliList" resultType="com.hzy.core.model.vo.app.AppViewUserInfoVo"
            parameterType="java.lang.Long">
        select u.id as userId,
        u.recode_code as recodeCode,
        u.is_online as isOnline,
        u.last_operating_time as lastOperatingTime,
        u.photo_album as photoAlbumStr,
        u.nick_name as nickName,
        u.is_real_person_auth as isRealPersonAuth,
        u.is_real_name_auth as isRealNameAuth,
        if(u.phone!=''and u.phone is not null,1,0) as isPhoneAuth,
        u.birthday,
        u.age,
        u.sex,
        u.personal_signature as personalSignature,
        u.head_portrait as headPortrait,
        u.education_background as educationBackground,
        u.constellation,
        u.height,
        u.weight,
        u.location,
        u.annual_income as annualIncome,
        u.purchase_situation as purchaseSituation,
        u.car_purchase_situation as carPurchaseSituation,
        u.self_introduction as selfIntroduction,
        u.label,
        u.voice_signature as voiceSignature,
        u.video,
        u.longitude,
        u.latitude,
        u.province,
        u.city,
        u.area
        from app_user_follow f
        LEFT JOIN app_user u on(u.id = f.user_id)
        where f.be_user_id = #{userId}
        order by f.id desc
    </select>

    <select id="getUserFollowCount" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select count(*)
        from app_user_follow
        where user_id = #{userId}
    </select>

    <select id="getUserFollowList" resultType="com.hzy.core.model.vo.app.AppViewUserInfoVo"
            parameterType="java.lang.Long">
        select u.id as userId,
        u.recode_code as recodeCode,
        u.is_online as isOnline,
        u.last_operating_time as lastOperatingTime,
        u.photo_album as photoAlbumStr,
        u.nick_name as nickName,
        u.birthday,
        u.age,
        u.is_real_person_auth as isRealPersonAuth,
        u.is_real_name_auth as isRealNameAuth,
        if(u.phone!=''and u.phone is not null,1,0) as isPhoneAuth,
        u.sex,
        u.personal_signature as personalSignature,
        u.head_portrait as headPortrait,
        u.education_background as educationBackground,
        u.constellation,
        u.height,
        u.weight,
        u.location,
        u.annual_income as annualIncome,
        u.purchase_situation as purchaseSituation,
        u.car_purchase_situation as carPurchaseSituation,
        u.self_introduction as selfIntroduction,
        u.label,
        u.voice_signature as voiceSignature,
        u.video,
        u.longitude,
        u.latitude,
        u.province,
        u.city,
        u.area
        from app_user_follow f
        LEFT JOIN app_user u on(u.id = f.be_user_id)
        where f.user_id = #{userId}
        order by f.id desc
    </select>

    <select id="getUserFriendCount" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select count(*)
        from app_user_follow f1,
        app_user_follow f2
        where f1.user_id = f2.be_user_id
        and f1.be_user_id = f2.user_id
        and f1.user_id = #{userId}
        and f2.be_user_id = #{userId}
    </select>


    <select id="getUserFriendList" resultType="com.hzy.core.model.vo.app.AppViewUserInfoVo"
            parameterType="java.lang.Long">
        select u.id as userId,
        u.recode_code as recodeCode,
        u.last_operating_time as lastOperatingTime,
        u.photo_album as photoAlbumStr,
        u.nick_name as nickName,
        u.is_online as isOnline,
        u.birthday,
        u.is_real_person_auth as isRealPersonAuth,
        u.is_real_name_auth as isRealNameAuth,
        if(u.phone!=''and u.phone is not null,1,0) as isPhoneAuth,
        u.age,
        u.sex,
        u.personal_signature as personalSignature,
        u.head_portrait as headPortrait,
        u.education_background as educationBackground,
        u.constellation,
        u.height,
        u.weight,
        u.location,
        u.annual_income as annualIncome,
        u.purchase_situation as purchaseSituation,
        u.car_purchase_situation as carPurchaseSituation,
        u.self_introduction as selfIntroduction,
        u.label,
        u.voice_signature as voiceSignature,
        u.longitude,
        u.latitude,
        u.province,
        u.city,
        u.area
        from app_user_follow f1,
        app_user_follow f2
        LEFT JOIN app_user u on(u.id = f2.user_id)
        where f1.user_id = f2.be_user_id
        and f1.be_user_id = f2.user_id
        and f1.user_id = #{userId}
        and f2.be_user_id = #{userId}
        order by if(f1.id > f2.id, f1.id, f2.id) desc
    </select>

    <select id="isAttentionToEachOther" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT
            CASE
                WHEN EXISTS (
                    SELECT 1
                    FROM app_user_follow f1
                    WHERE f1.user_id = #{userId} AND f1.be_user_id = #{beUserId}
                ) AND EXISTS (
                    SELECT 1
                    FROM app_user_follow f2
                    WHERE f2.user_id = #{beUserId} AND f2.be_user_id = #{userId}
                ) THEN 1 #互相关注
                ELSE 0 #未关注
            END AS follow_status;
    </select>
    <select id="isDoneFollowSixUser" resultType="java.lang.Boolean">
        select if(count(*)>=6,1,0)
        from app_user_follow f
        LEFT JOIN app_user u ON u.id = f.be_user_id
        where f.user_id = #{userId}
        and to_days(f.create_time) = to_days(now())
        and u.sex != (select sex from app_user where id = #{userId})
    </select>
</mapper>