package com.hzy.client.service;

import com.aliyun.dypnsapi20170525.Client;
import com.aliyun.dypnsapi20170525.models.GetMobileRequest;
import com.aliyun.dypnsapi20170525.models.GetMobileResponse;
import com.aliyun.dypnsapi20170525.models.GetMobileResponseBody;
import com.aliyun.teaopenapi.models.Config;
import com.hzy.client.controller.core.AppBaseController;
import com.hzy.client.webSocket.PrivateLetterWebSocket;
import com.hzy.core.config.RedisCache;
import com.hzy.core.constant.RedisKeyConsts;
import com.hzy.core.entity.*;
import com.hzy.core.enums.AppHtmlTypeEnums;
import com.hzy.core.enums.AppProblemTypeEnums;
import com.hzy.core.enums.WhetherTypeEnum;
import com.hzy.core.mapper.*;
import com.hzy.core.model.vo.AjaxResult;
import com.hzy.core.model.vo.app.AppProblemVo;
import com.hzy.core.model.vo.app.AppSubmitFeedbackVo;
import com.hzy.core.model.vo.app.AppViewUserInfoVo;
import com.hzy.core.service.AppHtmlService;
import com.hzy.core.service.common.CommonService;
import com.hzy.core.utils.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 通用服务
 */
@Service
public class AppCommonService extends AppBaseController {

    private static final Logger logger = LoggerFactory.getLogger(AppCommonService.class);
    /**
     * 阿里云访问域名
     */
    private final static String aliYunEndpoint = "dypnsapi.aliyuncs.com";
    @Value("${aliyun.accessKeyId}")
    private String aliYunAccessKeyId;
    @Value("${aliyun.accessKeySecret}")
    private String aliYunAccessKeySecret;
    @Resource
    private CommonService commonService;
    @Resource
    private AppHtmlService appHtmlService;
    @Resource
    private RedisCache redisCache;
    @Resource
    private Redisson redisson;
    @Resource
    private PrivateLetterWebSocket privateLetterWebSocket;
    @Resource
    private AppUserFollowMapper appUserFollowMapper;
    @Resource
    private AppFeedbackMapper appFeedbackMapper;
    @Resource
    private AppSensitiveLexiconMapper appSensitiveLexiconMapper;
    @Resource
    private AppUpgradeMapper appUpgradeMapper;
    @Resource
    private AppProblemMapper appProblemMapper;
    @Resource
    private AppBannerMapper appBannerMapper;
    @Resource
    private AppUserMapper appUserMapper;
    @Resource
    private AppConfigMapper appConfigMapper;

    /**
     * 获取轮播图列表
     *
     * @return
     */
    public AjaxResult getBannerList() {
        List<AppBanner> list = appBannerMapper.selectAppBannerList(new AppBanner());
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        } else {
            list.forEach(item -> {
                item.setUpdateBy(null);
                item.setUpdateTime(null);
                item.setCreateBy(null);
                item.setCreateTime(null);
            });
        }
        return AjaxResult.success(list);
    }

    /**
     * 获取app版本信息
     *
     * @param type 1安卓,2ios
     * @return
     */
    public AjaxResult getAPPNewVersions(Integer type) {
        if (null == type || (type != 1 && type != 2)) {
            type = 1;
        }
        AppUpgrade info = appUpgradeMapper.getAppVersion(type == 1 ? "android" : "ios");

        if(type == 2){
           info.setDownloadUrl("itms-services://?action=download-manifest&url=https://i.vvparty.cn/ios.plist");
           info.setUrl("itms-services://?action=download-manifest&url=https://i.vvparty.cn/ios.plist");
        }

        return AjaxResult.success(info);
    }

    /**
     * 是否开启聊天助手
     * <AUTHOR>
     * @date 2025/5/29 下午3:53
     */
    public AjaxResult isEnableChatHelper() {
        AppConfig appConfig = appConfigMapper.getAppConfig();
        return AjaxResult.success(appConfig.getIsEnableChatHelper());
    }


    /**
     * 判断文本是否包含敏感词
     *
     * @param text
     * @return
     */
    public boolean isContainsSensitiveLexicon(String text) {
        if (StringUtils.isBlank(text)) {
            return false;
        }
        List<String> sensitiveLexiconList = getSensitiveLexiconList();
        if (!CollectionUtils.isEmpty(sensitiveLexiconList)) {
            for (String content : sensitiveLexiconList) {
                if (text.contains(content)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * h
     *
     * @return
     */
    public List<String> getSensitiveLexiconList() {
        List<String> list = new ArrayList<>();
        try {
            // 先读缓存
            list = redisCache.getCacheObject(RedisKeyConsts.app_sensitive_lexicon_list_key);
        } catch (Exception e) {
        }
        if (CollectionUtils.isEmpty(list)) {// 未读到再查库
            list = appSensitiveLexiconMapper.getSensitiveLexiconList();
            if (!CollectionUtils.isEmpty(list)) {// 不为空存入缓存
                redisCache.setCacheObject(RedisKeyConsts.app_sensitive_lexicon_list_key, list);
            }
        }
        return list;
    }

    /**
     * 获取用户通话配置
     *
     * @param userId
     * @return
     */
    public AppUserCommunicateTelephoneConfig getUserCommunicateTelephoneConfig(Long userId) {

        return commonService.getUserCommunicateTelephoneConfig(userId);
    }

    /**
     * 获取app配置信息
     *
     * @return
     */
    public AppConfig getAppConfig() {
        return commonService.getAppConfig();
    }


    /**
     * 判断是否是好友
     *
     * @param userId
     * @param toUserId
     * @return 0否, 1是
     */
    public int isFriend(Long userId, Long toUserId) {
        AppUserFollow followTarget = appUserFollowMapper.selectAppUserFollowByUserIdAndBeUserId(userId, toUserId);
        AppUserFollow followMy = appUserFollowMapper.selectAppUserFollowByUserIdAndBeUserId(toUserId, userId);
        return (null != followTarget && null != followMy) ? WhetherTypeEnum.YES.getName() : WhetherTypeEnum.NO.getName();// 互相关注了才是好友
    }

    /**
     * 判断是否是好友
     *
     * @param followTarget
     * @param followMy
     * @return 0否, 1是
     */
    public int isFriend(AppUserFollow followTarget, AppUserFollow followMy) {
        return (null != followTarget && null != followMy) ? WhetherTypeEnum.YES.getName() : WhetherTypeEnum.NO.getName();// 互相关注了才是好友
    }


    /**
     * 判断用户是否在线
     *
     * @param userId 用户id
     * @return
     */
    public int getUserIsOnline(Long userId, Integer sex, Integer isOnline) {
        if (null == isOnline) {
            return WhetherTypeEnum.NO.getName();
        }
        return isOnline;
    }


    public AppViewUserInfoVo getUserInfoByUserIdByInfo(Long userId) {

        return commonService.getUserInfoByUserIdByInfo(userId);
    }

    /**
     * 根据用户id获取用户详情
     *
     * @param userId
     * @return
     */
    public AppViewUserInfoVo getUserInfoByUserId(Long userId) {


        return commonService.getUserInfoByUserId(userId);
    }


    /**
     * 阿里云解析手机号
     *
     * @param token
     * @return
     */
    public String aliYunAnalysisPhone(String token) {
        Config config = new Config().setAccessKeyId(aliYunAccessKeyId).setAccessKeySecret(aliYunAccessKeySecret);
        // 访问的域名
        config.endpoint = aliYunEndpoint;
        Client client = null;
        try {
            client = new Client(config);
        } catch (Exception e) {
            logger.error("阿里云解析手机号异常:token:{},异常方法:{},msg:{}", token, "new Client", e.getMessage());
            return null;
        }
        GetMobileRequest getMobileRequest = new GetMobileRequest().setAccessToken(token);
        try {
            GetMobileResponse mobileResponse = client.getMobile(getMobileRequest);
            GetMobileResponseBody responseBody = mobileResponse.getBody();
            if (!StringUtils.equals(responseBody.getCode(), "OK")) {
                logger.error("阿里云解析手机号异常:token:{},msg:{}", token, responseBody.getMessage());
                return null;
            }
            String phone = responseBody.getGetMobileResultDTO().getMobile();
            if (StringUtils.isBlank(phone)) {
                logger.error("阿里云解析手机号异常:token:{},msg:{}", token, "返回手机号为空");
                return null;
            }
            return phone;
        } catch (Exception e) {
            logger.error("阿里云解析手机号异常:token:{},msg:{}", token, e.getMessage());
            return null;
        }
    }


    /**
     * 根据type获取静态页面内容
     */
    public AjaxResult getHtmlContentByType(Long type) {
        String content = appHtmlService.getById(type).getContent();
        return AjaxResult.success("查询成功", content);
    }

    /**
     * 提交反馈
     */
    public AjaxResult submitFeedback(AppSubmitFeedbackVo vo,Long userId) {
        // 根据用户编码获取用户信息
        RLock lock = redisson.getLock("app:submitFeedback:" + userId);
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(60, TimeUnit.SECONDS);
        if (StringUtils.isBlank(vo.getTypeContent())) {
            lock.unlock();
            return AjaxResult.error("请选择反馈类型");
        }
        if (vo.getTypeContent().length() > 50) {
            lock.unlock();
            return AjaxResult.error("反馈类型不能超过50字");
        }
        if (StringUtils.isBlank(vo.getContent())) {
            lock.unlock();
            return AjaxResult.error("请填写反馈内容");
        }
        AppFeedback appFeedback = new AppFeedback();
        appFeedback.setUserId(userId);
        appFeedback.setContent(vo.getContent());
        appFeedback.setTypeContent(vo.getTypeContent());
        appFeedback.setCreateTime(new Date());
        appFeedback.setImgUrl(vo.getImgUrl());
        appFeedbackMapper.insertAppFeedback(appFeedback);
        lock.unlock();
        return AjaxResult.success("反馈成功");
    }

    /**
     * 根据问题类型获取问题列表
     *
     * @param type
     * @return
     */
    public AjaxResult getProblemListByType(Long type) {
        AppProblemTypeEnums typeEnums = AppProblemTypeEnums.getEnum(type.intValue());
        if (null == typeEnums) {
            return AjaxResult.error("问题类型不存在");
        }
        List<AppProblemVo> list = appProblemMapper.getProblemListByType(type);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }

        return AjaxResult.success(list);
    }

    public AjaxResult getHtmlContentByTypeAll() {

        List<String> content = appHtmlService.lambdaQuery().in(AppHtml::getId,3,4,5).list().stream().map(AppHtml::getContent).collect(Collectors.toList());
        Map<String, String> stringStringMap = new HashMap<>();
        if (CollectionUtils.isEmpty(content)) {
            return AjaxResult.success( "系统未配置",stringStringMap) ;
        }
        stringStringMap.put("QQ",content.get(0));
        stringStringMap.put("url",content.get(1));
        stringStringMap.put("email",content.get(2));
        return AjaxResult.success("查询成功", stringStringMap);
    }


}
