<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppPersonalDressingMapper">

    <resultMap type="AppPersonalDressing" id="AppPersonalDressingResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="imgUrl" column="img_url"/>
        <result property="gifUrl" column="gif_url"/>
        <result property="validDays" column="valid_days"/>
        <result property="isDel" column="is_del"/>
        <result property="colorValue" column="color_value"/>
        <result property="categoryId" column="category_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="sort" column="sort"/>
        <result property="goldPrice" column="gold_price"/>
    </resultMap>

    <sql id="selectAppPersonalDressingVo">
        select id,
        `name`,
        img_url,
        gif_url,
        valid_days,
        is_del,
        color_value,
        category_id,
        create_time,
        update_time,
        create_by,
        update_by,
        sort,
        gold_price
        from app_personal_dressing
    </sql>

    <select id="selectAppPersonalDressingList" parameterType="AppPersonalDressing"
            resultMap="AppPersonalDressingResult">
        <include refid="selectAppPersonalDressingVo"/>
        where is_del=false and id>31
        <if test="name != null  and name != ''">and `name` like concat('%', #{name}, '%')</if>
        <if test="categoryId != null ">and category_id = #{categoryId}</if>
        order by id desc
    </select>

    <select id="selectAppPersonalDressingById" parameterType="Long" resultMap="AppPersonalDressingResult">
        <include refid="selectAppPersonalDressingVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppPersonalDressing" parameterType="AppPersonalDressing" useGeneratedKeys="true" keyProperty="id">
        insert into app_personal_dressing
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="imgUrl != null">img_url,</if>
            <if test="gifUrl != null">gif_url,</if>
            <if test="validDays != null">valid_days,</if>
            <if test="isDel != null">is_del,</if>
            <if test="colorValue != null">color_value,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="sort != null">sort,</if>
            <if test="goldPrice != null">gold_price,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="gifUrl != null">#{gifUrl},</if>
            <if test="validDays != null">#{validDays},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="colorValue != null">#{colorValue},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="sort != null">#{sort},</if>
            <if test="goldPrice != null">#{goldPrice},</if>
        </trim>
    </insert>

    <update id="updateAppPersonalDressing" parameterType="AppPersonalDressing">
        update app_personal_dressing
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
            <if test="gifUrl != null">gif_url = #{gifUrl},</if>
            <if test="validDays != null">valid_days = #{validDays},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="colorValue != null">color_value = #{colorValue},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="goldPrice != null">gold_price = #{goldPrice},</if>
        </trim>
        where id = #{id}
    </update>


    <delete id="deleteAppPersonalDressingById" parameterType="Long">
        delete
        from app_personal_dressing
        where id = #{id}
    </delete>

    <update id="deleteAppPersonalDressingByIds" parameterType="String">
        update app_personal_dressing set is_del=true where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getMallList" resultType="com.hzy.core.model.vo.app.AppMallVo" parameterType="java.lang.Long">
        select id as goodsId,
        `name`,
        img_url as imgUrl,
        gif_url as gifUrl,
        valid_days as validDays,
        color_value as colorValue,
        category_id as categoryId,
        gold_price as goldPrice
        from app_personal_dressing
        where id>31
        and is_del=false
        <if test="categoryId!=null">
            and category_id=#{categoryId}
        </if>
        order by gold_price asc,id desc
    </select>
</mapper>