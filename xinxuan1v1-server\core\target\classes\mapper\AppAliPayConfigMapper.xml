<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppAliPayConfigMapper">

    <resultMap type="AppAliPayConfig" id="AppAliPayConfigResult">
        <result property="id" column="id"/>
        <result property="isDel" column="is_del"/>
        <result property="isEnable" column="is_enable"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="appId" column="app_id"/>
        <result property="privateKey" column="private_key"/>
        <result property="publicKeyPath" column="public_key_path"/>
        <result property="publicKeyRsa2Path" column="public_key_rsa2_path"/>
        <result property="rootCertPath" column="root_cert_path"/>
        <result property="name" column="name"/>
    </resultMap>

    <resultMap type="com.hzy.core.model.vo.app.AppAliPayConfigVo" id="AppAliPayConfigVoResult">
        <result property="id" column="id"/>
        <result property="isDel" column="is_del"/>
        <result property="isEnable" column="is_enable"/>
        <result property="appId" column="app_id"/>
        <result property="privateKey" column="private_key"/>
        <result property="publicKeyPath" column="public_key_path"/>
        <result property="publicKeyRsa2Path" column="public_key_rsa2_path"/>
        <result property="rootCertPath" column="root_cert_path"/>
    </resultMap>


    <select id="getEnableAliPayConfig" resultMap="AppAliPayConfigVoResult">
        select id, is_del, is_enable,app_id, private_key,
        public_key_path, public_key_rsa2_path, root_cert_path from app_ali_pay_config
        where is_del=false and is_enable=true
        order by id desc limit 0,1
    </select>

    <select id="isHaveEnableAliPayConfig" resultType="java.lang.Integer">
        select 1 from
        app_ali_pay_config
        where is_del=false and is_enable=true
        order by id desc limit 0,1
    </select>

    <select id="getAliPayConfigInfoById" resultMap="AppAliPayConfigVoResult" parameterType="java.lang.Long">
        select id, is_del, is_enable,app_id, private_key,
        public_key_path, public_key_rsa2_path, root_cert_path from app_ali_pay_config
        where id=#{id} and is_del=false
    </select>

    <sql id="selectAppAliPayConfigVo">
        select id, is_del, is_enable, create_time, update_time, create_by, update_by, app_id, private_key,
        public_key_path, public_key_rsa2_path, root_cert_path,`name` from app_ali_pay_config
    </sql>

    <select id="selectAppAliPayConfigList" parameterType="AppAliPayConfig" resultMap="AppAliPayConfigResult">
        <include refid="selectAppAliPayConfigVo"/>
        where is_del=false
        <if test="isEnable != null ">and is_enable = #{isEnable}</if>
        <if test="appId != null  and appId != ''">and app_id = #{appId}</if>
        <if test="name != null  and name != ''">and `name` like concat('%', #{name}, '%')</if>
        order by is_enable desc, id desc
    </select>

    <select id="selectAppAliPayConfigById" parameterType="Long" resultMap="AppAliPayConfigResult">
        <include refid="selectAppAliPayConfigVo"/>
        where id = #{id} and is_del=false
    </select>


    <select id="getAppAliPayConfigById" parameterType="Long" resultMap="AppAliPayConfigResult">
        <include refid="selectAppAliPayConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppAliPayConfig" parameterType="AppAliPayConfig" useGeneratedKeys="true" keyProperty="id">
        insert into app_ali_pay_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDel != null">is_del,</if>
            <if test="isEnable != null">is_enable,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="appId != null">app_id,</if>
            <if test="privateKey != null">private_key,</if>
            <if test="publicKeyPath != null">public_key_path,</if>
            <if test="publicKeyRsa2Path != null">public_key_rsa2_path,</if>
            <if test="rootCertPath != null">root_cert_path,</if>
            <if test="name != null">`name`,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDel != null">#{isDel},</if>
            <if test="isEnable != null">#{isEnable},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="appId != null">#{appId},</if>
            <if test="privateKey != null">#{privateKey},</if>
            <if test="publicKeyPath != null">#{publicKeyPath},</if>
            <if test="publicKeyRsa2Path != null">#{publicKeyRsa2Path},</if>
            <if test="rootCertPath != null">#{rootCertPath},</if>
            <if test="name != null">#{name},</if>
        </trim>
    </insert>

    <update id="updateAppAliPayConfig" parameterType="AppAliPayConfig">
        update app_ali_pay_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="isEnable != null">is_enable = #{isEnable},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="privateKey != null">private_key = #{privateKey},</if>
            <if test="publicKeyPath != null">public_key_path = #{publicKeyPath},</if>
            <if test="publicKeyRsa2Path != null">public_key_rsa2_path = #{publicKeyRsa2Path},</if>
            <if test="rootCertPath != null">root_cert_path = #{rootCertPath},</if>
            <if test="name != null">`name` = #{name},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteAppAliPayConfigById">
        update app_ali_pay_config set is_del=true,update_by=#{updateBy},update_time=now() where id = #{id}
    </update>

    <delete id="deleteAppAliPayConfigByIds" parameterType="String">
        delete from app_ali_pay_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="forbiddenAllAliPayConfig">
        update app_ali_pay_config set is_enable=0,update_by=#{updateBy},update_time=now()
    </update>
</mapper>