package com.hzy.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzy.core.entity.*;
import com.hzy.core.model.vo.admin.ChannelCommissionRecordVo;
import com.hzy.core.page.TableDataInfo;
import org.springframework.scheduling.annotation.Async;

/**
 * <p>
 * 渠道佣金记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
public interface ChannelCommissionRecordService extends IService<ChannelCommissionRecord> {

    /**
     * 判断用户来源渠道,将充值金额按照配置比例进行返点,并记账
     *
     * @param user  充值用户
     * @param order 订单
     */
    @Async
    void saveChannelCommissionRecords(AppUserEntity user, AppOrder order);

    /**
     * 接收礼物的用户判断来源渠道,收到的礼物,按照配置比例进行返点,并记账
     *
     * @param toUser      接收礼物的用户
     * @param gift        礼物
     * @param giftRecord 礼物给出记录
     */
    @Async
    void saveChannelGiftRecords(AppUserEntity toUser, AppGift gift, UserGiveGiftEntity giftRecord);

    /**
     * 保存通话记录的上级渠道分佣记录
     *
     * @param toUser  接听电话的用户
     * @param records 记录
     */
    @Async
    void saveChannelTelRecords(AppUserEntity toUser, AppCommunicateTelephoneRecords records);

    /**
     * 查询渠道分佣记录列表
     *
     * @param pageNum                 页面num
     * @param pageSize                页面大小
     * @param channelCommissionRecord 渠道佣金记录
     * @return {@link TableDataInfo }
     */
    TableDataInfo getRecordList(Integer pageNum, Integer pageSize, ChannelCommissionRecordVo channelCommissionRecord);
    /**
     * 查询渠道分佣记录列表-公会长只看下级1级的充值流水
     *
     * @param pageNum                 页面num
     * @param pageSize                页面大小
     * @param channelCommissionRecord 渠道佣金记录
     * @return {@link TableDataInfo }
     */
    TableDataInfo getRecordListOnlyOnce(Integer pageNum, Integer pageSize, ChannelCommissionRecordVo channelCommissionRecord);

    /**
     * 新的礼物分佣逻辑：仅当接收礼物的用户为女性时，给其上级渠道人员按礼物积分的10%进行分佣
     *
     * @param receiveUser 接收礼物的用户
     * @param gift        礼物
     * @param giftRecord  礼物给出记录
     */
    @Async
    void saveChannelGiftRecordsForFemale(AppUserEntity receiveUser, AppGift gift, UserGiveGiftEntity giftRecord);

    /**
     * 新的充值分佣逻辑：仅当充值用户为男性时，给其上级渠道固定返点30%的金币收益
     *
     * @param user  充值用户
     * @param order 订单
     */
    @Async
    void saveChannelCommissionRecordsForMale(AppUserEntity user, AppOrder order);

    /**
     * 新的电话收益分佣逻辑：仅当接收电话的用户为女性时，给其上级渠道人员按电话收益的10%进行分佣
     *
     * @param receiveUser 接收电话的用户
     * @param telRecords  通话记录
     */
    @Async
    void saveChannelTelRecordsForFemale(AppUserEntity receiveUser, AppCommunicateTelephoneRecords telRecords);
}
