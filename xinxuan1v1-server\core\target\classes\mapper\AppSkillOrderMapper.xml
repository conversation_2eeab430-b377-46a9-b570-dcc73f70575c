<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppSkillOrderMapper">

    <resultMap type="AppSkillOrder" id="AppSkillOrderResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="skillUserId" column="skill_user_id"/>
        <result property="userSkillId" column="user_skill_id"/>
        <result property="name" column="name"/>
        <result property="icoUrl" column="ico_url"/>
        <result property="isGame" column="is_game"/>
        <result property="chargeType" column="charge_type"/>
        <result property="price" column="price"/>
        <result property="num" column="num"/>
        <result property="createTime" column="create_time"/>
        <result property="serviceTime" column="service_time"/>
        <result property="orderOn" column="order_on"/>
        <result property="expiredTime" column="expired_time"/>
        <result property="orderStatus" column="order_status"/>
        <result property="orderPrice" column="order_price"/>
        <result property="serviceDuration" column="service_duration"/>
        <result property="msg" column="msg"/>
    </resultMap>

    <sql id="selectAppSkillOrderVo">
        select id, user_id, skill_user_id, user_skill_id, name, ico_url, is_game, charge_type, price, num, create_time,
        service_time, order_on, expired_time, order_status, order_price, service_duration, msg from app_skill_order
    </sql>

    <select id="selectAppSkillOrderList" parameterType="AppSkillOrder" resultMap="AppSkillOrderResult">
        <include refid="selectAppSkillOrderVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="skillUserId != null ">and skill_user_id = #{skillUserId}</if>
            <if test="userSkillId != null ">and user_skill_id = #{userSkillId}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="icoUrl != null  and icoUrl != ''">and ico_url = #{icoUrl}</if>
            <if test="isGame != null ">and is_game = #{isGame}</if>
            <if test="chargeType != null ">and charge_type = #{chargeType}</if>
            <if test="price != null ">and price = #{price}</if>
            <if test="num != null ">and num = #{num}</if>
            <if test="serviceTime != null  and serviceTime != ''">and service_time = #{serviceTime}</if>
            <if test="orderOn != null  and orderOn != ''">and order_on = #{orderOn}</if>
            <if test="expiredTime != null ">and expired_time = #{expiredTime}</if>
            <if test="orderStatus != null ">and order_status = #{orderStatus}</if>
            <if test="orderPrice != null ">and order_price = #{orderPrice}</if>
            <if test="serviceDuration != null ">and service_duration = #{serviceDuration}</if>
            <if test="msg != null  and msg != ''">and msg = #{msg}</if>
        </where>
    </select>

    <select id="selectAppSkillOrderById" parameterType="Long" resultMap="AppSkillOrderResult">
        <include refid="selectAppSkillOrderVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppSkillOrder" parameterType="AppSkillOrder" useGeneratedKeys="true" keyProperty="id">
        insert into app_skill_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="skillUserId != null">skill_user_id,</if>
            <if test="userSkillId != null">user_skill_id,</if>
            <if test="name != null">name,</if>
            <if test="icoUrl != null">ico_url,</if>
            <if test="isGame != null">is_game,</if>
            <if test="chargeType != null">charge_type,</if>
            <if test="price != null">price,</if>
            <if test="num != null">num,</if>
            <if test="createTime != null">create_time,</if>
            <if test="serviceTime != null">service_time,</if>
            <if test="orderOn != null">order_on,</if>
            <if test="expiredTime != null">expired_time,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="orderPrice != null">order_price,</if>
            <if test="serviceDuration != null">service_duration,</if>
            <if test="msg != null">msg,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="skillUserId != null">#{skillUserId},</if>
            <if test="userSkillId != null">#{userSkillId},</if>
            <if test="name != null">#{name},</if>
            <if test="icoUrl != null">#{icoUrl},</if>
            <if test="isGame != null">#{isGame},</if>
            <if test="chargeType != null">#{chargeType},</if>
            <if test="price != null">#{price},</if>
            <if test="num != null">#{num},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="serviceTime != null">#{serviceTime},</if>
            <if test="orderOn != null">#{orderOn},</if>
            <if test="expiredTime != null">#{expiredTime},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="orderPrice != null">#{orderPrice},</if>
            <if test="serviceDuration != null">#{serviceDuration},</if>
            <if test="msg != null">#{msg},</if>
        </trim>
    </insert>

    <update id="updateAppSkillOrder" parameterType="AppSkillOrder">
        update app_skill_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="skillUserId != null">skill_user_id = #{skillUserId},</if>
            <if test="userSkillId != null">user_skill_id = #{userSkillId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="icoUrl != null">ico_url = #{icoUrl},</if>
            <if test="isGame != null">is_game = #{isGame},</if>
            <if test="chargeType != null">charge_type = #{chargeType},</if>
            <if test="price != null">price = #{price},</if>
            <if test="num != null">num = #{num},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="serviceTime != null">service_time = #{serviceTime},</if>
            <if test="orderOn != null">order_on = #{orderOn},</if>
            <if test="expiredTime != null">expired_time = #{expiredTime},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="orderPrice != null">order_price = #{orderPrice},</if>
            <if test="serviceDuration != null">service_duration = #{serviceDuration},</if>
            <if test="msg != null">msg = #{msg},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppSkillOrderById" parameterType="Long">
        delete from app_skill_order where id = #{id}
    </delete>

    <delete id="deleteAppSkillOrderByIds" parameterType="String">
        delete from app_skill_order where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getUserSkillOrderList" resultType="com.hzy.core.model.vo.app.AppSkillOrderVo">
        select id as orderId,
        user_id as userId,
        skill_user_id as skillUserId,
        user_skill_id as userSkillId,
        name as name,
        ico_url as icoUrl,
        is_game as isGame,
        charge_type as chargeType,
        price as price,
        num as num,
        create_time as createTime,
        service_time as serviceTime,
        order_on as orderOn,
        expired_time as expiredTime,
        order_status as orderStatus,
        order_price as orderPrice,
        service_duration as serviceDuration,
        msg as msg from app_skill_order
        where order_status BETWEEN -1 AND 4
        <if test="status!=null">
            and order_status=#{status}
        </if>
        <if test="queryType!=null and queryType==1">
            and user_id=#{userId}
        </if>
        <if test="queryType!=null and queryType==2">
            and skill_user_id=#{userId}
        </if>
        <if test="queryType==null">
            and (user_id=#{userId} or skill_user_id=#{userId})
        </if>
        order by id desc
    </select>

    <select id="getSkillOrderDetails" resultType="com.hzy.core.model.vo.app.AppSkillOrderVo"
            parameterType="java.lang.Long">
        select id as orderId,
        user_id as userId,
        skill_user_id as skillUserId,
        user_skill_id as userSkillId,
        name as name,
        ico_url as icoUrl,
        is_game as isGame,
        charge_type as chargeType,
        price as price,
        num as num,
        create_time as createTime,
        service_time as serviceTime,
        order_on as orderOn,
        expired_time as expiredTime,
        order_status as orderStatus,
        order_price as orderPrice,
        service_duration as serviceDuration,
        msg as msg from app_skill_order
        where id=#{id} and order_status BETWEEN -1 AND 4
    </select>
</mapper>