<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppSkillEvaluateMapper">

    <resultMap type="AppSkillEvaluate" id="AppSkillEvaluateResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="orderId" column="order_id"/>
        <result property="content" column="content"/>
        <result property="userSkillId" column="user_skill_id"/>
        <result property="mark" column="mark"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectAppSkillEvaluateVo">
        select id, user_id, order_id, content, user_skill_id, mark, create_time from app_skill_evaluate
    </sql>

    <select id="selectAppSkillEvaluateList" parameterType="AppSkillEvaluate" resultMap="AppSkillEvaluateResult">
        <include refid="selectAppSkillEvaluateVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="orderId != null ">and order_id = #{orderId}</if>
            <if test="content != null  and content != ''">and content = #{content}</if>
            <if test="userSkillId != null ">and user_skill_id = #{userSkillId}</if>
            <if test="mark != null ">and mark = #{mark}</if>
        </where>
    </select>

    <select id="selectAppSkillEvaluateById" parameterType="Long" resultMap="AppSkillEvaluateResult">
        <include refid="selectAppSkillEvaluateVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppSkillEvaluate" parameterType="AppSkillEvaluate" useGeneratedKeys="true" keyProperty="id">
        insert into app_skill_evaluate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="content != null">content,</if>
            <if test="userSkillId != null">user_skill_id,</if>
            <if test="mark != null">mark,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="content != null">#{content},</if>
            <if test="userSkillId != null">#{userSkillId},</if>
            <if test="mark != null">#{mark},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateAppSkillEvaluate" parameterType="AppSkillEvaluate">
        update app_skill_evaluate
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="userSkillId != null">user_skill_id = #{userSkillId},</if>
            <if test="mark != null">mark = #{mark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppSkillEvaluateById" parameterType="Long">
        delete from app_skill_evaluate where id = #{id}
    </delete>

    <delete id="deleteAppSkillEvaluateByIds" parameterType="String">
        delete from app_skill_evaluate where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getSkillEvaluateList" resultType="com.hzy.core.model.vo.app.AppSkillEvaluateVo">
        select se.id as evaluateId, se.user_id as userId , se.content, se.mark, se.create_time as createTime
        from app_skill_evaluate se
        where se.user_skill_id=#{userSkillId}
    </select>
</mapper>