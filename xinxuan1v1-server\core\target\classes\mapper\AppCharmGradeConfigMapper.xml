<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppCharmGradeConfigMapper">

    <resultMap type="AppCharmGradeConfig" id="AppCharmGradeConfigResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="icoUrl" column="ico_url"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="gradeSize" column="grade_size"/>
        <result property="goldValue" column="gold_value"/>
    </resultMap>

    <sql id="selectAppCharmGradeConfigVo">
        select id,
        `name`,
        ico_url,
        create_time,
        update_time,
        create_by,
        update_by,
        grade_size,
        gold_value
        from app_charm_grade_config
    </sql>

    <select id="selectAppCharmGradeConfigList" parameterType="AppCharmGradeConfig"
            resultMap="AppCharmGradeConfigResult">
        <include refid="selectAppCharmGradeConfigVo"/>
        <where>
            <if test="name != null  and name != ''">and `name` like concat('%', #{name}, '%')</if>
            <if test="icoUrl != null  and icoUrl != ''">and ico_url = #{icoUrl}</if>
            <if test="gradeSize != null ">and grade_size = #{gradeSize}</if>
            <if test="goldValue != null ">and gold_value = #{goldValue}</if>
        </where>
        order by grade_size
    </select>


    <insert id="insertAppCharmGradeConfig" parameterType="AppCharmGradeConfig" useGeneratedKeys="true" keyProperty="id">
        insert into app_charm_grade_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">`name`,</if>
            <if test="icoUrl != null">ico_url,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="gradeSize != null">grade_size,</if>
            <if test="goldValue != null">gold_value,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="icoUrl != null">#{icoUrl},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="gradeSize != null">#{gradeSize},</if>
            <if test="goldValue != null">#{goldValue},</if>
        </trim>
    </insert>

    <update id="updateAppCharmGradeConfig" parameterType="AppCharmGradeConfig">
        update app_charm_grade_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">`name` = #{name},</if>
            <if test="icoUrl != null">ico_url = #{icoUrl},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="gradeSize != null">grade_size = #{gradeSize},</if>
            <if test="goldValue != null">gold_value = #{goldValue},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppCharmGradeConfigById" parameterType="Long">
        delete
        from app_charm_grade_config
        where id = #{id}
    </delete>

    <delete id="deleteAppCharmGradeConfigByIds" parameterType="String">
        delete from app_charm_grade_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getCharmGradeConfigList" resultType="com.hzy.core.model.vo.app.AppTitleNobilityGradeConfigVo">
        select id as gradeId,
        `name`,
        ico_url as icoUrl,
        grade_size as gradeSize,
        gold_value as upgradesValue
        from app_charm_grade_config
        order by grade_size asc, id desc
    </select>

    <select id="getMinCharmGradeConfig" resultMap="AppCharmGradeConfigResult">
        select id,
        `name`,
        ico_url,
        create_time,
        update_time,
        create_by,
        update_by,
        grade_size,
        gold_value
        from app_charm_grade_config
        order by grade_size asc limit 0, 1
    </select>

    <select id="getNextCharmGradeConfig" parameterType="java.lang.Long" resultMap="AppCharmGradeConfigResult">
        select id,
        `name`,
        ico_url,
        create_time,
        update_time,
        create_by,
        update_by,
        grade_size,
        gold_value
        from app_charm_grade_config
        where grade_size &gt; #{currentGradeSize}
        order by grade_size asc limit 0, 1
    </select>
    <select id="selectAppCharmGradeConfigById" resultType="com.hzy.core.entity.AppCharmGradeConfig">
        select id,
        `name`,
        ico_url,
        create_time,
        update_time,
        create_by,
        update_by,
        grade_size,
        gold_value
        from app_charm_grade_config
        where id = #{id}
    </select>
    <select id="getIconByUserId" resultType="java.lang.String">
        select ac.ico_url
        from app_charm_grade_config ac
                 left join app_user_charm_grade au on au.current_grade_id = ac.id
        where au.user_id = #{userId}
    </select>

    <!-- getCharmGradeConfigByGradeSize --> 

    <select id="getCharmGradeConfigByGradeSize" resultMap="AppCharmGradeConfigResult">
        select id,
        `name`,
        ico_url,
        create_time,
        update_time,
        create_by,
        update_by,
        grade_size,
        gold_value
        from app_charm_grade_config
        where gold_value &lt;= #{gradeSize}
        order by grade_size desc limit 0, 1
    </select>
    
</mapper>