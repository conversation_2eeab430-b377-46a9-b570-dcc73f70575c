<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppUserTopUpGradeMapper">

    <resultMap type="AppUserTopUpGrade" id="AppUserTopUpGradeResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="sumMoneyValue" column="sum_money_value"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAppUserTopUpGradeVo">
        select id, user_id, current_grade_id, sum_money_value, create_time, update_time
        from app_user_top_up_grade
    </sql>

    <select id="selectAppUserTopUpGradeList" parameterType="AppUserTopUpGrade" resultMap="AppUserTopUpGradeResult">
        <include refid="selectAppUserTopUpGradeVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="currentGradeId != null ">and current_grade_id = #{currentGradeId}</if>
            <if test="sumMoneyValue != null ">and sum_money_value = #{sumMoneyValue}</if>
        </where>
    </select>

    <select id="selectAppUserTopUpGradeById" parameterType="Long" resultMap="AppUserTopUpGradeResult">
        <include refid="selectAppUserTopUpGradeVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppUserTopUpGrade" parameterType="AppUserTopUpGrade" useGeneratedKeys="true" keyProperty="id">
        insert into app_user_top_up_grade
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="currentGradeId != null">current_grade_id,</if>
            <if test="sumMoneyValue != null">sum_money_value,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="currentGradeId != null">#{currentGradeId},</if>
            <if test="sumMoneyValue != null">#{sumMoneyValue},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateAppUserTopUpGrade" parameterType="AppUserTopUpGrade">
        update app_user_top_up_grade
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="currentGradeId != null">current_grade_id = #{currentGradeId},</if>
            <if test="sumMoneyValue != null">sum_money_value = #{sumMoneyValue},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppUserTopUpGradeById" parameterType="Long">
        delete
        from app_user_top_up_grade
        where id = #{id}
    </delete>

    <delete id="deleteAppUserTopUpGradeByIds" parameterType="String">
        delete from app_user_top_up_grade where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getUserTopUpGrade" parameterType="java.lang.Long"
            resultType="com.hzy.core.model.vo.app.AppUserTitleNobilityVo">
        select
        tg.id as currentGradeId,
        tg.name as currentName,
        tg.ico_url as currentIcoUrl,
        tg.grade_size as currentGradeSize,
        ug.sum_money_value as currentGradeValue
        from app_user_top_up_grade ug,
        app_top_up_grade_config tg
        where tg.id = ug.current_grade_id and ug.user_id = #{userId}
        order by ug.id desc limit 0, 1
    </select>
    <select id="selectAppUserTopUpGradeByUserId" resultType="com.hzy.core.entity.AppUserTopUpGrade">
        select
        id, user_id, current_grade_id , sum_money_value, create_time, update_time
        from app_user_top_up_grade
        where user_id = #{userId}
        order by id desc limit 0, 1
    </select>


</mapper>