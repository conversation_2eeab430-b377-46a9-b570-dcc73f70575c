<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppCommunityCommentMapper">

    <resultMap type="AppCommunityComment" id="AppCommunityCommentResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="communityId" column="community_id"/>
        <result property="content" column="content"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="fatherCommentId" column="father_comment_id"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectAppCommunityCommentVo">
        select id,
        user_id,
        community_id,
        content,
        create_time,
        update_by,
        update_time,
        father_comment_id,
        is_del
        from app_community_comment
    </sql>

    <select id="selectAppCommunityCommentList" parameterType="AppCommunityComment"
            resultMap="AppCommunityCommentResult">
        <include refid="selectAppCommunityCommentVo"/>
        where is_del=false
        <if test="userId != null ">and user_id = #{userId}</if>
        <if test="communityId != null ">and community_id = #{communityId}</if>
        <if test="content != null  and content != ''">and content = #{content}</if>
        <if test="fatherCommentId != null ">and father_comment_id = #{fatherCommentId}</if>
    </select>

    <select id="selectAppCommunityCommentById" parameterType="Long" resultMap="AppCommunityCommentResult">
        <include refid="selectAppCommunityCommentVo"/>
        where id = #{id} and is_del=false
    </select>

    <insert id="insertAppCommunityComment" parameterType="AppCommunityComment" useGeneratedKeys="true" keyProperty="id">
        insert into app_community_comment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="communityId != null">community_id,</if>
            <if test="content != null">content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="fatherCommentId != null">father_comment_id,</if>
            <if test="isDel != null">is_del,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="communityId != null">#{communityId},</if>
            <if test="content != null">#{content},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="fatherCommentId != null">#{fatherCommentId},</if>
            <if test="isDel != null">#{isDel},</if>
        </trim>
    </insert>

    <update id="updateAppCommunityComment" parameterType="AppCommunityComment">
        update app_community_comment
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="fatherCommentId != null">father_comment_id = #{fatherCommentId},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppCommunityCommentById" parameterType="Long">
        delete
        from app_community_comment
        where id = #{id}
    </delete>

    <delete id="deleteAppCommunityCommentByIds" parameterType="String">
        delete from app_community_comment where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="cancelChildCommentFatherId">
        update app_community_comment
        set father_comment_id=0
        where father_comment_id = #{commentId}
    </update>


    <select id="getPostCommentList" parameterType="java.lang.Long"
            resultType="com.hzy.core.model.vo.app.AppCommunityPostCommentVo">
        select
        com.id as commentId,
        ci.id as postId,
        com.content as content,
        com.father_comment_id as fatherCommentId,
        if(u.sex=-1,null,u.sex) as userSex,
        u.nick_name as userNickName,
        u.head_portrait as userHeadPortrait,
        com.create_time as createTime,
        <if test="null!=userId">
            (if(com.user_id=#{userId},1,0)) as isPresentUser,
        </if>
        <if test="null==userId">
            0 as isPresentUser,
        </if>
        if(u.age=-1,null,u.age) as userAge,
        u.id as userId,
        u.created_time as userCreatedTime
        from app_community_comment com
        LEFT JOIN app_user u on(u.id=com.user_id),
        app_community ci
        where com.is_del=false
        and ci.is_del=false
        and com.community_id=ci.id
        and ci.id=#{postId}
        <if test="null!=fatherCommentId and fatherCommentId>0">
            and com.father_comment_id=#{fatherCommentId}
        </if>
        <if test="null==fatherCommentId or fatherCommentId &lt;=0">
            and com.father_comment_id=0
        </if>
        order by com.id desc
    </select>

    <select id="getPostChildCommentCount" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(com.id)
        from app_community_comment com
        LEFT JOIN app_user u on(u.id=com.user_id),
        app_community ci
        where com.is_del = false
        and ci.is_del = false
        and com.community_id = ci.id
        and ci.id = #{postId}
        and 1=if(com.father_comment_id !=0,1,0)
        and com.father_comment_id = #{fatherCommentId}
    </select>

    <select id="getPostChildCommentList" parameterType="java.lang.Long"
            resultType="com.hzy.core.model.vo.app.AppCommunityPostCommentVo">
        select
        com.id as commentId,
        ci.id as postId,
        com.content as content,
        com.father_comment_id as fatherCommentId,
        if(u.sex=-1,null,u.sex) as userSex,
        u.nick_name as userNickName,
        u.head_portrait as userHeadPortrait,
        com.create_time as createTime,
        <if test="null!=userId">
            (if(com.user_id=#{userId},1,0)) as isPresentUser,
        </if>
        <if test="null==userId">
            0 as isPresentUser,
        </if>
        if(u.age=-1,null,u.age) as userAge,
        u.id as userId
        from app_community_comment com
        LEFT JOIN app_user u on(u.id=com.user_id),
        app_community ci
        where com.is_del=false
        and ci.is_del=false
        and com.community_id=ci.id
        and ci.id=#{postId}
        and com.father_comment_id !=0
        and com.father_comment_id=#{fatherCommentId}
        order by com.id desc
        limit 0,3
    </select>
    <select id="selectAppCommunityCommentUserList" resultType="com.hzy.core.model.vo.app.AppCommunityPostCommentVo"
            parameterType="java.lang.Long">
        SELECT acc.id as commentId,
        acc.community_id as postId,
        acc.content as content,
        acc.create_time as createTime,
        acc.user_id as userId,
        acc.community_id as postId
        FROM app_community as ac
        LEFT JOIN
        app_community_comment as acc on acc.user_id = ac.user_id
        WHERE ac.user_id = #{userId}
        and acc.is_del = 0
        and ac.is_del = 0
    </select>
</mapper>