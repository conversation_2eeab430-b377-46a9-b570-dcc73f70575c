spring:
  profiles:
    active: prod

# 项目相关配置
hzy:
  # 前缀名称
  prefix: admin
  # 名称
  name: Kita星球-后台管理API
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2023
  # 实例演示开关
  demoEnabled: false
  # 项目根路径
  rootPath: /opt/xinxuan1v1
  # 文件路径
  profile: ${hzy.rootPath}/file
  # 获取ip地址开关
  addressEnabled: true
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math


# 开发环境配置
server:
  port: 5001
  servlet:
    # 应用的访问路径
    context-path: /admin
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 10

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: yweuYuhr7263gyfhIfPofuu892
  # 令牌有效期（默认1440分钟也就是一天）
  expireTime: 1440

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice,/system/app/html,/system/app/Banner
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*


# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

mybatis-plus:
  # 如果是放在src/main/java目录下 classpath:/com/yourpackage/*/mapper/*Mapper.xml
  # 如果是放在resource目录 classpath:/mapper/*Mapper.xml
  mapper-locations: classpath:/mapper/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.hzy.core.entity
  configuration:
    #配置返回数据库(column下划线命名&&返回java实体是驼峰命名)，自动匹配无需as（没开启这个，SQL需要写as： select user_id as userId）
    map-underscore-to-camel-case: true
    cache-enabled: false
    #配置JdbcTypeForNull, oracle数据库必须配置
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: auto

# 支付相关配置
pay:
  # 微信支付相关配置
  wx:
    #支付回调通知
    notify-url-prefix: ${common-config.clientUrl}/service/payCallback/weChat/{}
    #证书路径
    key-path:

  # 支付宝支付相关配置
  ali:
    #支付宝证书存放的根路径
    key-root-path: ${hzy.rootPath}/ali_pay_key
    #回调地址
    notify-url-prefix: ${common-config.clientUrl}/service/payCallback/aliPay/{}

  #精秀第三方支付相关配置
  jx:
    #api网关
    api-url: https://gateway.jxpays.com/
    #回调地址
    notify-url: ${common-config.clientUrl}/service/payCallback/jxPayNew/{}

# 腾讯云短信服务配置
tencent:
  sms:
    # 腾讯云账户密钥对
    secret-id: AKIDc4b0Iav2yfATYOzNXS7XjfMo22nLeiX7
    secret-key: NVO8uW3YrwrY0zlIaeS2TyHUecNyKRJx
    # 短信应用ID
    sdk-app-id: 1400968359
    # 短信签名
    sign-name: 阜阳弘顺传媒
    # 模板ID配置
    template-id:
      # 验证码短信模板ID
      verification-code: 2372827

# 阿里云系统通知推送配置
aliyun:
  push:
    access-key-id: LTAI5tJSS7UupBPthByvtu7R
    access-key-secret: ******************************
    android-app-key: 335446530
    ios-app-key: 335460396

#阿里云相关配置
  accessKeyId: LTAI5tKSXXXurHMbeg9jEvNe
  accessKeySecret: ******************************
  # 短信签名
  signName: U乐
  #实人认证相关
  realPeopleAuth:
    #场景id
    sceneId: 1000012632