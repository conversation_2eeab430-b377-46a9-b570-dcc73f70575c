<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppHallOwnerEarningsRecordsMapper">

    <resultMap type="AppHallOwnerEarningsRecords" id="AppHallOwnerEarningsRecordsResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="userId" column="user_id"/>
        <result property="amount" column="amount"/>
        <result property="triggerObecjtId" column="trigger_obecjt_id"/>
        <result property="receiveObjectId" column="receive_object_id"/>
    </resultMap>

    <sql id="selectAppHallOwnerEarningsRecordsVo">
        select id, create_time, user_id, amount, trigger_obecjt_id, receive_object_id from
        app_hall_owner_earnings_records
    </sql>

    <select id="selectAppHallOwnerEarningsRecordsList" parameterType="AppHallOwnerEarningsRecords"
            resultMap="AppHallOwnerEarningsRecordsResult">
        <include refid="selectAppHallOwnerEarningsRecordsVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="amount != null ">and amount = #{amount}</if>
            <if test="triggerObecjtId != null ">and trigger_obecjt_id = #{triggerObecjtId}</if>
            <if test="receiveObjectId != null ">and receive_object_id = #{receiveObjectId}</if>
        </where>
    </select>

    <select id="selectAppHallOwnerEarningsRecordsById" parameterType="Long"
            resultMap="AppHallOwnerEarningsRecordsResult">
        <include refid="selectAppHallOwnerEarningsRecordsVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppHallOwnerEarningsRecords" parameterType="AppHallOwnerEarningsRecords" useGeneratedKeys="true"
            keyProperty="id">
        insert into app_hall_owner_earnings_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="amount != null">amount,</if>
            <if test="triggerObecjtId != null">trigger_obecjt_id,</if>
            <if test="receiveObjectId != null">receive_object_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="amount != null">#{amount},</if>
            <if test="triggerObecjtId != null">#{triggerObecjtId},</if>
            <if test="receiveObjectId != null">#{receiveObjectId},</if>
        </trim>
    </insert>

    <update id="updateAppHallOwnerEarningsRecords" parameterType="AppHallOwnerEarningsRecords">
        update app_hall_owner_earnings_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="triggerObecjtId != null">trigger_obecjt_id = #{triggerObecjtId},</if>
            <if test="receiveObjectId != null">receive_object_id = #{receiveObjectId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppHallOwnerEarningsRecordsById" parameterType="Long">
        delete from app_hall_owner_earnings_records where id = #{id}
    </delete>

    <delete id="deleteAppHallOwnerEarningsRecordsByIds" parameterType="String">
        delete from app_hall_owner_earnings_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="getHallOwnerEarningsDetailsList" resultType="com.hzy.core.model.vo.admin.AppHallOwnerEarningsDetailsVo">
        select
        id,
        create_time as createTime,
        user_id as userId,
        amount,
        trigger_obecjt_id as triggerObecjtId,
        receive_object_id as receiveObjectId
        from
        app_hall_owner_earnings_records
        where user_id=#{userId}
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(create_time,'%y%m%d%H%i%s') &gt;= date_format(#{queryBeginTime},'%y%m%d%H%i%s')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(create_time,'%y%m%d%H%i%s') &lt;= date_format(#{queryEndTime},'%y%m%d%H%i%s')
        </if>
        order by id desc
    </select>


    <select id="getHallOwnerSumEarningsDetailsByTime" resultType="java.math.BigDecimal">
        select
        ifnull(sum(amount),0)
        from
        app_hall_owner_earnings_records
        where user_id=#{userId}
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(create_time,'%y%m%d%H%i%s') &gt;= date_format(#{queryBeginTime},'%y%m%d%H%i%s')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(create_time,'%y%m%d%H%i%s') &lt;= date_format(#{queryEndTime},'%y%m%d%H%i%s')
        </if>
    </select>


    <select id="getHallOwnerSumEarningsDetailsByWeek" resultType="java.math.BigDecimal">
        select
        ifnull(sum(amount),0)
        from
        app_hall_owner_earnings_records
        where user_id=#{userId}
        and create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
        and create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
    </select>
</mapper>