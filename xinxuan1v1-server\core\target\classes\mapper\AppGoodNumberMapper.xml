<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppGoodNumberMapper">

    <resultMap type="AppGoodNumber" id="AppGoodNumberResult">
        <result property="id" column="id"/>
        <result property="goodNumber" column="good_number"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
        <result property="masonryPrice" column="masonry_price"/>
        <result property="buyUserId" column="buy_user_id"/>
        <result property="buyOrderId" column="buy_order_id"/>
        <result property="buyTime" column="buy_time"/>
    </resultMap>

    <sql id="selectAppGoodNumberVo">
        select id, good_number, `status`, create_time, update_time, create_by, update_by, is_del, masonry_price,
        buy_user_id, buy_order_id, buy_time from app_good_number
    </sql>

    <select id="selectAppGoodNumberList" parameterType="AppGoodNumber" resultMap="AppGoodNumberResult">
        select gn.id, gn.good_number, gn.status, gn.create_time, gn.update_time, gn.create_by, gn.update_by, gn.is_del,
        gn.masonry_price,
        gn.buy_user_id, gn.buy_order_id, gn.buy_time,u.nick_name as buyUserNickName,
        u.phone as buyUserPhone,
        u.head_portrait as buyUserHeadPortrait
        from app_good_number gn
        left join app_user u on(u.id=gn.buy_user_id)
        where gn.is_del=false
        <if test="goodNumber != null  and goodNumber != ''">
            and gn.good_number like lower(concat('%', #{goodNumber},'%'))
        </if>
        <if test="status != null ">and gn.status = #{status}</if>
        order by gn.id desc
    </select>

    <select id="selectAppGoodNumberById" parameterType="Long" resultMap="AppGoodNumberResult">
        select gn.id, gn.good_number, gn.status, gn.create_time, gn.update_time, gn.create_by, gn.update_by, gn.is_del,
        gn.masonry_price,
        gn.buy_user_id, gn.buy_order_id, gn.buy_time,u.nick_name as buyUserNickName,
        u.phone as buyUserPhone,
        u.head_portrait as buyUserHeadPortrait
        from app_good_number gn
        left join app_user u on(u.id=gn.buy_user_id)
        where gn.id = #{id} and gn.is_del=false
    </select>

    <insert id="insertAppGoodNumber" parameterType="AppGoodNumber" useGeneratedKeys="true" keyProperty="id">
        insert into app_good_number
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="goodNumber != null">good_number,</if>
            <if test="status != null">`status`,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
            <if test="masonryPrice != null">masonry_price,</if>
            <if test="buyUserId != null">buy_user_id,</if>
            <if test="buyOrderId != null">buy_order_id,</if>
            <if test="buyTime != null">buy_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="goodNumber != null">#{goodNumber},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="masonryPrice != null">#{masonryPrice},</if>
            <if test="buyUserId != null">#{buyUserId},</if>
            <if test="buyOrderId != null">#{buyOrderId},</if>
            <if test="buyTime != null">#{buyTime},</if>
        </trim>
    </insert>

    <update id="updateAppGoodNumber" parameterType="AppGoodNumber">
        update app_good_number
        <trim prefix="SET" suffixOverrides=",">
            <if test="goodNumber != null">good_number = #{goodNumber},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="masonryPrice != null">masonry_price = #{masonryPrice},</if>
            <if test="buyUserId != null">buy_user_id = #{buyUserId},</if>
            <if test="buyOrderId != null">buy_order_id = #{buyOrderId},</if>
            <if test="buyTime != null">buy_time = #{buyTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppGoodNumberById" parameterType="Long">
        delete from app_good_number where id = #{id}
    </delete>

    <delete id="deleteAppGoodNumberByIds" parameterType="String">
        delete from app_good_number where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="isExist" resultType="java.lang.Boolean" parameterType="java.lang.String">
        select if(count(*)>0,1,0) from app_good_number where good_number=#{number} and is_del=false
    </select>

    <select id="getGoodNumberById" parameterType="Long" resultMap="AppGoodNumberResult">
        select id, good_number, `status`, create_time, update_time, create_by, update_by, is_del, masonry_price,
        buy_user_id, buy_order_id, buy_time from app_good_number
        where id = #{id} and is_del=false
    </select>

    <select id="getGoodNumberList" resultType="com.hzy.core.model.vo.app.AppGoodNumberVo">
        select
        id as goodsId,
        good_number as goodNumber,
        `status`,
        masonry_price as masonryPrice
        from app_good_number
        where is_del=false
        and `status` !=2
        order by `status` desc,good_number asc, id desc
    </select>
</mapper>