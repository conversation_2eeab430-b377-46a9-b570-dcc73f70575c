<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.GamePoolGiftRelationMapper">

    <resultMap type="AppTurntableGiftConfig" id="AppTurntableGiftConfigResult">
        <result property="id" column="id"/>
        <result property="createTime" column="created_time"/>
        <result property="updateTime" column="updated_time"/>
        <result property="createdBy" column="created_by"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="isDel" column="deleted"/>
        <result property="ratio" column="num"/>
        <result property="bindGiftId" column="bindGiftId"/>
        <result property="jjcId" column="pool_id"/>
        <result property="gl" column="gl"/>
        <result property="sljcNum" column="sljc_num"/>
    </resultMap>

    <sql id="selectAppTurntableGiftConfigVo">
        select id,
               created_time,
               updated_time,
               created_by,
               updated_by,
               deleted,
               ratio,
               bind_gift_id,
               jjc_id,
               gl
        from game_pool_gift_relation
    </sql>

    <insert id="insertByBatchIds" parameterType="java.util.List">
        INSERT INTO game_pool_gift_relation
        (num, bind_gift_id, pool_id, created_by, updated_by, created_time, updated_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.number}, #{item.giftId}, #{item.jjcId}, #{item.createdBy}, #{item.updatedBy}, #{item.createdTime},
            #{item.updatedTime})
        </foreach>
    </insert>


    <select id="selectAppTurntableGiftConfigByJjcId" resultType="com.hzy.core.model.vo.app.AppTurntableGiftVo">
        select ag.id                 giftId,
               ag.gift_name          giftName,
               ag.masonry_price      giftPrice,
               ag.img_url            giftImgUrl,
               ag.give_gif_img_url   giftGiveGifImgUrl,
               ag.effect_picture_url giftEffectPictureUrl,
               atgc.num,
               atgc.gl
        from game_pool_gift_relation atgc
                 left join app_gift ag on atgc.bind_gift_id = ag.id
        where atgc.pool_id = #{jjcId}
          and atgc.deleted = false
    </select>

    <select id="selectAppTurntableGiftConfigList" parameterType="AppTurntableGiftConfig"
            resultMap="AppTurntableGiftConfigResult">
        select
        c.id,
        c.created_time,
        c.updated_time,
        c.created_by,
        c.updated_by,
        c.deleted,
        c.gl,
        c.num,
        c.bind_gift_id as bindGiftId,
        g.gift_name as giftName,
        g.masonry_price as giftPrice,
        g.img_url as giftImgUrl,
        g.give_gif_img_url as giftGiveGifImgUrl,
        g.effect_picture_url as giftEffectPictureUrl,
        c.pool_id,
        "0" as sljc_num
        from game_pool_gift_relation c
        LEFT JOIN app_gift g on(g.id=c.bind_gift_id)
        where c.deleted=false
        <if test="giftName!=null and giftName!='' ">
            and g.gift_name like concat('%', #{giftName}, '%')
        </if>
        <if test="jjcId!=null">
            and c.pool_id = #{jjcId}
        </if>
        <if test="sortType==0">
            order by g.masonry_price desc
        </if>
        <if test="sortType==1">
            order by g.masonry_price asc
        </if>
        <if test="sortType==null">
            order by g.masonry_price desc,c.id desc
        </if>
    </select>

    <select id="selectAppTurntableGiftConfigById" parameterType="Long" resultMap="AppTurntableGiftConfigResult">
        select c.id,
               c.created_time,
               c.updated_time,
               c.created_by,
               c.updated_by,
               c.deleted,
               c.pool_id,
               c.gl,
               c.num,
               c.bind_gift_id       as bindGiftId,
               g.gift_name          as giftName,
               g.masonry_price      as giftPrice,
               g.img_url            as giftImgUrl,
               g.give_gif_img_url   as giftGiveGifImgUrl,
               g.effect_picture_url as giftEffectPictureUrl
        from game_pool_gift_relation c,
             app_gift g
        where c.deleted = false
          and c.bind_gift_id = g.id
          and g.category_id = -1
          and c.id = #{id}
    </select>

    <update id="deleteAppTurntableGiftConfigById" parameterType="Long">
        update game_pool_gift_relation
        set deleted= true
        where id = #{id}
    </update>
    <update id="updateBybatch"
            parameterType="java.util.List">
        update game_pool_gift_relation
        <if test="num!=null">
            num = #{num},
        </if>
        where id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>
    <update id="updateByBatchIds" parameterType="java.util.List">
        update game_pool_gift_relation
        <if test="num!=null">
            num = #{num},
        </if>
        where id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>


    <delete id="deleteAppTurntableGiftConfigByIds" parameterType="String">
        delete from game_pool_gift_relation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="deleteAppGift">
        update game_pool_gift_relation
        set deleted= 1
        where id = #{id}
          and bind_gift_id = #{bindGiftId}
          and pool_id = #{jjcId}

    </update>
    <update id="updateByGift">
        update game_pool_gift_relation
        set num          = #{num},
            updated_time = now(),
            updated_by   = #{updatedBy},
            deleted      = 0
        where id = #{id}
          and bind_gift_id = #{bindGiftId}
          and pool_id = #{poolId}


    </update>

    <select id="getUserSumNum" resultType="java.math.BigDecimal" parameterType="java.lang.Long">
        select sum(abs(amount))
        from app_user_gold_bill
        where user_id = #{userId}
          and bill_type = 24
    </select>

    <select id="getUserToDaySumNum" resultType="java.math.BigDecimal" parameterType="java.lang.Long">
        select sum(abs(amount))
        from app_user_gold_bill
        where user_id = #{userId}
          and bill_type = 24
          and create_time >= CURDATE()
          AND create_time &lt; CURDATE() + INTERVAL 1 DAY
    </select>
    <select id="selectDiffCountGifts" resultType="com.hzy.core.entity.GamePoolGiftRelationEntity"
            parameterType="java.util.List">
        SELECT
        id,
        pool_id,
        num,
        bind_gift_id
        FROM
        game_pool_gift_relation AS r1
        WHERE
        bind_gift_id IN
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item.giftId}
        </foreach>
        AND pool_id IN
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item.jjcId}
        </foreach>
        and num in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item.number}
        </foreach>
    </select>
    <select id="selectBatchList" resultType="com.hzy.core.entity.GamePoolGiftRelationEntity"
            parameterType="java.util.List">
        select
        g.id,
        g.pool_id,
        g.num,
        g.bind_gift_id,
        g.deleted
        from game_pool_gift_relation as g
        where
        g.pool_id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item.jjcId}
        </foreach>
        and g.bind_gift_id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item.giftId}
        </foreach>
    </select>
    <select id="selectGamePoolGiftById" resultType="com.hzy.core.entity.GamePoolGiftRelationEntity">
        select g.id,
               g.bind_gift_id as bindGiftId,
               g.pool_id      as jjcId,
               g.num          as number,
               g.deleted
        from game_pool_gift_relation as g
        where g.id = #{id}
          and g.deleted = false
    </select>

</mapper>