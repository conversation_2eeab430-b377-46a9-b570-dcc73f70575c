<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppSignGoldConfigMapper">

    <resultMap type="AppSignGoldConfig" id="AppSignGoldConfigResult">
        <result property="id" column="id"/>
        <result property="weekName" column="week_name"/>
        <result property="sort" column="sort"/>
        <result property="goldNum" column="gold_num"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="selectAppSignGoldConfigVo">
        select id, week_name, sort, gold_num, create_time, update_time, create_by, update_by from app_sign_gold_config
    </sql>

    <select id="selectAppSignGoldConfigList" parameterType="AppSignGoldConfig" resultMap="AppSignGoldConfigResult">
        <include refid="selectAppSignGoldConfigVo"/>
        order by sort asc,id asc
    </select>

    <select id="selectAppSignGoldConfigById" parameterType="Long" resultMap="AppSignGoldConfigResult">
        <include refid="selectAppSignGoldConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppSignGoldConfig" parameterType="AppSignGoldConfig" useGeneratedKeys="true" keyProperty="id">
        insert into app_sign_gold_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="weekName != null">week_name,</if>
            <if test="sort != null">sort,</if>
            <if test="goldNum != null">gold_num,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="weekName != null">#{weekName},</if>
            <if test="sort != null">#{sort},</if>
            <if test="goldNum != null">#{goldNum},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateAppSignGoldConfig" parameterType="AppSignGoldConfig">
        update app_sign_gold_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="weekName != null">week_name = #{weekName},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="goldNum != null">gold_num = #{goldNum},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppSignGoldConfigById" parameterType="Long">
        delete from app_sign_gold_config where id = #{id}
    </delete>

    <delete id="deleteAppSignGoldConfigByIds" parameterType="String">
        delete from app_sign_gold_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getGoldNumByWeekName" resultType="java.math.BigDecimal" parameterType="java.lang.String">
        select gold_num from app_sign_gold_config where week_name=#{weekName} order by id desc
    </select>
</mapper>