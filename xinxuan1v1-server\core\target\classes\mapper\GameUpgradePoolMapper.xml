<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.GameUpgradePoolMapper">

    <resultMap type="AppMzlsUpgradeConfig" id="AppMzlsUpgradeConfigResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="minAmount" column="min_amount"/>
        <result property="maxAmount" column="max_amount"/>
        <result property="sumPut" column="sum_put"/>
        <result property="sumOut" column="sum_out"/>
        <result property="sumProfit" column="sum_profit"/>
        <result property="extractedNum" column="extracted_num"/>
        <result property="sumPutGl" column="sum_put_gl"/>
        <result property="sumOutGl" column="sum_out_gl"/>
        <result property="sumProfitGl" column="sum_profit_gl"/>
        <result property="sumTurnGl" column="sum_turn_gl"/>
        <result property="extractedNumGl" column="extracted_num_gl"/>
        <result property="isDel" column="deleted"/>
        <result property="minKzl" column="min_kzl"/>
        <result property="maxKzl" column="max_kzl"/>
        <result property="slcBl" column="slc_bl"/>
        <result property="mzlsJcType" column="mzls_jc_type"/>
    </resultMap>

    <sql id="selectAppMzlsUpgradeConfigVo">
        select id, name, min_amount, max_amount, sum_put, sum_out, sum_profit, extracted_num, sum_put_gl, sum_out_gl,
        sum_profit_gl, sum_turn_gl, extracted_num_gl, deleted, min_kzl, max_kzl, slc_bl, mzls_jc_type from
        game_upgrade_pool
    </sql>

    <delete id="deleteAppMzlsUpgradeConfigByIds" parameterType="String">
        delete from game_upgrade_pool where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="addSumPut">
        update game_upgrade_pool set sum_put=sum_put+#{price}
        where id=#{id}
    </update>

    <update id="addExtractedNum">
        update game_upgrade_pool set extracted_num=extracted_num+#{num}
        where id=#{id}
    </update>

    <update id="resetExtractedNum">
        update game_upgrade_pool set extracted_num=0
        where id=#{id}
    </update>

    <select id="getName" resultType="java.lang.String" parameterType="java.lang.Long">
        select name from game_upgrade_pool where id=#{id}
    </select>

    <update id="subSumProfitBl">
        update app_config set sum_profit_gl=sum_profit_gl-#{price}
    </update>
    <update id="increaseExtractNum">
        update game_upgrade_pool set extracted_num = extracted_num + 1
        where id = #{id}
    </update>
</mapper>