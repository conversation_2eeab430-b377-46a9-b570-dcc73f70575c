<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppRoomBroadcastMapper">
    
    <resultMap type="com.hzy.core.entity.AppRoomBroadcast" id="AppRoomBroadcastResult">
        <id property="id" column="id"/>
        <result property="roomId" column="room_id"/>
        <result property="userId" column="user_id"/>
        <result property="roomTheme" column="room_theme"/>
        <result property="roomName" column="room_name"/>
        <result property="roomRequirements" column="room_requirements" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="broadcastCost" column="broadcast_cost"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>
    
    <sql id="selectAppRoomBroadcastVo">
        SELECT id, room_id, user_id, room_theme, room_name, room_requirements, broadcast_cost,
               start_time, end_time, status, create_by, create_time, update_by, update_time, remark
        FROM app_room_broadcast
    </sql>
    
    <!-- 查询有效的广播消息列表 -->
    <select id="selectActiveBroadcasts" parameterType="java.util.Date" resultMap="AppRoomBroadcastResult">
        <include refid="selectAppRoomBroadcastVo"/>
        WHERE status = 1
        AND start_time &lt;= #{currentTime}
        AND end_time &gt; #{currentTime}
        ORDER BY create_time DESC
    </select>
    
    <!-- 更新过期的广播消息状态 -->
    <update id="updateExpiredBroadcasts">
        UPDATE app_room_broadcast
        SET status = #{expiredStatus},
            update_time = NOW()
        WHERE status = 1
        AND end_time &lt;= #{currentTime}
    </update>
    
    <!-- 根据用户ID查询该用户发布的广播消息 -->
    <select id="selectUserBroadcasts" parameterType="Long" resultMap="AppRoomBroadcastResult">
        <include refid="selectAppRoomBroadcastVo"/>
        WHERE user_id = #{userId}
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据房间ID查询有效的广播消息 -->
    <select id="selectActiveRoomBroadcast" resultMap="AppRoomBroadcastResult">
        <include refid="selectAppRoomBroadcastVo"/>
        WHERE room_id = #{roomId}
        AND status = 1
        AND start_time &lt;= #{currentTime}
        AND end_time &gt; #{currentTime}
        LIMIT 1
    </select>
    
</mapper> 