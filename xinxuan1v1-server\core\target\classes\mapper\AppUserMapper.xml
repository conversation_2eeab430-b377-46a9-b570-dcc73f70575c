<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppUserMapper">

    <resultMap type="AppUserEntity" id="AppUserResult">
        <result property="id" column="id"/>
        <result property="phone" column="phone"/>
        <result property="weixinOpenid" column="weixin_openid"/>
        <result property="userStatus" column="user_status"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedTime" column="updated_time"/>
        <result property="createdBy" column="created_by"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="lastLoginIp" column="last_login_ip"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="recodeCode" column="recode_code"/>
        <result property="channelCode" column="channel_code"/>
        <result property="upChannelCode" column="up_channel_code"/>
        <result property="token" column="token"/>
        <result property="password" column="password"/>
        <result property="cancellationTime" column="cancellation_time"/>
        <result property="equipmentType" column="equipment_type"/>
        <result property="pushId" column="push_id"/>
        <result property="equipmentName" column="equipment_name"/>
        <result property="lastOperatingTime" column="last_operating_time"/>
        <result property="qqOpenid" column="qq_openid"/>
        <result property="iosOpenid" column="ios_openid"/>
        <result property="isPerfectInfo" column="is_perfect_info"/>
        <result property="photoAlbum" column="photo_album"/>
        <result property="nickName" column="nick_name"/>
        <result property="birthday" column="birthday"/>
        <result property="age" column="age"/>
        <result property="sex" column="sex"/>
        <result property="personalSignature" column="personal_signature"/>
        <result property="headPortrait" column="head_portrait"/>
        <result property="perfectInfoTime" column="perfect_info_time"/>
        <result property="recodeUserId" column="recode_user_id"/>
        <result property="recodeCode" column="recode_code"/>
        <result property="educationBackground" column="education_background"/>
        <result property="constellation" column="constellation"/>
        <result property="height" column="height"/>
        <result property="weight" column="weight"/>
        <result property="location" column="location"/>
        <result property="annualIncome" column="annual_income"/>
        <result property="purchaseSituation" column="purchase_situation"/>
        <result property="carPurchaseSituation" column="car_purchase_situation"/>
        <result property="selfIntroduction" column="self_introduction"/>
        <result property="label" column="label"/>
        <result property="voiceSignature" column="voice_signature"/>
        <result property="video" column="video"/>
        <result property="goldBalance" column="gold_balance"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="area" column="area"/>
        <result property="isVirtual" column="is_virtual"/>
        <result property="isRealPersonAuth" column="is_real_person_auth"/>
        <result property="isRealNameAuth" column="is_real_name_auth"/>
        <result property="pointsBalance" column="points_balance"/>
        <result property="bindRecodeUserTime" column="bind_recode_user_time"/>
        <result property="realName" column="real_name"/>
        <result property="idNumber" column="id_number"/>
        <result property="isOnline" column="is_online"/>
        <result property="isGoodNumber" column="is_good_number"/>
        <result property="isTx" column="is_tx"/>
        <result property="toBeSettled" column="to_be_settled"/>
        <result property="canTransfer" column="can_transfer"/>
        <result property="isFreeVoice" column="is_free_voice"/>
    </resultMap>

    <sql id="selectAppUserVo">
        select id,
               phone,
               weixin_openid,
               user_status,
               created_time,
               updated_time,
               created_by,
               updated_by,
               last_login_ip,
               last_login_time,
               recode_code,
               channel_code,
               up_channel_code,
               token,
               password,
               cancellation_time,
               equipment_type,
               push_id,
               equipment_name,
               last_operating_time,
               qq_openid,
               ios_openid,
               is_perfect_info,
               photo_album,
               nick_name,
               birthday,
               age,
               sex,
               personal_signature,
               head_portrait,
               perfect_info_time,
               recode_user_id,
               education_background,
               constellation,
               height,
               weight,
               location,
               annual_income,
               purchase_situation,
               car_purchase_situation,
               self_introduction,
               label,
               voice_signature,
               video,
               gold_balance,
               candy_balance,
               longitude,
               latitude,
               province,
               city,
               area,
               is_virtual,
               is_real_person_auth,
               is_real_name_auth,
               points_balance,
               bind_recode_user_time,
               real_name,
               id_number,
               is_online,
               is_good_number,
               is_tx,
               is_vip,
               to_be_settled,
               can_transfer,
               is_free_voice,
               voice_check_status,
               video_check_status,
               authentication
        from app_user
    </sql>

    <sql id="maskingUserInformation">
        select id,
               phone,
               user_status,
               recode_code,
               channel_code,
               up_channel_code,
               equipment_type,
               push_id,
               equipment_name,
               last_operating_time,
               is_perfect_info,
               photo_album,
               nick_name,
               birthday,
               age,
               sex,
               personal_signature,
               head_portrait,
               perfect_info_time,
               recode_user_id,
               education_background,
               constellation,
               height,
               weight,
               location,
               annual_income,
               purchase_situation,
               car_purchase_situation,
               self_introduction,
               label,
               voice_signature,
               video,
               gold_balance,
               candy_balance,
               longitude,
               latitude,
               province,
               city,
               area,
               is_virtual,
               is_real_person_auth,
               is_real_name_auth,
               points_balance,
               bind_recode_user_time,
               is_online,
               is_good_number,
               is_tx,
               is_vip,
               to_be_settled,
               can_transfer,
               is_free_voice,
               voice_check_status,
               video_check_status,
               authentication
        from app_user
    </sql>

    <select id="selectAppUserList" parameterType="AppUserEntity" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        <where>
            <if test="phone != null  and phone != ''">and phone = #{phone}</if>
            <if test="weixinOpenid != null  and weixinOpenid != ''">and weixin_openid = #{weixinOpenid}</if>
            <if test="userStatus != null ">and user_status = #{userStatus}</if>
            <if test="lastLoginIp != null  and lastLoginIp != ''">and last_login_ip = #{lastLoginIp}</if>
            <if test="lastLoginTime != null ">and last_login_time = #{lastLoginTime}</if>
            <if test="recodeCode != null  and recodeCode != ''">and recode_code = #{recodeCode}</if>
            <if test="token != null  and token != ''">and token = #{token}</if>
            <if test="password != null  and password != ''">and password = #{password}</if>
            <if test="cancellationTime != null ">and cancellation_time = #{cancellationTime}</if>
            <if test="equipmentType != null ">and equipment_type = #{equipmentType}</if>
            <if test="pushId != null  and pushId != ''">and push_id = #{pushId}</if>
            <if test="equipmentName != null  and equipmentName != ''">and equipment_name like concat('%',
                #{equipmentName}, '%')
            </if>
            <if test="lastOperatingTime != null ">and last_operating_time = #{lastOperatingTime}</if>
            <if test="qqOpenid != null  and qqOpenid != ''">and qq_openid = #{qqOpenid}</if>
            <if test="iosOpenid != null  and iosOpenid != ''">and ios_openid = #{iosOpenid}</if>
            <if test="isPerfectInfo != null ">and is_perfect_info = #{isPerfectInfo}</if>
            <if test="photoAlbum != null  and photoAlbum != ''">and photo_album = #{photoAlbum}</if>
            <if test="nickName != null  and nickName != ''">and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="birthday != null  and birthday != ''">and birthday = #{birthday}</if>
            <if test="age != null ">and age = #{age}</if>
            <if test="sex != null ">and sex = #{sex}</if>
            <if test="personalSignature != null  and personalSignature != ''">and personal_signature =
                #{personalSignature}
            </if>
            <if test="headPortrait != null  and headPortrait != ''">and head_portrait = #{headPortrait}</if>
            <if test="perfectInfoTime != null ">and perfect_info_time = #{perfectInfoTime}</if>
            <if test="recodeUserId != null ">and recode_user_id = #{recodeUserId}</if>
            <if test="educationBackground != null  and educationBackground != ''">and education_background =
                #{educationBackground}
            </if>
            <if test="constellation != null  and constellation != ''">and constellation = #{constellation}</if>
            <if test="height != null  and height != ''">and height = #{height}</if>
            <if test="weight != null  and weight != ''">and weight = #{weight}</if>
            <if test="location != null  and location != ''">and location = #{location}</if>
            <if test="annualIncome != null  and annualIncome != ''">and annual_income = #{annualIncome}</if>
            <if test="purchaseSituation != null ">and purchase_situation = #{purchaseSituation}</if>
            <if test="carPurchaseSituation != null ">and car_purchase_situation = #{carPurchaseSituation}</if>
            <if test="selfIntroduction != null  and selfIntroduction != ''">and self_introduction =
                #{selfIntroduction}
            </if>
            <if test="label != null  and label != ''">and label = #{label}</if>
            <if test="voiceSignature != null  and voiceSignature != ''">and voice_signature = #{voiceSignature}</if>
            <if test="goldBalance != null ">and gold_balance = #{goldBalance}</if>
            <if test="candyBalance != null ">and candy_balance = #{candyBalance}</if>
            <if test="longitude != null  and longitude != ''">and longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''">and latitude = #{latitude}</if>
            <if test="province != null  and province != ''">and province = #{province}</if>
            <if test="city != null  and city != ''">and city = #{city}</if>
            <if test="area != null  and area != ''">and area = #{area}</if>
            <if test="isVirtual != null ">and is_virtual = #{isVirtual}</if>
            <if test="isRealPersonAuth != null ">and is_real_person_auth = #{isRealPersonAuth}</if>
            <if test="isRealNameAuth != null ">and is_real_name_auth = #{isRealNameAuth}</if>
            <if test="bindRecodeUserTime != null ">and bind_recode_user_time = #{bindRecodeUserTime}</if>
            <if test="realName != null  and realName != ''">and real_name like concat('%', #{realName}, '%')</if>
            <if test="idNumber != null  and idNumber != ''">and id_number = #{idNumber}</if>
            <if test="isOnline != null ">and is_online = #{isOnline}</if>
            <if test="isGoodNumber != null ">and is_good_number = #{isGoodNumber}</if>
            <if test="isTx != null ">and is_tx = #{isTx}</if>
            <if test="toBeSettled != null ">and to_be_settled = #{toBeSettled}</if>
            <if test="isFreeVoice != null ">and is_free_voice = #{isFreeVoice}</if>
        </where>
    </select>

    <select id="selectAppUserById" parameterType="Long" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where id = #{id}
    </select>
    <select id="selectMaskingAppUserById" parameterType="Long" resultMap="AppUserResult">
        <include refid="maskingUserInformation"/>
        where id = #{id}
    </select>

    <select id="selectAppUserByRecode" parameterType="java.lang.String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where recode_code = #{code}
        order by id desc limit 0,1
    </select>


    <select id="selectAppUserByUserId" parameterType="Long" resultType="AppUserEntity">
        select id
        from app_user
        where id = #{userId}
    </select>

    <insert id="insertAppUser" parameterType="AppUserEntity" useGeneratedKeys="true" keyProperty="id">
        insert into app_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="phone != null">phone,</if>
            <if test="weixinOpenid != null">weixin_openid,</if>
            <if test="userStatus != null">user_status,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="lastLoginIp != null">last_login_ip,</if>
            <if test="lastLoginTime != null">last_login_time,</if>
            <if test="recodeCode != null">recode_code,</if>
            <if test="channelCode != null">channel_code,</if>
            <if test="upChannelCode != null">up_channel_code,</if>
            <if test="token != null">token,</if>
            <if test="password != null">password,</if>
            <if test="cancellationTime != null">cancellation_time,</if>
            <if test="equipmentType != null">equipment_type,</if>
            <if test="pushId != null">push_id,</if>
            <if test="equipmentName != null">equipment_name,</if>
            <if test="lastOperatingTime != null">last_operating_time,</if>
            <if test="qqOpenid != null">qq_openid,</if>
            <if test="iosOpenid != null">ios_openid,</if>
            <if test="isPerfectInfo != null">is_perfect_info,</if>
            <if test="photoAlbum != null">photo_album,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="birthday != null">birthday,</if>
            <if test="age != null">age,</if>
            <if test="sex != null">sex,</if>
            <if test="personalSignature != null">personal_signature,</if>
            <if test="headPortrait != null">head_portrait,</if>
            <if test="perfectInfoTime != null">perfect_info_time,</if>
            <if test="recodeUserId != null">recode_user_id,</if>
            <if test="educationBackground != null">education_background,</if>
            <if test="constellation != null">constellation,</if>
            <if test="height != null">height,</if>
            <if test="weight != null">weight,</if>
            <if test="location != null">location,</if>
            <if test="annualIncome != null">annual_income,</if>
            <if test="purchaseSituation != null">purchase_situation,</if>
            <if test="carPurchaseSituation != null">car_purchase_situation,</if>
            <if test="selfIntroduction != null">self_introduction,</if>
            <if test="label != null">label,</if>
            <if test="voiceSignature != null">voice_signature,</if>
            <if test="video != null">video,</if>
            <if test="goldBalance != null">gold_balance,</if>
            <if test="candyBalance != null">candy_balance,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="area != null">area,</if>
            <if test="isVirtual != null">is_virtual,</if>
            <if test="isRealPersonAuth != null">is_real_person_auth,</if>
            <if test="isRealNameAuth != null">is_real_name_auth,</if>
            <if test="pointsBalance != null">points_balance,</if>
            <if test="bindRecodeUserTime != null">bind_recode_user_time,</if>
            <if test="realName != null">real_name,</if>
            <if test="idNumber != null">id_number,</if>
            <if test="isOnline != null">is_online,</if>
            <if test="isGoodNumber != null">is_good_number,</if>
            <if test="isTx != null">is_tx,</if>
            <if test="toBeSettled != null">to_be_settled,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="phone != null">#{phone},</if>
            <if test="weixinOpenid != null">#{weixinOpenid},</if>
            <if test="userStatus != null">#{userStatus},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="lastLoginIp != null">#{lastLoginIp},</if>
            <if test="lastLoginTime != null">#{lastLoginTime},</if>
            <if test="recodeCode != null">#{recodeCode},</if>
            <if test="channelCode != null">#{channelCode},</if>
            <if test="upChannelCode != null">#{upChannelCode},</if>
            <if test="token != null">#{token},</if>
            <if test="password != null">#{password},</if>
            <if test="cancellationTime != null">#{cancellationTime},</if>
            <if test="equipmentType != null">#{equipmentType},</if>
            <if test="pushId != null">#{pushId},</if>
            <if test="equipmentName != null">#{equipmentName},</if>
            <if test="lastOperatingTime != null">#{lastOperatingTime},</if>
            <if test="qqOpenid != null">#{qqOpenid},</if>
            <if test="iosOpenid != null">#{iosOpenid},</if>
            <if test="isPerfectInfo != null">#{isPerfectInfo},</if>
            <if test="photoAlbum != null">#{photoAlbum},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="birthday != null">#{birthday},</if>
            <if test="age != null">#{age},</if>
            <if test="sex != null">#{sex},</if>
            <if test="personalSignature != null">#{personalSignature},</if>
            <if test="headPortrait != null">#{headPortrait},</if>
            <if test="perfectInfoTime != null">#{perfectInfoTime},</if>
            <if test="recodeUserId != null">#{recodeUserId},</if>
            <if test="educationBackground != null">#{educationBackground},</if>
            <if test="constellation != null">#{constellation},</if>
            <if test="height != null">#{height},</if>
            <if test="weight != null">#{weight},</if>
            <if test="location != null">#{location},</if>
            <if test="annualIncome != null">#{annualIncome},</if>
            <if test="purchaseSituation != null">#{purchaseSituation},</if>
            <if test="carPurchaseSituation != null">#{carPurchaseSituation},</if>
            <if test="selfIntroduction != null">#{selfIntroduction},</if>
            <if test="label != null">#{label},</if>
            <if test="voiceSignature != null">#{voiceSignature},</if>
            <if test="video != null">#{video},</if>
            <if test="goldBalance != null">#{goldBalance},</if>
            <if test="candyBalance != null">#{candyBalance},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="area != null">#{area},</if>
            <if test="isVirtual != null">#{isVirtual},</if>
            <if test="isRealPersonAuth != null">#{isRealPersonAuth},</if>
            <if test="isRealNameAuth != null">#{isRealNameAuth},</if>
            <if test="pointsBalance != null">#{pointsBalance},</if>
            <if test="bindRecodeUserTime != null">#{bindRecodeUserTime},</if>
            <if test="realName != null">#{realName},</if>
            <if test="idNumber != null">#{idNumber},</if>
            <if test="isOnline != null">#{isOnline},</if>
            <if test="isGoodNumber != null">#{isGoodNumber},</if>
            <if test="isTx != null">#{isTx},</if>
            <if test="toBeSettled != null">#{toBeSettled},</if>
        </trim>
    </insert>

    <update id="updateAppUser" parameterType="AppUserEntity">
        update app_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="phone != null">phone = #{phone},</if>
            <if test="weixinOpenid != null">weixin_openid = #{weixinOpenid},</if>
            <if test="userStatus != null">user_status = #{userStatus},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            <if test="lastLoginIp != null">last_login_ip = #{lastLoginIp},</if>
            <if test="lastLoginTime != null">last_login_time = #{lastLoginTime},</if>
            <if test="recodeCode != null">recode_code = #{recodeCode},</if>
            <if test="channelCode != null">channel_code = #{channelCode},</if>
            <if test="upChannelCode != null">up_channel_code = #{upChannelCode},</if>
            <if test="token != null">token = #{token},</if>
            <if test="password != null">password = #{password},</if>
            <if test="cancellationTime != null">cancellation_time = #{cancellationTime},</if>
            <if test="equipmentType != null">equipment_type = #{equipmentType},</if>
            <if test="pushId != null">push_id = #{pushId},</if>
            <if test="equipmentName != null">equipment_name = #{equipmentName},</if>
            <if test="lastOperatingTime != null">last_operating_time = #{lastOperatingTime},</if>
            <if test="qqOpenid != null">qq_openid = #{qqOpenid},</if>
            <if test="iosOpenid != null">ios_openid = #{iosOpenid},</if>
            <if test="isPerfectInfo != null">is_perfect_info = #{isPerfectInfo},</if>
            <if test="photoAlbum != null">photo_album = #{photoAlbum},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="age != null">age = #{age},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="personalSignature != null">personal_signature = #{personalSignature},</if>
            <if test="headPortrait != null">head_portrait = #{headPortrait},</if>
            <if test="perfectInfoTime != null">perfect_info_time = #{perfectInfoTime},</if>
            <if test="recodeUserId != null">recode_user_id = #{recodeUserId},</if>
            <if test="educationBackground != null">education_background = #{educationBackground},</if>
            <if test="constellation != null">constellation = #{constellation},</if>
            <if test="height != null">height = #{height},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="location != null">location = #{location},</if>
            <if test="annualIncome != null">annual_income = #{annualIncome},</if>
            <if test="purchaseSituation != null">purchase_situation = #{purchaseSituation},</if>
            <if test="carPurchaseSituation != null">car_purchase_situation = #{carPurchaseSituation},</if>
            <if test="selfIntroduction != null">self_introduction = #{selfIntroduction},</if>
            <if test="label != null">label = #{label},</if>
            <if test="voiceSignature != null">voice_signature = #{voiceSignature},</if>
            <if test="voiceCheckStatus != null">voice_check_status = #{voiceCheckStatus},</if>
            <if test="video != null">video = #{video},</if>
            <if test="videoCheckStatus != null">video_check_status = #{videoCheckStatus},</if>
            <if test="authentication != null">authentication = #{authentication},</if>
            <if test="authenticationTime != null">authentication_time = #{authenticationTime},</if>
            <if test="goldBalance != null">gold_balance = #{goldBalance},</if>
            <if test="candyBalance != null">candy_balance = #{candyBalance},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="area != null">area = #{area},</if>
            <if test="isVirtual != null">is_virtual = #{isVirtual},</if>
            <if test="isRealPersonAuth != null">is_real_person_auth = #{isRealPersonAuth},</if>
            <if test="isRealNameAuth != null">is_real_name_auth = #{isRealNameAuth},</if>
            <if test="pointsBalance != null">points_balance = #{pointsBalance},</if>
            <if test="bindRecodeUserTime != null">bind_recode_user_time = #{bindRecodeUserTime},</if>
            <if test="realName != null">real_name = #{realName},</if>
            <if test="idNumber != null">id_number = #{idNumber},</if>
            <if test="isOnline != null">is_online = #{isOnline},</if>
            <if test="isGoodNumber != null">is_good_number = #{isGoodNumber},</if>
            <if test="isTx != null">is_tx = #{isTx},</if>
            <if test="toBeSettled != null">to_be_settled = #{toBeSettled},</if>
            <if test="isFreeVoice != null">is_free_voice = #{isFreeVoice},</if>
        </trim>
        where id = #{id}
    </update>


    <delete id="deleteAppUserById" parameterType="Long">
        delete
        from app_user
        where id = #{id}
    </delete>

    <delete id="deleteAppUserByIds" parameterType="String">
        delete from app_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectAppUserByPhone" parameterType="java.lang.String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where phone = #{phone}
        order by id desc limit 0,1
    </select>

    <select id="selectAppUserByWxOpenId" parameterType="java.lang.String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where weixin_openid = #{wxOpenId}
    </select>

    <select id="selectAppUserByQqOpenId" parameterType="java.lang.String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where qq_openid = #{qqOpenId}
    </select>

    <select id="selectAppUserByIosOpenId" parameterType="java.lang.String" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where ios_openid = #{iosOpenId}
    </select>

    <select id="getNickNameByUserId" resultType="java.lang.String" parameterType="java.lang.Long">
        select u.nick_name
        from app_user u
        where u.id = #{userId}
    </select>


    <select id="getUserInfoByMap" resultType="java.util.Map" parameterType="java.lang.Long">
        select u.nick_name as nickName, sex
        from app_user u
        where u.id = #{userId}
    </select>

    <resultMap type="com.hzy.core.model.vo.app.AppSearchUserInfoResultVo" id="searchUserInfoResultVo">
        <result property="userId" column="userId"/>
        <result property="recodeCode" column="recode_code"/>
        <result property="photoAlbumStr" column="photoAlbumStr"/>
        <result property="nickName" column="nick_name"/>
        <result property="birthday" column="birthday"/>
        <result property="age" column="age"/>
        <result property="sex" column="sex"/>
        <result property="personalSignature" column="personal_signature"/>
        <result property="headPortrait" column="head_portrait"/>
        <result property="educationBackground" column="education_background"/>
        <result property="constellation" column="constellation"/>
        <result property="height" column="height"/>
        <result property="weight" column="weight"/>
        <result property="location" column="location"/>
        <result property="annualIncome" column="annual_income"/>
        <result property="purchaseSituation" column="purchase_situation"/>
        <result property="carPurchaseSituation" column="car_purchase_situation"/>
        <result property="selfIntroduction" column="self_introduction"/>
        <result property="label" column="label"/>
        <result property="voiceSignature" column="voice_signature"/>
        <result property="video" column="video"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="area" column="area"/>
        <result property="lastOperatingTime" column="last_operating_time"/>
        <result property="distance" column="distance"/>
        <result property="createTime" column="createdTime"/>
        <result property="isVirtual" column="isVirtual"/>
        <result property="userPhone" column="userPhone"/>
        <result property="isRealPersonAuth" column="isRealPersonAuth"/>
        <result property="isRealNameAuth" column="isRealNameAuth"/>
        <result property="isPhoneAuth" column="isPhoneAuth"/>
        <result property="goldBalance" column="goldBalance"/>
        <result property="pointsBalance" column="pointsBalance"/>
        <result property="userStatus" column="userStatus"/>
        <result property="lastLoginIp" column="lastLoginIp"/>
        <result property="realName" column="realName"/>
        <result property="idNumber" column="idNumber"/>
        <result property="bindRecodeUserTime" column="bindRecodeUserTime"/>
        <result property="canTransfer" column="canTransfer"/>
        <result property="videoMinutesGold" column="videoMinutesGold"/>
    </resultMap>


    <select id="searchUser" resultMap="searchUserInfoResultVo"
            parameterType="com.hzy.core.model.vo.app.AppSearchUserParameterVo">
        select u.id as userId,u.channel_code,u.up_channel_code,
        u.is_real_person_auth as isRealPersonAuth,
        u.is_real_name_auth as isRealNameAuth,
        if(u.phone!=''and u.phone is not null,1,0) as isPhoneAuth,
        u.bind_recode_user_time as bindRecodeUserTime,
        u.gold_balance as goldBalance,
        u.candy_balance as candyBalance,
        u.points_balance as pointsBalance,
        u.user_status as userStatus,
        u.last_login_ip as lastLoginIp,
        u.recode_code,
        u.push_id as pushId,
        u.is_online as isOnline,
        u.last_operating_time,
        u.photo_album as photoAlbumStr,
        u.nick_name,
        u.birthday,
        u.age,
        u.sex,
        u.created_time as createdTime,
        u.phone as userPhone,
        u.real_name as realName,
        u.id_number as idNumber,
        u.personal_signature,
        u.head_portrait,
        u.education_background,
        u.constellation,
        u.height,
        u.weight,
        u.location,
        u.annual_income,
        u.purchase_situation,
        u.car_purchase_situation,
        u.self_introduction,
        u.label,
        u.is_tx as isTx,
        u.can_transfer as canTransfer,
        u.voice_signature,u.video,
        u.voice_check_status,
        u.video_check_status,
        u.authentication,
        u.longitude,
        u.latitude,
        u.province,
        (rand()+rand()) as sjpx,
        u.city,
        u.is_virtual as isVirtual,
        tc.video_minutes_gold as videoMinutesGold,
        <if test="keyword==null or keyword =='' ">
            <if test="null!=queryType and queryType==2">
                round(
                (
                6371 * acos(
                cos(radians(#{latitude}))
                * cos(radians(u.latitude))
                * cos(radians(u.longitude) - radians(#{longitude}))
                + sin(radians(#{latitude}))
                * sin(radians(u.latitude))
                )
                )
                , 2) as distance,
            </if>
        </if>
        u.area
        from app_user u
        left join app_user_communicate_telephone_config tc on u.id = tc.user_id
        where
        1=1
        <!--<if test="userId!=null">
            and u.id not in (select b.to_user_id from app_blacklist b where b.user_id = #{userId})
            and u.id not in (select b.user_id from app_blacklist b where b.to_user_id = #{userId})
            and user_status = true
        </if>-->
        <if test="channelCode != null and channelCode != ''">
            and u.channel_code = #{channelCode}
        </if>
        <if test="upChannelCode != null and upChannelCode != ''">
            and u.up_channel_code = #{upChannelCode}
        </if>
        <if test="userId != null">
            and u.id = #{userId}
        </if>
        <if test="lastLoginIp != null and lastLoginIp != ''">
            and u.last_login_ip like concat('%', #{lastLoginIp}, '%')
        </if>
        <if test="pushId != null and pushId != ''">
            and u.push_id = #{pushId}
        </if>
        <if test="userStatus!=null">
            and u.user_status=#{userStatus}
        </if>
        <if test="bindRecodeUserId!=null">
            and u.recode_user_id=#{bindRecodeUserId}
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(u.created_time,'%y%m%d%H%i%s') &gt;= date_format(#{queryBeginTime},'%y%m%d%H%i%s')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(u.created_time,'%y%m%d%H%i%s') &lt;= date_format(#{queryEndTime},'%y%m%d%H%i%s')
        </if>
        <if test="isQueryTopUp!=null">
            <if test="isQueryTopUp==1">
                and u.id in(select o.user_id from app_order o where o.order_status=1 and o.order_type=1 and
                o.is_del=false GROUP BY o.user_id)
            </if>
            <if test="isQueryTopUp==0">
                and u.id not in(select o.user_id from app_order o where o.order_status=1 and o.order_type=1 and
                o.is_del=false GROUP BY o.user_id)
            </if>
        </if>
        <if test="isVirtual!=null">
            and u.is_virtual=#{isVirtual}
        </if>
        <if test="isRealNameAuth==0">
            and u.is_real_name_auth=#{isRealNameAuth}
        </if>
        <if test="isRealNameAuth==1">
            and u.is_real_name_auth=#{isRealNameAuth}
        </if>
        <!--<if test="null!=serviceUserId">
            and u.id !=#{serviceUserId}
        </if>-->
        <if test="null!=sex">
            and u.sex=#{sex}
        </if>
        <if test="null!=location and location!=''">
            and u.location=#{location}
        </if>
        <if test="null!=educationBackground and educationBackground!=''">
            and u.education_background=#{educationBackground}
        </if>
        <if test="null!=ageMin">
            and u.age!=-1 and u.age>=#{ageMin}
        </if>
        <if test="null!=ageMax">
            and u.age!=-1 and u.age &lt;=#{ageMax}
        </if>
        <if test="null!=heightMin">
            and u.height is not null and u.height !='' and u.height >=#{heightMin}
        </if>
        <if test="null!=heightMax">
            and u.height is not null and u.height !='' and u.height &lt;=#{heightMax}
        </if>
        <if test="null!=weightMin">
            and u.weight is not null and u.weight !='' and u.weight >=#{weightMin}
        </if>
        <if test="null!=weightMax">
            and u.weight is not null and u.weight !='' and u.weight &lt;=#{weightMax}
        </if>
        <if test="null!=annualIncomeMin">
            and u.annual_income is not null and u.annual_income !='' and u.annual_income >=#{annualIncomeMin}
        </if>
        <if test="null!=annualIncomeMax">
            and u.annual_income is not null and u.annual_income !='' and u.annual_income &lt;=#{annualIncomeMax}
        </if>
        <if test="null!=purchaseSituation">
            and u.purchase_situation=#{purchaseSituation}
        </if>
        <if test="null!=carPurchaseSituation">
            and u.car_purchase_situation=#{carPurchaseSituation}
        </if>
        <if test="null!=userNickName and userNickName!=''">
            and u.nick_name like concat('%', #{userNickName}, '%')
        </if>
        <if test="null!=userPhone and userPhone!=''">
            and u.phone like concat('%', #{userPhone}, '%')
        </if>
        <if test="null!=recodeCode and recodeCode!=''">
            and u.recode_code like concat('%', #{recodeCode}, '%')
        </if>

        <if test="null!=isQueryOnline and isQueryOnline==1">
            and u.is_online=1
        </if>

        <if test="keyword!=null and keyword !='' ">
            and (u.recode_code like concat('%', #{keyword}, '%') or u.nick_name like concat('%', #{keyword}, '%'))
            order by u.id desc
        </if>
        <if test="keyword==null or keyword =='' ">
            <!--<if test="userId!=null">
                and u.id !=#{userId}
            </if>-->
            <if test="null!=queryType">
                <if test="queryType==1">
                    and u.created_time BETWEEN CURDATE() AND CURDATE() + INTERVAL 1 DAY
                    order by u.is_online desc,u.id desc
                </if>
                <if test="queryType==2">
                    and u.latitude is not null and u.latitude!='' and u.latitude>0
                    and u.longitude is not null and u.longitude!='' and u.longitude>0
                    order by u.is_online desc, distance asc,u.id desc
                </if>
                <if test="queryType==3">
                    order by u.is_online desc,sjpx, u.id
                </if>
                <if test="queryType==4">
                    order by u.is_online desc, sjpx, u.id
                </if>
            </if>
            <if test="orderType==0">
                order by u.gold_balance desc, u.id desc
            </if>
            <if test="orderType==1">
                order by u.gold_balance asc, u.id desc
            </if>
            <if test="orderType==2">
                order by u.points_balance desc, u.id desc
            </if>
            <if test="orderType==3">
                order by u.points_balance asc, u.id desc
            </if>
            <if test="null==queryType">
                <if test="orderType==null">
                    <if test="null!=isRealPersonOrderBy and isRealPersonOrderBy">
                        order by u.is_real_person_auth desc,u.id desc
                    </if>
                    <if test="null==isRealPersonOrderBy or !isRealPersonOrderBy">
                        order by u.id desc
                    </if>
                </if>
            </if>
        </if>
    </select>

    <select id="getBalanceTotal" resultType="com.hzy.core.model.vo.admin.AppUserBalanceSummaryVO"
            parameterType="com.hzy.core.model.dto.AppSearchUserParameterDTO">
        select
        IFNULL(sum(u.gold_balance),0) as goldBalanceTotal,
        IFNULL(sum(u.points_balance),0) as pointsBalanceTotal
        from app_user u
        where
        1=1
        <if test="isRealNameAuth==0">
            and is_real_name_auth=#{isRealNameAuth}
        </if>
        <if test="isRealNameAuth==1">
            and is_real_name_auth=#{isRealNameAuth}
        </if>
        <if test="null!=recodeCode and recodeCode!=''">
            and recode_code like concat('%', #{recodeCode}, '%')
        </if>
        <if test="null!=userPhone and userPhone!=''">
            and phone like concat('%', #{userPhone}, '%')
        </if>
        <if test="null!=userNickName and userNickName!=''">
            and nick_name like concat('%', #{userNickName}, '%')
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(u.created_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(u.created_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        <if test="null!=sex">
            and sex=#{sex}
        </if>
        <if test="userId!=null">
            and u.id=#{userId}
        </if>
        <if test="userStatus!=null">
            and user_status=#{userStatus}
        </if>
        <if test="isQueryTopUp!=null">
            <if test="isQueryTopUp==1">
                and u.id in(select o.user_id from app_order o where o.order_status=1 and o.order_type=1 and
                o.is_del=false GROUP BY o.user_id)
            </if>
            <if test="isQueryTopUp==0">
                and u.id not in(select o.user_id from app_order o where o.order_status=1 and o.order_type=1 and
                o.is_del=false GROUP BY o.user_id)
            </if>
        </if>
        <if test="isVirtual!=null">
            and is_virtual=#{isVirtual}
        </if>
        <if test="null==queryType">
            <if test="orderType==null">
                <if test="null!=isRealPersonOrderBy and isRealPersonOrderBy">
                    order by is_real_person_auth desc,id desc
                </if>
                <if test="null==isRealPersonOrderBy or !isRealPersonOrderBy">
                    order by id desc
                </if>

            </if>
            <if test="orderType==0">
                order by gold_balance desc,id desc
            </if>
            <if test="orderType==1">
                order by gold_balance asc,id desc
            </if>
            <if test="orderType==2">
                order by points_balance desc,id desc
            </if>
            <if test="orderType==3">
                order by points_balance asc,id desc
            </if>
        </if>



    </select>

    <select id="randomGetOneVirtualUser" parameterType="Long" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where is_virtual=true
        and user_status=true
        and id not in(select r.virtual_user_id from app_send_virtual_msg_records r where r.virtual_user_id=app_user.id
        and r.receive_user_id=#{truthUserId})
        <if test="null!=querySex">
            and sex=#{querySex}
        </if>
        and app_user.id not in (select b.to_user_id from app_blacklist b where b.user_id = #{truthUserId})
        and app_user.id not in (select b.user_id from app_blacklist b where b.to_user_id = #{truthUserId})
        order by rand() limit 0,1
    </select>

    <update id="addUserGoldBalance">
        update app_user
        set gold_balance=gold_balance + #{goldBalance}
        where id = #{userId}
    </update>

    <update id="subtractUserGoldBalance">
        update app_user
        set gold_balance=gold_balance - #{goldBalance}
        where id = #{userId}
          and gold_balance >= #{goldBalance}
    </update>

    <update id="addUserCandyBalance">
        update app_user
        set candy_balance=candy_balance + #{candyBalance}
        where id = #{userId}
    </update>

    <update id="subtractUserCandyBalance">
        update app_user
        set candy_balance=candy_balance - #{candyBalance}
        where id = #{userId}
          and candy_balance >= #{candyBalance}
    </update>

    <update id="subUserGoldBalance">
        update app_user
        set gold_balance=if((gold_balance - #{goldBalance}) >= 0, (gold_balance - #{goldBalance}), 0)
        where id = #{userId}
    </update>

    <update id="addUserPointsBalance">
        update app_user
        set points_balance=points_balance + #{pointsBalance}
        where id = #{userId}
    </update>
    <update id="addUserPointsBalanceByChannelCode">
        update app_user
        set points_balance = points_balance + #{pointsBalance}
        where channel_code = #{channelCode}
    </update>

    <update id="subtractUserPointsBalance">
        update app_user
        set points_balance=points_balance - #{pointsBalance}
        where id = #{userId}
          and points_balance >= #{pointsBalance}
    </update>

    <!--<select id="getLeaderboardList" resultType="com.hzy.core.model.vo.app.AppLeaderboardResultVo"
            parameterType="com.hzy.core.model.vo.app.AppLeaderboardParameterVo">
        select u.id as userId,
        u.recode_code as recodeCode,
        u.is_real_person_auth as isRealPersonAuth,
        u.is_real_name_auth as isRealNameAuth,
        if(u.phone!=''and u.phone is not null,1,0) as isPhoneAuth,
        u.last_operating_time as lastOperatingTime,
        u.photo_album as photoAlbumStr,
        u.nick_name as nickName,
        u.is_online as isOnline,
        u.phone as userPhone,
        u.birthday,
        u.age,
        u.sex,
        u.personal_signature as personalSignature,
        u.head_portrait as headPortrait,
        u.education_background as educationBackground,
        u.constellation,
        u.height,
        u.weight,
        u.location,
        u.annual_income as annualIncome,
        u.purchase_situation as purchaseSituation,
        u.car_purchase_situation as carPurchaseSituation,
        u.self_introduction as selfIntroduction,
        u.label,
        u.voice_signature as voiceSignature,
        u.longitude,
        u.latitude,
        u.province,
        u.city,
        <if test="queryType==0">
            ifnull(sum(bill.to_user_charm_value),0) as charmValue,
        </if>
        <if test="queryType==1">
            ifnull(sum(bill.user_contribution_value),0) as charmValue,
        </if>
        u.area
        from
        app_user_gold_bill bill
        <if test="queryType==0">
            LEFT JOIN app_user u on(bill.object_id=u.id)
        </if>
        <if test="queryType==1">
            LEFT JOIN app_user u on(bill.user_id=u.id)
        </if>
        where 1=1

        <if test="chatRoomId!=null and chatRoomId>0">
            and bill.chat_room_id=#{chatRoomId}
        </if>
        <if test="dateQueryType==1">
            and bill.create_time BETWEEN CURDATE() AND CURDATE() + INTERVAL 1 DAY
        </if>
        <if test="dateQueryType==2">
            and bill.create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="dateQueryType==3">
            and bill.create_time BETWEEN DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') AND LAST_DAY(CURRENT_DATE)
        </if>
        <if test="queryType==0">
            and 1=if(bill.bill_type=2 or bill.bill_type=21,1,0)
            and 1=if(bill.to_user_charm_value>0,1,0)
        </if>
        <if test="queryType==1">
            and 1=if(bill.bill_type=2 or bill.bill_type=21,1,0)
            and 1=if(bill.user_contribution_value >0,1,0)
        </if>
        group by u.id
        order by charmValue desc
        limit 0,100
    </select>-->

    <select id="getLeaderboardList" resultType="com.hzy.core.model.vo.app.AppLeaderboardResultVo"
            parameterType="com.hzy.core.model.vo.app.AppLeaderboardParameterVo">
        select u.id as userId,
        u.recode_code as recodeCode,
        u.is_real_person_auth as isRealPersonAuth,
        u.is_real_name_auth as isRealNameAuth,
        if(u.phone!=''and u.phone is not null,1,0) as isPhoneAuth,
        u.last_operating_time as lastOperatingTime,
        u.photo_album as photoAlbumStr,
        u.nick_name as nickName,
        u.is_online as isOnline,
        u.phone as userPhone,
        u.birthday,
        u.age,
        u.sex,
        u.personal_signature as personalSignature,
        u.head_portrait as headPortrait,
        u.education_background as educationBackground,
        u.constellation,
        u.height,
        u.weight,
        u.location,
        u.annual_income as annualIncome,
        u.purchase_situation as purchaseSituation,
        u.car_purchase_situation as carPurchaseSituation,
        u.self_introduction as selfIntroduction,
        u.label,
        u.voice_signature as voiceSignature,
        u.video,
        u.longitude,
        u.latitude,
        u.province,
        u.city,
        <if test="queryType==0">
            ifnull(sum(bill.gift_gold_total_amount),0) as charmValue,
            abs(sum(bill.gift_gold_total_amount) - LAG(sum(bill.gift_gold_total_amount), 1, 0) OVER (ORDER BY sum(bill.gift_gold_total_amount) DESC)) AS diff,
        </if>
        <if test="queryType==1">
            ifnull(sum(bill.gift_gold_total_amount),0) as charmValue,
            abs(sum(bill.gift_gold_total_amount) - LAG(sum(bill.gift_gold_total_amount), 1, 0) OVER (ORDER BY sum(bill.gift_gold_total_amount) DESC)) AS diff,
        </if>
        u.area
        from
        -- app_user_gold_bill bill
        user_give_gift bill
        <if test="queryType==0">
            LEFT JOIN app_user u on(bill.to_user_id=u.id)
        </if>
        <if test="queryType==1">
            LEFT JOIN app_user u on(bill.user_id=u.id)
        </if>
        where 1=1

        <if test="chatRoomId!=null and chatRoomId>0">
            and bill.chat_room_id=#{chatRoomId}
        </if>
        <if test="dateQueryType==1">
            and bill.created_time BETWEEN CURDATE() AND CURDATE() + INTERVAL 1 DAY
        </if>
        <if test="dateQueryType==2">
            and bill.created_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.created_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="dateQueryType==3">
            and bill.created_time BETWEEN DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') AND LAST_DAY(CURRENT_DATE)
        </if>

        group by u.id
        order by charmValue desc
        limit 0,100
    </select>


    <select id="getLeaderboardListWithCall" resultType="com.hzy.core.model.vo.app.AppLeaderboardResultVo"
            parameterType="com.hzy.core.model.vo.app.AppLeaderboardParameterVo">
        select u.id as userId,
               u.recode_code as recodeCode,
               u.is_real_person_auth as isRealPersonAuth,
               u.is_real_name_auth as isRealNameAuth,
               if(u.phone!=''and u.phone is not null,1,0) as isPhoneAuth,
               u.last_operating_time as lastOperatingTime,
               u.photo_album as photoAlbumStr,
               u.nick_name as nickName,
               u.is_online as isOnline,
               u.phone as userPhone,
               u.birthday,
               u.age,
               u.sex,
               u.personal_signature as personalSignature,
               u.head_portrait as headPortrait,
               u.education_background as educationBackground,
               u.constellation,
               u.height,
               u.weight,
               u.location,
               u.annual_income as annualIncome,
               u.purchase_situation as purchaseSituation,
               u.car_purchase_situation as carPurchaseSituation,
               u.self_introduction as selfIntroduction,
               u.label,
               u.voice_signature as voiceSignature,
               u.video,
               u.longitude,
               u.latitude,
               u.province,
               u.city,
               <if test="queryType==0">
                   (IFNULL(gift_sum.total_amount, 0) + IFNULL(call_sum.total_amount, 0)) as charmValue,
                   ABS((IFNULL(gift_sum.total_amount, 0) + IFNULL(call_sum.total_amount, 0)) -
                       LAG(IFNULL(gift_sum.total_amount, 0) + IFNULL(call_sum.total_amount, 0), 1, 0)
                       OVER (ORDER BY (IFNULL(gift_sum.total_amount, 0) + IFNULL(call_sum.total_amount, 0)) DESC)) AS diff,
               </if>
               <if test="queryType==1">
                   (IFNULL(gift_sum.total_amount, 0) + IFNULL(call_sum.total_amount, 0)) as charmValue,
                   ABS((IFNULL(gift_sum.total_amount, 0) + IFNULL(call_sum.total_amount, 0)) -
                       LAG(IFNULL(gift_sum.total_amount, 0) + IFNULL(call_sum.total_amount, 0), 1, 0)
                       OVER (ORDER BY (IFNULL(gift_sum.total_amount, 0) + IFNULL(call_sum.total_amount, 0)) DESC)) AS diff,
               </if>
               u.area
        from app_user u
        LEFT JOIN (
            SELECT
                <if test="queryType==0">
                    to_user_id as user_id,
                </if>
                <if test="queryType==1">
                    user_id,
                </if>
                SUM(gift_gold_total_amount) as total_amount
            FROM user_give_gift
            WHERE 1=1
            <if test="chatRoomId!=null and chatRoomId>0">
                AND chat_room_id=#{chatRoomId}
            </if>
            <if test="dateQueryType==1">
                AND created_time BETWEEN CURDATE() AND CURDATE() + INTERVAL 1 DAY
            </if>
            <if test="dateQueryType==2">
                AND created_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
                AND created_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
            </if>
            <if test="dateQueryType==3">
                AND created_time BETWEEN DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') AND LAST_DAY(CURRENT_DATE)
            </if>
            GROUP BY
            <if test="queryType==0">
                to_user_id
            </if>
            <if test="queryType==1">
                user_id
            </if>
        ) gift_sum ON u.id = gift_sum.user_id
        LEFT JOIN (
            SELECT
                <if test="queryType==0">
                    receive_user_id as user_id,
                </if>
                <if test="queryType==1">
                    initiate_user_id as user_id,
                </if>
                SUM(consumption_gold) as total_amount
            FROM app_communicate_telephone_records
            WHERE status = 3
            <if test="chatRoomId!=null and chatRoomId>0">
                AND chat_room_id=#{chatRoomId}
            </if>
            <if test="dateQueryType==1">
                AND create_time BETWEEN CURDATE() AND CURDATE() + INTERVAL 1 DAY
            </if>
            <if test="dateQueryType==2">
                AND create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
                AND create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
            </if>
            <if test="dateQueryType==3">
                AND create_time BETWEEN DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') AND LAST_DAY(CURRENT_DATE)
            </if>
            GROUP BY
            <if test="queryType==0">
                receive_user_id
            </if>
            <if test="queryType==1">
                initiate_user_id
            </if>
        ) call_sum ON u.id = call_sum.user_id
        WHERE (gift_sum.total_amount > 0 OR call_sum.total_amount > 0)
        ORDER BY charmValue DESC
        LIMIT 0,100
    </select>

    <select id="randomGetThreeUserList" resultType="com.hzy.core.model.vo.app.AppViewUserInfoVo"
            parameterType="java.lang.Long">
        SELECT u.id                                            AS userId,
               u.is_online                                     as isOnline,
               u.recode_code                                   AS recodeCode,
               u.last_operating_time                           AS lastOperatingTime,
               u.photo_album                                   AS photoAlbumStr,
               u.nick_name                                     AS nickName,
               u.is_real_person_auth                           as isRealPersonAuth,
               u.is_real_name_auth                             as isRealNameAuth,
               if(u.phone != '' and u.phone is not null, 1, 0) as isPhoneAuth,
               u.birthday,
               u.age,
               u.sex,
               u.personal_signature                            AS personalSignature,
               u.head_portrait                                 AS headPortrait,
               u.education_background                          AS educationBackground,
               u.constellation,
               u.height,
               u.weight,
               u.location,
               u.annual_income                                 AS annualIncome,
               u.purchase_situation                            AS purchaseSituation,
               u.car_purchase_situation                        AS carPurchaseSituation,
               u.self_introduction                             AS selfIntroduction,
               u.label,
               u.voice_signature                               AS voiceSignature,
               u.video,
               u.longitude,
               u.latitude,
               u.province,
               u.city,
               u.area
        FROM app_user u
        WHERE u.id != #{userId}
          AND u.nick_name is not null
          AND u.nick_name != ''
          AND u.head_portrait is not null
          AND u.head_portrait != ''
          AND u.user_status = true
          AND u.is_perfect_info = true
          AND u.id NOT IN (SELECT b.to_user_id FROM app_blacklist b WHERE b.user_id = #{userId})
          AND u.id NOT IN (SELECT b.user_id FROM app_blacklist b WHERE b.to_user_id = #{userId})
          AND u.id NOT IN (SELECT uin.to_user_id FROM app_user_intimacy uin WHERE uin.user_id = #{userId})
          AND u.id NOT IN (SELECT uin.user_id FROM app_user_intimacy uin WHERE uin.to_user_id = #{userId})
          AND u.id NOT IN (SELECT uf.be_user_id FROM app_user_follow uf WHERE uf.user_id = #{userId})
          AND u.id NOT IN (SELECT uf.user_id FROM app_user_follow uf WHERE uf.be_user_id = #{userId})
          AND 1 = (SELECT if((count(DISTINCT cr1.id))
                                 > 0
                              , 0
                              , 1)
                   FROM app_chatting_records cr1
                      , app_chatting_records cr2
                   WHERE cr1.send_user_id = cr2.receive_user_id
                     AND cr1.receive_user_id = cr2.send_user_id
                     AND cr1.is_family = false
                     AND cr2.is_family = false
                     AND cr1.send_user_id = #{userId}
                     AND cr2.send_user_id = u.id)
        ORDER BY rand()
        LIMIT 0, 3
    </select>


    <select id="getUserProtectionResultList" resultType="com.hzy.core.model.vo.app.AppUserProtectionResultVo"
            parameterType="java.lang.Long">
        select u.id                                                                 as userId,
               u.recode_code                                                        as recodeCode,
               u.is_online                                                          as isOnline,
               u.is_real_person_auth                                                as isRealPersonAuth,
               u.is_real_name_auth                                                  as isRealNameAuth,
               if(u.phone != '' and u.phone is not null, 1, 0)                      as isPhoneAuth,
               u.last_operating_time                                                as lastOperatingTime,
               u.photo_album                                                        as photoAlbumStr,
               u.nick_name                                                          as nickName,
               u.phone                                                              as userPhone,
               u.birthday,
               u.age,
               u.sex,
               u.personal_signature                                                 as personalSignature,
               u.head_portrait                                                      as headPortrait,
               u.education_background                                               as educationBackground,
               u.constellation,
               u.height,
               u.weight,
               u.location,
               u.annual_income                                                      as annualIncome,
               u.purchase_situation                                                 as purchaseSituation,
               u.car_purchase_situation                                             as carPurchaseSituation,
               u.self_introduction                                                  as selfIntroduction,
               u.label,
               u.voice_signature                                                    as voiceSignature,
               u.video,
               u.longitude,
               u.latitude,
               u.province,
               u.city,
               ifnull(sum(abs(bill.amount) + abs(bill.user_contribution_value)), 0) as contributionValue,
               u.area
        from app_user_gold_bill bill
                 LEFT JOIN app_user u on (bill.user_id = u.id)
        where bill.object_id = #{userId}
          and 1 = if(bill.bill_type = 2 or bill.bill_type = 21, 1, 0)
        group by bill.user_id
        order by contributionValue desc
        limit 0,100
    </select>


    <select id="getUserRecordsUserList" resultType="com.hzy.core.model.vo.app.AppViewUserInfoVo"
            parameterType="java.lang.Long">
        SELECT u.id                                            AS userId,
               u.is_online                                     as isOnline,
               u.recode_code                                   AS recodeCode,
               u.last_operating_time                           AS lastOperatingTime,
               u.photo_album                                   AS photoAlbumStr,
               u.nick_name                                     AS nickName,
               u.is_real_person_auth                           as isRealPersonAuth,
               u.is_real_name_auth                             as isRealNameAuth,
               if(u.phone != '' and u.phone is not null, 1, 0) as isPhoneAuth,
               u.birthday,
               u.age,
               u.sex,
               u.personal_signature                            AS personalSignature,
               u.head_portrait                                 AS headPortrait,
               u.education_background                          AS educationBackground,
               u.constellation,
               u.height,
               u.weight,
               u.location,
               u.annual_income                                 AS annualIncome,
               u.purchase_situation                            AS purchaseSituation,
               u.car_purchase_situation                        AS carPurchaseSituation,
               u.self_introduction                             AS selfIntroduction,
               u.label,
               u.voice_signature                               AS voiceSignature,
               u.video,
               u.longitude,
               u.latitude,
               u.province,
               u.city,
               u.area,
               u.bind_recode_user_time                         as subjoinParameter
        FROM app_user u
        WHERE u.recode_user_id = #{userId}
        ORDER BY u.bind_recode_user_time desc, u.id desc
    </select>


    <select id="getUserInviteEarningsList" resultType="com.hzy.core.model.vo.app.AppUserInviteEarningsVo"
            parameterType="java.lang.Long">
        SELECT u.id             AS userId,
               u.nick_name      AS nickName,
               u.head_portrait  AS headPortrait,
               bill.amount      as earningsNum,
               bill.create_time as earningsTime
        FROM app_user u,
             app_user_gold_bill bill
        WHERE bill.user_id = #{userId}
          and bill.object_id = u.id
          and bill.bill_type = 15
        order by bill.id desc
    </select>


    <select id="getUserInviteGiftEarningsList" resultType="com.hzy.core.model.vo.app.AppUserInviteEarningsVo"
            parameterType="java.lang.Long">
        SELECT u.id             AS userId,
               u.nick_name      AS nickName,
               u.head_portrait  AS headPortrait,
               bill.amount      as earningsNum,
               bill.create_time as earningsTime
        FROM app_user u,
             app_user_points_bill bill
        WHERE bill.user_id = #{userId}
          and bill.object_id = u.id
          and bill.bill_type = 13
        order by bill.create_time desc, bill.id desc
    </select>

    <select id="getUserInviteTopUpEarningsList" resultType="com.hzy.core.model.vo.app.AppUserInviteEarningsVo"
            parameterType="java.lang.Long">
        SELECT u.id             AS userId,
               u.nick_name      AS nickName,
               u.head_portrait  AS headPortrait,
               bill.amount      as earningsNum,
               bill.create_time as earningsTime
        FROM app_user u,
             app_user_points_bill bill
        WHERE bill.user_id = #{userId}
          and bill.object_id = u.id
          and bill.bill_type = 14
        order by bill.create_time desc, bill.id desc
    </select>


    <select id="getToDayInviteEarningsSumCount" resultType="java.lang.Integer"
            parameterType="java.lang.Long">
        SELECT count(*)
        FROM app_user u,
             app_user_gold_bill bill
        WHERE bill.object_id = u.id
          and bill.bill_type = 15
          and to_days(bill.create_time) = to_days(now())
    </select>


    <update id="updUserLoginInfo">
        update app_user
        set last_login_ip=#{lastLoginIp},
            last_login_time=#{lastLoginTime},
            location = #{location}
        where id = #{userId}
    </update>

    <select id="getAllUserIdList" resultType="java.lang.Long">
        select id
        from app_user
        order by id desc
    </select>

    <select id="getUserIdByAuth" resultType="Integer" parameterType="Long">
        select is_real_name_auth as isRealNameAuthfrom app_user
        where id = #{userId}
    </select>

    <select id="getIdNumberAuthCount" resultType="java.lang.Integer" parameterType="java.lang.String">
        select count(*)
        from app_user
        where id_number = #{idNumber}
    </select>


    <update id="updUserOnlineStatus">
        update app_user
        set is_online=#{isOnline}
        where id = #{userId}
    </update>

    <select id="checkReCodeIsExist" resultType="java.lang.Boolean" parameterType="java.lang.String">
        select if(count(*) > 0, 1, 0)
        from app_user
        where recode_code = #{reCode}
    </select>

    <select id="getUserIdByRecode" resultType="java.lang.Long" parameterType="java.lang.String">
        select id
        from app_user
        where recode_code = #{recode}
        order by id desc
        limit 0,1
    </select>

    <select id="getUserIdByChannelCode" resultType="java.lang.Long" parameterType="java.lang.String">
        select id
        from app_user
        where channel_code = #{channelCode}
        order by id desc
        limit 0,1
    </select>

    <select id="getUserId" resultType="java.lang.Long" parameterType="java.lang.Long">
        select id
        from app_user
        where id = #{userId}
        order by id desc
        limit 0,1
    </select>

    <select id="getUserHeadPortrait" resultType="java.lang.String">
        select head_portrait from app_user where id = #{userId}
    </select>

    <select id="getUserInfoByChattingRecords" resultType="java.util.Map" parameterType="java.lang.Long">
        select nick_name as nickName, recode_code as recodeCode, head_portrait as headPortrait
        from app_user
        where id = #{userId}
    </select>

    <update id="addToBeSettled">
        update app_user
        set to_be_settled=to_be_settled + #{val}
        where id = #{id}
    </update>

    <update id="subToBeSettled">
        update app_user
        set to_be_settled=to_be_settled - #{val}
        where id = #{id}
    </update>
    <update id="bannedUser" parameterType="java.lang.Long">
        update app_user
        set user_status=4
        where id = #{userId}
    </update>
    <update id="setOnlineStatus">
        update app_user
        set is_online=1, updated_time=now()
        where id in
        <foreach collection="userIdList" item="userId" index="index" open="(" separator="," close=")">
                #{userId}
        </foreach>
    </update>
    <update id="setOfflineStatusExcept">
        update app_user
        set is_online=0, updated_time=now()
        where  is_virtual =0 and is_online =1
        <if test="userIdList != null and userIdList.size() > 0">
            and id not in
            <foreach collection="userIdList" item="userId" index="index" open="(" separator="," close=")">
                    #{userId}
            </foreach>
        </if>
    </update>

    <select id="getAllToBeSettledUserList" resultMap="AppUserResult">
        <include refid="selectAppUserVo"/>
        where to_be_settled>0
        order by id desc
    </select>

    <select id="getUserInfoMyMap" resultType="java.util.Map" parameterType="java.lang.Long">
        select id                        as userId,
               recode_code               as recodeCode,
               nick_name                 as nickName,
               birthday                  as birthday,
               age                       as age,
               sex                       as sex,
               head_portrait             as headPortrait,
               CAST(is_online AS SIGNED) as isOnline
        from app_user
        where id = #{userId}
    </select>
    <select id="selectAppUserByRecodeCode" resultType="com.hzy.core.entity.AppUserEntity">
        select *
        from app_user
        where recode_code = #{recodeCode}
    </select>
    <select id="selectAppUser" resultType="com.hzy.core.entity.AppUserEntity">
        select * from app_user au
        where 1=1
        <if test="keyWord!=null and keyWord!=''">
            and (au.recode_code like concat('%', #{keyWord}, '%') or au.nick_name like concat('%', #{keyWord}, '%'))
        </if>
    </select>

    <sql id="selectAppUserVo2">
        select au.id,
               phone,
               weixin_openid,
               user_status,
               created_time,
               updated_time,
               created_by,
               updated_by,
               last_login_ip,
               last_login_time,
               recode_code,
               channel_code,
               up_channel_code,
               cancellation_time,
               equipment_type,
               push_id,
               equipment_name,
               last_operating_time,
               qq_openid,
               ios_openid,
               is_perfect_info,
               photo_album,
               nick_name,
               birthday,
               age,
               sex,
               personal_signature,
               head_portrait,
               perfect_info_time,
               recode_user_id,
               education_background,
               constellation,
               height,
               weight,
               location,
               annual_income,
               purchase_situation,
               car_purchase_situation,
               self_introduction,
               label,
               voice_signature,
               video,
               gold_balance,
               longitude,
               latitude,
               province,
               city,
               area,
               is_virtual,
               is_real_person_auth,
               is_real_name_auth,
               points_balance,
               bind_recode_user_time,
               real_name,
               id_number,
               is_online,
               is_good_number,
               is_tx,
               to_be_settled,
               can_transfer,
               is_free_voice,
               video_check_status,
               voice_check_status,
               cf.voice_minutes_gold,
               cf.video_minutes_gold,
               cf.is_enable_video,
               cf.is_enable_voice,
               CASE
                 WHEN TIMESTAMPDIFF(MINUTE, updated_time, NOW()) &lt;= 59
                 THEN CONCAT(TIMESTAMPDIFF(MINUTE, updated_time, NOW()), '分钟前')
                 ELSE NULL
               END as offlineTime
        FROM
        app_user au
    </sql>

    <select id="getHomeGuildUserList" resultType="com.hzy.core.entity.AppUserEntity" parameterType="java.lang.Long">
        <include refid="selectAppUserVo2" />
        JOIN
        app_user_communicate_telephone_config cf ON au.id=cf.user_id
        <where>
            <if test="sex != null">
                and au.sex = #{sex}
            </if>
            <if test="age != null">
                and au.age &gt;= #{age}
            </if>
            <if test="age2 != null">
                and au.age &lt;= #{age2}
            </if>
            <if test="label != null">
                and au.label like CONCAT('%', #{label}, '%')
            </if>
            and au.is_virtual = 0
            and au.is_online = 1
            and au.id != #{userId}
            and au.nick_name not like '%客服%'
        </where>
    </select>

    <select id="getHomeUserList" resultType="com.hzy.core.entity.AppUserEntity" parameterType="java.lang.Long">
        <include refid="selectAppUserVo2" />
        JOIN app_user_communicate_telephone_config cf ON au.id=cf.user_id
        LEFT JOIN (
            SELECT
                receive_user_id,
                IFNULL(SUM(CASE WHEN type in (1,3) THEN consumption_gold ELSE 0 END), 0) as voice_amount,
                IFNULL(SUM(CASE WHEN type in (2,4) THEN consumption_gold ELSE 0 END), 0) as video_amount,
                IFNULL(SUM(consumption_gold), 0) as total_call_amount
            FROM app_communicate_telephone_records
            WHERE status = 3
            GROUP BY receive_user_id
        ) call_stats ON au.id = call_stats.receive_user_id
        <where>
            and au.is_virtual = 0
            and au.user_status = 1
            and au.id != #{userId}
            and au.nick_name != "客服"
            <if test="sex != null and sex != 2">
                and au.sex = #{sex}
            </if>
            <if test="age != null">
                and au.age &gt;= #{age}
            </if>
            <if test="age2 != null">
                and au.age &lt;= #{age2}
            </if>
            <if test="label != null">
                and au.label like CONCAT('%', #{label}, '%')
            </if>
            <if test="tab == 1">
            and cf.is_enable_video = 1
            and au.is_real_name_auth = 1
            </if>
            <if test="tab == 2">
            and cf.is_enable_voice = 1
            and au.is_real_name_auth = 1
            </if>
        </where>
        <if test="tab == 1">
            ORDER BY au.is_online DESC,
            case
                when au.is_online = true then au.authentication
                else 0
            end DESC,
            case
                when au.is_online = true then au.video_check_status
                else au.updated_time
            end DESC
        </if>
        <if test="tab == 2">
            ORDER BY au.is_online DESC,
            case
                when au.is_online = true then au.authentication
                else 0
            end DESC,
            case
                when au.is_online = true then au.voice_check_status
                else au.updated_time
            end DESC
        </if>
        <if test="tab == 3">
            ORDER BY au.is_online DESC,
            case
                when au.is_online = true then au.created_time
                when au.is_online = false then au.updated_time
            end DESC
        </if>
        <if test="tab == 4">
            ORDER BY au.is_online DESC,
            case
                when au.is_online = false then au.updated_time
            end DESC
        </if>
    </select>

    <select id="isExistByDeviceId" resultType="java.lang.String" parameterType="java.lang.String">
        select up_channel_code
        from app_user
        where push_id = #{equipmentId}
        limit 0,1
    </select>
    <select id="isExistByNiceName" resultType="java.lang.Boolean">
        select if(count(*) &gt; 0, 1, 0)
        from app_user
        where nick_name = #{nickName}
        and id != #{userId}
    </select>
    <select id="getAllRandomUserList" resultType="com.hzy.core.entity.AppUserEntity">
        select au.id,
               au.nick_name,
               au.created_time
        from app_user au
        WHERE au.user_status = 1
          AND au.sex != 0
          AND au.id != #{receiveUserId}
          AND au.is_virtual = 0
          AND au.is_online = 1
          AND au.nick_name not like '%客服%'
    </select>

    <select id="getAuthorizedUser" resultType="com.hzy.core.entity.AppUserEntity">
        select id, nick_name, recode_code, head_portrait, phone, sex, authentication_time
        from app_user
        <where>
            authentication = 1
            <if test="recodeCode != null and recodeCode != ''">
                and recode_code = #{recodeCode}
            </if>
            <if test="startTime != null and startTime != ''">
                and date_format(authentication_time,'%y%m%d') &gt;= date_format(#{startTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''">
                and date_format(authentication_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
        </where>
        order by authentication_time desc
    </select>
    <select id="getTodayRegisterUserList" resultType="com.hzy.core.model.vo.app.AppUserInfoVo">
        SELECT
            id,
            nick_name as nickName,
            recode_code as recodeCode,
            phone,
            sex,
            head_portrait as headPortrait,
            location,
            up_channel_code,
            channel_code,
            created_time as createTime
        FROM
            app_user
        <where>
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                and created_time &gt;= #{startTime}
                and created_time &lt;= #{endTime}
            </if>
            <if test="recodeCode != null and recodeCode != ''">
                and recode_code = #{recodeCode}
            </if>
            <if test="channelType != null and channelType == 'empty'">
                and (up_channel_code = '--' or up_channel_code is null)
            </if>
            <if test="channelType != null and channelType == 'nonempty'">
                and up_channel_code != '--' 
                and up_channel_code is not null
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <update id="setVipExpiredStatus">
        update app_user
        set is_vip = false, updated_time = now()
        where is_vip = true and id in
        <foreach collection="userIdList" item="userId" index="index" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>

    <select id="getRegisterPayTotalAmount" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(o.order_price), 0)
        FROM
        app_user u
        INNER JOIN
        app_order o ON u.id = o.user_id
        <where>
            o.is_del = false
            and o.order_status = 1
            and o.order_type = 1
            and to_days(u.created_time) = to_days(o.create_time)
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                and u.created_time &gt;= #{startTime}
                and u.created_time &lt;= #{endTime}
            </if>
            <if test="recodeCode != null and recodeCode != ''">
                and u.recode_code = #{recodeCode}
            </if>
            <if test="channelType != null and channelType == 'empty'">
                and (u.up_channel_code = '--' or u.up_channel_code is null)
            </if>
            <if test="channelType != null and channelType == 'nonempty'">
                and u.up_channel_code != '--'
                and u.up_channel_code is not null
            </if>
        </where>
    </select>

    <select id="getRegisterPayUserList" resultType="com.hzy.core.model.vo.app.AppUserInfoVo">
        SELECT
        u.id as userId,
        u.nick_name as nickName,
        u.recode_code as recodeCode,
        u.phone,
        u.sex,
        u.head_portrait as headPortrait,
        u.location,
        u.up_channel_code as upChannelCode,
        u.channel_code as channelCode,
        u.created_time as createTime,
        o.create_time as payTime,
        o.order_price as payAmount
        FROM
        app_user u
        INNER JOIN
        app_order o ON u.id = o.user_id
        <where>
            o.is_del = false
            and o.order_status = 1
            and o.order_type = 1
            and to_days(u.created_time) = to_days(o.create_time)
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                and u.created_time &gt;= #{startTime}
                and u.created_time &lt;= #{endTime}
            </if>
            <if test="recodeCode != null and recodeCode != ''">
                and u.recode_code = #{recodeCode}
            </if>
            <if test="channelType != null and channelType == 'empty'">
                and (u.up_channel_code = '--' or u.up_channel_code is null)
            </if>
            <if test="channelType != null and channelType == 'nonempty'">
                and u.up_channel_code != '--'
                and u.up_channel_code is not null
            </if>
        </where>
        <if test="sortField == 1">
            <if test="sortOrder == 1">
                ORDER BY o.create_time
            </if>
            <if test="sortOrder == 2">
                ORDER BY o.create_time desc
            </if>
        </if>
        <if test="sortField == 2">
            <if test="sortOrder == 1">
                ORDER BY u.created_time
            </if>
            <if test="sortOrder == 2">
                ORDER BY u.created_time desc
            </if>
        </if>

    </select>

    <select id="getRandomlyUsers" resultType="com.hzy.core.entity.AppUserEntity">
        select id, nick_name, head_portrait
        from app_user
        where sex = #{sex}
        and is_virtual = 0
        and nick_name not like '%客服%'
        and user_status = 1
        order by is_online desc, rand()
        limit 6
    </select>

    <select id="getChatUpList" resultType="com.hzy.core.entity.AppUserEntity">
        select au.id, au.nick_name, au.head_portrait, au.personal_signature, au.sex, au.age
        from app_user au
        left join (select receive_user_id
                   from app_vip_chat_record
                   where send_user_id = #{userId}) c on au.id = c.receive_user_id
        where au.sex = #{sex}
        and au.is_virtual = 0
        and au.user_status = 1
        and au.age != -1
        and c.receive_user_id is NULL
        and au.nick_name not like '%客服%'
        order by au.is_online desc, au.authentication desc, au.updated_time desc
    </select>


</mapper>
