<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppDressUpChestConfigMapper">

    <resultMap type="AppDressUpChestConfig" id="AppDressUpChestConfigResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
        <result property="ratio" column="ratio"/>
        <result property="chestId" column="chest_id"/>
        <result property="dressUpId" column="dress_up_id"/>
    </resultMap>

    <sql id="selectAppDressUpChestConfigVo">
        select id, create_time, update_time, create_by, update_by, is_del, ratio, chest_id, dress_up_id from
        app_dress_up_chest_config
    </sql>


    <select id="getPrizePoolList" parameterType="java.lang.Long"
            resultType="com.hzy.core.model.vo.app.AppOpenDressUpVo">
        select
        c.dress_up_id as dressUpId,
        d.name,
        d.img_url as imgUrl,
        d.gif_url as gifUrl,
        d.valid_days as validDays,
        d.color_value as colorValue,
        d.category_id as categoryId,
        d.gold_price as goldPrice
        from
        app_dress_up_chest_config c,
        app_personal_dressing d
        where c.is_del=false
        and c.chest_id = #{chestId}
        and c.dress_up_id = d.id
        order by d.gold_price asc, c.id desc
    </select>


    <select id="getListByChestId" parameterType="java.lang.Long"
            resultType="com.hzy.core.model.vo.app.AppOpenDressUpVo">
        select
        c.ratio,
        c.dress_up_id as dressUpId,
        d.name,
        d.img_url as imgUrl,
        d.gif_url as gifUrl,
        d.valid_days as validDays,
        d.color_value as colorValue,
        d.category_id as categoryId,
        d.gold_price as goldPrice
        from
        app_dress_up_chest_config c,
        app_personal_dressing d
        where c.is_del=false
        and c.chest_id = #{chestId}
        and c.dress_up_id = d.id
        order by c.id desc
    </select>

    <select id="selectAppDressUpChestConfigList" parameterType="AppDressUpChestConfig"
            resultMap="AppDressUpChestConfigResult">
        select
        c.id,
        c.create_time,
        c.update_time,
        c.create_by,
        c.update_by,
        c.is_del,
        c.ratio,
        c.chest_id,
        c.dress_up_id,
        d.name,
        d.img_url as imgUrl,
        d.gif_url as gifUrl,
        d.valid_days as validDays,
        d.color_value as colorValue,
        d.category_id as categoryId,
        d.gold_price as goldPrice
        from
        app_dress_up_chest_config c,
        app_personal_dressing d
        where c.is_del=false
        and c.chest_id = #{chestId}
        and c.dress_up_id = d.id
        <if test="name != null  and name != ''">and d.name like concat('%', #{name}, '%')</if>
        <if test="categoryId != null ">and d.category_id = #{categoryId}</if>
        order by c.id desc
    </select>

    <select id="selectAppDressUpChestConfigById" parameterType="Long" resultMap="AppDressUpChestConfigResult">
        <include refid="selectAppDressUpChestConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppDressUpChestConfig" parameterType="AppDressUpChestConfig" useGeneratedKeys="true"
            keyProperty="id">
        insert into app_dress_up_chest_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
            <if test="ratio != null">ratio,</if>
            <if test="chestId != null">chest_id,</if>
            <if test="dressUpId != null">dress_up_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="ratio != null">#{ratio},</if>
            <if test="chestId != null">#{chestId},</if>
            <if test="dressUpId != null">#{dressUpId},</if>
        </trim>
    </insert>

    <update id="updateAppDressUpChestConfig" parameterType="AppDressUpChestConfig">
        update app_dress_up_chest_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="ratio != null">ratio = #{ratio},</if>
            <if test="chestId != null">chest_id = #{chestId},</if>
            <if test="dressUpId != null">dress_up_id = #{dressUpId},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteAppDressUpChestConfigById" parameterType="Long">
        update app_dress_up_chest_config set is_del=true where id = #{id}
    </update>

    <delete id="deleteAppDressUpChestConfigByIds" parameterType="String">
        delete from app_dress_up_chest_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>