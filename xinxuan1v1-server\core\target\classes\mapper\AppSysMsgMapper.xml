<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppSysMsgMapper">

    <resultMap type="AppSysMsg" id="AppSysMsgResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="content" column="content"/>
        <result property="title" column="title"/>
        <result property="isDel" column="is_del"/>
        <result property="isRead" column="is_read"/>
        <result property="msgType" column="msg_type"/>
        <result property="objectId" column="object_id"/>
        <result property="taskId" column="task_id"/>
    </resultMap>

    <sql id="selectAppSysMsgVo">
        select id,
        user_id,
        create_time,
        update_time,
        create_by,
        update_by,
        content,
        title,
        is_del,
        is_read,
        msg_type,
        object_id,
        task_id
        from app_sys_msg
    </sql>

    <select id="selectAppSysMsgList" parameterType="AppSysMsg" resultMap="AppSysMsgResult">
        <include refid="selectAppSysMsgVo"/>
        where is_del=false and msg_type =1
        and object_id is null
        group by task_id
        order by create_time asc
    </select>

    <select id="selectAppSysMsgById" parameterType="Long" resultMap="AppSysMsgResult">
        <include refid="selectAppSysMsgVo"/>
        where id = #{id} and is_read=false
    </select>

    <insert id="insertAppSysMsg" parameterType="AppSysMsg" useGeneratedKeys="true" keyProperty="id">
        insert into app_sys_msg
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="content != null">content,</if>
            <if test="title != null">title,</if>
            <if test="isDel != null">is_del,</if>
            <if test="isRead != null">is_read,</if>
            <if test="msgType != null">msg_type,</if>
            <if test="objectId != null">object_id,</if>
            <if test="taskId != null">task_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="content != null">#{content},</if>
            <if test="title != null">#{title},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="isRead != null">#{isRead},</if>
            <if test="msgType != null">#{msgType},</if>
            <if test="objectId != null">#{objectId},</if>
            <if test="taskId != null">#{taskId},</if>
        </trim>
    </insert>

    <update id="updateAppSysMsg" parameterType="AppSysMsg">
        update app_sys_msg
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="content != null">content = #{content},</if>
            <if test="title != null">title = #{title},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="isRead != null">is_read = #{isRead},</if>
            <if test="msgType != null">msg_type = #{msgType},</if>
            <if test="objectId != null">object_id = #{objectId},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppSysMsgByTaskId" parameterType="java.lang.String">
        delete
        from app_sys_msg
        where task_id = #{taskId} and msg_type=1
    </delete>

    <delete id="deleteAppSysMsgByIds" parameterType="String">
        delete from app_sys_msg where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="setMsgRead" parameterType="java.lang.Long">
        update app_sys_msg
        set is_read= true
        where is_read = false
        and is_del = false
        and id = #{id}
    </update>

    <select id="getUserSysMsgNoReadCount" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(*)
        from app_sys_msg
        where is_read = false
        and is_del = false
        and user_id = #{userId}
    </select>

    <select id="getUserAppSysMsgList" parameterType="java.lang.Long" resultType="com.hzy.core.model.vo.app.AppSysMsgVo">
        select id as msgId,
        content,
        title,
        is_read as isRead,
        msg_type as msgType,
        create_time as createTime,
        object_id as objectId
        from app_sys_msg
        where is_del = false
        and user_id = #{userId}
        order by create_time desc
    </select>

    <select id="getUserAppSysMsgDetails" parameterType="java.lang.Long"
            resultType="com.hzy.core.model.vo.app.AppSysMsgVo">
        select id as msgId,
        content,
        title,
        is_read as isRead,
        msg_type as msgType,
        create_time as createTime,
        object_id as objectId
        from app_sys_msg
        where is_del = false
        and user_id = #{userId}
        and id = #{msgId}
    </select>
</mapper>