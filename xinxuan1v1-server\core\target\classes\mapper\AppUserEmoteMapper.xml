<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppUserEmoteMapper">

    <resultMap type="AppUserEmote" id="AppUserEmoteResult">
        <result property="id" column="id"/>
        <result property="url" column="url"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="userId" column="user_id"/>
    </resultMap>

    <sql id="selectAppUserEmoteVo">
        select id, url, create_time, update_time, create_by, update_by, user_id from app_user_emote
    </sql>

    <select id="selectAppUserEmoteList" parameterType="AppUserEmote" resultMap="AppUserEmoteResult">
        <include refid="selectAppUserEmoteVo"/>
        <where>
            <if test="url != null  and url != ''">and url = #{url}</if>
            <if test="userId != null ">and user_id = #{userId}</if>
        </where>
    </select>

    <select id="selectAppUserEmoteById" parameterType="Long" resultMap="AppUserEmoteResult">
        <include refid="selectAppUserEmoteVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppUserEmote" parameterType="AppUserEmote" useGeneratedKeys="true" keyProperty="id">
        insert into app_user_emote
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="url != null">url,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="userId != null">user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="url != null">#{url},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="userId != null">#{userId},</if>
        </trim>
    </insert>

    <update id="updateAppUserEmote" parameterType="AppUserEmote">
        update app_user_emote
        <trim prefix="SET" suffixOverrides=",">
            <if test="url != null">url = #{url},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="userId != null">user_id = #{userId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppUserEmoteById" parameterType="Long">
        delete from app_user_emote where id = #{id}
    </delete>

    <delete id="deleteAppUserEmoteByIds" parameterType="String">
        delete from app_user_emote where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getUserEmoteList" resultType="com.hzy.core.model.vo.app.AppUserEmoteVo">
        select
        id as userEmoteId,
        url
        from app_user_emote
        where user_id=#{userId}
        order by id desc
    </select>


    <select id="getUserEmoteIdByUserIdAndUrl" resultType="java.lang.Long">
        select
        id
        from app_user_emote
        where user_id=#{userId}
        and url=#{url}
        order by id desc limit 0,1
    </select>

    <select id="getUserEmoteCount" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select
        count(*)
        from app_user_emote
        where user_id=#{userId}
    </select>

    <select id="selectAppUserList" parameterType="com.hzy.core.model.dto.admin.AppUserEmoteDTO"
            resultType="com.hzy.core.model.vo.app.AppUserEmoteVo">
        select au.id AS id,
        au.url AS url,
        au.user_id userId,
        au.create_time as createTime,
        u.nick_name as username,
        u.recode_code as recodeCode
        from app_user_emote au
        left join app_user u ON u.id = au.user_id
        <where>
            <if test="userId != null ">and au.user_id = #{userId}</if>
            <if test="recodeCode != null ">and u.recode_code = #{recodeCode}</if>
        </where>
    </select>

    <delete id="deleteAppUserEmoteByIdAndUserId" parameterType="Long">
        delete from app_user_emote where user_id=#{userId}
        and id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>