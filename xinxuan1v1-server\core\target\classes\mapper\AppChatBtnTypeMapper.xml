<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppChatBtnTypeMapper">

    <resultMap type="AppChatBtnType" id="AppChatBtnTypeResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="toUserId" column="to_user_id"/>
        <result property="btnType" column="btn_type"/>
    </resultMap>

    <sql id="selectAppChatBtnTypeVo">
        select id, user_id, to_user_id, btn_type from app_chat_btn_type
    </sql>

    <select id="selectAppChatBtnTypeList" parameterType="AppChatBtnType" resultMap="AppChatBtnTypeResult">
        <include refid="selectAppChatBtnTypeVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="toUserId != null ">and to_user_id = #{toUserId}</if>
            <if test="btnType != null ">and btn_type = #{btnType}</if>
        </where>
    </select>

    <select id="selectAppChatBtnTypeById" parameterType="Long" resultMap="AppChatBtnTypeResult">
        <include refid="selectAppChatBtnTypeVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppChatBtnType" parameterType="AppChatBtnType" useGeneratedKeys="true" keyProperty="id">
        insert into app_chat_btn_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="toUserId != null">to_user_id,</if>
            <if test="btnType != null">btn_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="toUserId != null">#{toUserId},</if>
            <if test="btnType != null">#{btnType},</if>
        </trim>
    </insert>

    <update id="updateAppChatBtnType" parameterType="AppChatBtnType">
        update app_chat_btn_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="toUserId != null">to_user_id = #{toUserId},</if>
            <if test="btnType != null">btn_type = #{btnType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppChatBtnTypeById" parameterType="Long">
        delete from app_chat_btn_type where id = #{id}
    </delete>

    <delete id="deleteAppChatBtnTypeByIds" parameterType="String">
        delete from app_chat_btn_type where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectAppChatBtnTypeByUserIdAndToUserId" parameterType="java.lang.Long"
            resultMap="AppChatBtnTypeResult">
        <include refid="selectAppChatBtnTypeVo"/>
        where user_id = #{userId} and to_user_id=#{toUserId}
        order by id desc limit 0,1
    </select>

    <select id="getChatBtnType" parameterType="java.lang.Long" resultMap="AppChatBtnTypeResult">
        <include refid="selectAppChatBtnTypeVo"/>
        where ( (user_id = #{userId} and to_user_id=#{toUserId}) or (user_id = #{toUserId} and to_user_id=#{userId}) )
        order by id desc limit 0,1
    </select>
</mapper>