<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppFamilyMemberMapper">

    <resultMap type="AppFamilyMember" id="AppFamilyMemberResult">
        <result property="id" column="id"/>
        <result property="familyId" column="family_id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="lastDynamicTime" column="last_dynamic_time"/>
    </resultMap>

    <sql id="selectAppFamilyMemberVo">
        select id,
        family_id,
        user_id,
        create_time,
        update_time,
        create_by,
        update_by,
        last_dynamic_time
        from app_family_member
    </sql>

    <select id="selectAppFamilyMemberList" parameterType="AppFamilyMember" resultMap="AppFamilyMemberResult">
        <include refid="selectAppFamilyMemberVo"/>
        <where>
            <if test="familyId != null ">and family_id = #{familyId}</if>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="lastDynamicTime != null ">and last_dynamic_time = #{lastDynamicTime}</if>
        </where>
        order create_time asc
    </select>

    <select id="selectAppFamilyMemberById" parameterType="Long" resultMap="AppFamilyMemberResult">
        <include refid="selectAppFamilyMemberVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppFamilyMember" parameterType="AppFamilyMember" useGeneratedKeys="true" keyProperty="id">
        insert into app_family_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="familyId != null">family_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="lastDynamicTime != null">last_dynamic_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="familyId != null">#{familyId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="lastDynamicTime != null">#{lastDynamicTime},</if>
        </trim>
    </insert>

    <update id="updateAppFamilyMember" parameterType="AppFamilyMember">
        update app_family_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="familyId != null">family_id = #{familyId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="lastDynamicTime != null">last_dynamic_time = #{lastDynamicTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppFamilyMemberById" parameterType="Long">
        delete
        from app_family_member
        where id = #{id}
    </delete>

    <delete id="deleteAppFamilyMemberByIds" parameterType="String">
        delete from app_family_member where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getFamilyAllMemberUserIds" parameterType="java.lang.Long" resultType="java.lang.Long">
        select user_id
        from app_family_member
        where family_id = #{familyId}
    </select>

    <select id="isJoinByFamilyIdAndUserId" parameterType="java.lang.Long" resultType="java.lang.Boolean">
        select if(count(*) > 0, true, false)
        from app_family_member
        where family_id = #{familyId}
        and user_id = #{userId}
    </select>

    <select id="selectAppFamilyMemberByFamilyIdAndUserId" parameterType="java.lang.Long"
            resultMap="AppFamilyMemberResult">
        <include refid="selectAppFamilyMemberVo"/>
        where family_id = #{familyId}
        and user_id=#{userId}
    </select>


    <select id="getFamilyMemberList" resultType="com.hzy.core.model.vo.app.AppViewUserInfoVo">
        select DISTINCT u.id as userId,
        u.phone as phone,
        u.recode_code as recodeCode,
        u.nick_name as nickName,
        u.birthday as birthday,
        u.is_real_person_auth as isRealPersonAuth,
        u.is_real_name_auth as isRealNameAuth,
        if(u.phone!=''and u.phone is not null,1,0) as isPhoneAuth,
        u.age as age,
        u.sex as sex,
        u.personal_signature as personalSignature,
        u.head_portrait as headPortrait
        from
        app_family f,
        app_user u
        left JOIN app_family_member fm on(fm.user_id=u.id and fm.family_id = #{familyId})
        where (u.id = fm.user_id or f.user_id=u.id)
        and f.is_del=false
        and f.id=#{familyId}
        <if test="keyword!=null and keyword !='' ">
            and (u.phone like concat('%', #{keyword}, '%') or u.recode_code like concat('%', #{keyword}, '%') or
            u.nick_name like concat('%', #{keyword}, '%'))
        </if>
        order by fm.create_time asc
    </select>

    <delete id="delByFamilyIdAndUserId" parameterType="java.lang.Long">
        delete
        from app_family_member
        where family_id = #{familyId}
        and user_id = #{userId}
    </delete>

    <delete id="delByFamilyId" parameterType="java.lang.Long">
        delete
        from app_family_member
        where family_id = #{familyId}
    </delete>
</mapper>