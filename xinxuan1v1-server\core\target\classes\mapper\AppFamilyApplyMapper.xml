<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppFamilyApplyMapper">

    <resultMap type="AppFamilyApply" id="AppFamilyApplyResult">
        <result property="id" column="id"/>
        <result property="initiateUserId" column="initiate_user_id"/>
        <result property="familyId" column="family_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="remarks" column="remarks"/>
    </resultMap>

    <sql id="selectAppFamilyApplyVo">
        select id, initiate_user_id, family_id, create_time, update_time, status, remarks
        from app_family_apply
    </sql>

    <select id="selectAppFamilyApplyList" parameterType="AppFamilyApply" resultMap="AppFamilyApplyResult">
        <include refid="selectAppFamilyApplyVo"/>
        <where>
            <if test="initiateUserId != null ">and initiate_user_id = #{initiateUserId}</if>
            <if test="familyId != null ">and family_id = #{familyId}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="remarks != null  and remarks != ''">and remarks = #{remarks}</if>
        </where>
    </select>

    <select id="selectAppFamilyApplyById" parameterType="Long" resultMap="AppFamilyApplyResult">
        <include refid="selectAppFamilyApplyVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppFamilyApply" parameterType="AppFamilyApply" useGeneratedKeys="true" keyProperty="id">
        insert into app_family_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="initiateUserId != null">initiate_user_id,</if>
            <if test="familyId != null">family_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="remarks != null">remarks,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="initiateUserId != null">#{initiateUserId},</if>
            <if test="familyId != null">#{familyId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="remarks != null">#{remarks},</if>
        </trim>
    </insert>

    <update id="updateAppFamilyApply" parameterType="AppFamilyApply">
        update app_family_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="initiateUserId != null">initiate_user_id = #{initiateUserId},</if>
            <if test="familyId != null">family_id = #{familyId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppFamilyApplyById" parameterType="Long">
        delete
        from app_family_apply
        where id = #{id}
    </delete>

    <delete id="deleteAppFamilyApplyByIds" parameterType="String">
        delete from app_family_apply where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectAppFamilyApplyByUserIdAndFamilyId" parameterType="Long" resultMap="AppFamilyApplyResult">
        <include refid="selectAppFamilyApplyVo"/>
        where initiate_user_id = #{userId} and family_id=#{familyId}
    </select>


    <select id="getFamilyJoinApplyList" resultType="com.hzy.core.model.vo.app.AppFamilyJoinApplyListVo"
            parameterType="java.lang.Long">
        select fa.id as applyId,
        fa.initiate_user_id as initiateUserId,
        initiateUser.nick_name as initiateUserNickName,
        if(initiateUser.sex=-1,null,initiateUser.sex) as initiateUserSex,
        initiateUser.head_portrait as initiateUserHeadPortrait,
        fa.status as `status`,
        initiateUser.is_real_person_auth as isRealPersonAuth,
        initiateUser.is_real_name_auth as isRealNameAuth,
        if(initiateUser.phone!=''and initiateUser.phone is not null,1,0) as isPhoneAuth,
        fa.remarks as remarks
        from app_family_apply fa,
        app_user initiateUser
        where fa.initiate_user_id = initiateUser.id
        and initiateUser.user_status = true
        and fa.family_id = (select f.id
        from app_family f
        where f.user_id = #{userId}
        and f.is_del = false
        order by f.create_time desc limit 0, 1
        )
        order by fa.create_time desc
    </select>

    <delete id="delFamilyApplyByFamilyId" parameterType="java.lang.Long">
        delete from app_family_apply where family_id=#{familyId}
    </delete>

    <delete id="delFamilyApplyByFamilyIdAndUserId" parameterType="java.lang.Long">
        delete from app_family_apply where family_id=#{familyId} and initiate_user_id=#{userId}
    </delete>
</mapper>