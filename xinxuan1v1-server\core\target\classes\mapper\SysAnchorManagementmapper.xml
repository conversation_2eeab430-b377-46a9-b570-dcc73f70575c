<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.SysAnchorManagementMapper">
	<insert id="insertModifyRecord">
        INSERT INTO sys_anchor_commission (user_id, nick_name, old_commission_rate, new_commission_rate, created_by,
                                           created_time, updated_time,guild_id)
        values (#{userId}, #{nickName}, #{oldCommissionRate}, #{newCommissionRate}, #{createdBy}, #{createdTime},
                #{updatedTime},#{guildId})
    </insert>


	<update id="updateAnchorBroker" parameterType="com.hzy.core.entity.AppAnchorEntity">
		update app_guild_member
		set sys_user_id = #{brokerId}
		where user_id = #{userId}
		<if test="guildId !=null">
			and guild_id = #{guildId}
		</if>
	</update>
	<update id="updateAnchorSelfLifting" parameterType="com.hzy.core.model.vo.admin.AppAnchorVo">
		UPDATE app_chat_room acr
			JOIN (
			SELECT app_guild_member.guild_id,app_guild_member.user_id
			FROM app_guild_member
			WHERE guild_id = #{guildId}
			) agm ON acr.hall_owner_user_id = agm.user_id
			SET acr.hall_owner_commission_scale = #{anchorSelfLifting}
		WHERE acr.hall_owner_user_id = #{userId}
		  AND agm.guild_id = #{guildId};
    </update>

	<select id="getAnchorList" resultType="com.hzy.core.model.vo.admin.AppAnchorVo"
            parameterType="com.hzy.core.entity.AppAnchorEntity">
		SELECT
		au.id as userId,
		agm.create_time as createTime,
		agm.gift_income_scale as anchorSelfLifting,
		au.is_tx,
		au.nick_name ,
		su.nick_name as brokerName,
		acr.type as anchorType,
		au.recode_code as recodeCode,
		agm.guild_id as guildId,
		ag.guild_name as guildName,
		acr.third_id as thirdId
		FROM
		app_user au
		LEFT JOIN
		app_guild_member agm ON au.id=agm.user_id
		LEFT JOIN
		app_chat_room acr ON acr.hall_owner_user_id =au.id
		LEFT JOIN
		sys_user su on su.user_id = agm.sys_user_id
		left join
		app_guild ag on ag.id = agm.guild_id
		<where>
			<if test="userId!=null">
				and au.id = #{userId}
			</if>
		    <if test="recodeCode!=null">
				and au.recode_code = #{recodeCode}
			</if>
			<if test="anchorType!=null">
				and acr.type = #{anchorType}
			</if>
			<if test="guildId !=null">
				and agm.guild_id = #{guildId}
			</if>
			<if test="guildIds != null">
				<choose>
					<when test="guildIds.size() != 0">
						AND ag.id in
						<foreach collection="guildIds" item="id" separator="," open="(" close=")">
							#{id}
						</foreach>
					</when>
					<otherwise>
						and 1=0
					</otherwise>
				</choose>
			</if>
        <if test="brokerId != null">
            AND agm.sys_user_id = #{brokerId}
        </if>
			<if test="queryType != null and queryType != 3 and queryType == 2">
            	AND agm.sys_user_id is null
        	</if>
			<if test="startTime != null and startTime != ''">
				and date_format(agm.create_time,'%y%m%d') &gt;= date_format(#{startTime},'%y%m%d')
			</if>
			<if test="endTime != null and endTime != ''">
				and date_format(agm.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
			</if>
		</where>
		order by agm.create_time desc
	</select>
	<select id="getAnchorSelfLiftingLog" resultType="com.hzy.core.entity.SysAnchorModifyRecord"
			parameterType="java.lang.Long">
		select
			sac.id,
			sac.user_id,
			sac.old_commission_rate as oldCommissionRate,
			sac.new_commission_rate as newCommissionRate,
			sac.created_by ,
			sac.created_time,
			sac.updated_time,
			au.nick_name as nickName,
			sac.guild_id as guildId,
			sac.type,
			au.recode_code as recodeCode
		from
			sys_anchor_commission sac
				join  app_user au on au.id = sac.user_id
		where
			user_id = #{anchorId}
		order by
			created_time desc
	</select>
	<select id="queryAnchorById" resultType="com.hzy.core.model.vo.admin.AppAnchorVo"
			parameterType="java.lang.Long">
        SELECT agm.user_id,
               agm.create_time                 as createTime,
               au.is_tx,
               au.nick_name,
               acr.hall_owner_commission_scale as anchorSelfLifting,
               su.nick_name                    as brokerName,
               acr.type                        as anchorType,
               acr.guild_id as guildId,
               acr.third_id,
               acr.gift_income_scale    as giftPercentage
        FROM app_guild_member agm
                 JOIN
             app_user au ON au.id = agm.user_id
                 JOIN
             app_chat_room acr ON acr.hall_owner_user_id = agm.user_id
                 JOIN
             sys_user su on su.user_id = agm.sys_user_id
        where agm.user_id = #{userId}

    </select>
	<select id="queryBrokerList" resultType="com.hzy.core.entity.SysUser">
		select su.user_id, su.nick_name
		from sys_user su
		where del_flag = '0'
		<if test="nickName !=null and nickName !=''">
			and su.nick_name like concat('%', #{nickName}, '%')
		</if>
		<if test="sysUserIds != null ">
            AND su.user_id in
            <foreach collection="sysUserIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
	</select>
	<select id="getGuildId" resultType="java.lang.Long">
		select id from app_guild where sys_user_id = #{userId} LIMIT 0,1
	</select>
</mapper>