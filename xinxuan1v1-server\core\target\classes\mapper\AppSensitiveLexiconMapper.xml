<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppSensitiveLexiconMapper">

    <resultMap type="AppSensitiveLexicon" id="AppSensitiveLexiconResult">
        <result property="id" column="id"/>
        <result property="content" column="content"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAppSensitiveLexiconVo">
        select id, content, create_time, update_time
        from app_sensitive_lexicon
    </sql>

    <select id="selectAppSensitiveLexiconList" parameterType="AppSensitiveLexicon"
            resultMap="AppSensitiveLexiconResult">
        <include refid="selectAppSensitiveLexiconVo"/>
        <where>
            <if test="content != null  and content != ''">and content like concat('%', #{content}, '%')</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectAppSensitiveLexiconById" parameterType="Long" resultMap="AppSensitiveLexiconResult">
        <include refid="selectAppSensitiveLexiconVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppSensitiveLexicon" parameterType="AppSensitiveLexicon" useGeneratedKeys="true" keyProperty="id">
        insert into app_sensitive_lexicon
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="content != null">content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="content != null">#{content},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateAppSensitiveLexicon" parameterType="AppSensitiveLexicon">
        update app_sensitive_lexicon
        <trim prefix="SET" suffixOverrides=",">
            <if test="content != null">content = #{content},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppSensitiveLexiconById" parameterType="Long">
        delete
        from app_sensitive_lexicon
        where id = #{id}
    </delete>

    <delete id="deleteAppSensitiveLexiconByIds" parameterType="String">
        delete from app_sensitive_lexicon where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="checkTextIsSensitiveLexicon" resultType="java.lang.Boolean" parameterType="java.lang.String">
        select if(count(*) > 0, 1, 0)
        from app_sensitive_lexicon
        where content = #{text}
    </select>

    <select id="getSensitiveLexiconList" resultType="java.lang.String">
        select content
        from app_sensitive_lexicon
    </select>
</mapper>