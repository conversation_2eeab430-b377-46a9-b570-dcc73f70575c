package com.hzy.client.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.hutool.core.lang.Validator;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.hzy.client.controller.core.AppBaseController;
import com.hzy.core.config.RedisCache;
import com.hzy.core.constant.Constants;
import com.hzy.core.entity.*;
import com.hzy.core.enums.*;
import com.hzy.core.exception.AppException;
import com.hzy.core.exception.AppUserBannedException;
import com.hzy.core.mapper.*;
import com.hzy.core.model.vo.AjaxResult;
import com.hzy.core.model.vo.app.AppLoginResultVo;
import com.hzy.core.model.vo.app.AppLoginVo;
import com.hzy.core.model.vo.app.AppUpdPwdVo;
import com.hzy.core.service.AppUserCommunicateTelephoneConfigService;
import com.hzy.core.service.async.ClientAsyncFactory;
import com.hzy.core.utils.GenChanelIdUtil;
import com.hzy.core.utils.SecurityUtils;
import com.hzy.core.utils.ShareCodeUtil;
import com.hzy.core.utils.UidUtils;
import com.hzy.core.utils.ip.IpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 登录鉴权服务
 */
@Slf4j
@Service
public class AppLoginService extends AppBaseController {

    @Resource
    private TokenService tokenService;

    @Resource
    private Redisson redisson;

    @Resource
    private AppUserMapper appUserMapper;

    @Resource
    private AppChatRoomUserMapper appChatRoomUserMapper;

    @Resource
    private AppUserGoldBillMapper appUserGoldBillMapper;

    @Resource
    private RedisCache redisCache;

    @Resource
    private AppCommonService appCommonService;

    @Resource
    private AppChatBtnTypeMapper appChatBtnTypeMapper;

    @Resource
    private AppGoodNumberMapper appGoodNumberMapper;

    @Resource
    private ClientAsyncFactory clientAsyncFactory;

    @Resource
    private AppForbiddenEquipmentMapper appForbiddenEquipmentMapper;

    @Value("${equipment-info.equipment-type-key}")
    private String equipmentTypeKey;

    @Value("${equipment-info.equipment-id-key}")
    private String equipmentIdKey;
    @Resource
    private SysChannelMapper sysChannelMapper;
    @Resource
    private ChannelUserRelationMapper channelUserRelationMapper;
    @Resource
    private AppIpBannedMapper appIpBannedMapper;
    @Resource
    private WxMaService wxService;
    @Resource
    private AppMessageService appMessageService;

    @Value("${wx.app-id}")
    private String appId;
    @Value("${wx.app-secret}")
    private String appSecret;

    // /**
    // * 添加客户用户与真实用户之间的聊天类型为私信
    // *
    // * @param userId
    // */
    // @Transactional(propagation = Propagation.REQUIRED, rollbackFor =
    // {Exception.class})
    // public void addServiceUserAndUserChatBtType(Long userId) {
    // AppConfig config = appCommonService.getAppConfig();
    //
    // if (null == config || null == config.getServiceUserId() ||
    // config.getServiceUserId().intValue() < 1) {
    // return;
    // }
    // AppChatBtnType chatBtnType = appChatBtnTypeMapper.getChatBtnType(userId,
    // config.getServiceUserId());
    // if (null == chatBtnType) {//为空就新增
    // chatBtnType = new AppChatBtnType();
    // chatBtnType.setBtnType(WhetherTypeEnum.YES.getName());//这里默认就是私信类型
    // chatBtnType.setUserId(config.getServiceUserId());
    // chatBtnType.setToUserId(userId);
    // appChatBtnTypeMapper.insertAppChatBtnType(chatBtnType);
    // } else {//如果不为空就去判断按钮类型
    // if (chatBtnType.getBtnType().intValue() == WhetherTypeEnum.NO.getName())
    // {//如果按钮类型为打招呼，就改成私信类型
    // chatBtnType.setBtnType(WhetherTypeEnum.YES.getName());//这里默认就是私信类型
    // appChatBtnTypeMapper.updateAppChatBtnType(chatBtnType);
    // }
    // }
    // }
    @Resource
    private AppUserCommunicateTelephoneConfigService appUserCommunicateTelephoneConfigService;

    /**
     * 创建用户ID
     *
     * @return
     */
    public synchronized String generateUid(Long uId) {

        String code = UidUtils.generateUid(uId, 11120L).toString();

        // 检测id锁
        Boolean lock = redisCache.getCacheObject("app-createUserId-" + code);
        if (null != lock) {// 如果被锁住那就继续生成
            return this.generateUid(uId);
        }

        // 锁住id
        redisCache.setCacheObject("app-createUserId-" + code, true);

        // 校验id是否存在或者是否是靓号
        if (appUserMapper.checkReCodeIsExist(code) || appGoodNumberMapper.isExist(code)) {// 如果存在或者是靓号就继续调用
            // 释放锁
            redisCache.deleteObject("app-createUserId-" + code);
            return this.generateUid(uId);
        }

        return code;
    }

    /**
     * 登录验证
     *
     * @param login 登录信息
     * @param request HTTP请求
     * @return 登录结果
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult login(AppLoginVo login, HttpServletRequest request) {
        log.info("登录入参->" + JSONObject.toJSONString(login));
        
        // 获取登录IP和设备ID
        String loginIp = IpUtils.getIpAddr(request);
        String deviceId = login.getDeviceId();
        
        // 判断IP是否被封禁
        if (!StringUtils.isBlank(loginIp) && null != appIpBannedMapper.isIpBanned(loginIp)) {
            return AjaxResult.error("环境异常");
        }
        
        // 验证登录类型参数
        if (null == login.getLoginType()) {
            return AjaxResult.error("参数【loginType】为空");
        }
        AppLoginTypeEnums loginTypeEnums = AppLoginTypeEnums.getEnum(login.getLoginType());
        if (null == loginTypeEnums) {
            return AjaxResult.error("参数【loginType】错误");
        }
        
        // 处理邀请码
        if ("null".equals(login.getInviteCode())) {
            login.setInviteCode(null);
        }
        
        // 设备相关处理
        boolean isH5Request = isH5Request(request);
        Integer equipmentType = null;
        String equipmentId = null;
        
        if (!isH5Request) {
            // 检查设备信息并验证
            AjaxResult equipmentResult = checkAndGetEquipment(request, deviceId);
            if (equipmentResult != null) {
                return equipmentResult;
            }
            
            String equipmentTypeStr = request.getHeader(equipmentTypeKey);
            equipmentId = request.getHeader(equipmentIdKey);
            
            if (!StringUtils.isBlank(equipmentTypeStr)) {
                equipmentType = Integer.parseInt(equipmentTypeStr);
            }
            
            // 新注册用户检查设备ID是否已注册
            if (StringUtils.isNotBlank(equipmentId)) {
                String upChannelCode = appUserMapper.isExistByDeviceId(equipmentId);
                if (StringUtils.isNotBlank(upChannelCode)) {
                    login.setInviteCode(upChannelCode);
                }
            }
        }
        
        boolean isNewUser = false; // 是否是新用户
        RLock lock = null;
        String lockKey = null;
        
        try {
            // 根据不同登录类型获取锁
            if (loginTypeEnums.getId() == AppLoginTypeEnums.TYPE0.getId()) {
                lockKey = login.getAliYunToken();
            } else if (loginTypeEnums.getId() == AppLoginTypeEnums.TYPE1.getId() || 
                    loginTypeEnums.getId() == AppLoginTypeEnums.TYPE2.getId()) {
                lockKey = login.getPhone();
            } else if (loginTypeEnums.getId() == AppLoginTypeEnums.TYPE3.getId() || 
                    loginTypeEnums.getId() == AppLoginTypeEnums.TYPE4.getId() || 
                    loginTypeEnums.getId() == AppLoginTypeEnums.TYPE5.getId()) {
                lockKey = login.getThirdOpenId();
            }
            
            if (lockKey != null) {
                lock = redisson.getLock("app:login:" + lockKey);
                if (lock.isLocked()) {
                    return AjaxResult.frequent();
                }
                lock.lock(60, TimeUnit.SECONDS);
            }
            
            // 根据登录类型处理登录
            AppUserEntity user = null;
            
            switch (loginTypeEnums.getId()) {
                case 0: // 一键快捷登录
                    user = handleQuickLogin(login, equipmentId);
                    isNewUser = user.getCreatedTime().isAfter(LocalDateTime.now().minusSeconds(10));
                    break;
                case 1: // 验证码登录
                    user = handleSmsCodeLogin(login, equipmentId);
                    isNewUser = user.getCreatedTime().isAfter(LocalDateTime.now().minusSeconds(10));
                    break;
                case 2: // 账号密码登录
                    user = handlePasswordLogin(login);
                    break;
                case 3: // 微信登录
                case 4: // QQ登录
                case 5: // IOS登录
                    user = handleThirdPartyLogin(login, loginTypeEnums, equipmentId);
                    isNewUser = user.getCreatedTime().isAfter(LocalDateTime.now().minusSeconds(10));
                    break;
                case 6: // H5注册
                    throw new AppException("Interface not open");
                default:
                    throw new AppException("不支持的登录类型");
            }
            
            // 验证房间管理员权限
            if (login.getIsChatRoomAdminH5() == WhetherTypeEnum.YES.getName()) {
                Long chatRoomId = appChatRoomUserMapper.getUserAdminChatRoomId(user.getId());
                if (null == chatRoomId) {
                    throw new AppException("账号无权限");
                }
            }
            
            // 验证用户状态
            AppUserEntity userUpd = verifyUserStatus(user);
            Long userId = user.getId();
            
            // 生成用户token
            String token = tokenService.createToken(
                    StringUtils.isBlank(user.getPhone()) ? user.getRecodeCode() : user.getPhone());
            if (StringUtils.isBlank(token)) {
                logger.error("登录异常，token生成异常，phone:{},userId:{},loginType:{}", 
                        user.getPhone(), user.getId(), loginTypeEnums.getId());
                throw new AppException("token生成异常");
            }
            
            // 处理推荐人绑定和奖励
            handleRecommendBinding(user, login.getRecodeCode(), userUpd);
            
            // 更新用户登录信息
            userUpd.setId(userId);
            userUpd.setToken(token);
            userUpd.setLastOperatingTime(new Date());
            userUpd.setPushId(equipmentId);
            userUpd.setEquipmentType(equipmentType);
            userUpd.setLatitude(login.getLatitude());
            userUpd.setLongitude(login.getLongitude());
            appUserMapper.updateAppUser(userUpd);
            
            // 渠道绑定
            handleChannelBinding(userId, login.getInviteCode());
            
            // 记录用户登录日志
            recordLoginLog(userId, loginIp, request.getRemotePort(), deviceId);
            
            // 给新用户发放奖励
            giveNewUserReward(userId, isNewUser);
            
            // 删除上一次登录的token缓存
            tokenService.removeAllUserTokens(userId);
            
            // 用户token信息缓存
            tokenService.addUserToken(token, user.getId());
            
            // 清除验证码缓存
            if (loginTypeEnums.getId() == AppLoginTypeEnums.TYPE1.getId()) {
                try {
                    redisCache.deleteObject(SmsSendTypeEnum.TYPE1.getRegionName() + login.getPhone());
                } catch (Exception exc) {
                    // 忽略删除缓存异常
                }
            }
            
            // 拼装登录结果返回
            AppLoginResultVo loginResult = new AppLoginResultVo();
            loginResult.setToken(token);
            loginResult.setIsPerfectInfo(user.getIsPerfectInfo()); // 是否完善了资料
            loginResult.setIsSetPassword(!StringUtils.isBlank(user.getPassword()) ? 
                    WhetherTypeEnum.YES.getName() : WhetherTypeEnum.NO.getName());
            loginResult.setIsBindingPhone(!StringUtils.isBlank(user.getPhone()) ? 
                    WhetherTypeEnum.YES.getName() : WhetherTypeEnum.NO.getName());
            loginResult.setIsNewUser(0); // 强制设置为0
            
            return AjaxResult.success("登录成功", loginResult);
        } catch (Exception e) {
            log.error("登录异常，phone:{},msg:{},loginType:{}", login.getPhone(), e.getMessage(), loginTypeEnums.getId());
            if (e instanceof AppException) {
                return AjaxResult.error(e.getMessage());
            }
            if (e instanceof AppUserBannedException) {
                return AjaxResult.error(e.getMessage());
            }
            return AjaxResult.error("系统异常,请联系客服处理");
        } finally {
            if (null != lock) {
                // 移除锁
                lock.unlock();
            }
        }
    }
    
    /**
     * 检查并获取设备信息
     *
     * @param request HTTP请求
     * @param deviceId 设备ID
     * @return 错误信息，如果没有错误则返回null
     */
    private AjaxResult checkAndGetEquipment(HttpServletRequest request, String deviceId) {
        String equipmentTypeStr = request.getHeader(equipmentTypeKey);
        String equipmentId = request.getHeader(equipmentIdKey);
        
        if (!StringUtils.isBlank(equipmentTypeStr) && !StringUtils.isBlank(equipmentId)) {
            try {
                Integer equipmentType = Integer.parseInt(equipmentTypeStr);
                if (null != AppEquipmentTypeEnum.getEnum(equipmentType)) {
                    // 判断是否是封禁设备
                    if (appForbiddenEquipmentMapper.isForbiddenEquipment(equipmentId, equipmentType)) {
                        return AjaxResult.error("当前设备存在风险,暂不支持登录", null);
                    }
                }
            } catch (Exception e) {
                return AjaxResult.error("设备类型错误", null);
            }
        }
        
        return null;
    }
    
    /**
     * 记录用户登录日志
     *
     * @param userId 用户ID
     * @param loginIp 登录IP
     * @param remotePort 远程端口
     * @param deviceId 设备ID
     */
    private void recordLoginLog(Long userId, String loginIp, int remotePort, String deviceId) {
        AppUserLoginLog appUserLoginLog = new AppUserLoginLog();
        appUserLoginLog.setUserId(userId);
        appUserLoginLog.setLoginIp(loginIp + ":" + remotePort);
        appUserLoginLog.setLoginTime(new Date());
        appUserLoginLog.setDeviceId(deviceId);
        clientAsyncFactory.asyncAddUserLoginLog(appUserLoginLog);
    }

    /**
     * 修改密码
     *
     * @param vo
     * @param request
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult updPassword(AppUpdPwdVo vo, HttpServletRequest request) {
        String loginIp = IpUtils.getIpAddr(request);
        if (!StringUtils.isBlank(loginIp) && null != appIpBannedMapper.isIpBanned(loginIp)) {
            return AjaxResult.error("环境异常");
        }
        if (StringUtils.isBlank(vo.getPhone())) {
            return AjaxResult.error("手机号不能为空");
        }
        if (!Validator.isMobile(vo.getPhone())) {
            return AjaxResult.error("手机号格式错误");
        }

        RLock lock = redisson.getLock("app:updPassword:" + vo.getPhone());
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(120, TimeUnit.SECONDS);

        boolean isH5Request = isH5Request(request);

        try {

            if (StringUtils.isBlank(vo.getPassword())) {
                return AjaxResult.error("密码不能为空");
            }

            if (StringUtils.isBlank(vo.getConfirmPassword())) {
                return AjaxResult.error("请确认密码");
            }

            if (!StringUtils.equals(vo.getPassword(), vo.getConfirmPassword())) {
                return AjaxResult.error("两次密码输入不一致");
            }

            if (StringUtils.isBlank(vo.getCode())) {
                return AjaxResult.error("验证码为空");
            }
            // 根据手机查询用户
            AppUserEntity user = appUserMapper.selectAppUserByPhone(vo.getPhone());
            if (null == user) {
                return AjaxResult.error("手机号未注册");
            }
            // 校验验证码
            String cacheCode = redisCache.getCacheObject(SmsSendTypeEnum.TYPE2.getRegionName() + vo.getPhone());
            if (StringUtils.isBlank(cacheCode)) {
                return AjaxResult.error("验证码无效");
            }

            if (!StringUtils.equals(vo.getCode(), cacheCode)) {
                return AjaxResult.error("验证码错误");
            }

            Integer userStatus = user.getUserStatus();// 用户状态

            AppUserEntity userUpd = new AppUserEntity();
            userUpd.setId(user.getId());
            // 设置新密码
            userUpd.setPassword(SecurityUtils.encryptPassword(vo.getPassword()));
            appUserMapper.updateAppUser(userUpd);
            try {
                redisCache.deleteObject(SmsSendTypeEnum.TYPE2.getRegionName() + vo.getPhone());
            } catch (Exception exc) {
            }
            if (null == vo.getLogin() || !vo.getLogin()) {
                return AjaxResult.success("修改成功");
            } else {// 处理登录

                Integer equipmentType = null;
                String equipmentId = null;

                if (!isH5Request) {
                    String equipmentTypeStr = request.getHeader(equipmentTypeKey);
                    equipmentId = request.getHeader(equipmentIdKey);
                    if (StringUtils.isBlank(equipmentTypeStr) || StringUtils.isBlank(equipmentId)) {
                        lock.unlock();
                        return AjaxResult.error("请升级最新版本", null);
                    }
                    try {
                        equipmentType = Integer.parseInt(equipmentTypeStr);
                        if (null == AppEquipmentTypeEnum.getEnum(equipmentType)) {
                            lock.unlock();
                            return AjaxResult.error("设备类型不存在", null);
                        }
                        // 判断是否是封禁设备
                        if (appForbiddenEquipmentMapper.isForbiddenEquipment(equipmentId, equipmentType)) {
                            lock.unlock();
                            return AjaxResult.error("当前设备存在风险,暂不支持登录", null);
                        }
                    } catch (Exception e) {
                        lock.unlock();
                        return AjaxResult.error("设备类型错误", null);
                    }
                }

                // 获取用户状态枚举,并校验用户状态
                AppUserStatusTypeEnums userStatusTypeEnums = AppUserStatusTypeEnums.getEnum(userStatus);
                if (null == userStatusTypeEnums) {
                    return AjaxResult.error("修改成功,账号状态异常无法登录");
                }
                if (userStatus.intValue() != AppUserStatusTypeEnums.TYPE1.getCode()) {// 状态非正常直接返回
                    return AjaxResult.error("修改成功,账号" + userStatusTypeEnums.getInfo() + ",无法登录");
                }

                // 生成用户token
                String token = tokenService.createToken(user.getPhone());
                if (StringUtils.isBlank(token)) {
                    logger.error("修改密码-自动登录异常，token生成异常，phone:{},userId:{}", user.getPhone(), user.getId());
                    return AjaxResult.error("修改成功,token生成异常,无法登录");
                }

                // 拼装登录结果返回
                AppLoginResultVo loginResult = new AppLoginResultVo();
                loginResult.setToken(token);
                loginResult.setIsPerfectInfo(user.getIsPerfectInfo());// 是否完善了资料
                // 是否设置了密码
                loginResult.setIsSetPassword(!StringUtils.isBlank(user.getPassword()) ? WhetherTypeEnum.YES.getName()
                        : WhetherTypeEnum.NO.getName());
                // 是否绑定了手机
                loginResult.setIsBindingPhone(!StringUtils.isBlank(user.getPhone()) ? WhetherTypeEnum.YES.getName()
                        : WhetherTypeEnum.NO.getName());

                // 用户老token
                String oldToken = user.getToken();

                Long userId = user.getId();

                // 更新用户登录信息
                AppUserEntity appuserUpd = new AppUserEntity();
                appuserUpd.setId(user.getId());
                // 保存新token
                appuserUpd.setToken(token);
                // 设置最后操作时间
                appuserUpd.setLastOperatingTime(new Date());
                appuserUpd.setPushId(equipmentId);
                appuserUpd.setEquipmentType(equipmentType);
                appUserMapper.updateAppUser(appuserUpd);

                // 记录用户登录日志
                AppUserLoginLog appUserLoginLog = new AppUserLoginLog();
                appUserLoginLog.setUserId(userId);
                appUserLoginLog.setLoginIp(loginIp + ":" + request.getRemotePort());
                appUserLoginLog.setLoginTime(new Date());
                clientAsyncFactory.asyncAddUserLoginLog(appUserLoginLog);

                // 删除上一次登录的token缓存
                tokenService.removeToken(oldToken);
                // 用户token信息缓存
                tokenService.addUserToken(token, user.getId());
                return AjaxResult.success("修改成功", loginResult);
            }
        } catch (Exception e) {
            logger.error("修改密码异常，phone:{},msg:{}", vo.getPhone(), e.getMessage());
            throw new AppException("系统异常,请联系客服处理");
        } finally {
            // 移除锁
            lock.unlock();
        }
    }

    /**
     * 退出登录
     *
     * @param request
     * @return
     */
    public AjaxResult toLogout(HttpServletRequest request) {
        AppUserEntity user = getUser(request);
        Long userId = user.getId();
        RLock lock = redisson.getLock("app:toLogout:" + userId);
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(60, TimeUnit.SECONDS);
        String token = getToken(request);
        tokenService.removeToken(token);// 一致清空一个

        AppUserEntity userUpd = new AppUserEntity();
        userUpd.setId(userId);
        userUpd.setToken("");
        userUpd.setIsOnline(WhetherTypeEnum.NO.getName());
        appUserMapper.updateAppUser(userUpd);
        // 移除锁
        lock.unlock();
        return AjaxResult.success("退出成功");
    }

    /**
     * 快捷登录处理
     *
     * @param login 登录信息
     * @param equipmentId 设备ID
     * @return 用户实体
     */
    private AppUserEntity handleQuickLogin(AppLoginVo login, String equipmentId) {
        if (StringUtils.isBlank(login.getAliYunToken())) {
            throw new AppException("参数【aliYunToken】为空");
        }
        
        // 解析手机号
        String phone = appCommonService.aliYunAnalysisPhone(login.getAliYunToken());
        if (StringUtils.isBlank(phone)) {
            throw new AppException("手机号解析失败");
        }
        login.setPhone(phone);
        
        // 根据手机号查询用户
        AppUserEntity user = appUserMapper.selectAppUserByPhone(login.getPhone());
        
        if (null == user) {
            // 不存在就注册
            user = createNewUser(login, "一键快捷登录注册", equipmentId);
        }
        
        return user;
    }
    
    /**
     * 验证码登录处理
     *
     * @param login 登录信息
     * @param equipmentId 设备ID
     * @return 用户实体
     */
    private AppUserEntity handleSmsCodeLogin(AppLoginVo login, String equipmentId) {
        if (StringUtils.isBlank(login.getPhone())) {
            throw new AppException("手机号不能为空");
        }
        if (!Validator.isMobile(login.getPhone())) {
            throw new AppException("手机号格式错误");
        }
        if (StringUtils.isBlank(login.getLoginCode())) {
            throw new AppException("验证码不能为空");
        }
        
        // 校验验证码
        verifyLoginCode(login.getPhone(), login.getLoginCode());
        
        // 根据手机号查询用户
        AppUserEntity user = appUserMapper.selectAppUserByPhone(login.getPhone());
        if (null == user) {
            // 不存在就注册
            user = createNewUser(login, "验证码登录注册", equipmentId);
        }
        
        return user;
    }
    
    /**
     * 账号密码登录处理
     *
     * @param login 登录信息
     * @return 用户实体
     */
    private AppUserEntity handlePasswordLogin(AppLoginVo login) {
        if (StringUtils.isBlank(login.getPhone())) {
            throw new AppException("手机号不能为空");
        }
        if (!Validator.isMobile(login.getPhone())) {
            throw new AppException("手机号格式错误");
        }
        if (StringUtils.isBlank(login.getLoginCode())) {
            throw new AppException("密码不能为空");
        }
        
        // 根据手机号查询用户
        AppUserEntity user = appUserMapper.selectAppUserByPhone(login.getPhone());
        if (null == user) {
            throw new AppException("手机号未注册");
        }
        if (StringUtils.isBlank(user.getPassword())) {
            throw new AppException("账号未设置密码");
        }
        
        // 密码校验
        if (!SecurityUtils.matchesPassword(login.getLoginCode(), user.getPassword())) {
            throw new AppException("手机号或密码错误");
        }
        
        return user;
    }
    
    /**
     * 第三方登录处理
     *
     * @param login 登录信息
     * @param loginTypeEnums 登录类型
     * @param equipmentId 设备ID
     * @return 用户实体
     */
    private AppUserEntity handleThirdPartyLogin(AppLoginVo login, AppLoginTypeEnums loginTypeEnums, String equipmentId) {
        if (StringUtils.isBlank(login.getThirdOpenId())) {
            throw new AppException("参数【thirdOpenId】为空");
        }
        
        AppUserEntity user = null;
        
        if (loginTypeEnums.getId() == AppLoginTypeEnums.TYPE3.getId()) {
            // 微信登录
            String openId = getWechatOpenId(login.getThirdOpenId());
            login.setThirdOpenId(openId);
            user = appUserMapper.selectAppUserByWxOpenId(openId);
        } else if (loginTypeEnums.getId() == AppLoginTypeEnums.TYPE4.getId()) {
            // QQ登录
            user = appUserMapper.selectAppUserByQqOpenId(login.getThirdOpenId());
        } else if (loginTypeEnums.getId() == AppLoginTypeEnums.TYPE5.getId()) {
            // IOS登录
            user = appUserMapper.selectAppUserByIosOpenId(login.getThirdOpenId());
        }
        
        if (null == user) {
            // 不存在就注册
            user = createNewUser(login, "第三方登录注册", equipmentId);
            
            // 设置对应的第三方平台ID
            if (loginTypeEnums.getId() == AppLoginTypeEnums.TYPE3.getId()) {
                user.setWeixinOpenid(login.getThirdOpenId());
            } else if (loginTypeEnums.getId() == AppLoginTypeEnums.TYPE4.getId()) {
                user.setQqOpenid(login.getThirdOpenId());
            } else if (loginTypeEnums.getId() == AppLoginTypeEnums.TYPE5.getId()) {
                user.setIosOpenid(login.getThirdOpenId());
            }
            
            appUserMapper.updateAppUser(user);
        }
        
        return user;
    }
    
    /**
     * 获取微信OpenID
     *
     * @param code 微信授权码
     * @return OpenID
     */
    private String getWechatOpenId(String code) {
        String api = String.format("https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code",
                appId, appSecret, code);
        String result = HttpUtil.get(api);
        log.info("微信小程序返回user信息:{}", result);
        JSONObject sessionInfo = JSONObject.parseObject(result);

        // 检查返回结果
        if (sessionInfo.containsKey("errcode") && sessionInfo.getIntValue("errcode") != 0) {
            throw new AppException("微信登录失败：" + sessionInfo.getString("errmsg"));
        }

        String openId = sessionInfo.getString("openid");
        if (StringUtils.isBlank(openId)) {
            throw new AppException("获取微信用户信息失败");
        }
        
        return openId;
    }
    
    /**
     * 验证登录验证码
     *
     * @param phone 手机号
     * @param loginCode 验证码
     */
    private void verifyLoginCode(String phone, String loginCode) {
        String cacheCode = redisCache.getCacheObject(SmsSendTypeEnum.TYPE1.getRegionName() + phone);
        
        // 万能验证码
        if (redisCache.hasKey("loginCode")) {
            String superLoginCode = redisCache.getCacheObject("loginCode").toString();
            if (StringUtils.equals(superLoginCode, loginCode)) {
                return;
            }
        }
        
        if (StringUtils.isBlank(cacheCode)) {
            throw new AppException("验证码无效");
        }
        
        if (!StringUtils.equals(loginCode, cacheCode)) {
            throw new AppException("验证码错误");
        }
    }
    
    /**
     * 创建新用户
     *
     * @param login 登录信息
     * @param createdBy 创建来源
     * @param equipmentId 设备ID
     * @return 新用户实体
     */
    private AppUserEntity createNewUser(AppLoginVo login, String createdBy, String equipmentId) {
        AppUserEntity user = new AppUserEntity();
        user.setCreatedBy(createdBy);
        user.setCreatedTime(LocalDateTime.now());
        user.setUserStatus(AppUserStatusTypeEnums.TYPE1.getCode());  // 用户状态为正常
        user.setPhone(login.getPhone());
        user.setIsPerfectInfo(WhetherTypeEnum.NO.getName());  // 设置为没有完善资料
        user.setPhotoAlbum("[]");
        user.setIsVirtual(WhetherTypeEnum.NO.getName());
        user.setRecodeUserId(0L);
        user.setIsOnline(WhetherTypeEnum.YES.getName());
        user.setChannelCode(GenChanelIdUtil.genCodeByInviteCode(login.getInviteCode()));
        user.setUpChannelCode(login.getInviteCode());
        user.setLatitude(login.getLatitude());
        user.setLongitude(login.getLongitude());
        user.setIsRealNameAuth(1);
        user.setIsRealPersonAuth(1);
        user.setIsFreeVoice(1);
        appUserMapper.insertAppUser(user);
        
        // 创建通话配置
        appUserCommunicateTelephoneConfigService.getUserCommunicateTelephoneConfig(user.getId(), null);
        
        // 生成唯一邀请码
        String recodeCode = generateUniqueInviteCode();
        user.setRecodeCode(recodeCode);
        appUserMapper.updateAppUser(user);
        
        return user;
    }
    
    /**
     * 生成唯一邀请码
     *
     * @return 6位数字邀请码
     */
    private String generateUniqueInviteCode() {
        String recodeCode = ShareCodeUtil.generateUniqueSixDigitCode();
        
        // 查看recodeCode 是否已经存在
        AppUserEntity appUserEntity = appUserMapper.selectAppUserByRecodeCode(recodeCode);
        while (null != appUserEntity) {
            recodeCode = ShareCodeUtil.generateUniqueSixDigitCode();
            appUserEntity = appUserMapper.selectAppUserByRecodeCode(recodeCode);
        }
        
        return recodeCode;
    }
    
    /**
     * 验证用户状态
     *
     * @param user 用户实体
     * @return 更新用户实体
     */
    private AppUserEntity verifyUserStatus(AppUserEntity user) {
        AppUserEntity userUpd = new AppUserEntity();
        
        // 获取用户状态枚举,并校验用户状态
        AppUserStatusTypeEnums userStatusTypeEnums = AppUserStatusTypeEnums.getEnum(user.getUserStatus());
        if (null == userStatusTypeEnums) {
            throw new AppException("账号状态异常,请联系客服处理");
        }
        
        if (user.getUserStatus() != AppUserStatusTypeEnums.TYPE1.getCode()
                && user.getUserStatus() != AppUserStatusTypeEnums.TYPE4.getCode()) {
            // 状态非正常并且不为4(封禁)直接返回
            throw new AppException("账号" + userStatusTypeEnums.getInfo());
        }
        
        if (user.getUserStatus() == AppUserStatusTypeEnums.TYPE4.getCode()) {
            // 如果是封禁状态
            boolean hasKey = redisCache.hasKey(Constants.APP_USER_BANNED_KEY + user.getId());
            if (!hasKey) {
                // 如果缓存中没有封禁信息，那就是封禁结束了,给用户状态改为正常
                userUpd.setUserStatus(AppUserStatusTypeEnums.TYPE1.getCode());
            } else {
                // 没有结束就拼装信息返回
                throw new AppUserBannedException("账号已经封禁，请联系管理员");
            }
        }
        
        return userUpd;
    }
    
    /**
     * 处理渠道绑定
     *
     * @param userId 用户ID
     * @param inviteCode 邀请码
     */
    public void handleChannelBinding(Long userId, String inviteCode) {
        if (inviteCode != null) {
            // 查询渠道链接
            SysChannel channel = sysChannelMapper.selectSysChannelByInviteCode(inviteCode);
            if (channel != null) {
                // 先查询是否已存在关系
                ChannelUserRelation existRelation = channelUserRelationMapper.selectChannelUserRelationByUserId(userId, channel.getId());
                if (existRelation == null) {
                    // 关系不存在，才进行插入
                    // 把该用户绑定到该渠道
                    ChannelUserRelation userRelation = new ChannelUserRelation();
                    userRelation.setUserId(userId);
                    userRelation.setChannelId(channel.getId());
                    userRelation.setCreatedTime(LocalDateTime.now());
                    userRelation.setUpdatedTime(LocalDateTime.now());
                    channelUserRelationMapper.insert(userRelation);
                }
            } else {
                logger.error("渠道链接不存在，邀请码：{}", inviteCode);
            }
        }
    }
    
    /**
     * 处理推荐人绑定和奖励
     *
     * @param user 用户
     * @param recodeCode 推荐码
     * @param userUpd 用户更新实体
     */
    private void handleRecommendBinding(AppUserEntity user, String recodeCode, AppUserEntity userUpd) {
        if (!StringUtils.isBlank(recodeCode)) {
            // 传入了邀请码,并且当前用户未绑定推荐人
            if (null == user.getRecodeUserId() || user.getRecodeUserId().equals(0L)) {
                // 并且当前用户没有绑定推荐人查询推荐用户信息
                AppUserEntity reUser = appUserMapper.selectAppUserByRecode(recodeCode);
                if (null != reUser && !reUser.getId().equals(user.getId())) {
                    // 推荐人存在,并且推荐人不为自己
                    userUpd.setRecodeUserId(reUser.getId());
                    userUpd.setBindRecodeUserTime(new Date());
                    
                    AppConfig config = appCommonService.getAppConfig();
                    
                    // 给推荐人赠送金币
                    appUserMapper.addUserGoldBalance(reUser.getId(), config.getInviteGold());
                    
                    // 金币账单记录
                    AppUserGoldBill userGoldBill = new AppUserGoldBill();
                    userGoldBill.setUserId(reUser.getId());
                    userGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE15.getId());
                    userGoldBill.setAmount(config.getInviteGold());
                    userGoldBill.setObjectId(user.getId());
                    userGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
                    appUserGoldBillMapper.insertAppUserGoldBill(userGoldBill);
                }
            }
        }
    }
    
    /**
     * 给新用户发放注册奖励
     *
     * @param userId 用户ID
     * @param isNewUser 是否是新用户
     */
    private void giveNewUserReward(Long userId, boolean isNewUser) {
        if (isNewUser) {
            AppConfig config = appCommonService.getAppConfig();
            if (null != config && null != config.getNewUserGoldCount() && 
                    config.getNewUserGoldCount().compareTo(new BigDecimal("0")) > 0) {
                // 如果是新用户并且新用户注册赠送的金币数量大于0那就去赠送
                appUserMapper.addUserGoldBalance(userId, config.getNewUserGoldCount());
                
                // 金币账单记录
                AppUserGoldBill userGoldBill = new AppUserGoldBill();
                userGoldBill.setUserId(userId);
                userGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE19.getId());
                userGoldBill.setAmount(config.getNewUserGoldCount());
                userGoldBill.setObjectId(config.getId());
                userGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
                appUserGoldBillMapper.insertAppUserGoldBill(userGoldBill);
            }
        }
    }

}
