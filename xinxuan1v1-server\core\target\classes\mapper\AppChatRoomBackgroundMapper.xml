<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppChatRoomBackgroundMapper">

    <resultMap type="AppChatRoomBackground" id="AppChatRoomBackgroundResult">
        <result property="id" column="id"/>
        <result property="url" column="url"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="selectAppChatRoomBackgroundVo">
        select id, url, create_time, update_time, create_by, update_by
        from app_chat_room_background
    </sql>


    <insert id="insertAppChatRoomBackground" parameterType="AppChatRoomBackground" useGeneratedKeys="true"
            keyProperty="id">
        insert into app_chat_room_background
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="url != null">url,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="thumbUrl != null">thumb_url,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="url != null">#{url},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="thumbUrl != null">#{thumbUrl},</if>
        </trim>
    </insert>

    <update id="updateAppChatRoomBackground" parameterType="AppChatRoomBackground">
        update app_chat_room_background
        <trim prefix="SET" suffixOverrides=",">
            <if test="url != null">url = #{url},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="thumbUrl != null">thumb_url=#{thumbUrl},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppChatRoomBackgroundById" parameterType="Long">
        delete
        from app_chat_room_background
        where id = #{id}
    </delete>

    <delete id="deleteAppChatRoomBackgroundByIds" parameterType="String">
        delete from app_chat_room_background where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="isChatRoomBackground" parameterType="java.lang.String" resultType="java.lang.Boolean">
        select if(count(*) > 0, 1, 0)
        from app_chat_room_background
        where url = #{url}
    </select>

    <select id="selectAppChatRoomBackgroundList" resultType="com.hzy.core.entity.AppChatRoomBackground"
            parameterType="com.hzy.core.entity.AppChatRoomBackground">
        select id,
        url,
        create_time as createTime,
        update_time as updateTime,
        create_by as createBy,
        update_by as updateBy,
        thumb_url as thumbUrl
        from app_chat_room_background
        order by update_time desc

    </select>
    <select id="selectAppChatRoomBackgroundById" resultType="com.hzy.core.entity.AppChatRoomBackground"
            parameterType="java.lang.Long">
        select id,
        url,
        create_time as createTime,
        update_time as updateTime,
        create_by as createBy,
        update_by as updateBy,
        thumb_url as thumbUrl
        from app_chat_room_background
        where id = #{id}
        order by update_time desc
    </select>
    <select id="getChatRoomBackgroundList" resultType="com.hzy.core.entity.AppChatRoomBackground">
        select url,thumb_url as thumbUrl
        from app_chat_room_background
        order by create_time desc
    </select>
</mapper>