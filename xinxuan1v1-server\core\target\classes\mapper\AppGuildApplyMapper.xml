<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppGuildApplyMapper">

    <resultMap type="AppGuildApply" id="AppGuildApplyResult">
        <result property="id" column="id"/>
        <result property="initiateUserId" column="initiate_user_id"/>
        <result property="guildId" column="guild_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="remarks" column="remarks"/>
    </resultMap>

    <sql id="selectAppGuildApplyVo">
        select id, initiate_user_id, guild_id, create_time, update_time, status, remarks from app_guild_apply
    </sql>

    <select id="selectAppGuildApplyList" parameterType="AppGuildApply" resultMap="AppGuildApplyResult">
        <include refid="selectAppGuildApplyVo"/>
        <where>
            <if test="initiateUserId != null ">and initiate_user_id = #{initiateUserId}</if>
            <if test="guildId != null ">and guild_id = #{guildId}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="remarks != null  and remarks != ''">and remarks = #{remarks}</if>
        </where>
    </select>

    <select id="selectAppGuildApplyById" parameterType="Long" resultMap="AppGuildApplyResult">
        <include refid="selectAppGuildApplyVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppGuildApply" parameterType="AppGuildApply" useGeneratedKeys="true" keyProperty="id">
        insert into app_guild_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="initiateUserId != null">initiate_user_id,</if>
            <if test="guildId != null">guild_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="remarks != null">remarks,</if>
            <if test="brokerId !=null">sys_user_id</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="initiateUserId != null">#{initiateUserId},</if>
            <if test="guildId != null">#{guildId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="brokerId !=null">#{brokerId}</if>

        </trim>
    </insert>

    <update id="updateAppGuildApply" parameterType="AppGuildApply">
        update app_guild_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="initiateUserId != null">initiate_user_id = #{initiateUserId},</if>
            <if test="guildId != null">guild_id = #{guildId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateRefund">
        update app_guild_return_records
        set status = #{status} , end_time = now()
        where user_id = #{userId}
        and  status = '0'
    </update>

    <delete id="deleteAppGuildApplyById" parameterType="Long">
        delete from app_guild_apply where id = #{id}
    </delete>

    <delete id="deleteAppGuildApplyByIds" parameterType="String">
        delete from app_guild_apply where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getGuildApplyList" resultType="com.hzy.core.model.vo.app.AppGuildApplyListVo">
        select fa.id as applyId,
        fa.initiate_user_id as initiateUserId,
        initiateUser.nick_name as initiateUserNickName,
        initiateUser.recode_code as recodeCode,
        initiateUser.sex as initiateUserSex,
        initiateUser.head_portrait as initiateUserHeadPortrait,
        initiateUser.birthday as initiateUserBirthday,
        initiateUser.age as initiateUserAge,
        fa.status as `status`,
        fa.create_time as createTime,
        fa.remarks as remarks
        from app_guild_apply fa,
        app_user initiateUser
        where
        fa.guild_id =#{guildId}
        <if test="null==queryHistory or queryHistory==0">
            and fa.status=0
        </if>
        <if test="null!=queryHistory and queryHistory==1">
            and fa.status!=0
        </if>
        and fa.initiate_user_id = initiateUser.id
        order by fa.id desc
    </select>

    <select id="getUserNewGuildApply" parameterType="Long" resultMap="AppGuildApplyResult">
        <include refid="selectAppGuildApplyVo"/>
        where initiate_user_id = #{userId} and guild_id=#{guildId}
        order by id desc limit 0,1
    </select>

    <select id="selectGuildApplyList" parameterType="Long" resultType="com.hzy.core.model.vo.app.AppGuildApplyListVo">
        SELECT u.id AS initiateUserId,
        u.head_portrait AS initiateUserHeadPortrait,
        u.nick_name AS initiateUserNickName,
        u.sex AS initiateUserSex,
        ga.status AS status,
        ga.create_time AS createTime
        FROM app_guild_apply ga
        LEFT JOIN app_user u ON u.id = ga.initiate_user_id
        WHERE ga.guild_id = #{guildId}
    </select>

    <!-- isExistNotHandleApply --> 

    <select id="isExistNotHandleApply" resultType="boolean">
        select count(1)
        from app_guild_apply
        where initiate_user_id = #{userId}
          and guild_id = #{guildId}
          and status = 0
          and type = 2
    </select>
</mapper>