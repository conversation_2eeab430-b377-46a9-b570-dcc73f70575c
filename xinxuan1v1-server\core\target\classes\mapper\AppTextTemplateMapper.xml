<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppTextTemplateMapper">

    <resultMap type="AppTextTemplate" id="AppTextTemplateResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="textContent" column="text_content"/>
        <result property="applySex" column="apply_sex"/>
    </resultMap>

    <sql id="selectAppTextTemplateVo">
        select id, create_time, update_time, create_by, update_by, text_content, apply_sex
        from app_text_template
    </sql>

    <select id="selectAppTextTemplateList" parameterType="AppTextTemplate" resultMap="AppTextTemplateResult">
        <include refid="selectAppTextTemplateVo"/>
        <where>
            <if test="textContent != null  and textContent != ''">and text_content = #{textContent}</if>
            <if test="applySex != null ">and apply_sex = #{applySex}</if>
        </where>
    </select>

    <select id="selectAppTextTemplateById" parameterType="Long" resultMap="AppTextTemplateResult">
        <include refid="selectAppTextTemplateVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppTextTemplate" parameterType="AppTextTemplate" useGeneratedKeys="true" keyProperty="id">
        insert into app_text_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="textContent != null">text_content,</if>
            <if test="applySex != null">apply_sex,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="textContent != null">#{textContent},</if>
            <if test="applySex != null">#{applySex},</if>
        </trim>
    </insert>

    <update id="updateAppTextTemplate" parameterType="AppTextTemplate">
        update app_text_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="textContent != null">text_content = #{textContent},</if>
            <if test="applySex != null">apply_sex = #{applySex},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppTextTemplateById" parameterType="Long">
        delete
        from app_text_template
        where id = #{id}
    </delete>

    <delete id="deleteAppTextTemplateByIds" parameterType="String">
        delete from app_text_template where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="randomGetTextTemplate" resultType="java.lang.String" parameterType="java.lang.Integer">
        select text_content
        from app_text_template
        where 1=1
        <if test="null!=applySex">
            and (apply_sex =#{applySex} or apply_sex = -1)
        </if>
        <if test="null==applySex">
            and apply_sex = -1
        </if>
        order by rand() limit 0,1
    </select>
</mapper>