✅私信视频发送不了
✅广场图片上传失败
✅私信图片发送不了

✅女主播给男用户打视频扣男用户的金币（现在是扣发起人的）

请修复视频通话系统中的费用结算逻辑问题。当前系统存在以下问题：

**当前问题分析：**
1. 发起通话接口：`initiateCommunicateTelephone`
2. 结束通话接口：`finishCommunicateTelephone`
3. 核心结算逻辑：`appCommunicateTelephoneRecordsService.getTelResult`

**现有问题：**
- 问题1：发起通话时只检查发起人的余额，但实际应该检查付费方（男性用户）的余额
- 问题2：结束通话时扣费逻辑与收益记账逻辑不一致，导致账单记录错误

**具体修复需求：**

**需求1 - 男性发起通话场景(对方是女性时)：**
- 发起前：检查男性用户金币余额是否足够支付通话费用
- 余额不足：返回充值提示
- 余额充足：允许发起通话
- 结束后：从男性用户扣除金币，在女性用户钻石账单中增加对应收益

**需求2 - 女性发起通话场景(对方是男性时)：**
- 发起前：检查男性用户（付费方）金币余额是否足够
- 余额不足：在女性客户端返回"对方金币不足"的提示信息
- 余额充足：允许发起通话
- 结束后：从男性用户扣除金币，在女性用户钻石账单中增加对应收益
 
 需求3 - 男和男打,女和女打
发起前:检查发起人的金币余额是否足够支付通话费用
余额不足:返回充值提示
余额充足:允许发起通话
结束后:从发起人扣除金币，在接收人用户钻石账单中增加对应收益


**技术要求：**
1. 修改发起通话接口的余额检查逻辑，确保始终检查付费方（男性）的余额
2. 修复结束通话接口的费用结算逻辑，确保扣费和收益记账的一致性
3. 确保钻石账单记录的准确性，收益应记录在女性用户账户中

请分析相关代码文件，制定详细的修改计划，并实施这些修复。



✅女用户给男用户发送三条私信，男的只要有回复，女用户才能主动给该男用户打视频
✅腾讯云监管取消**
✅一键清除聊天内容，只删除自己的记录，对方依然能看到
一键清除聊天内容接口
POST  https://api.vvparty.cn/client/app/api/message/clearAllChatHistory/v1_0_0

✅Vip功能不能用
✅礼物收入比例0.5。现在0。6
✅后台手动调整女主播价格
✅解锁所有的视频通话金币设置


隐藏语音房间厅 
女用户端直接点私信发送搭讪文案
一键清楚聊天内容
密友功能隐藏
Logo 下拉等待图



# 渠道邀请

✅1.A邀请B下载,B是男用户,充值100元,A获得30%的收益
✅2.A邀请B下载,B是女用户,当B收到100元礼物和视频通话,A获得5%的收益，
✅3.多层级收益业务移除



# 公会长收益
✅1.主播加入公会,默认是50%的礼物收益,实时结算
✅2.A主播收到100元礼物和视频通话,A主播获得50%的收益,公会长可以获得15%的收益


以下内容基于对 `xinxuan1v1-server/client/src/main/java/com/hzy/client/service/AppGiftService.java` 中 `processGiftGiving` 及其相关辅助方法/调用方的源码阅读所得，力求还原真实的技术实现。若存在逻辑缺陷或潜在 Bug，也一并指出，供参考。  

--------------------------------------------------------------------
一、分佣对象  
1. 接收者（主播／被送礼用户）  
2. 厅主（房间拥有者，chatRoom.hallOwnerUserId）  
3. 公会长（接收者所在公会的会长）  
4. 渠道上级（仅在调用方 `giveGifts2 / giveGifts3` 中额外处理，`processGiftGiving` 本身不管）  
5. **系统**（并未显式建账，但礼物剩余部分即视为平台留存）  

--------------------------------------------------------------------
二、分佣比例计算逻辑  
1. 接收者钻石比例 giftIncomeScale  
   • 默认：`AppConfig.giftIncomeScale`（全局配置）。  
   • 若收礼人未实名认证 → 直接用默认值。  
   • 已实名认证：  
      ① 先取当月累计礼物收入 `billSumAmountOfMonth`。  
      ② 调用 `appGiftIncomeConfigService.getGiftIncomeRate(...)`  
          – 按「每月收入区间表」(`app_gift_income_config`) 返回对应比例；  
          – 若收礼人是公会成员，则优先用 `AppGuildMember.giftIncomeScale`；  
          – 都无匹配时才回落到全局默认。  
   • 最终公式  
      `receiverPoints = price × giftIncomeScale ÷ onePointsEqGold`  
   ※ price 为本次礼物金币总价。`onePointsEqGold` 为金币↔钻石汇率。  

2. 厅主分成 commissionScale  
   • 来源：`chatRoom.getHallOwnerCommissionScale()`，房间维度配置。  
   • 公式  
      `hallOwnerPoints = price × commissionScale ÷ onePointsEqGold`  
   • 仅在礼物发生于聊天室且配置不为 null 时生效。  

3. 公会长分成 guildLeadScale  
   • `AppGuildMapper.getGuildLeadScale(toUserId)` 返回  
      `(会长礼物比例 − 主播礼物比例)`，例如 0.7-0.65=0.05。  
   • 公式  
      `guildLeadPoints = price × guildLeadScale ÷ onePointsEqGold`  
   • 仅当差值 > 0 时生成。  

4. 渠道分成（调用方额外逻辑，非本方法核心）  
   • 入口：`channelCommissionRecordService.saveChannelGiftRecordsForFemale(...)`  
   • 触发条件：  
      – 赠送的不是「游戏背包礼物」；  
      – 收礼人性别为女；  
   • 固定比例 10%（硬编码 `BigDecimal("0.10")`）；生成积分分佣记录并给上级渠道用户加积分。  

--------------------------------------------------------------------
三、分佣流程与顺序  
1. 计算 receiverPoints → 计算 hallOwner / guildLeadPoints → 封装 `PointsDistribution`  
2. 写入礼物主记录 `user_give_gift`  
3. 账单落库（`recordBillsAndUpdateBalances`）：  
   a) 赠送者金币账单（负数）  
   b) 接收者钻石账单  
      • 若主播没有加入公会或其 `giftIncomeScale == 0.6` → 立即加余额  
      • 否则仅记账单，金额置 0，等待后续结算（公会抽成）  
   c) 厅主账单 + 立即加余额  
   d) 公会长账单（**只记账，添加余额的代码被注释掉** – 可能是未完成的业务）  
4. 等级更新、消息推送、房间热度等附加操作  
5. 调用方返回后，若满足渠道返佣条件，再异步生成渠道分佣记录并给渠道用户加积分。  

--------------------------------------------------------------------
四、配置来源  
1. `AppConfig` 表  
   • `giftIncomeScale`  系统默认主播收益比例  
   • `oneGoldEqRmb / oneRmbEqPoints / onePointsEqGold` 各类汇率  
   • `fullServerFloatingScreenPrice` 全服飘屏阈值  
2. `app_gift_income_config` 表  
   • 每月礼物收入区间与对应收益比例  
3. `app_chat_room.hall_owner_commission_scale` 字段  
4. `app_guild_member.gift_income_scale` 公会主播独立比例  
5. `AppGuildMapper.getGuildLeadScale` 由会长与主播比例差值计算  
6. 渠道分佣比例(10%) 直接在代码中硬编码，暂不可配置。  

--------------------------------------------------------------------
五、特殊业务规则 / 潜在缺陷  
1. 未实名认证主播统一走默认 60% 逻辑。  
2. 公会主播若分成比例 ≠ 0.6，将不立即到账，需要后续「公会结算」流程发放；否则立即入账。  
3. GuildLead Points 只写账单、未加余额，可能遗漏实际收益发放。  
4. `goldCalculatePoints` 中区间判断写法使用 `i+1`，当遍历到最后一个元素会越界（潜在 Bug）。  
5. `new BigDecimal("0.6")` 与数据库比例精度不同时可能导致 `compareTo` 结果异常。  
6. 频道返佣目前仅针对女性收礼场景，且比例写死为 10%，缺少配置灵活性。  

--------------------------------------------------------------------
六、数据库落表一览  
1. `user_give_gift`        —— 礼物主记录  
2. `app_user_gold_bill`    —— 赠送者金币账单  
3. `app_user_points_bill`  —— 接收者 / 厅主 / 公会长钻石账单  
4. `app_user`              —— 通过 `addUserPointsBalance / subtractUserGoldBalance` 更新余额字段  
5. `channel_commission_record`（调用方生成） —— 渠道分佣记录  

--------------------------------------------------------------------
总结  
`processGiftGiving` 的核心思路是“先把接收者应得钻石算出来 → 再追加厅主、公会长差额 → 账单入库 → 按条件即时加余额”。  
• 接收者收益比例可随实名认证状态、月收入区间、是否加入公会而动态变化；  
• 厅主、公会长分成则是固定比例差额；  
• 渠道返佣和部分公会结算被放在外围或后续流程中完成。  

若要保证收益结算准确，应重点关注：  
1) 礼物收益比例配置的完整性及越界问题；  
2) 公会长收益未到账的缺漏；  
3) 确认各比例之和是否符合公司财务预期，避免 “总分佣 > 100%” 的隐性错误。

# 友盟无码邀请下载
注册友盟


# 修改通话扣费逻辑

修改通话扣费的时间，发起通话时后，如果对方接通了；
在appMessageService.handleCommunicateTelephoneInvite方法中就可以进行第一分钟的扣费的业务；
后续的每分钟的第一秒中，根据监听的redis的key，进行扣费的逻辑；
假如A的通话费用为100金币1分钟，B的金币余额为150金币；
B向A发起通话，A接通了；当时就从B的金币余额中扣除100金币，并记录到A的钻石账单中；
然后A和B开始通话，通话过程中，每分钟的第一秒中，根据监听的redis的key，进行扣费的逻辑；
第二分钟的第一秒,B的余额为150-100=50金币；此时是不够第二分钟通话的，在第一秒的时候进行ws通知双方，B的余额不足（现有的代码中有通知余额不足的逻辑），然后在第二分钟的第17秒强制进行挂断电话；并将B余额内全部扣除，并记录到A的钻石账单中；

监听的事件在RedisVerdueKeyListening中；
PrivateLetterWebSocket中还有通话的心跳判断在线的逻辑，也有redis监听事件；

现在的需求是，要设计一个合理的方案，达成这个需求；