<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppGiftCategoryMapper">

    <resultMap type="AppGiftCategory" id="AppGiftCategoryResult">
        <result property="id" column="id"/>
        <result property="categoryName" column="category_name"/>
        <result property="icoUrl" column="ico_url"/>
        <result property="isDel" column="deleted"/>
        <result property="sort" column="sort"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedTime" column="updated_time"/>
        <result property="createdBy" column="created_by"/>
        <result property="updatedBy" column="updated_by"/>
    </resultMap>

    <sql id="selectAppGiftCategoryVo">
        select id,
        category_name,
        ico_url,
        deleted,
        sort,
        created_time,
        updated_time,
        created_by,
        updated_by
        from app_gift_category
    </sql>

    <select id="getName" resultType="java.lang.String" parameterType="java.lang.Long">
        select category_name from app_gift_category where id=#{id}
    </select>

    <select id="selectAppGiftCategoryList" parameterType="AppGiftCategory" resultMap="AppGiftCategoryResult">
        <include refid="selectAppGiftCategoryVo"/>
        where deleted=false
        <if test="categoryName != null  and categoryName != ''">and category_name like concat('%', #{categoryName},
            '%')
        </if>
        order by sort asc, created_time desc
    </select>

    <select id="selectAppGiftCategoryById" parameterType="Long" resultMap="AppGiftCategoryResult">
        <include refid="selectAppGiftCategoryVo"/>
        where id = #{id} and deleted=false
    </select>

    <update id="updateAppGiftCategory" parameterType="AppGiftCategory">
        update app_gift_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryName != null">category_name = #{categoryName},</if>
            <if test="icoUrl != null">ico_url = #{icoUrl},</if>
            <if test="isDel != null">deleted = #{isDel},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppGiftCategoryById" parameterType="Long">
        delete
        from app_gift_category
        where id = #{id}
    </delete>

    <delete id="deleteAppGiftCategoryByIds" parameterType="String">
        delete from app_gift_category where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getGiftCountByCategoryId" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select count(*)
        from app_gift gi
        where gi.deleted = false
        and gi.category_id = #{giftCategoryId}
    </select>
    <select id="selectAppGiftCategoryByName" resultType="com.hzy.core.entity.AppGiftCategory"
            parameterType="java.lang.String">
        SELECT id, category_name, ico_url, deleted, sort, created_time, updated_time, created_by, updated_by
        FROM app_gift_category
        WHERE category_name = #{categoryName} AND deleted = '0'
    </select>

</mapper>