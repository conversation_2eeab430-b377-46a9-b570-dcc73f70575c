<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppUserIntimacyMapper">

    <resultMap type="AppUserIntimacy" id="AppUserIntimacyResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="toUserId" column="to_user_id"/>
        <result property="intimacyValue" column="intimacy_value"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAppUserIntimacyVo">
        select id, user_id, to_user_id, intimacy_value, create_time, update_time
        from app_user_intimacy
    </sql>

    <select id="selectAppUserIntimacyList" parameterType="AppUserIntimacy" resultMap="AppUserIntimacyResult">
        <include refid="selectAppUserIntimacyVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="toUserId != null ">and to_user_id = #{toUserId}</if>
            <if test="intimacyValue != null ">and intimacy_value = #{intimacyValue}</if>
        </where>
    </select>

    <select id="selectAppUserIntimacyById" parameterType="Long" resultMap="AppUserIntimacyResult">
        <include refid="selectAppUserIntimacyVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppUserIntimacy" parameterType="AppUserIntimacy">
        insert into app_user_intimacy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="toUserId != null">to_user_id,</if>
            <if test="intimacyValue != null">intimacy_value,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="toUserId != null">#{toUserId},</if>
            <if test="intimacyValue != null">#{intimacyValue},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateAppUserIntimacy" parameterType="AppUserIntimacy">
        update app_user_intimacy
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="toUserId != null">to_user_id = #{toUserId},</if>
            <if test="intimacyValue != null">intimacy_value = #{intimacyValue},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppUserIntimacyById" parameterType="Long">
        delete
        from app_user_intimacy
        where id = #{id}
    </delete>

    <delete id="deleteAppUserIntimacyByIds" parameterType="String">
        delete from app_user_intimacy where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getUserIntimacy" parameterType="Long" resultMap="AppUserIntimacyResult">
        <include refid="selectAppUserIntimacyVo"/>
        where ((user_id = #{userId} and to_user_id=#{toUserId}) or (user_id = #{toUserId} and to_user_id=#{userId}))
    </select>

    <update id="addUserIntimacy">
        update app_user_intimacy
        set intimacy_value=intimacy_value + #{intimacyValue},
        update_time=now()
        where id = #{id}
    </update>

    <select id="getUserIntimacyList" resultType="com.hzy.core.model.vo.app.AppViewUserInfoVo"
            parameterType="java.lang.Long">
        select u.id as userId,
        u.recode_code as recodeCode,
        u.last_operating_time as lastOperatingTime,
        u.photo_album as photoAlbumStr,
        u.is_online as isOnline,
        u.nick_name as nickName,
        u.is_real_person_auth as isRealPersonAuth,
        u.is_real_name_auth as isRealNameAuth,
        if(u.phone!=''and u.phone is not null,1,0) as isPhoneAuth,

        u.birthday,
        u.age,
        u.sex,
        u.personal_signature as personalSignature,
        u.head_portrait as headPortrait,
        u.education_background as educationBackground,
        u.constellation,
        u.height,
        u.weight,
        u.location,
        u.annual_income as annualIncome,
        u.purchase_situation as purchaseSituation,
        u.car_purchase_situation as carPurchaseSituation,
        u.self_introduction as selfIntroduction,
        u.label,
        u.voice_signature as voiceSignature,
        u.video,
        u.longitude,
        u.latitude,
        u.province,
        u.city,
        u.area,
        uin.intimacy_value as intimacyValue
        from app_user_intimacy uin,
        app_user u
        where (uin.user_id = #{userId} or uin.to_user_id = #{userId})
        and u.id = if(uin.user_id = #{userId}, uin.to_user_id, uin.user_id)
        and u.id not in (select b.to_user_id from app_blacklist b where b.user_id = #{userId})
        and u.id not in (select b.user_id from app_blacklist b where b.to_user_id = #{userId})
        order by intimacyValue desc, uin.id desc
    </select>
</mapper>