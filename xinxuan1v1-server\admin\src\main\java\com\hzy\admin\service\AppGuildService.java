package com.hzy.admin.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hzy.core.config.RedisCache;
import com.hzy.core.constant.ThreadPoolConstants;
import com.hzy.core.controller.BaseController;
import com.hzy.core.entity.*;
import com.hzy.core.enums.AppChatRoomStatusTypeEnums;
import com.hzy.core.enums.AppChatRoomTypeEnums;
import com.hzy.core.enums.AppSysMsgTypeEnums;
import com.hzy.core.enums.WhetherTypeEnum;
import com.hzy.core.exception.AppException;
import com.hzy.core.mapper.*;
import com.hzy.core.model.dto.admin.AddGuildMemberDto;
import com.hzy.core.model.vo.AjaxResult;
import com.hzy.core.model.vo.admin.GuildVo;
import com.hzy.core.model.vo.app.*;
import com.hzy.core.page.TableDataInfo;
import com.hzy.core.service.AppGuildApplyService;
import com.hzy.core.service.AppGuildMemberService;
import com.hzy.core.service.SysUserService;
import com.hzy.core.service.common.CommonService;
import com.hzy.core.service.common.IdentityPermissionsUtilService;
import com.hzy.core.utils.PageHelperUtils;
import com.hzy.core.utils.SecurityUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 公会Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-27
 */
@Service
public class AppGuildService extends BaseController {
    private final static ArrayBlockingQueue<Runnable> WORK_QUEUE = new ArrayBlockingQueue<>(3000);
    private final static RejectedExecutionHandler HANDLER = new ThreadPoolExecutor.CallerRunsPolicy();
    private static final ThreadPoolExecutor executorService = new ThreadPoolExecutor(ThreadPoolConstants.corePoolSize, ThreadPoolConstants.maxPoolSize, ThreadPoolConstants.keepAliveSeconds, TimeUnit.SECONDS, WORK_QUEUE, HANDLER);
    @Resource
    private AppGuildMapper appGuildMapper;
    @Resource
    private AppGuildMemberService appGuildMemberService;
    @Resource
    private AppGuildStatisticsMapper appGuildStatisticsMapper;
    @Resource
    private AppChatRoomMapper appChatRoomMapper;
    @Resource
    private AppUserMapper appUserMapper;
    @Resource
    private RedisCache redisCache;
    @Resource
    private AppGuildMemberMapper appGuildMemberMapper;
    @Resource
    private CommonService commonService;
    @Resource
    private AppGuildReturnRecordsMapper appGuildReturnRecordsMapper;
    @Resource
    private AppGuildApplyMapper appGuildApplyMapper;
    @Resource
    private AppGuildApplyService appGuildApplyService;
    @Resource
    private AppSysMsgMapper appSysMsgMapper;
    @Resource
    private AppConfigMapper appConfigMapper;
    @Autowired
    private SysUserService sysUserService;
    @Resource
    private SysUserClosureMapper sysUserClosureMapper;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private SysAnchorManagementMapper sysAnchorManagementmapper;
    @Resource
    private IdentityPermissionsUtilService identityPermissionsUtilService;

    /**
     * 查询公会
     *
     * @param id 公会主键
     * @return 公会
     */
    public AppGuild selectAppGuildById(Long id) {
        AppGuild appGuild = appGuildMapper.selectAppGuildById(id);
        if (null == appGuild) {
            throw new AppException("公会不存在");
        }
        return appGuild;
    }

    /**
     * 查询公会列表
     *
     * @param appGuild 公会
     * @return 公会
     */
    public List<AppGuild> selectAppGuildList(AppGuild appGuild) {
        return appGuildMapper.selectAppGuildList(appGuild);
    }

    public List<AppGuild> selectAppGuildList1(AppGuild appGuild) {
        List<AppGuild> list = appGuildMapper.selectAppGuildList1(appGuild);
        for (AppGuild guild : list) {
            String brokerName = sysUserClosureMapper.selectAncestorsName(guild.getSysUserId());
            if (ObjectUtil.isNotEmpty(brokerName)) {
                guild.setSysUserNickName(brokerName);
            }
        }
        return list;
    }

    /**
     * 查询某运营下的公会列表
     */
    public AjaxResult getGuildOfOperation(Long operationId) {

        if (operationId == 1){
            List<AppGuildListVo> guildList = appGuildMapper.getGuildList(null);
            return AjaxResult.success(guildList);
        }

        List<Long> guildId = identityPermissionsUtilService.getGuildIdsBySysUserId(operationId);
        if (ObjectUtil.isEmpty(guildId)) {
            return AjaxResult.success(new ArrayList<>());
        }
        List<AppGuild> appGuilds = appGuildMapper.selectBatchIds(guildId);
        return AjaxResult.success(appGuilds);
    }

    /**
     * 查询工会经纪人
     *
     * <AUTHOR>
     * @date 2025/4/18 下午3:03
     */
    public AjaxResult getGuildBrokers(Long guildId) {
        // 查询工会
        AppGuild appGuild = appGuildMapper.selectAppGuildById(guildId);
        if (ObjectUtil.isEmpty(appGuild)) {
            return AjaxResult.error("公会不存在");
        }

        // 获取工会长id
        Long userId = appGuild.getUserId();
        List<Long> brokerIds = sysUserClosureMapper.selectAllDescendants(userId);
        if (ObjectUtil.isEmpty(brokerIds)) {
            return AjaxResult.success(new ArrayList<>());
        }
        List<SysUser> brokerList = sysAnchorManagementmapper.queryBrokerList(null, brokerIds);
        return AjaxResult.success(brokerList);
    }

    public BigDecimal selectAppGuildTotal1(AppGuild appGuild) {
        List<AppGuild> guildList = appGuildMapper.selectAppGuildTotal1(appGuild);
        List<BigDecimal> collect = guildList.stream().map(AppGuild::getGuildFlowAmount).collect(Collectors.toList());
        BigDecimal amount = collect.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        return amount;
    }

    public BigDecimal selectAppGuildTotal2(AppGuild appGuild) {
        AppGuild appGuild1 = null;
        BigDecimal totalAmount = BigDecimal.ZERO;
        List<AppGuild> guildList = appGuildMapper.selectAppGuildList2(appGuild);
        for (AppGuild guild : guildList) {
            appGuild1 = new AppGuild();
            appGuild1.setId(guild.getId());
            appGuild1.setQueryBeginTime(appGuild.getQueryBeginTime());
            appGuild1.setQueryEndTime(appGuild.getQueryEndTime());
            appGuild1.setType(appGuild.getType());
            totalAmount = appGuildMapper.getTotalByGuildId(appGuild1);
            guild.setGuildFlowAmount(totalAmount);
        }
        List<BigDecimal> collect = guildList.stream().map(AppGuild::getGuildFlowAmount).collect(Collectors.toList());
        BigDecimal amount = collect.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        return amount;
    }

    /**
     * 新增公会
     *
     * @param appGuild 公会
     * @return 结果
     */
    public int insertAppGuild(AppGuild appGuild) {

        appGuild.setUserId(null);
        appGuild.setIsTop(null);

        // Long guildLeaderUserId = appUserMapper.getUserIdByRecode(appGuild.getGuildLeaderUserRecodeCode());
        Long userId = appUserMapper.getUserIdByRecode(appGuild.getGuildLeaderUserRecodeCode());
        if (userId == null) {
            throw new AppException("会长不存在");
        }
        // Integer userIdByAuth = appUserMapper.getUserIdByAuth(userId);
        AppUserEntity appUserEntity = appUserMapper.selectAppUserById(userId);
        if (appUserEntity.getIsRealNameAuth().equals(0)) {
            throw new AppException("该用户还没有实名");
        }
        /*if (null == guildLeaderUserId) {
            throw new AppException("会长不存在");
        }*/

        // 判断用户是否已经是公会会长
        if (appGuildMapper.userIsGuild(userId)) {
            throw new AppException("该用户已是其他公会会长");
        }
        // 判断用户是否已加入其他公会
        Long userJoinGuildId = appGuildMapper.getUserJoinGuildId(userId);
        if (null != userJoinGuildId) {
            throw new AppException("该用户已加入其他公会");
        }

        AppChatRoom appChatRoom = new AppChatRoom();
        appChatRoom.setHallOwnerUserId(userId);
        appChatRoom.setStatus(Long.valueOf(AppChatRoomStatusTypeEnums.TYPE1.getId()));
        // 建工会不校验是否有房间
        // List<AppChatRoom> appChatRoomList = appChatRoomMapper.selectAppChatRoomList(appChatRoom);
        // if (ObjectUtils.isEmpty(appChatRoomList)) {
        //     throw new AppException("该用户不是聊天室正常状态的房主，请重新填写");
        // }

        // 添加工会
        appGuild.setIsDel(WhetherTypeEnum.NO.getName());
        appGuild.setCreateTime(new Date());
        appGuild.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        appGuild.setUserId(userId);
        appGuild.setGuildNumber(generateGuildNumber());
        appGuild.setReviewStatus(1);
        // appGuild.setGuildCommissionScale(new BigDecimal("0.7"));
        appGuildMapper.insert(appGuild);

        // 添加工会成员
        AppGuildMember guildMember = new AppGuildMember();
        guildMember.setGuildId(appGuild.getId())
                .setUserId(userId)
                .setIsAdmin(1)
                .setGiftIncomeScale(appConfigMapper.getAppConfig().getGiftIncomeScale())
                .setCreateTime(new Date());
        return appGuildMemberService.save(guildMember) ? 1 : 0;

    }

    /**
     * 修改公会
     *
     * @param appGuild 公会
     * @return 结果
     */
    public int updateAppGuild(AppGuild appGuild) {
        if (null == appGuild.getId()) {
            throw new AppException("参数【id】为空");
        }
        appGuild.setIsTop(null);
        appGuild.setUserId(null);
        AppGuild appGuildOld = appGuildMapper.getGuildInfoById(appGuild.getId());
        if (null == appGuildOld) {
            throw new AppException("公会不存在");
        }
        if (!StringUtils.isBlank(appGuild.getGuildLeaderUserRecodeCode())) {

            Long guildLeaderUserId = appUserMapper.getUserIdByRecode(appGuild.getGuildLeaderUserRecodeCode());
            if (null == guildLeaderUserId) {
                throw new AppException("会长不存在");
            }
            if (!guildLeaderUserId.equals(appGuildOld.getUserId())) {
                // 判断用户是否已经是公会会长
                if (appGuildMapper.userIsGuild(guildLeaderUserId)) {
                    throw new AppException("该用户已是其他公会会长");
                }
                // 判断用户是否已加入其他公会
                Long userJoinGuildId = appGuildMapper.getUserJoinGuildId(guildLeaderUserId);
                if (null != userJoinGuildId) {
                    throw new AppException("该用户已加入其他公会");
                }

                appGuild.setUserId(guildLeaderUserId);
            }


        }

        // 查询后台用户id,是否已经被绑定
        final SysUser sysUser = sysUserService.selectUserById(appGuild.getSysUserId());
        if (ObjectUtil.isNull(sysUser) && ObjectUtil.isNotNull(appGuild.getSysUserId())) {
            throw new AppException("后台用户不存在");
        }

        AppGuild guildBySysUserId = appGuildMapper.getGuildBySysUserId(appGuild.getSysUserId());
        if (ObjectUtil.isNotNull(guildBySysUserId) && !guildBySysUserId.getId().equals(appGuild.getId())) {
            throw new AppException("该后台用户已绑定其他公会");
        }

        appGuild.setGuildNumber(null);
        appGuild.setIsDel(null);
        appGuild.setCreateTime(null);
        appGuild.setCreateBy(null);
        appGuild.setUpdateTime(new Date());
        appGuild.setUpdateBy(SecurityUtils.getLoginUser().getUsername());
        appGuild.setToBeSettled(null);
        return appGuildMapper.updateAppGuild(appGuild);
    }

    /**
     * 获取公会房主成员列表
     *
     * @param page
     * @param size
     * @param guildId
     * @param keyword
     * @return
     */
    public TableDataInfo getGuildHostMemberList(int page, int size, Long guildId, String keyword) {
        if (null == guildId || guildId <= 0) {
            return AjaxResult.getDataTableError("参数【guildId】为空");
        }
        if (com.hzy.core.utils.StringUtils.isBlank(keyword)) {
            keyword = null;
        }
        Page p = PageHelperUtils.startPage(page, size, true);
        // List<Map<String, Object>> list = appGuildMemberMapper.getGuildHostMemberList(guildId, keyword, WhetherTypeEnum.YES.getName());
        List<GuildVo> list = appGuildMemberMapper.getGuildMemberHallList(guildId);
        List<GuildVo> collect = list.stream()
                .collect(Collectors.toMap(
                        GuildVo::getUserId, // key 是id
                        Function.identity(), // value 是Person对象本身
                        (existing, replacement) -> existing // 如果key冲突，保留现有的
                )).values().stream().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            collect = new ArrayList<>();
        }

        return AjaxResult.getDataTable(collect, p.getTotal());
    }

    /**
     * 获取公会普通成员列表
     *
     * @param page
     * @param size
     * @param guildId
     * @param keyword
     * @return
     */
    public TableDataInfo getGuildMemberList(int page, int size, Long guildId, String keyword) {
        if (null == guildId || guildId <= 0) {
            return AjaxResult.getDataTableError("参数【guildId】为空");
        }
        if (com.hzy.core.utils.StringUtils.isBlank(keyword)) {
            keyword = null;
        }

        Page p = PageHelperUtils.startPage(page, size, true);
        List<Map<String, Object>> list = appGuildMemberMapper.getGuildMemberList(guildId, keyword, WhetherTypeEnum.YES.getName());
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }

        return AjaxResult.getDataTable(list, p.getTotal());
    }

    /**
     * 获取本周新增成员列表
     *
     * @param page
     * @param size
     * @param guildId
     * @return
     */
    public TableDataInfo getWeekNewAddMemberList(int page, int size, Long guildId) {
        if (null == guildId || guildId <= 0) {
            return AjaxResult.getDataTableError("参数【guildId】为空");
        }
        AppGuildVo appGuildVo = appGuildMapper.getAppGuild(guildId);
        if (null == appGuildVo) {
            return AjaxResult.getDataTableError("公会不存在");
        }
        Page p = PageHelperUtils.startPage(page, size, true);
        List<Map<String, Object>> list = appGuildMemberMapper.getWeekNewAddMemberList(guildId);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }

        return AjaxResult.getDataTable(list, p.getTotal());
    }

    /**
     * 获取本周活跃成员列表
     *
     * @param page
     * @param size
     * @param guildId
     * @return
     */
    public TableDataInfo getWeekDynamicMemberList(int page, int size, Long guildId) {
        if (null == guildId || guildId <= 0) {
            return AjaxResult.getDataTableError("参数【guildId】为空");
        }

        AppGuildVo appGuildVo = appGuildMapper.getAppGuild(guildId);
        if (null == appGuildVo) {
            return AjaxResult.getDataTableError("公会不存在");
        }


        Page p = PageHelperUtils.startPage(page, size, true);
        List<Map<String, Object>> list = appGuildMemberMapper.getWeekDynamicMemberList(guildId);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }

        return AjaxResult.getDataTable(list, p.getTotal());
    }

    /**
     * 获取公会营收数据
     *
     * @param guildId
     * @return
     */


    /*public AjaxResult getGuildRevenueStatisticsInfo(Long guildId) {
        if (null == guildId || guildId <= 0) {
            return AjaxResult.error("参数【guildId】为空");
        }
        AppGuildVo appGuildVo = appGuildMapper.getAppGuild(guildId);
        if (null == appGuildVo) {
            return AjaxResult.error("公会不存在");
        }


        Map<String, Object> result = new HashMap<>();

        //今日数据
        AppGuildRevenueStatisticsInfoVo toDayInfo = new AppGuildRevenueStatisticsInfoVo();
        toDayInfo.setGuildGiftSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildGiftSum(guildId, 1));
        toDayInfo.setChatRoomSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByChatRoomSum(guildId, 1));
        toDayInfo.setGuildChatRoomSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildChatRoomSum(guildId, 1));
        toDayInfo.setGuildEarnings(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildEarnings(guildId, 1));


        //本周数据
        AppGuildRevenueStatisticsInfoVo currentWeekInfo = new AppGuildRevenueStatisticsInfoVo();
        currentWeekInfo.setGuildGiftSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildGiftSum(guildId, 2));
        currentWeekInfo.setChatRoomSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByChatRoomSum(guildId, 2));
        currentWeekInfo.setGuildChatRoomSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildChatRoomSum(guildId, 2));
        currentWeekInfo.setGuildEarnings(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildEarnings(guildId, 2));


        //本月数据
        AppGuildRevenueStatisticsInfoVo currentMonthInfo = new AppGuildRevenueStatisticsInfoVo();
        currentMonthInfo.setGuildGiftSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildGiftSum(guildId, 3));
        currentMonthInfo.setChatRoomSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByChatRoomSum(guildId, 3));
        currentMonthInfo.setGuildChatRoomSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildChatRoomSum(guildId, 3));
        currentMonthInfo.setGuildEarnings(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildEarnings(guildId, 3));

        result.put("toDayInfo", toDayInfo);//今日数据

        result.put("currentWeekInfo", currentWeekInfo);//本周数据

        result.put("currentMonthInfo", currentMonthInfo);//本月数据


        return AjaxResult.success(result);
    }
*/
    public AjaxResult getGuildRevenueStatisticsInfo(Long guildId) {
        if (null == guildId || guildId <= 0) {
            return AjaxResult.error("参数【guildId】为空");
        }
        AppGuildVo appGuildVo = appGuildMapper.getAppGuild(guildId);
        if (null == appGuildVo) {
            return AjaxResult.error("公会不存在");
        }


        Map<String, Object> result = new HashMap<>();

        /*//今日数据
        AppGuildRevenueStatisticsInfoVo toDayInfo = new AppGuildRevenueStatisticsInfoVo();
        toDayInfo.setGuildGiftSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildGiftSum1(guildId, 1));
        toDayInfo.setChatRoomSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByChatRoomSum1(guildId, 1));
        toDayInfo.setGuildChatRoomSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildChatRoomSum(guildId, 1));
        toDayInfo.setGuildEarnings(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildEarnings(guildId, 1));*/

        // 今日数据
        AppGuildRevenueStatisticsInfoVo toDayInfo = new AppGuildRevenueStatisticsInfoVo();
        toDayInfo.setGuildGiftSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildGiftSum2(guildId, 1));
        toDayInfo.setChatRoomSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByChatRoomSum1(guildId, 1));
        toDayInfo.setGuildChatRoomSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByChatRoomSum1(guildId, 1));
        toDayInfo.setGuildEarnings(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildEarnings(guildId, 1));


        /*//本周数据
        AppGuildRevenueStatisticsInfoVo currentWeekInfo = new AppGuildRevenueStatisticsInfoVo();
        currentWeekInfo.setGuildGiftSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildGiftSum1(guildId, 2));
        currentWeekInfo.setChatRoomSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByChatRoomSum1(guildId, 2));
        currentWeekInfo.setGuildChatRoomSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildChatRoomSum(guildId, 2));
        currentWeekInfo.setGuildEarnings(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildEarnings(guildId, 2));*/

        // 本周数据
        AppGuildRevenueStatisticsInfoVo currentWeekInfo = new AppGuildRevenueStatisticsInfoVo();
        currentWeekInfo.setGuildGiftSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildGiftSum2(guildId, 2));
        currentWeekInfo.setChatRoomSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByChatRoomSum1(guildId, 2));
        currentWeekInfo.setGuildChatRoomSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByChatRoomSum1(guildId, 2));
        currentWeekInfo.setGuildEarnings(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildEarnings(guildId, 2));

        // 本月数据
        AppGuildRevenueStatisticsInfoVo currentMonthInfo = new AppGuildRevenueStatisticsInfoVo();
        currentMonthInfo.setGuildGiftSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildGiftSum2(guildId, 3));
        currentMonthInfo.setChatRoomSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByChatRoomSum1(guildId, 3));
        currentMonthInfo.setGuildChatRoomSum(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByChatRoomSum1(guildId, 3));
        currentMonthInfo.setGuildEarnings(appGuildStatisticsMapper.getGuildRevenueStatisticsInfoByGuildEarnings(guildId, 3));

        result.put("toDayInfo", toDayInfo);// 今日数据

        result.put("currentWeekInfo", currentWeekInfo);// 本周数据

        result.put("currentMonthInfo", currentMonthInfo);// 本月数据


        return AjaxResult.success(result);
    }

    /**
     * 获取公会成员统计信息
     *
     * @param guildId
     * @return
     */
    public AjaxResult getGuildMemberStatisticsInfo(Long guildId) {
        if (null == guildId || guildId <= 0) {
            return AjaxResult.error("参数【guildId】为空");
        }
        AppGuildVo appGuildVo = appGuildMapper.getAppGuild(guildId);
        if (null == appGuildVo) {
            return AjaxResult.error("公会不存在");
        }

        // 今日数据
        AppGuildMemberStatisticsInfoVo toDayInfo = new AppGuildMemberStatisticsInfoVo();
        toDayInfo.setLoginNumber(appGuildStatisticsMapper.getGuildMemberStatisticsInfoByLoginNumber(guildId, 1));
        toDayInfo.setReceiveGiftsNumber(appGuildStatisticsMapper.getGuildMemberStatisticsInfoByReceiveGiftsNumber(guildId, 1));
        toDayInfo.setGearNumber(appGuildStatisticsMapper.getGuildMemberStatisticsInfoByGearNumber(guildId, 1));


        // 本周数据
        AppGuildMemberStatisticsInfoVo currentWeekInfo = new AppGuildMemberStatisticsInfoVo();
        currentWeekInfo.setLoginNumber(appGuildStatisticsMapper.getGuildMemberStatisticsInfoByLoginNumber(guildId, 2));
        currentWeekInfo.setReceiveGiftsNumber(appGuildStatisticsMapper.getGuildMemberStatisticsInfoByReceiveGiftsNumber(guildId, 2));
        currentWeekInfo.setGearNumber(appGuildStatisticsMapper.getGuildMemberStatisticsInfoByGearNumber(guildId, 2));

        // 本月数据
        AppGuildMemberStatisticsInfoVo currentMonthInfo = new AppGuildMemberStatisticsInfoVo();
        currentMonthInfo.setLoginNumber(appGuildStatisticsMapper.getGuildMemberStatisticsInfoByLoginNumber(guildId, 3));
        currentMonthInfo.setReceiveGiftsNumber(appGuildStatisticsMapper.getGuildMemberStatisticsInfoByReceiveGiftsNumber(guildId, 3));
        currentMonthInfo.setGearNumber(appGuildStatisticsMapper.getGuildMemberStatisticsInfoByGearNumber(guildId, 3));

        Map<String, Object> result = new HashMap<>();

        result.put("toDayInfo", toDayInfo);// 今日数据

        result.put("currentWeekInfo", currentWeekInfo);// 本周数据

        result.put("currentMonthInfo", currentMonthInfo);// 本月数据


        return AjaxResult.success(result);
    }

    public PageInfo<AppGuildApplyListVo> getGuildJoinRecordsList(Integer pageNum, Integer pageSize, @NotNull Long guildId) {
        PageHelper.startPage(pageNum, pageSize);
        List<AppGuildApplyListVo> list = appGuildApplyMapper.selectGuildApplyList(guildId);
        return new PageInfo<>(list);
    }

    /**
     * 获取公会退会记录
     *
     * @param page
     * @param size
     * @param guildId
     * @return
     */
    public TableDataInfo getGuildReturnRecordsList(int page, int size, Long guildId) {
        if (null == guildId || guildId <= 0) {
            return AjaxResult.getDataTableError("参数【guildId】为空");
        }

        AppGuildVo appGuildVo = appGuildMapper.getAppGuild(guildId);
        if (null == appGuildVo) {
            return AjaxResult.getDataTableError("公会不存在");
        }

        Page p = PageHelperUtils.startPage(page, size, true);
        List<AppGuildReturnRecordsVo> list = appGuildReturnRecordsMapper.getGuildReturnRecordsList(guildId);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        } else {
            list.forEach(item -> {
                if (null != item.getUserSex() && item.getUserSex().intValue() == -1) {
                    item.setUserSex(null);
                }
                if (com.hzy.core.utils.StringUtils.isBlank(item.getUserBirthday())) {
                    item.setUserAge(null);
                }
                if (null != item.getUserAge() && item.getUserAge().intValue() == -1) {
                    item.setUserAge(null);
                }
            });

        }

        return AjaxResult.getDataTable(list, p.getTotal());
    }

    /**
     * 获取公会房间统计详情
     *
     * @param
     * @param guildId
     * @param chatRoomId
     * @param queryBeginTime
     * @return
     */
    public AjaxResult getGuildChatRoomStatisticsInfo(Long guildId, Long chatRoomId, String queryBeginTime) {
        if (null == guildId || guildId <= 0) {
            return AjaxResult.error("参数【guildId】为空");
        }
        if (null == chatRoomId || chatRoomId <= 0) {
            return AjaxResult.error("参数【chatRoomId】为空");
        }

        AppGuildVo appGuildVo = appGuildMapper.getAppGuild(guildId);
        if (null == appGuildVo) {
            return AjaxResult.error("公会不存在");
        }

        AppChatRoom chatRoom = appChatRoomMapper.selectAppChatRoomById(chatRoomId);
        if (null == chatRoom) {
            return AjaxResult.error("房间不存在");
        }
        /*if (!chatRoom.getGuildId().equals(guildId)) {
            return AjaxResult.error("非本公会房间");
        }*/


        Map<String, Object> result = new HashMap<>();

        // 获取公会成员用户id
        List<Long> guildMemberUserIdList = appGuildMemberMapper.getGuildMemberUserIdList(guildId);
        if (CollectionUtils.isEmpty(guildMemberUserIdList)) {
            guildMemberUserIdList = new ArrayList<>();
        }
        // 将会长添加到list中
        guildMemberUserIdList.add(appGuildVo.getUserId());


        /*//今日数据
        AppGuildChatRoomStatisticsInfoVo toDayInfo = new AppGuildChatRoomStatisticsInfoVo();
        toDayInfo.setChatRoomSum(appGuildStatisticsMapper.getGuildChatRoomStatisticsByChatRoomSum1(guildId, chatRoomId, 1));
        toDayInfo.setGuildSum(appGuildStatisticsMapper.getGuildChatRoomStatisticsByGuildSum1(guildId, chatRoomId, 1, guildMemberUserIdList));
        toDayInfo.setNoGuildSum(toDayInfo.getChatRoomSum().subtract(toDayInfo.getGuildSum()).compareTo(new BigDecimal("0")) < 0 ? new BigDecimal("0") : toDayInfo.getChatRoomSum().subtract(toDayInfo.getGuildSum()));
        toDayInfo.setConsumptionNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByConsumptionNumber1(guildId, chatRoomId, 1));
        toDayInfo.setReceiveGiftsNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByReceiveGiftsNumber1(guildId, chatRoomId, 1));
        toDayInfo.setGearNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByGearNumber(guildId, chatRoomId, 1));
        toDayInfo.setDirectNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByDirectNumber(guildId, chatRoomId, 1));
*/

        // 今日数据
        AppGuildChatRoomStatisticsInfoVo toDayInfo = new AppGuildChatRoomStatisticsInfoVo();
//        toDayInfo.setChatRoomSum(appGuildStatisticsMapper.getGuildChatRoomStatisticsByChatRoomSum2(guildId, chatRoomId, 1));
        toDayInfo.setChatRoomSum(appGuildStatisticsMapper.getGuildChatRoomStatisticsByChatRoomSum3(guildId, chatRoomId, queryBeginTime));
        BigDecimal guildSum = appGuildStatisticsMapper.getGuildChatRoomStatisticsByGuildSum2(guildId, chatRoomId, 1, guildMemberUserIdList, queryBeginTime);
        toDayInfo.setGuildSum(guildSum);
        toDayInfo.setNoGuildSum(toDayInfo.getChatRoomSum()
                .subtract(toDayInfo.getGuildSum())
                .compareTo(new BigDecimal("0")) < 0 ?
                new BigDecimal("0") : toDayInfo.getChatRoomSum()
                .subtract(toDayInfo.getGuildSum()));
        toDayInfo.setConsumptionNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByConsumptionNumber1(guildId, chatRoomId, 1));
        toDayInfo.setReceiveGiftsNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByReceiveGiftsNumber1(guildId, chatRoomId, 1));
        toDayInfo.setGearNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByGearNumber(guildId, chatRoomId, 1));
        toDayInfo.setDirectNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByDirectNumber(guildId, chatRoomId, 1));

        /*//本周数据
        AppGuildChatRoomStatisticsInfoVo currentWeekInfo = new AppGuildChatRoomStatisticsInfoVo();
        currentWeekInfo.setChatRoomSum(appGuildStatisticsMapper.getGuildChatRoomStatisticsByChatRoomSum1(guildId, chatRoomId, 2));
        currentWeekInfo.setGuildSum(appGuildStatisticsMapper.getGuildChatRoomStatisticsByGuildSum1(guildId, chatRoomId, 2, guildMemberUserIdList));
        currentWeekInfo.setNoGuildSum(currentWeekInfo.getChatRoomSum().subtract(currentWeekInfo.getGuildSum()).compareTo(new BigDecimal("0")) < 0 ? new BigDecimal("0") : currentWeekInfo.getChatRoomSum().subtract(currentWeekInfo.getGuildSum()));
        currentWeekInfo.setConsumptionNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByConsumptionNumber1(guildId, chatRoomId, 2));
        currentWeekInfo.setReceiveGiftsNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByReceiveGiftsNumber1(guildId, chatRoomId, 2));
        currentWeekInfo.setGearNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByGearNumber(guildId, chatRoomId, 2));
        currentWeekInfo.setDirectNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByDirectNumber(guildId, chatRoomId, 2));*/

        // 本周数据
        AppGuildChatRoomStatisticsInfoVo currentWeekInfo = new AppGuildChatRoomStatisticsInfoVo();
        currentWeekInfo.setChatRoomSum(appGuildStatisticsMapper.getGuildChatRoomStatisticsByChatRoomSum2(guildId, chatRoomId, 2));
        currentWeekInfo.setGuildSum(appGuildStatisticsMapper.getGuildChatRoomStatisticsByGuildSum2(guildId, chatRoomId, 2, guildMemberUserIdList, queryBeginTime));
        currentWeekInfo.setNoGuildSum(currentWeekInfo.getChatRoomSum()
                .subtract(currentWeekInfo.getGuildSum())
                .compareTo(new BigDecimal("0")) < 0 ? new BigDecimal("0") : currentWeekInfo.getChatRoomSum()
                .subtract(currentWeekInfo.getGuildSum()));
        currentWeekInfo.setConsumptionNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByConsumptionNumber1(guildId, chatRoomId, 2));
        currentWeekInfo.setReceiveGiftsNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByReceiveGiftsNumber1(guildId, chatRoomId, 2));
        currentWeekInfo.setGearNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByGearNumber(guildId, chatRoomId, 2));
        currentWeekInfo.setDirectNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByDirectNumber(guildId, chatRoomId, 2));

        // 本月数据
        AppGuildChatRoomStatisticsInfoVo currentMonthInfo = new AppGuildChatRoomStatisticsInfoVo();
        currentMonthInfo.setChatRoomSum(appGuildStatisticsMapper.getGuildChatRoomStatisticsByChatRoomSum2(guildId, chatRoomId, 3));
        currentMonthInfo.setGuildSum(appGuildStatisticsMapper.getGuildChatRoomStatisticsByGuildSum2(guildId, chatRoomId, 3, guildMemberUserIdList, queryBeginTime));
        currentMonthInfo.setNoGuildSum(currentMonthInfo.getChatRoomSum()
                .subtract(currentMonthInfo.getGuildSum())
                .compareTo(new BigDecimal("0")) < 0 ? new BigDecimal("0") : currentMonthInfo.getChatRoomSum()
                .subtract(currentMonthInfo.getGuildSum()));
        currentMonthInfo.setConsumptionNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByConsumptionNumber1(guildId, chatRoomId, 3));
        currentMonthInfo.setReceiveGiftsNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByReceiveGiftsNumber1(guildId, chatRoomId, 3));
        currentMonthInfo.setGearNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByGearNumber(guildId, chatRoomId, 3));
        currentMonthInfo.setDirectNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByDirectNumber(guildId, chatRoomId, 3));

        /*//上周数据
        AppGuildChatRoomStatisticsInfoVo lastWeekInfo = new AppGuildChatRoomStatisticsInfoVo();
        lastWeekInfo.setChatRoomSum(appGuildStatisticsMapper.getGuildChatRoomStatisticsByChatRoomSum1(guildId, chatRoomId, 4));
        lastWeekInfo.setGuildSum(appGuildStatisticsMapper.getGuildChatRoomStatisticsByGuildSum1(guildId, chatRoomId, 4, guildMemberUserIdList));
        lastWeekInfo.setNoGuildSum(lastWeekInfo.getChatRoomSum().subtract(lastWeekInfo.getGuildSum()).compareTo(new BigDecimal("0")) < 0 ? new BigDecimal("0") : lastWeekInfo.getChatRoomSum().subtract(lastWeekInfo.getGuildSum()));
        lastWeekInfo.setConsumptionNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByConsumptionNumber1(guildId, chatRoomId, 4));
        lastWeekInfo.setReceiveGiftsNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByReceiveGiftsNumber1(guildId, chatRoomId, 4));
        lastWeekInfo.setGearNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByGearNumber(guildId, chatRoomId, 4));
        lastWeekInfo.setDirectNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByDirectNumber(guildId, chatRoomId, 4));*/

        // 上周数据
        AppGuildChatRoomStatisticsInfoVo lastWeekInfo = new AppGuildChatRoomStatisticsInfoVo();
        lastWeekInfo.setChatRoomSum(appGuildStatisticsMapper.getGuildChatRoomStatisticsByChatRoomSum2(guildId, chatRoomId, 4));
        lastWeekInfo.setGuildSum(appGuildStatisticsMapper.getGuildChatRoomStatisticsByGuildSum2(guildId, chatRoomId, 4, guildMemberUserIdList, queryBeginTime));
        lastWeekInfo.setNoGuildSum(lastWeekInfo.getChatRoomSum()
                .subtract(lastWeekInfo.getGuildSum())
                .compareTo(new BigDecimal("0")) < 0 ? new BigDecimal("0") : lastWeekInfo.getChatRoomSum()
                .subtract(lastWeekInfo.getGuildSum()));
        lastWeekInfo.setConsumptionNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByConsumptionNumber1(guildId, chatRoomId, 4));
        lastWeekInfo.setReceiveGiftsNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByReceiveGiftsNumber1(guildId, chatRoomId, 4));
        lastWeekInfo.setGearNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByGearNumber(guildId, chatRoomId, 4));
        lastWeekInfo.setDirectNumber(appGuildStatisticsMapper.getGuildChatRoomStatisticsByDirectNumber(guildId, chatRoomId, 4));


        result.put("toDayInfo", toDayInfo);// 今日数据
        result.put("currentWeekInfo", currentWeekInfo);// 本周数据
        result.put("currentMonthInfo", currentMonthInfo);// 本月数据
        result.put("lastWeekInfo", lastWeekInfo);// 上周数据


        return AjaxResult.success(result);
    }

    /**
     * 获取公会房间列表
     *
     * @param page
     * @param size
     * @param guildId 公会主键
     * @param keyWord 关键字
     * @param status  房间状态：1、进行中，2、已结束
     * @param type    房间类型：1女生，2男生，3点唱，7交友
     * @return
     */
    public TableDataInfo getGuildChatRoomList(int page, int size, Long guildId, String keyWord, Integer status, Integer type) {
        if (null == guildId || guildId <= 0) {
            return AjaxResult.getDataTableError("参数【guildId】为空");
        }
        if (StringUtils.isBlank(keyWord)) {
            keyWord = null;
        }

        Page p = PageHelperUtils.startPage(page, size, true);
        List<AppChatRoomListVo> list = appChatRoomMapper.getGuildChatRoomList(guildId, keyWord, status, type, null, WhetherTypeEnum.YES.getName(), null);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        } else {
            list.forEach(item -> {
                // 获取房间类型名称
                AppChatRoomTypeEnums chatRoomTypeEnums = AppChatRoomTypeEnums.getEnum(item.getChatRoomType()
                        .intValue());
                item.setChatRoomTypeName(null == chatRoomTypeEnums ? "" : chatRoomTypeEnums.getDesc());
            });
        }
        return AjaxResult.getDataTable(list, p.getTotal());
    }

    /**
     * 添加公会管理员
     *
     * @param guildId
     * @param recodeCode
     * @return
     */
    public AjaxResult addAdmin(Long guildId, Long recodeCode) {
        if (null == guildId || guildId <= 0) {
            return AjaxResult.error("参数【guildId】为空");
        }
        if (recodeCode == null) {
            return AjaxResult.error("请填写用户ID");
        }
        AppGuildVo appGuildVo = appGuildMapper.getAppGuild(guildId);
        if (null == appGuildVo) {
            return AjaxResult.error("公会不存在");
        }

        AppUserEntity user = appUserMapper.selectAppUserByUserId(recodeCode);
        if (null == user) {
            return AjaxResult.error("用户不存在");
        }
        if (user.getId().equals(appGuildVo.getUserId())) {
            return AjaxResult.error("该用户是本公会会长，不可添加");
        }
        AppGuildMember guildMember = appGuildMemberMapper.getGuildMemberByUserIdAndGuildId(guildId, user.getId());
        if (null == guildMember) {
            return AjaxResult.error("该用户未加入该公会");
        }
        if (guildMember.getIsAdmin().intValue() == WhetherTypeEnum.YES.getName()) {
            return AjaxResult.error("该用户已是该公会管理员");
        }

        guildMember.setIsAdmin(WhetherTypeEnum.YES.getName());
        appGuildMemberMapper.updateAppGuildMember(guildMember);


        return AjaxResult.success("操作成功");
    }

    /**
     * 移除公会管理员
     *
     * @param guildId
     * @param userId
     * @return
     */
    public AjaxResult delAdmin(Long guildId, Long userId) {
        if (null == guildId || guildId <= 0) {
            return AjaxResult.error("参数【guildId】为空");
        }
        if (null == userId || userId <= 0) {
            return AjaxResult.error("请选择用户");
        }
        AppGuildVo appGuildVo = appGuildMapper.getAppGuild(guildId);
        if (null == appGuildVo) {
            return AjaxResult.error("公会不存在");
        }

        if (userId.equals(appGuildVo.getUserId())) {
            return AjaxResult.error("该用户非该公会管理员");
        }
        AppGuildMember guildMember = appGuildMemberMapper.getGuildMemberByUserIdAndGuildId(guildId, userId);
        if (null == guildMember) {
            return AjaxResult.error("该用户未加入该公会");
        }
        if (guildMember.getIsAdmin().intValue() == WhetherTypeEnum.NO.getName()) {
            return AjaxResult.error("该用户非该公会管理员");
        }

        guildMember.setIsAdmin(WhetherTypeEnum.NO.getName());
        appGuildMemberMapper.updateAppGuildMember(guildMember);


        return AjaxResult.success("操作成功");
    }

    /**
     * 移除公会房间
     *
     * @param guildId
     * @param chatRoomId
     * @return
     */
    public AjaxResult delChatRoom(Long guildId, Long chatRoomId) {
        if (null == guildId || guildId <= 0) {
            return AjaxResult.error("参数【guildId】为空");
        }
        if (null == chatRoomId || chatRoomId <= 0) {
            return AjaxResult.error("请选择房间");
        }
        AppGuildVo appGuildVo = appGuildMapper.getAppGuild(guildId);
        if (null == appGuildVo) {
            return AjaxResult.error("公会不存在");
        }

        AppChatRoom chatRoom = appChatRoomMapper.selectAppChatRoomById(chatRoomId);
        if (null == chatRoom) {
            return AjaxResult.error("房间不存在");
        }
        if (chatRoom.getGuildId().equals(0L)) {
            return AjaxResult.error("该房间未绑定公会");
        }

        if (!chatRoom.getGuildId().equals(guildId)) {
            return AjaxResult.error("该房间未绑定到该公会");
        }

        AppChatRoom chatRoomUpd = new AppChatRoom();
        chatRoomUpd.setId(chatRoom.getId());
        chatRoomUpd.setGuildId(0L);
        appChatRoomMapper.updateAppChatRoom(chatRoomUpd);

        return AjaxResult.success("操作成功");

    }

    /**
     * 添加公会房间
     *
     * @param guildId
     * @param thirdId
     * @return
     */
    public AjaxResult addChatRoom(Long guildId, String thirdId) {
        if (null == guildId || guildId <= 0) {
            return AjaxResult.error("参数【guildId】为空");
        }
        if (StringUtils.isBlank(thirdId)) {
            return AjaxResult.error("请填写房间ID");
        }
        AppGuildVo appGuildVo = appGuildMapper.getAppGuild(guildId);
        if (null == appGuildVo) {
            return AjaxResult.error("公会不存在");
        }

        AppChatRoom chatRoom = appChatRoomMapper.selectAppChatRoomByThirdId(thirdId);
        if (null == chatRoom) {
            return AjaxResult.error("房间不存在");
        }
        if (null == AppChatRoomTypeEnums.getEnum2(chatRoom.getType().intValue())) {
            return AjaxResult.error("该房间不支持添加");
        }
        if (!chatRoom.getGuildId().equals(0L)) {
            if (chatRoom.getGuildId().equals(guildId)) {
                return AjaxResult.error("该房间已被绑定到该公会");
            } else {
                return AjaxResult.error("该房间已被绑定到其他公会");
            }
        }

        AppChatRoom chatRoomUpd = new AppChatRoom();
        chatRoomUpd.setId(chatRoom.getId());
        chatRoomUpd.setGuildId(guildId);
        appChatRoomMapper.updateAppChatRoom(chatRoomUpd);


        return AjaxResult.success("操作成功");


    }

    /**
     * 获取用户加入的公会详情
     *
     * @param userId
     * @return
     */
    public AjaxResult getUserJoinGuildInfo(Long userId) {
        Long userJoinGuildId = appGuildMapper.getUserJoinGuildId(userId);
        if (null == userJoinGuildId) {
            return AjaxResult.success("查询成功", null);
        }

        AppGuildVo appGuildVo = appGuildMapper.getAppGuild(userJoinGuildId);
        if (null == appGuildVo) {
            return AjaxResult.error("公会不存在或已删除");
        }

        // 判断是否是会长
        appGuildVo.setIsGuildLeader(userId.equals(appGuildVo.getUserId()) ? WhetherTypeEnum.YES.getName() : WhetherTypeEnum.NO.getName());

        // 获取会长用户信息
        appGuildVo.setGuildLeaderUserInfo(commonService.getUserInfoByUserId(appGuildVo.getUserId()));

        appGuildVo.setIsJoin(WhetherTypeEnum.YES.getName());

        if (appGuildVo.getIsGuildLeader() == WhetherTypeEnum.NO.getName()) {// 不为会长时
            // 根据用户id和公会id获取详情
            AppGuildMember guildMember = appGuildMemberMapper.getGuildMemberByUserIdAndGuildId(userJoinGuildId, userId);
            appGuildVo.setIsAdmin(null != guildMember ? guildMember.getIsAdmin() : WhetherTypeEnum.NO.getName());
        } else {// 为会长时
            appGuildVo.setIsAdmin(WhetherTypeEnum.NO.getName());
        }

        // 获取公会成员总数
        appGuildVo.setMemberNumber(appGuildMemberMapper.getGuildMemberNumber(userJoinGuildId) + 1);


        return AjaxResult.success(appGuildVo);
    }

    /**
     * 获取公会管理员列表
     *
     * @param page
     * @param size
     * @param guildId
     * @param keyword
     * @return
     */
    public TableDataInfo getGuildAdminList(int page, int size, Long guildId, String keyword) {
        if (null == guildId || guildId <= 0) {
            return AjaxResult.getDataTableError("参数【guildId】为空");
        }
        if (com.hzy.core.utils.StringUtils.isBlank(keyword)) {
            keyword = null;
        }

        Page p = PageHelperUtils.startPage(page, size, true);
        List<Map<String, Object>> list = appGuildMemberMapper.getGuildAdminList(guildId, keyword, WhetherTypeEnum.YES.getName());
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }

        return AjaxResult.getDataTable(list, p.getTotal());
    }

    /**
     * 生成公会ID
     *
     * @return
     */
    public synchronized String generateGuildNumber() {

        // 获取当前日期
        Date time = new Date();
        // 然后分别获取年月日时分秒
        int year = time.getYear() + 1900 + Integer.parseInt(RandomStringUtils.randomNumeric(3));// 年的基础上再随机生成3位数相加
        int month = time.getMonth() + 1;
        int day = time.getDate();
        int hour = time.getHours();
        int minute = time.getMinutes();
        int second = time.getSeconds();
        // 先将当前时间取年月日时分秒累加起来得到一个总和
        String code = (year + month + day + hour + minute + second) + "";
        if (code.length() < 7) {// 如果总和数长度小于6位
            // 再生成一个随机数：随机数长度为，6减去总和数的长度
            String random = RandomStringUtils.randomNumeric(7 - code.length());
            // 最终用户id:为随机数+上总和数
            code = random + code;
        }


        if (StringUtils.equals("0", code.substring(0, 1))) {
            code = RandomUtil.randomInt(1, 10) + code.substring(1);
        }

        // 检测id锁
        Boolean lock = redisCache.getCacheObject("generateGuildNumber:" + code);
        if (null != lock) {
            // 如果被锁住那就继续生成
            return this.generateGuildNumber();
        }

        // 锁住id
        redisCache.setCacheObject("generateGuildNumber:" + code, true);

        // 校验id是否存在或者是否是靓号
        if (null != appGuildMapper.selectAppGuildByGuildNumber(code)) {// 如果存在或者是靓号就继续调用
            // 释放锁
            redisCache.deleteObject("generateGuildNumber:" + code);
            return this.generateGuildNumber();
        }
        redisCache.deleteObject("generateGuildNumber:" + code);
        return code;
    }

    /**
     * 批量删除公会
     *
     * @param ids 需要删除的公会主键
     * @return 结果
     */
    public int deleteAppGuildByIds(Long[] ids) {
        return appGuildMapper.deleteAppGuildByIds(ids);
    }

    /**
     * 删除公会信息
     *
     * @param id 公会主键
     * @return 结果
     */

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public int deleteAppGuildById(Long id) {
        AppGuild appGuildOld = appGuildMapper.getGuildInfoById(id);
        if (null == appGuildOld) {
            throw new AppException("公会不存在");
        }

        int count = appGuildMapper.deleteAppGuildById(id);
        if (count > 0) {
            // 清空公会绑定的房间
            appChatRoomMapper.clearGuildBindChatRoom(id);
            // 清空公会所有成员
            appGuildMemberMapper.clearGuildAllMember(id);
        }
        return count;
    }

    /**
     * 获取公会房间上周魅力榜(前100名)
     *
     * @param chatRoomId
     * @return
     */
    public AjaxResult getChatRoomLastWeekCharmLeaderboard(Long chatRoomId) {
        if (null == chatRoomId || chatRoomId <= 0) {
            return AjaxResult.error("参数【chatRoomId】为空");
        }

        AppChatRoom appChatRoom = appChatRoomMapper.selectAppChatRoomById(chatRoomId);
        if (null == appChatRoom) {
            return AjaxResult.error("房间不存在");
        }
        /*if (null == appChatRoom.getGuildId() || appChatRoom.getGuildId().equals(0L)) {
            return AjaxResult.error("非公会房间");
        }*/

        /*AppGuildVo appGuildVo = appGuildMapper.getAppGuild(appChatRoom.getGuildId());
        if (null == appGuildVo) {
            return AjaxResult.error("公会不存在");
        }*/

        List<AppLeaderboardResultVo> list = appGuildMapper.getChatRoomLastWeekCharmLeaderboard1(chatRoomId);
        if (CollectionUtils.isEmpty(list)) {
            return AjaxResult.success(new ArrayList<>());
        } else {
            Callable<List<AppLeaderboardResultVo>> callable = () -> handleChatRoomLastWeekCharmLeaderboard(list);
            FutureTask<List<AppLeaderboardResultVo>> callableTask = new FutureTask<>(callable);
            executorService.submit(callableTask);
            try {
                List<AppLeaderboardResultVo> resultList = callableTask.get();
                int ranking = 1;
                for (AppLeaderboardResultVo item : resultList) {
                    item.setRanking(ranking);
                    ranking = ranking + 1;
                }
                return AjaxResult.success(resultList);
            } catch (Exception e) {
                return AjaxResult.error("线程处理异常");
            }
        }
    }

    /**
     * 处理公会房间上周魅力榜结果
     *
     * @param list
     * @return
     */
    public List<AppLeaderboardResultVo> handleChatRoomLastWeekCharmLeaderboard(List<AppLeaderboardResultVo> list) {
        for (AppLeaderboardResultVo item : list) {
            item.setUserPhone(null);
            if (null != item.getSex() && item.getSex().intValue() == -1) {
                item.setSex(null);
            }
            if (com.hzy.core.utils.StringUtils.isBlank(item.getBirthday())) {
                item.setAge(null);
            }
            if (null != item.getAge() && item.getAge().intValue() == -1) {
                item.setAge(null);
            }
        }
        return list;
    }

    // 入会申请审批
    public AjaxResult approve(Long userId, String status, Long guildId) {
        return commonService.approve(userId, status, guildId);
    }

    // 退会申请审批
    public AjaxResult refund(Long id, String status, Long guildId) {
        return commonService.refund(id, status, guildId);
    }

    // 查看公会 用户流水详情
    public TableDataInfo getGuildUser(Integer pageNum, Integer pageSize, Long userId, Long guildId, String queryBeginTime, String queryEndTime) {

        List<AppGuildEntity> all = appGuildMapper.getGuildUserRecords(guildId, userId, queryBeginTime, queryEndTime);
        Page p = PageHelperUtils.startPage(pageNum, pageSize, true);
        // 查询用户流水-房间内-私聊收到的礼物流水
        List<AppGuildEntity> list = appGuildMapper.getGuildUserRecords(guildId, userId, queryBeginTime, queryEndTime);
        if (CollectionUtils.isEmpty(list)) {
            return AjaxResult.getDataTable(new ArrayList<>(), 0);
        }

        Long total = 0L;
        for (AppGuildEntity appGuildEntity : all) {
            total += (appGuildEntity.getGiftGoldTotalAmount() != null ? appGuildEntity.getGiftGoldTotalAmount() : 0L);
        }

        return AjaxResult.getDataTable(list, p.getTotal(), total);
    }

    public TableDataInfo getGuildApplyList(Integer pageNum, Integer pageSize, Long guildId, Long userId, String nickName, Long recodeCode) {
        Page p = PageHelperUtils.startPage(pageNum, pageSize, true);
        List<AppGuildVo> guildList = appGuildMapper.getGuildApplyList(guildId, userId, nickName, recodeCode);
        if (CollectionUtils.isEmpty(guildList)) {
            return AjaxResult.getDataTable(new ArrayList<>(), 0);
        }
        return AjaxResult.getDataTable(guildList, p.getTotal());
    }

    public AjaxResult updReviewStatus(Long id, String status) {
        if (null == id || id <= 0) {
            return AjaxResult.error("参数【id】为空");
        }
        if (com.hzy.core.utils.StringUtils.isBlank(status)) {
            return AjaxResult.error("参数【status】为空");
        }
        int i = appGuildMapper.updReviewStatus(id, status);
        if (i > 0) {
            return AjaxResult.success("操作成功");
        }
        return AjaxResult.error("操作失败");
    }

    /**
     * 添加公会成员
     *
     * @param addGuildMemberDto
     * @return
     */
    public AjaxResult addGuildMember(AddGuildMemberDto addGuildMemberDto) {

        AppUserEntity appUserEntity = appUserMapper.selectAppUserByRecode(addGuildMemberDto.getRecodeCode().toString());
        if (appUserEntity == null) {
            return AjaxResult.error("该靓号用户不存在");
        }

        // 判断是否加入其他公会
        List<AppGuildMember> appGuildMembers = appGuildMemberService.lambdaQuery()
                .eq(AppGuildMember::getUserId, appUserEntity.getId()).list();
        if (!appGuildMembers.isEmpty()) {
            return AjaxResult.error("当前用户已经加入其他公会");
        }

        final List<Long> guildIdsBySysUserId = identityPermissionsUtilService.getGuildIdsBySysUserId(Long.valueOf(addGuildMemberDto.getSysUserId()));
        if (guildIdsBySysUserId == null || guildIdsBySysUserId.get(0) == -1L) {
            return AjaxResult.error("当前经纪人没有加入公会");
        }
        addGuildMemberDto.setGuildId(guildIdsBySysUserId.get(0));
        // 判断是否有其他未处理的公会邀请
        final boolean existNotHandleApply = appGuildApplyMapper.isExistNotHandleApply(appUserEntity.getId(), addGuildMemberDto.getGuildId());
        if (existNotHandleApply) {
            return AjaxResult.error("当前用户有未处理的公会邀请");
        }

        final AppConfig appConfig = appConfigMapper.getAppConfig();
        if (addGuildMemberDto.getGiftIncomeScale() != null) {
            if (addGuildMemberDto.getGiftIncomeScale().compareTo(appConfig.getGiftIncomeScale()) < 0) {
                return AjaxResult.error("无法邀请,因为低于默认礼物收益比例");
            }
        }

        // 判断用户创建时间是否超过24小时
        long diffInHours = ChronoUnit.HOURS.between(appUserEntity.getCreatedTime(), LocalDateTime.now());

        if (diffInHours > 24 * 3) {
            return AjaxResult.error("当前用户已经注册超过3天,暂不能邀请加入公会");
        }

        // 添加到公会申请记录
        AppGuildApply appGuildApply = new AppGuildApply();
        appGuildApply.setInitiateUserId(appUserEntity.getId());
        appGuildApply.setType(2);
        appGuildApply.setGuildId(addGuildMemberDto.getGuildId());
        BigDecimal scale = ObjectUtil.isNotEmpty(addGuildMemberDto.getGiftIncomeScale()) ? addGuildMemberDto.getGiftIncomeScale() : appConfig.getGiftIncomeScale();
        appGuildApply.setRemarks(scale.toString());
        appGuildApply.setSysUserId(addGuildMemberDto.getSysUserId());
        appGuildApplyService.save(appGuildApply);

        AppGuild guildInfoById = appGuildMapper.getGuildInfoById(addGuildMemberDto.getGuildId());

        // 发送加入公会邀请的系统通知
        AppSysMsgTypeEnums msgTypeEnums = AppSysMsgTypeEnums.TYPE3;
        AppSysMsg appSysMsg = new AppSysMsg();
        appSysMsg.setCreateTime(new Date());
        appSysMsg.setIsDel(WhetherTypeEnum.NO.getName());
        appSysMsg.setIsRead(WhetherTypeEnum.NO.getName());
        appSysMsg.setUserId(appUserEntity.getId());
        appSysMsg.setMsgType((long) msgTypeEnums.getId());
        appSysMsg.setTitle(msgTypeEnums.getDesc());
        String str = String.format("邀请您加入【%s】公会,您是否同意?", guildInfoById.getGuildName());
        appSysMsg.setContent(str);
        appSysMsgMapper.insertAppSysMsg(appSysMsg);
        return AjaxResult.success();
    }


}
