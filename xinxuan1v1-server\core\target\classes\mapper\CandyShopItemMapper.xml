<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.candy.CandyShopItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hzy.core.entity.candy.CandyShopItem">
        <id column="item_id" property="itemId" />
        <result column="gift_id" property="giftId" />
        <result column="name" property="name" />
        <result column="image_url" property="imageUrl" />
        <result column="gold_price" property="goldPrice" />
        <result column="candy_cost" property="candyCost" />
        <result column="description" property="description" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        item_id, gift_id, name, image_url, gold_price, candy_cost, description, status, create_time, update_time
    </sql>

</mapper>
