<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppViewUserRecordsMapper">
    <resultMap type="AppViewUserRecords" id="AppViewUserRecordsResult">
        <result property="id" column="id"/>
        <result property="viewUserId" column="view_user_id"/>
        <result property="beViewUserId" column="be_view_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
        <result property="isRead" column="is_read"/>
    </resultMap>

    <sql id="selectAppViewUserRecordsVo">
        select id, view_user_id, be_view_user_id, create_time, update_time, is_del, is_read
        from app_view_user_records
    </sql>

    <select id="selectAppViewUserRecordsList" parameterType="AppViewUserRecords" resultMap="AppViewUserRecordsResult">
        <include refid="selectAppViewUserRecordsVo"/>
        where 1=1
        <if test="viewUserId != null ">and view_user_id = #{viewUserId}</if>
        <if test="beViewUserId != null ">and be_view_user_id = #{beViewUserId}</if>
        order by id desc
    </select>

    <select id="selectAppViewUserRecordsById" parameterType="Long" resultMap="AppViewUserRecordsResult">
        <include refid="selectAppViewUserRecordsVo"/>
        where id = #{id} and is_del=false
    </select>

    <insert id="insertAppViewUserRecords" parameterType="AppViewUserRecords" useGeneratedKeys="true" keyProperty="id">
        insert into app_view_user_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="viewUserId != null">view_user_id,</if>
            <if test="beViewUserId != null">be_view_user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isDel != null">is_del,</if>
            <if test="isRead != null">is_read,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="viewUserId != null">#{viewUserId},</if>
            <if test="beViewUserId != null">#{beViewUserId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="isRead != null">#{isRead},</if>
        </trim>
    </insert>

    <update id="updateAppViewUserRecords" parameterType="AppViewUserRecords">
        update app_view_user_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="viewUserId != null">view_user_id = #{viewUserId},</if>
            <if test="beViewUserId != null">be_view_user_id = #{beViewUserId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="isRead != null">is_read = #{isRead},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppViewUserRecordsById" parameterType="Long">
        delete
        from app_view_user_records
        where id = #{id}
    </delete>

    <delete id="deleteAppViewUserRecordsByIds" parameterType="String">
        delete from app_view_user_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="getViewUserRecordsByViewUserIdAndBeViewUserId" parameterType="Long"
            resultMap="AppViewUserRecordsResult">
        <include refid="selectAppViewUserRecordsVo"/>
        where view_user_id = #{viewUserId} and be_view_user_id=#{beViewUserId}
        order by id desc limit 0,1
    </select>

    <update id="clearBeViewUserRecords">
        update app_view_user_records
        set is_read = true, update_time = now()
        where be_view_user_id = #{userId}
        and is_read = false
    </update>

    <update id="clearViewUserRecords" parameterType="java.lang.Long">
        update app_view_user_records
        set is_del = true, update_time = now()
        where view_user_id = #{userId}
        and is_del = false
    </update>


    <select id="getBeViewUserRecordsList" resultType="com.hzy.core.model.vo.app.AppViewUserInfoVo">
        select
            au.id as userId,
            au.is_online as isOnline,
            au.last_operating_time as lastOperatingTime,
            au.nick_name as nickName,
            au.birthday,
            au.age,
            au.sex,
            au.head_portrait as headPortrait,
            count(*) as viewCount,
            max(avur.create_time) as lastViewTime
        from app_view_user_records avur
        left join app_user au on au.id = avur.view_user_id
        where avur.is_read = false
        and avur.be_view_user_id = #{userId}
        group by avur.view_user_id
        order by lastViewTime desc
    </select>

    <select id="getViewUserRecordsList" resultType="com.hzy.core.model.vo.app.AppViewUserInfoVo">
        select
        au.id as userId,
        au.is_online as isOnline,
        au.last_operating_time as lastOperatingTime,
        au.nick_name as nickName,
        au.birthday,
        au.age,
        au.sex,
        au.head_portrait as headPortrait,
        count(*) as viewCount,
        max(avur.create_time) as lastViewTime
        from app_view_user_records avur
        left join app_user au on au.id = avur.be_view_user_id
        where avur.is_del = false
        and avur.view_user_id = #{userId}
        group by avur.be_view_user_id
        order by lastViewTime desc
    </select>


    <select id="getUserBeViewCount" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select count(*)
        from app_view_user_records re
        where re.be_view_user_id = #{userId}
    </select>

    <update id="setRecordsReadByIds" parameterType="java.lang.Long">
        update app_view_user_records
        set is_read=true,
        update_time=now()
        where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


    <select id="getToViewRecordsUnreadCount" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select count(*)
        from app_view_user_records re
        where re.be_view_user_id = #{userId}
        and re.is_read = false
    </select>

</mapper>