<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppBannerMapper">

    <resultMap type="AppBanner" id="AppBannerResult">
        <result property="id" column="id"/>
        <result property="imgUrl" column="img_url"/>
        <result property="h5Url" column="h5_url"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="sort" column="sort"/>
        <result property="title" column="title"/>
        <result property="context" column="context"/>
        <result property="content" column="content"/>
        <result property="type" column="type"/>
    </resultMap>

    <sql id="selectAppBannerVo">
        select id,
               img_url,
               h5_url,
               create_time,
               update_time,
               create_by,
               update_by,
               sort,
               title,
               content,
               type
        from app_banner
    </sql>

    <select id="selectAppBannerList" parameterType="AppBanner" resultMap="AppBannerResult">
        <include refid="selectAppBannerVo"/>
        <if test="title != null  and title != ''">where title like concat('%', #{title}, '%')</if>
        order by sort asc,id desc
    </select>

    <select id="selectAppBannerById" parameterType="Long" resultMap="AppBannerResult">
        <include refid="selectAppBannerVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppBanner" parameterType="AppBanner" useGeneratedKeys="true" keyProperty="id">
        insert into app_banner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="imgUrl != null">img_url,</if>
            <if test="h5Url != null">h5_url,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="sort != null">sort,</if>
            <if test="title != null">title,</if>
            <if test="context != null">context,</if>
            <if test="content != null">content,</if>
            <if test="type != null">type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="h5Url != null">#{h5Url},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="sort != null">#{sort},</if>
            <if test="title != null">#{title},</if>
            <if test="context != null">#{context},</if>
            <if test="content != null">#{content},</if>
            <if test="type != null">#{type},</if>
        </trim>
    </insert>

    <update id="updateAppBanner" parameterType="AppBanner">
        update app_banner
        <trim prefix="SET" suffixOverrides=",">
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
            <if test="h5Url != null">h5_url = #{h5Url},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="title != null">title = #{title},</if>
            <if test="context != null">context = #{context},</if>
            <if test="content != null">content = #{content},</if>
            <if test="type != null">type = #{type},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppBannerById" parameterType="Long">
        delete from app_banner where id = #{id}
    </delete>

    <delete id="deleteAppBannerByIds" parameterType="String">
        delete from app_banner where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>