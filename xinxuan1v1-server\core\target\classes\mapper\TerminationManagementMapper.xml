<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.TerminationManagementMapper">

    <select id="getAnchorManagementList" resultType="com.hzy.core.entity.TerminationManagement">
        SELECT agrr.id,
            agrr.user_id,
               au.nick_name,
               acr.type,
               aga.update_time                 AS  startTime,
               acr.hall_owner_commission_scale AS anchorSelfLifting,
               agrr.`status`,
               agrr.create_time,
               agrr.end_time,
               su.nick_name                    AS sysUserName,
               agrr.update_time
        FROM app_guild_return_records agrr
                 JOIN app_guild_apply aga ON aga.initiate_user_id = agrr.user_id
                 JOIN app_user au ON au.id = agrr.user_id
                 JOIN app_chat_room acr ON acr.hall_owner_user_id = agrr.user_id
                 JOIN sys_user su ON su.user_id = agrr.sys_user_id
        WHERE aga.`status` = 1
          and agrr.guild_id = #{guildId}
        <if test="userId!=null">
            and agrr.user_id = #{userId}
        </if>
        <if test="status!=null">
            and agrr.status = #{status}
        </if>        
        <if test="sysUserIds != null ">
            AND agrr.sys_user_id in
            <foreach collection="sysUserIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </select>
</mapper>