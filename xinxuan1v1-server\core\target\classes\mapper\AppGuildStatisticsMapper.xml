<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppGuildStatisticsMapper">


    <select id="getGuildChatRoomStatisticsByChatRoomSum" resultType="java.math.BigDecimal">
        select
        ifnull(sum(bill.user_contribution_value),0)
        from app_user_gold_bill bill
        where bill.chat_room_id=#{chatRoomId}
        and bill.user_contribution_value >0
        <if test="queryType==1">
            and to_days(bill.create_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.create_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        <if test="queryType==4">
            and DATE(bill.create_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 7 DAY)
            and DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 1 DAY)
        </if>
    </select>

    <select id="getGuildChatRoomStatisticsByChatRoomSum1" resultType="java.math.BigDecimal">
        select
        ifnull(sum(bill.gift_gold_total_amount),0)
        from user_give_gift bill
        where bill.chat_room_id=#{chatRoomId}
        and bill.gift_gold_total_amount >0
        <if test="queryType==1">
            and to_days(bill.created_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.created_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.created_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.created_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        <if test="queryType==4">
            and DATE(bill.created_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 7 DAY)
            and DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 1 DAY)
        </if>
    </select>

    <select id="getGuildChatRoomStatisticsByChatRoomSum2" resultType="java.math.BigDecimal">
        <!--SELECT
        ifnull(SUM(bill.gift_gold_total_amount),0) AS total
        FROM
        user_give_gift bill
        LEFT JOIN
        app_guild_member gm ON gm.user_id = bill.user_id
        LEFT JOIN
        app_guild g ON g.id = gm.guild_id
        WHERE
        bill.created_time &gt;= gm.create_time
        AND bill.created_time &lt;= CURRENT_TIMESTAMP
        <if test="queryType==1">
            and to_days(bill.created_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.created_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.created_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.created_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
            <if test="queryType==4">
                and DATE(bill.created_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 7 DAY)
                and DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 1 DAY)
            </if>
        and bill.chat_room_id=#{chatRoomId}-->

        SELECT
        ifnull(SUM(bill.gift_gold_total_amount),0) AS total
        FROM
        user_give_gift bill

        WHERE bill.deleted = 0
        <if test="queryType==1">
            and to_days(bill.created_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.created_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.created_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.created_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        <if test="queryType==4">
            and DATE(bill.created_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 7 DAY)
            and DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 1 DAY)
        </if>
        <!--        <if test="queryBeginTime != null and queryBeginTime != ''">-->
        <!--            AND DATE(bill.created_time) = #{queryBeginTime}-->
        <!--        </if>-->
        <!--        <if test="queryEndTime != null and queryEndTime != ''">-->
        <!--            and date_format(bill.created_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')-->
        <!--        </if>-->
        and bill.chat_room_id=#{chatRoomId}
    </select>

    <select id="getGuildChatRoomStatisticsByGuildSum" resultType="java.math.BigDecimal">
        select
        ifnull(sum(bill.user_contribution_value),0)
        from app_user_gold_bill bill
        where bill.chat_room_id=#{chatRoomId}
        and bill.user_contribution_value >0
        <if test="queryType==1">
            and to_days(bill.create_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.create_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        <if test="queryType==4">
            and DATE(bill.create_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 7 DAY)
            and DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 1 DAY)
        </if>
        <if test="guildMemberUserIdList!=null and guildMemberUserIdList.size()>0">
            and bill.object_id in
            <foreach item="uid" collection="guildMemberUserIdList" open="(" separator="," close=")">
                #{uid}
            </foreach>
        </if>
        and 1=if(bill.bill_type=21 or bill.bill_type=2,1,0)
    </select>

    <select id="getGuildChatRoomStatisticsByGuildSum1" resultType="java.math.BigDecimal">
        select
        ifnull(sum(bill.gift_gold_total_amount),0)
        -- from app_user_gold_bill bill
        from user_give_gift bill
        where bill.chat_room_id=#{chatRoomId}
        and bill.gift_gold_total_amount >0
        <if test="queryType==1">
            and to_days(bill.created_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.created_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.created_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.created_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        <if test="queryType==4">
            and DATE(bill.created_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 7 DAY)
            and DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 1 DAY)
        </if>
        <if test="guildMemberUserIdList!=null and guildMemberUserIdList.size()>0">
            and bill.user_id in
            <foreach item="uid" collection="guildMemberUserIdList" open="(" separator="," close=")">
                #{uid}
            </foreach>
        </if>
        -- and 1=if(bill.bill_type=21 or bill.bill_type=2,1,0)
    </select>


    <select id="getGuildChatRoomStatisticsByConsumptionNumber" resultType="java.lang.Long">
        select
        count(DISTINCT bill.user_id)
        from app_user_gold_bill bill
        where bill.chat_room_id=#{chatRoomId}
        and bill.user_contribution_value >0
        <if test="queryType==1">
            and to_days(bill.create_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.create_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        <if test="queryType==4">
            and DATE(bill.create_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 7 DAY)
            and DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 1 DAY)
        </if>
    </select>


    <select id="getGuildChatRoomStatisticsByConsumptionNumber1" resultType="java.lang.Long">
        select
        count(DISTINCT bill.user_id)
        -- from app_user_gold_bill bill
        from user_give_gift bill
        where bill.chat_room_id=#{chatRoomId}
        and bill.gift_gold_total_amount >0
        <if test="queryType==1">
            and to_days(bill.created_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.created_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.created_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.created_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        <if test="queryType==4">
            and DATE(bill.created_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 7 DAY)
            and DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 1 DAY)
        </if>
    </select>


    <select id="getGuildChatRoomStatisticsByReceiveGiftsNumber" resultType="java.lang.Long">
        select
        count(DISTINCT bill.object_id)
        from app_user_gold_bill bill
        where bill.chat_room_id=#{chatRoomId}
        and bill.user_contribution_value >0
        and bill.bill_type in(21,2)
        <if test="queryType==1">
            and to_days(bill.create_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.create_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        <if test="queryType==4">
            and DATE(bill.create_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 7 DAY)
            and DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 1 DAY)
        </if>
    </select>

    <select id="getGuildChatRoomStatisticsByReceiveGiftsNumber1" resultType="java.lang.Long">
        select
        count(DISTINCT bill.to_user_id)
        -- from app_user_gold_bill bill
        from user_give_gift bill
        where bill.chat_room_id=#{chatRoomId}
        and bill.gift_gold_total_amount >0
        -- and bill.bill_type in(21,2)
        <if test="queryType==1">
            and to_days(bill.created_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.created_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.created_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.created_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        <if test="queryType==4">
            and DATE(bill.created_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 7 DAY)
            and DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 1 DAY)
        </if>
    </select>

    <select id="getGuildChatRoomStatisticsByGearNumber" resultType="java.lang.Long">
        select
        count(DISTINCT ow.user_id)
        from app_context_owner ow
        where ow.chat_room_id=#{chatRoomId}
        <if test="queryType==1">
            and to_days(DATE_FORMAT( ow.date_str,'%Y-%m-%d')) = to_days(now())
        </if>
        <if test="queryType==2">
            and DATE_FORMAT( ow.date_str,'%Y-%m-%d') >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and DATE_FORMAT( ow.date_str,'%Y-%m-%d') &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(DATE_FORMAT( ow.date_str,'%Y-%m-%d'),'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        <if test="queryType==4">
            and DATE(DATE_FORMAT( ow.date_str,'%Y-%m-%d')) BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 7
            DAY)
            and DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 1 DAY)
        </if>
        and 1=if(ow.user_id>0,1,0)
    </select>

    <select id="getGuildChatRoomStatisticsByDirectNumber" resultType="java.lang.Long">
        select count(*) from
        app_chat_room_user cru
        where cru.is_del=false
        and cru.is_emcee=true
        and cru.chat_room_id=#{chatRoomId}
        <if test="queryType==1">
            and to_days(cru.add_emcee_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and cru.add_emcee_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and cru.add_emcee_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(cru.add_emcee_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        <if test="queryType==4">
            and DATE(cru.add_emcee_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 7 DAY)
            and DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 1 DAY)
        </if>
    </select>


    <select id="getGuildMemberStatisticsInfoByLoginNumber" resultType="java.lang.Long">
        select
        count(*)
        from app_user u,
        app_guild_member me
        where me.user_id=u.id
        and me.guild_id=#{guildId}
        <if test="queryType==1">
            and to_days(u.last_operating_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and u.last_operating_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and u.last_operating_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(u.last_operating_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
    </select>

    <select id="getGuildMemberStatisticsInfoByReceiveGiftsNumber" resultType="java.lang.Long">
        select
        count(DISTINCT bill.object_id)
        from app_user_gold_bill bill,
        app_chat_room cr
        where bill.chat_room_id=cr.id
        and bill.user_contribution_value >0
        and bill.bill_type in(21,2)
        <if test="queryType==1">
            and to_days(bill.create_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.create_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        and cr.guild_id=#{guildId}
    </select>

    <select id="getGuildMemberStatisticsInfoByGearNumber" resultType="java.lang.Long">
        select
        count(DISTINCT ow.user_id)
        from app_context_owner ow,
        app_chat_room cr
        where ow.chat_room_id=cr.id
        and ow.user_id>0
        <if test="queryType==1">
            and to_days(DATE_FORMAT( ow.date_str,'%Y-%m-%d')) = to_days(now())
        </if>
        <if test="queryType==2">
            and DATE_FORMAT( ow.date_str,'%Y-%m-%d') >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and DATE_FORMAT( ow.date_str,'%Y-%m-%d') &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(DATE_FORMAT( ow.date_str,'%Y-%m-%d'),'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        and cr.guild_id=#{guildId}
    </select>


    <select id="getGuildRevenueStatisticsInfoByGuildGiftSum" resultType="java.math.BigDecimal">
        select
        ifnull(sum(bill.user_contribution_value),0)
        from app_user_gold_bill bill,
        app_chat_room cr
        where bill.chat_room_id=cr.id
        and bill.user_contribution_value >0
        <if test="queryType==1">
            and to_days(bill.create_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.create_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        and cr.guild_id=#{guildId}
    </select>


    <select id="getGuildRevenueStatisticsInfoByGuildGiftSum1" resultType="java.math.BigDecimal">
        <!--select
        ifnull(sum(bill.gift_gold_total_amount),0)
        from user_give_gift bill,
        app_chat_room cr
        where bill.chat_room_id=cr.id
        and bill.gift_gold_total_amount >0
        <if test="queryType==1">
            and to_days(bill.created_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.created_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.created_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.created_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        and cr.guild_id=#{guildId}-->
        SELECT
        ifnull(SUM(bill.gift_gold_total_amount),0) AS total
        FROM
        user_give_gift bill
        LEFT JOIN
        app_guild_member gm ON gm.user_id = bill.to_user_id
        LEFT JOIN
        app_guild g ON g.id = gm.guild_id
        WHERE
        bill.created_time &gt;= gm.create_time
        AND bill.created_time &lt;= CURRENT_TIMESTAMP
        <if test="queryType==1">
            and to_days(bill.created_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.created_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.created_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.created_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        and gm.guild_id=#{guildId}
    </select>

    <select id="getGuildRevenueStatisticsInfoByGuildGiftSum2" resultType="java.math.BigDecimal">
        SELECT
        COALESCE(SUM(bill.gift_gold_total_amount), 0) AS total
        FROM
        user_give_gift bill
        INNER JOIN
        app_guild_member gm ON gm.user_id = bill.to_user_id
        INNER JOIN
        app_guild g ON g.id = gm.guild_id

        INNER JOIN
        app_chat_room cr ON cr.id = bill.chat_room_id


        WHERE
        bill.created_time &gt;= gm.create_time
        -- AND bill.created_time &lt;= CURRENT_TIMESTAMP
        <if test="queryType==1">
            and to_days(bill.created_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.created_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.created_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.created_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        AND gm.guild_id = #{guildId}
    </select>

    <select id="getGuildRevenueStatisticsInfoByChatRoomSum" resultType="java.math.BigDecimal">
        select
        ifnull(sum(bill.user_contribution_value),0)
        from app_user_gold_bill bill,
        app_chat_room cr
        where bill.chat_room_id=cr.id
        and bill.user_contribution_value >0
        <if test="queryType==1">
            and to_days(bill.create_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.create_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        and cr.guild_id=#{guildId}
    </select>

    <select id="getGuildRevenueStatisticsInfoByChatRoomSum1" resultType="java.math.BigDecimal">
        SELECT ifnull(sum(gift_gold_total_amount),0) FROM user_give_gift WHERE chat_room_id IN (SELECT cr.id AS
        chatRoomId
        FROM app_chat_room cr
        INNER JOIN app_guild_member gm ON cr.hall_owner_user_id = gm.user_id
        LEFT JOIN app_user au ON au.id = gm.user_id
        WHERE gm.guild_id = #{guildId})

        <if test="queryType==1">
            and to_days(created_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and created_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and created_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(created_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
    </select>

    <select id="getGuildRevenueStatisticsInfoByChatRoomSum11" resultType="java.math.BigDecimal">
        SELECT
        COALESCE(SUM(bill.gift_gold_total_amount), 0) AS total
        FROM
        user_give_gift bill
        INNER JOIN
        app_guild_member gm ON gm.user_id = bill.to_user_id
        INNER JOIN
        app_guild g ON g.id = gm.guild_id

        INNER JOIN
        app_chat_room cr ON cr.id = bill.chat_room_id


        WHERE
        bill.created_time &gt;= gm.create_time
        -- AND bill.created_time &lt;= CURRENT_TIMESTAMP
        <if test="queryType==1">
            and to_days(bill.created_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.created_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.created_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.created_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        AND gm.guild_id = #{guildId}
    </select>

    <select id="getGuildRevenueStatisticsInfoByGuildChatRoomSum" resultType="java.math.BigDecimal">
        select
        ifnull(sum(bill.user_contribution_value),0)
        from app_user_gold_bill bill,
        app_chat_room cr
        where bill.chat_room_id=cr.id
        and bill.user_contribution_value >0
        <if test="queryType==1">
            and to_days(bill.create_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.create_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        and cr.guild_id=#{guildId}
    </select>

    <select id="getGuildRevenueStatisticsInfoByGuildChatRoomSum1" resultType="java.math.BigDecimal">
        select
        ifnull(sum(bill.gift_gold_total_amount),0)
        from user_give_gift bill,
        app_chat_room cr
        where bill.chat_room_id=cr.id
        and bill.gift_gold_total_amount >0
        <if test="queryType==1">
            and to_days(bill.create_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.create_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
            AND bill.created_time &gt;= gm.create_time AND ugg.created_time
            &lt;= CURRENT_TIMESTAMP
        </if>
        and cr.guild_id=#{guildId}
    </select>


    <select id="getGuildRevenueStatisticsInfoByGuildChatRoomSum11" resultType="java.math.BigDecimal">
        SELECT
        COALESCE(SUM(bill.gift_gold_total_amount), 0) AS total
        FROM
        user_give_gift bill
        INNER JOIN
        app_guild_member gm ON gm.user_id = bill.to_user_id
        INNER JOIN
        app_guild g ON g.id = gm.guild_id

        INNER JOIN
        app_chat_room cr ON cr.id = bill.chat_room_id


        WHERE
        bill.created_time &gt;= gm.create_time
        -- AND bill.created_time &lt;= CURRENT_TIMESTAMP
        <if test="queryType==1">
            and to_days(bill.created_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.created_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.created_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.created_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        AND gm.guild_id = #{guildId}
    </select>

    <select id="getGuildRevenueStatisticsInfoByGuildEarnings" resultType="java.math.BigDecimal">
        select
        ifnull(sum(ger.amount),0)
        from
        app_guild_earnings_records ger
        where ger.guild_id=#{guildId}
        and ger.amount >0
        <if test="queryType==1">
            and to_days(ger.create_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and ger.create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and ger.create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(ger.create_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
    </select>
    <select id="getGuildChatRoomStatisticsByGuildSum2" resultType="java.math.BigDecimal">
        SELECT
        ifnull(SUM(bill.gift_gold_total_amount),0) AS total
        FROM
        user_give_gift bill
        LEFT JOIN
        app_guild_member gm ON gm.user_id = bill.to_user_id
        LEFT JOIN
        app_guild g ON g.id = gm.guild_id
        WHERE
        bill.created_time &gt;= gm.create_time
        <if test="guildId">
            AND g.id = #{guildId}
        </if>
        <if test="queryType==1">
            and to_days(bill.created_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.created_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.created_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.created_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
        <if test="queryType==4">
            and DATE(bill.created_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 7 DAY)
            and DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 1 DAY)
        </if>
        <if test="queryBeginTime==null and queryBeginTime == ''">
            and to_days(bill.created_time) = to_days(now())
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            AND DATE(bill.created_time) = #{queryBeginTime}
        </if>
        and bill.to_user_id IN
        <foreach collection="guildMemberUserIdList" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and bill.chat_room_id=#{chatRoomId}
    </select>
    <select id="getGuildChatRoomStatisticsByChatRoomSum3" resultType="java.math.BigDecimal">
        SELECT
        ifnull(SUM(bill.gift_gold_total_amount),0) AS total
        FROM
        user_give_gift bill

        WHERE bill.deleted = 0
        <if test="queryBeginTime==null and queryBeginTime == ''">
            and to_days(bill.created_time) = to_days(now())
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            AND DATE(bill.created_time) = #{queryBeginTime}
        </if>
        and bill.chat_room_id=#{chatRoomId}
    </select>
</mapper>