<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppVipMapper">

    <select id="selectValidVipList" resultType="com.hzy.core.entity.AppVip">
        select * from app_vip
        where status = true
        limit #{offset}, #{limit}
    </select>

    <select id="getVipByUserId" resultType="com.hzy.core.entity.AppVip">
        select * from app_vip
        where user_id = #{userId}
    </select>

    <select id="getVipUsers" resultType="com.hzy.core.entity.AppVip">
        select av.*, au.nick_name, au.recode_code
        from app_vip av
        left join app_user au on av.user_id = au.id
        where status = true
        <if test="nickname != null and nickname != ''">
            and au.nick_name = #{nickname}
        </if>
        <if test="recodeCode!= null and recodeCode!= ''">
            and au.recode_code = #{recodeCode}
        </if>
        order by av.update_time desc
    </select>

    <update id="batchExpireStatus">
        update app_vip
        set status = false, update_time = now()
        where id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>
</mapper>