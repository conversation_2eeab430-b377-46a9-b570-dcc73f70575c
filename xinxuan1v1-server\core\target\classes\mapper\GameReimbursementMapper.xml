<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.GameReimbursementMapper">

    <resultMap type="AppMzlsBfjl" id="AppMzlsBfjlResult">
        <result property="id" column="id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="created_time"/>
        <result property="jjcId" column="jjc_id"/>
        <result property="giftId" column="gift_id"/>
        <result property="giftNum" column="gift_num"/>
        <result property="giftPrice" column="gift_price"/>
        <result property="userId" column="user_id"/>
        <result property="isSubKc" column="is_sub_kc"/>
        <result property="subNum" column="sub_num"/>
        <result property="subTime" column="sub_time"/>
    </resultMap>

    <sql id="selectAppMzlsBfjlVo">
        select id, create_by, created_time, jjc_id, gift_id, gift_num, gift_price, sum_gift_price, user_id, is_sub_kc,
        sub_num, sub_time from game_reimbursement
    </sql>

    <select id="getUserBfJlList" parameterType="java.lang.Long" resultMap="AppMzlsBfjlResult">
        select id, create_by, created_time, jjc_id, gift_id, gift_num, gift_price, sum_gift_price, user_id, is_sub_kc,
        sub_num, sub_time from game_reimbursement
        where user_id=#{userId} and deleted = false
        and sub_num>0
    </select>


    <select id="selectAppMzlsBfjlList" parameterType="AppMzlsBfjl" resultMap="AppMzlsBfjlResult">
        select b.id,
        b.created_by,
        b.created_time,
        b.pool_id,
        b.gift_id,
        g.masonry_price as gift_price,
        b.user_id,
        b.is_sub_kc,
        b.sub_time,
        u.nick_name as userNickName,
        u.recode_code as userRecode,
        u.phone as userPhone,
        u.head_portrait as userAvatar,
        g.gift_name as giftName,
        g.img_url as giftImgUrl
        from game_reimbursement b
        LEFT JOIN app_user u on(u.id=b.user_id)
        LEFT JOIN app_gift g on(g.id=b.gift_id)
        where b.deleted = false
        <if test="gameId != null ">
            and b.game_id = #{gameId}
        </if>
        <if test="isSubKc != null ">
            and b.is_sub_kc = #{isSubKc}
        </if>
        <if test="userRecode!=null and userRecode!=''">
            and u.recode_code like concat('%', #{userRecode}, '%')
        </if>
        <if test="userId !=null">
            and u.id = #{userId}
        </if>
        <if test="userPhone!=null and userPhone!=''">
            and u.phone like concat('%', #{userPhone}, '%')
        </if>
        <if test="userNickName!=null and userNickName!=''">
            and u.nick_name like concat('%', #{userNickName}, '%')
        </if>
        <if test="giftName!=null and giftName!=''">
            and g.gift_name like concat('%', #{giftName}, '%')
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(b.updated_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(b.updated_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        <if test="subBeginTime != null and subBeginTime != ''">
            and date_format(b.sub_time,'%y%m%d') &gt;= date_format(#{subBeginTime},'%y%m%d')
        </if>
        <if test="subEndTime != null and subEndTime != ''">
            and date_format(b.sub_time,'%y%m%d') &lt;= date_format(#{subEndTime},'%y%m%d')
        </if>
        order by b.updated_time desc, b.created_time desc
    </select>

</mapper>