<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppContentCheckMapper">


    <select id="getAppContentChecksByBizId" resultType="com.hzy.core.entity.AppContentCheck">
        SELECT * FROM app_content_check
        WHERE biz_id = #{bizId}
        AND is_del = false
    </select>

    <select id="getContentCheckListForChat" resultType="com.hzy.core.entity.AppContentCheck">
        (select acc.*, au.nick_name, au.recode_code as recordCode, au2.nick_name as receive_nick_name, au2.recode_code as
        receiveRecordCode, au2.id as receiveUserId, null as startTime, null as endTime
        from app_content_check acc
        left join app_user au on acc.send_user_id = au.id
        left join app_chatting_records acr on acc.biz_id = acr.id
        left join app_user au2 on acr.receive_user_id = au2.id
        <where>
            acc.scene = 1 and acc.is_del = false
            and acc.type in (1, 2, 3, 4)
            <if test="userId != null">
                and acc.send_user_id = #{userId}
            </if>
            <if test="receiveUserId != null">
                and acr.receive_user_id = #{receiveUserId}
            </if>
            <if test="type != null">
                and acc.type = #{type}
            </if>
            <if test="taskId != null and taskId!= ''">
                and acc.task_id = #{taskId}
            </if>
            <if test="tencentSuggestion != null and tencentSuggestion != ''">
                and acc.tencent_suggestion = #{tencentSuggestion}
            </if>
        </where>)

        union all

        (select acc.*, au.nick_name, au.recode_code as recordCode, au2.nick_name as receive_nick_name, au2.recode_code as
        receiveRecordCode, au2.id as receiveUserId, actr.connect_time as startTime, actr.hang_up_time as endTime
        from app_content_check acc
        left join app_user au on acc.send_user_id = au.id
        left join app_communicate_telephone_records actr on acc.biz_id = actr.id
        left join app_user au2 on actr.receive_user_id = au2.id
        <where>
            acc.scene = 1 and acc.is_del = false
            and acc.type in (5, 6)
            <if test="userId != null">
                and acc.send_user_id = #{userId}
            </if>
            <if test="receiveUserId != null">
                and actr.receive_user_id = #{receiveUserId}
            </if>
            <if test="type != null">
                and acc.type = #{type}
            </if>
            <if test="taskId != null and taskId!= ''">
                and acc.task_id = #{taskId}
            </if>
            <if test="tencentSuggestion != null and tencentSuggestion != ''">
                and acc.tencent_suggestion = #{tencentSuggestion}
            </if>
        </where>)
        order by id desc
    </select>
</mapper>