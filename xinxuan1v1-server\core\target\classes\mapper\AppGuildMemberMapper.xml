<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppGuildMemberMapper">

    <resultMap type="AppGuildMember" id="AppGuildMemberResult">
        <result property="id" column="id"/>
        <result property="guildId" column="guild_id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="isAdmin" column="is_admin"/>
    </resultMap>

    <sql id="selectAppGuildMemberVo">
        select id, guild_id, user_id, create_time, is_admin, gift_income_scale
        from app_guild_member
    </sql>

    <select id="selectAppGuildMemberList" parameterType="AppGuildMember" resultMap="AppGuildMemberResult">
        <include refid="selectAppGuildMemberVo"/>
        <where>
            <if test="guildId != null ">and guild_id = #{guildId}</if>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="isAdmin != null ">and is_admin = #{isAdmin}</if>
        </where>
    </select>

    <select id="selectAppGuildMemberById" parameterType="Long" resultMap="AppGuildMemberResult">
        <include refid="selectAppGuildMemberVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppGuildMember" parameterType="AppGuildMember" useGeneratedKeys="true" keyProperty="id">
        insert into app_guild_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="guildId != null">guild_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="isAdmin != null">is_admin,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="guildId != null">#{guildId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="isAdmin != null">#{isAdmin},</if>
        </trim>
    </insert>

    <update id="updateAppGuildMember" parameterType="AppGuildMember">
        update app_guild_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="guildId != null">guild_id = #{guildId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="isAdmin != null">is_admin = #{isAdmin},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppGuildMemberById" parameterType="Long">
        delete
        from app_guild_member
        where id = #{id}
    </delete>

    <delete id="deleteAppGuildMemberByIds" parameterType="String">
        delete from app_guild_member where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getGuildMemberByUserIdAndGuildId" parameterType="Long" resultMap="AppGuildMemberResult">
        <include refid="selectAppGuildMemberVo"/>
        where guild_id=#{guildId} and user_id=#{userId}
        order by id desc limit 0,1
    </select>

    <select id="getGuildMemberByUserId" resultType="com.hzy.core.entity.AppGuildMember">
        <include refid="selectAppGuildMemberVo"/>
        where user_id=#{userId}
        order by id desc
        limit 1
    </select>

    <select id="getGuildMemberNumber" resultType="java.lang.Long">
        select count(*)
        from app_guild_member
        where guild_id = #{guildId}
    </select>

    <select id="getGuildMemberList" resultType="java.util.Map">
        select DISTINCT u.id as userId,
        u.phone as phone,
        u.recode_code as recodeCode,
        u.nick_name as nickName,
        u.sex as sex,
        u.head_portrait as headPortrait,
        m.gift_income_scale as giftIncomeScale,
        CAST(m.create_time AS CHAR) as createTime
        from
        app_guild_member m,
        app_user u
        where
        m.user_id=u.id
        and m.guild_id=#{guildId}
        <if test="keyword!=null and keyword !='' ">
            and (u.recode_code like concat('%', #{keyword}, '%') or u.nick_name like concat('%', #{keyword}, '%'))
        </if>
        <if test="isAdminQuery==1">
            order by m.id desc
        </if>
        <if test="isAdminQuery==0">
            order by m.id asc
        </if>

    </select>

    <select id="getGuildAdminList" resultType="java.util.Map">
        select DISTINCT u.id as userId,
        u.phone as phone,
        u.recode_code as recodeCode,
        u.nick_name as nickName,
        u.sex as sex,
        u.head_portrait as headPortrait,
        CAST(m.create_time AS CHAR) as createTime
        from
        app_guild_member m,
        app_user u
        where
        m.user_id=u.id
        and m.guild_id=#{guildId}
        and m.is_admin=true
        <if test="keyword!=null and keyword !='' ">
            and (u.recode_code like concat('%', #{keyword}, '%') or u.nick_name like concat('%', #{keyword}, '%'))
        </if>
        <if test="isAdminQuery==1">
            order by m.id desc
        </if>
        <if test="isAdminQuery==0">
            order by m.id asc
        </if>

    </select>


    <select id="getGuildHostMemberList" resultType="java.util.Map">
        select DISTINCT u.id as userId,
        u.phone as phone,
        u.recode_code as recodeCode,
        u.nick_name as nickName,
        u.sex as sex,
        u.head_portrait as headPortrait,
        CAST(m.create_time AS CHAR) as createTime,
        cr.id as chatRoomId,
        cr.name as chatRoomName
        from
        app_guild_member m,
        app_user u,
        app_chat_room cr
        where
        m.user_id=u.id

        and cr.is_del=false
        and m.guild_id=#{guildId}
        and cr.hall_owner_user_id=u.id

        <if test="keyword!=null and keyword !='' ">
            and (u.recode_code like concat('%', #{keyword}, '%') or u.nick_name like concat('%', #{keyword}, '%'))
        </if>
        <if test="isAdminQuery==1">
            GROUP by u.id order by m.id desc
        </if>
        <if test="isAdminQuery==0">
            GROUP by u.id order by m.id asc
        </if>

    </select>

    <select id="getGuildMemberHallList" parameterType="Long" resultType="com.hzy.core.model.vo.admin.GuildVo">
        SELECT DISTINCT au.id            AS userId,
                        au.recode_code   AS recodeCode,
                        au.nick_name     AS nickName,
                        au.head_portrait AS headPortrait,
                        cr.id            AS chatRoomId,
                        cr.name          AS chatRoomName,
                        gm.guild_id      AS guildId,
                        gm.create_time   AS createTime
        FROM app_chat_room cr
                 INNER JOIN app_guild_member gm ON cr.hall_owner_user_id = gm.user_id
                 LEFT JOIN app_user au ON au.id = gm.user_id
        WHERE gm.guild_id = #{guildId}
    </select>

    <delete id="clearGuildAllMember" parameterType="java.lang.Long">
        delete
        from app_guild_member
        where guild_id = #{guildId}
    </delete>


    <select id="getWeekNewAddMemberList" resultType="java.util.Map" parameterType="java.lang.Long">
        select DISTINCT u.id                        as userId,
                        u.phone                     as phone,
                        u.recode_code               as recodeCode,
                        u.nick_name                 as nickName,
                        u.sex                       as sex,
                        u.head_portrait             as headPortrait,
                        u.age                       as age,
                        CAST(m.create_time AS CHAR) as createTime
        from app_guild_member m,
             app_user u
        where m.user_id = u.id
          and m.guild_id = #{guildId}
          and m.create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
          and m.create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        order by m.id desc
    </select>

    <select id="getWeekDynamicMemberList" resultType="java.util.Map" parameterType="java.lang.Long">
        select DISTINCT u.id                        as userId,
                        u.phone                     as phone,
                        u.recode_code               as recodeCode,
                        u.nick_name                 as nickName,
                        u.sex                       as sex,
                        u.head_portrait             as headPortrait,
                        u.age                       as age,
                        CAST(m.create_time AS CHAR) as createTime
        from app_guild_member m,
             app_user u
        where m.user_id = u.id
          and m.guild_id = #{guildId}
          and u.last_operating_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
          and u.last_operating_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        order by m.id desc
    </select>

    <select id="getGuildMemberUserIdList" parameterType="java.lang.Long" resultType="java.lang.Long">
        select ga.initiate_user_id
        from app_guild_apply ga
        where ga.guild_id = #{guildId}
          and ga.`status` = 1
        group by ga.initiate_user_id
    </select>


    <delete id="deleteGuildMemberByUserIdAndGuildId" parameterType="Long">
        delete
        from app_guild_member
        where user_id = #{userId}
          and guild_id = #{guildId}
    </delete>

    <!-- getGuildMemberByUserIdAndGuildIds 非分页版本 -->
    <select id="getGuildMemberByUserIdAndGuildIds" resultType="com.hzy.core.entity.AppUserEntity">
        SELECT u.*
        FROM app_user u
        LEFT JOIN app_guild_member agm ON agm.user_id = u.id
        WHERE
        1=1
        <if test="guildId == -1">
            and agm.id IS NULL
        </if>
        <if test="userId != null">
            AND u.id = #{userId}
        </if>
        <if test="guildId != -1">
            <if test="guildId != null">
            AND agm.guild_id = #{guildId}
            </if>
            <if test="guildIds != null and guildIds.size() > 0">
            AND agm.guild_id IN
            <foreach collection="guildIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            </if>
        </if>
    </select>
    
    <!-- getGuildMemberByUserIdAndGuildIdsPage 分页版本 -->
    <select id="getGuildMemberByUserIdAndGuildIdsPage" resultType="com.hzy.core.entity.AppUserEntity">
        SELECT u.*
        FROM app_user u
        LEFT JOIN app_guild_member agm ON agm.user_id = u.id
        WHERE
        1=1
        <if test="guildId == -1">
            and agm.id IS NULL
        </if>
        <if test="userId != null">
            AND u.id = #{userId}
        </if>
        <if test="guildId != -1">
            <if test="guildId != null">
            AND agm.guild_id = #{guildId}
            </if>
            <if test="guildIds != null and guildIds.size() > 0">
            AND agm.guild_id IN
            <foreach collection="guildIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            </if>
        </if>
    </select>

    <select id="getGuildIdByUserId" parameterType="java.lang.Long" resultType="java.lang.Long">
        select guild_id
        from app_guild_member
        where user_id = #{userId}
        order by id desc
        limit 1
    </select>

</mapper>
