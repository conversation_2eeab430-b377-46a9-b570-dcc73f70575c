<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppVipChatTemplatesMapper">


    <select id="getChatTemplates" resultType="com.hzy.core.entity.AppVipChatTemplates">
        select *
        from app_vip_chat_templates
        where is_del = false
          <if test="isAll == false">
              and status = 2
          </if>
        and user_id = #{userId}
        order by update_time desc
    </select>

    <select id="countChatTemplates" resultType="java.lang.Integer">
        select count(id)
        from app_vip_chat_templates
        where is_del = false
        and user_id = #{userId}
    </select>
</mapper>