package com.hzy.client.service;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.aliyun.cloudauth20190307.models.DescribeFaceVerifyResponseBody;
import com.aliyun.cloudauth20190307.models.InitFaceVerifyResponseBody;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.hzy.core.config.RedisCache;
import com.hzy.core.constant.*;
import com.hzy.core.entity.*;
import com.hzy.core.enums.*;
import com.hzy.core.exception.AppException;
import com.hzy.core.mapper.*;
import com.hzy.core.model.vo.AjaxResult;
import com.hzy.core.model.vo.app.*;
import com.hzy.core.page.TableDataInfo;
import com.hzy.core.service.*;
import com.hzy.core.service.async.ClientAsyncFactory;
import com.hzy.core.service.common.AppTitleNobilityService;
import com.hzy.core.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;

import static com.hzy.core.model.vo.AjaxResult.CODE_TAG;
import static com.hzy.core.model.vo.AjaxResult.DATA_TAG;
import static java.util.stream.Collectors.toList;

/**
 * 用户中心服务
 */
@Slf4j
@Service
public class AppUserService extends ServiceImpl<AppUserMapper, AppUserEntity> {

    private final static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    private final static DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final static ArrayBlockingQueue<Runnable> WORK_QUEUE = new ArrayBlockingQueue<>(3000);
    private final static RejectedExecutionHandler HANDLER = new ThreadPoolExecutor.CallerRunsPolicy();
    private static ThreadPoolExecutor executorService = new ThreadPoolExecutor(ThreadPoolConstants.corePoolSize,
            ThreadPoolConstants.maxPoolSize, ThreadPoolConstants.keepAliveSeconds, TimeUnit.SECONDS, WORK_QUEUE,
            HANDLER);
    @Resource
    private Redisson redisson;
    @Resource
    private AppCommunityService appCommunityService;
    @Resource
    private RedisCache redisCache;
    @Resource
    private AppUserMapper appUserMapper;
    @Resource
    private AppSendSmsCodeService appSendSmsCodeService;
    @Resource
    private TokenService tokenService;
    @Resource
    private AppUserGoldBillMapper appUserGoldBillMapper;
    @Resource
    private AppUserPointsBillMapper appUserPointsBillMapper;
    @Resource
    private AppCommonService appCommonService;
    @Resource
    private AppChattingRecordsMapper appChattingRecordsMapper;
    @Resource
    private ClientAsyncFactory clientAsyncFactory;
    @Resource
    private AppViewUserRecordsMapper appViewUserRecordsMapper;
    @Resource
    private AppCommunicateTelephoneRecordsMapper appCommunicateTelephoneRecordsMapper;
    @Resource
    private AppUserFollowMapper appUserFollowMapper;
    @Resource
    private AppGuildMapper appGuildMapper;
    @Resource
    private AppChatBtnTypeMapper appChatBtnTypeMapper;
    @Resource
    private AppUserBeautySetMapper appUserBeautySetMapper;
    @Resource
    private AppCompleteTaskRecordMapper appCompleteTaskRecordMapper;
    @Resource
    private AppBlacklistMapper appBlacklistMapper;
    @Resource
    private AppPrivateLetterListMapper appPrivateLetterListMapper;
    @Resource
    private AppUserCommunicateTelephoneConfigMapper appUserCommunicateTelephoneConfigMapper;
    @Resource
    private AppSignInRecordsMapper appSignInRecordsMapper;
    @Resource
    private AppUserIntimacyMapper appUserIntimacyMapper;
    @Resource
    private AppChatRoomUserMapper appChatRoomUserMapper;
    @Resource
    private UserGiveGiftMapper userGiveGiftMapper;
    @Resource
    private AppUserPersonalDressingMapper appUserPersonalDressingMapper;
    @Resource
    private AppTitleNobilityService appTitleNobilityService;
    @Resource
    private AuthCardUtil authCardUtil;
    @Resource
    private AppSignGoldConfigMapper appSignGoldConfigMapper;
    @Resource
    private AppWithdrawRecordsMapper appWithdrawRecordsMapper;
    @Resource
    private GameConfigService gameConfigService;
    @Resource
    private AppSysMsgService appSysMsgService;
    @Resource
    private AppCharmGradeConfigMapper appCharmGradeConfigMapper;
    @Resource
    private AppTopUpGradeConfigMapper appTopUpGradeConfigMapper;
    @Resource
    private SysDictTypeService sysDictTypeService;
    @Resource
    private AppCommunityMapper appCommunityMapper;
    @Resource
    private AppUserBeautySetService appUserBeautySetService;
    @Resource
    private AppMessageService appMessageService;
    @Autowired
    private AppContentCheckService appContentCheckService;
    @Autowired
    private AppVipService appVipService;
    @Resource
    private AppVipPrivilegesConfigMapper appVipPrivilegesConfigMapper;
    @Resource
    private AppConfigMapper appConfigMapper;
    @Resource
    private AppLoginService appLoginService;
    @Resource
    private AppUserVideoCardService appUserVideoCardService;
    @Resource
    private AppUserCommunicateTelephoneConfigService appUserCommunicateTelephoneConfigService;
    /**
     * 赠送金币
     *
     * @param toUserId
     * @param num
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult giveDiamond(AppUserEntity user, Long toUserId, Long num) {

        if (user.getCanTransfer().equals(WhetherTypeEnum.NO.getName())) {
            return AjaxResult.error("该用户已设置无法转赠");
        }
        RLock lock = redisson.getLock("app:giveDiamond:" + user.getId());
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(120, TimeUnit.SECONDS);

        if (null == toUserId) {
            lock.unlock();
            return AjaxResult.error("参数【toUserId】为空");
        }
        if (null == num) {
            lock.unlock();
            return AjaxResult.error("请填写转增数量");
        }
        if (num <= 0) {
            lock.unlock();
            return AjaxResult.error("转增数量必须大于0");
        }

        if (user.getGoldBalance().compareTo(new BigDecimal(num)) < 0) {
            lock.unlock();
            return AjaxResult.error("金币余额不足");
        }
        AppUserEntity toUser = appUserMapper.selectAppUserById(toUserId);
        if (null == toUser) {
            lock.unlock();
            return AjaxResult.error("对方账号不存在");
        }
        // 扣除用户金币余额
        if (appUserMapper.subtractUserGoldBalance(user.getId(), new BigDecimal(num)) <= 0) {
            lock.unlock();
            throw new AppException("金币余额不足");
        }

        Date time = new Date();
        AppUserGoldBill userGoldBill = new AppUserGoldBill();
        userGoldBill.setUserId(user.getId());
        userGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE30.getId());
        userGoldBill.setObjectId(toUserId);
        userGoldBill.setAmount(new BigDecimal(num).negate());
        userGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
        appUserGoldBillMapper.insertAppUserGoldBill(userGoldBill);

        appUserMapper.addUserGoldBalance(toUserId, new BigDecimal(num));

        AppUserGoldBill toUserGoldBill = new AppUserGoldBill();
        toUserGoldBill.setUserId(toUserId);
        toUserGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE31.getId());
        toUserGoldBill.setObjectId(user.getId());
        toUserGoldBill.setAmount(new BigDecimal(num));
        toUserGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
        appUserGoldBillMapper.insertAppUserGoldBill(toUserGoldBill);

        lock.unlock();

        return AjaxResult.success("转赠成功");
    }

    /**
     * 签到
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult toSignIn(Long userId) {
        RLock lock = redisson.getLock("app:toSignIn:" + userId);
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(60, TimeUnit.SECONDS);

        // 判断用户今日是否已签到
        if (appSignInRecordsMapper.isTodaySignIn(userId)) {
            lock.unlock();
            return AjaxResult.error("您今日已完成签到");
        }

        Date time = new Date();
        String weekName = DateUtils.getWeekOfDate(time);
        // 根据周名称获取金币数量
        BigDecimal goldNum = appSignGoldConfigMapper.getGoldNumByWeekName(weekName);
        AppTaskTypeEnums taskTypeEnums = AppTaskTypeEnums.TYPE10;
        if (null == goldNum) {
            goldNum = taskTypeEnums.getPoints();
        }

        // 签到记录入库
        AppSignInRecords signInRecords = new AppSignInRecords();
        signInRecords.setGold(goldNum);
        signInRecords.setUserId(userId);
        signInRecords.setCreateTime(time);
        appSignInRecordsMapper.insertAppSignInRecords(signInRecords);

        // 获取本周内累计签到数量
        int weeklySignInDays = appSignInRecordsMapper.getWeeklySignInDays(userId);
        if (weeklySignInDays == 5) {
            // 累计签到5天赠送一张视频卡卷
            appUserVideoCardService.addUserVideoCard(userId, AppVideoCardBillTypeEnums.SIGN_IN_TASK, 1);
        }

        // 判断该用户有没有完成每日签到任务
        taskToDo(userId, taskTypeEnums, goldNum);

        Map<String, Object> map = new HashMap<>();
        map.put("goldNum", goldNum);
        map.put("videoCardAmount", weeklySignInDays == 5 ? 1 : 0);

        lock.unlock();
        return AjaxResult.success("签到成功", map);
    }

    /**
     * 获取签到周历列表
     */
    public AjaxResult getSignInWeeklyCalendarList(Long userId) {
        // 获取当前周所有日期
        List<LocalDate> dateList = Arrays.asList(DayOfWeek.values())
                .stream()
                .map(LocalDate.now()::with)
                .collect(toList());
        List<AppSignInResultVo> resultList = new ArrayList<>();
        AppTaskTypeEnums taskTypeEnums = AppTaskTypeEnums.TYPE10;
        dateList.forEach(date -> {
            String weekName = DateUtils.getWeekOfDate(Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            AppSignInResultVo resultVo = new AppSignInResultVo();
            resultVo.setTime(date);
            // 根据周名称获取金币数量
            BigDecimal goldNum = appSignGoldConfigMapper.getGoldNumByWeekName(weekName);
            if (null == goldNum) {
                goldNum = taskTypeEnums.getPoints();
            }
            resultVo.setGold(goldNum);
            resultVo.setIsSignIn(appSignInRecordsMapper.isDateTodaySignIn(userId, date.format(df)));
            resultList.add(resultVo);
        });

        return AjaxResult.success(resultList);
    }

    /**
     * 获取任务中心列表
     */
    public AjaxResult getTaskCentreList(Long userId) {
        String weekName = DateUtils.getWeekOfDate(new Date());
        // 根据周名称获取金币数量
        BigDecimal goldNum = appSignGoldConfigMapper.getGoldNumByWeekName(weekName);
        List<AppTaskCentreVo> list = AppTaskTypeEnums.getList();
        list = list.stream().filter(item -> !item.getType().equals(AppTaskTypeEnums.TYPE10.getType()))
                .collect(toList());
        list.forEach(item -> {
            if (item.getType() == AppTaskTypeEnums.TYPE10.getType()) {
                // 如果是签到
                if (null != goldNum) {
                    item.setPoints(goldNum);
                }
            }
            item.setIsComplete(appCompleteTaskRecordMapper.isCompleteByTaskTypeAndUserId(item.getType()
                    .longValue(), userId, item.isBeginnerTask()));

            // type12单独设置任务名称
            if (item.getType() == AppTaskTypeEnums.TYPE12.getType()) {
                final AppUserEntity appUserEntity = appUserMapper.selectAppUserById(userId);
                String name = appUserEntity.getSex().equals(0) ? "关注6个女性" : "关注6个男性";
                item.setName(name);
            }
        });
        return AjaxResult.success(list);
    }

    /**
     * 获取用户美颜设置
     */
    public AjaxResult getUserBeautySet(Long userId) {
        final AppUserBeautySet result = appUserBeautySetService.lambdaQuery().eq(AppUserBeautySet::getUserId, userId).one();
        return AjaxResult.success(result);
    }

    /**
     * 修改用户美颜设置
     */
    public AjaxResult updUserBeautySet(AppUserBeautySet userBeautySetVo, Long userId) {
        RLock lock = redisson.getLock("app:updUserBeautySet:" + userId);
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(60, TimeUnit.SECONDS);

        // 根据用户id获取该用户的美颜设置
        AppUserBeautySet userBeautySet = appUserBeautySetService.lambdaQuery().eq(AppUserBeautySet::getUserId, userId).one();
        if (null == userBeautySet) {// 没有就新增
            userBeautySet = new AppUserBeautySet();
            BeanUtils.copyProperties(userBeautySetVo, userBeautySet);
            userBeautySet.setUserId(userId);
            userBeautySet.setCreateTime(new Date());
            appUserBeautySetService.save(userBeautySet);
        } else {// 存在就修改
            AppUserBeautySet userBeautySetUpd = new AppUserBeautySet();
            BeanUtils.copyProperties(userBeautySetVo, userBeautySetUpd);
            userBeautySetUpd.setId(userBeautySet.getId());
            userBeautySetUpd.setUpdateTime(new Date());
            appUserBeautySetService.saveOrUpdate(userBeautySetUpd);
        }

        lock.unlock();
        return AjaxResult.success("保存成功");
    }

    /**
     * 向老手机号下发修改手机校验码(只有绑定过才可以操作)
     */
    public AjaxResult toOldPhoneSendUpdPhoneCheckCode(AppUserEntity user) {
        if (StringUtils.isBlank(user.getPhone())) {
            return AjaxResult.error("账户未绑定手机号");
        }
        AjaxResult sendSmsCodeResult = appSendSmsCodeService.toSendSmsCode(SmsSendTypeEnum.TYPE4.getId(),
                user.getPhone());
        return sendSmsCodeResult;
    }

    /**
     * 修改绑定手机(只有绑定过才可以操作)
     *
     * @param phoneNew 新手机号
     * @param codeNew  新手机号验证码
     * @param codeOld  老手机号验证码
     * @return
     */
    public AjaxResult updBindingPhone(String phoneNew, String codeNew, String codeOld, AppUserEntity user) {
        String oldPhone = user.getPhone();

        RLock lock = redisson.getLock("app:updBindingPhone:" + user.getId());
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(60, TimeUnit.SECONDS);

        if (StringUtils.isBlank(oldPhone)) {
            lock.unlock();
            return AjaxResult.error("账户未绑定手机号");
        }
        if (StringUtils.equals(oldPhone, phoneNew)) {
            lock.unlock();
            return AjaxResult.error("新手机号不能和原手机号一样");
        }
        if (StringUtils.isBlank(codeOld)) {
            lock.unlock();
            return AjaxResult.error("原手机号验证码不能为空");
        }
        // 获取缓存中的原手机号验证码
        String cacheCodeOld = redisCache.getCacheObject(SmsSendTypeEnum.TYPE4.getRegionName() + oldPhone);
        // 校验老手机号验证码
        if (StringUtils.isBlank(cacheCodeOld)) {
            lock.unlock();
            return AjaxResult.error("原手机号验证码无效");
        }
        if (!StringUtils.equals(codeOld, cacheCodeOld)) {
            lock.unlock();
            return AjaxResult.error("原手机号验证码错误");
        }

        if (StringUtils.isBlank(phoneNew)) {
            lock.unlock();
            return AjaxResult.error("新手机号不能为空");
        }
        if (!Validator.isMobile(phoneNew)) {
            lock.unlock();
            return AjaxResult.error("手机号格式错误");
        }
        if (StringUtils.isBlank(codeNew)) {
            lock.unlock();
            return AjaxResult.error("新手机号验证码不能为空");
        }
        // 获取缓存中的新手机号验证码
        String cacheCodeNew = redisCache.getCacheObject(SmsSendTypeEnum.TYPE3.getRegionName() + phoneNew);
        // 校验老手机号验证码
        if (StringUtils.isBlank(cacheCodeNew)) {
            lock.unlock();
            return AjaxResult.error("新手机号验证码无效");
        }
        if (!StringUtils.equals(codeNew, cacheCodeNew)) {
            lock.unlock();
            return AjaxResult.error("新手机号验证码错误");
        }
        // 判断新手机号是否已被绑定
        AppUserEntity appUser = appUserMapper.selectAppUserByPhone(phoneNew);
        if (null != appUser) {
            lock.unlock();
            return AjaxResult.error("您输入的新手机号已被其他账号绑定");
        }
        AppUserEntity userUpd = new AppUserEntity();
        userUpd.setId(user.getId());
        // 设置新手机号
        userUpd.setPhone(phoneNew);
        appUserMapper.updateAppUser(userUpd);
        try {
            // 删除缓存中的新手机号验证码
            redisCache.deleteObject(SmsSendTypeEnum.TYPE3.getRegionName() + phoneNew);
        } catch (Exception e) {
        }
        try {
            // 删除缓存中的老手机号验证码
            redisCache.deleteObject(SmsSendTypeEnum.TYPE4.getRegionName() + oldPhone);
        } catch (Exception e) {
        }

        lock.unlock();

        return AjaxResult.success("修改成功");
    }

    /**
     * 获取用户在线状态
     *
     * @param viewUserId
     * @return
     */
    public AjaxResult getUserOnlineStatus(Long viewUserId) {
        AppUserEntity user = appUserMapper.selectAppUserById(viewUserId);
        if (null == user) {
            return AjaxResult.success("ok", WhetherTypeEnum.NO.getName());
        }
        return AjaxResult.success("ok",
                appCommonService.getUserIsOnline(viewUserId, user.getSex(), user.getIsOnline()));
    }

    /**
     * 查看用户资料详情
     *
     * @param viewUserId       查看用户id
     * @param currentUserId    当前用户id
     * @param isAddViewRecords 是添加视图记录
     * @return {@link AppViewUserInfoVo }
     */
    public AppViewUserInfoVo viewUserInformation(Long viewUserId, Long currentUserId, Integer isAddViewRecords) {
        if (null == viewUserId || viewUserId.intValue() <= 0) {
            throw new AppException("参数【viewUserId】为空");
        }
        if (null == isAddViewRecords) {
            isAddViewRecords = WhetherTypeEnum.YES.getName();
        }
        // 先读缓存
        AppUserEntity user = appUserMapper.selectAppUserById(viewUserId);
        if (null == user) {
            throw new AppException("无法查看,对方账号不存在");
        }
        AppUserStatusTypeEnums userStatusTypeEnums = AppUserStatusTypeEnums.getEnum(user.getUserStatus().intValue());
        if (null == userStatusTypeEnums) {
            throw new AppException("无法查看,对方账号状态异常");
        }
        if (userStatusTypeEnums.getCode() != AppUserStatusTypeEnums.TYPE1.getCode()) {
            throw new AppException("无法查看,对方账号" + userStatusTypeEnums.getInfo());
        }

        AppViewUserInfoVo viewUserInfoVo = new AppViewUserInfoVo();

        BeanUtils.copyProperties(user, viewUserInfoVo);

        if (null != viewUserInfoVo.getSex() && viewUserInfoVo.getSex().intValue() == -1) {
            viewUserInfoVo.setSex(null);
        }
        if (StringUtils.isBlank(viewUserInfoVo.getBirthday())) {
            viewUserInfoVo.setAge(null);
        }
        if (null != viewUserInfoVo.getAge() && viewUserInfoVo.getAge().intValue() == -1) {
            viewUserInfoVo.setAge(null);
        }
        if (!StringUtils.isBlank(user.getPhotoAlbum())) {
            String[] photoAlbum = JSON.parseObject(user.getPhotoAlbum(), String[].class);
            viewUserInfoVo.setPhotoAlbum(photoAlbum);
        } else {
            viewUserInfoVo.setPhotoAlbum(new String[]{});
        }
        if (null != viewUserInfoVo.getPurchaseSituation() && viewUserInfoVo.getPurchaseSituation().intValue() == -1) {
            viewUserInfoVo.setPurchaseSituation(null);
        }
        if (null != viewUserInfoVo.getCarPurchaseSituation() && viewUserInfoVo.getCarPurchaseSituation()
                .intValue() == -1) {
            viewUserInfoVo.setCarPurchaseSituation(null);
        }
        viewUserInfoVo.setUserId(user.getId());
        // 查询用户音视频价格
        AppUserCommunicateTelephoneConfig userCommunicateTelephoneConfig = appUserCommunicateTelephoneConfigMapper.getUserCommunicateTelephoneConfigByUserId(user.getId());
        if (userCommunicateTelephoneConfig != null) {
            viewUserInfoVo.setVideoMinutesGold(userCommunicateTelephoneConfig.getVideoMinutesGold());
            viewUserInfoVo.setVoiceMinutesGold(userCommunicateTelephoneConfig.getVoiceMinutesGold());
        }

        // 附加参数
        Map<String, Object> subjoinParameter = new HashMap<>();
        if (!currentUserId.equals(viewUserId)) {// 不是自己看自己

            AppUserFollow followTarget = appUserFollowMapper.selectAppUserFollowByUserIdAndBeUserId(currentUserId,
                    viewUserId);

            subjoinParameter.put("isFollowTarget",
                    null == followTarget ? WhetherTypeEnum.NO.getName() : WhetherTypeEnum.YES.getName());// 当前用户是否关注了对方:0否,1是

            AppUserFollow followMy = appUserFollowMapper.selectAppUserFollowByUserIdAndBeUserId(viewUserId,
                    currentUserId);

            subjoinParameter.put("isFollowMy",
                    null == followMy ? WhetherTypeEnum.NO.getName() : WhetherTypeEnum.YES.getName());// 对方是否关注了当前用户:0否,1是

            // 判断当前是否已拉黑被查看用户
            subjoinParameter.put("isShieldTarget",
                    null != appBlacklistMapper.selectAppBlacklistByUserIdAndToUserId(currentUserId, viewUserId)
                            ? WhetherTypeEnum.YES.getName()
                            : WhetherTypeEnum.NO.getName());
            // 判断被查看用户是否已拉黑当前用户
            subjoinParameter.put("isShieldMy",
                    null != appBlacklistMapper.selectAppBlacklistByUserIdAndToUserId(viewUserId, currentUserId)
                            ? WhetherTypeEnum.YES.getName()
                            : WhetherTypeEnum.NO.getName());

            // //查询当前用户和被查看用户的聊天按钮类型
            // AppChatBtnType chatBtnType =
            // appChatBtnTypeMapper.getChatBtnType(currentUserId, viewUserId);
            // if (null != chatBtnType) {
            // viewUserInfoVo.setChatBtnType(chatBtnType.getBtnType());
            // } else {//为空默认就是打招呼
            // viewUserInfoVo.setChatBtnType(WhetherTypeEnum.NO.getName());
            // }
            viewUserInfoVo.setChatBtnType(WhetherTypeEnum.YES.getName());

            // if (viewUserInfoVo.getChatBtnType() == WhetherTypeEnum.NO.getName())
            // {//如果聊天按钮是打招呼就去判断是否是好友
            // if (appCommonService.isFriend(currentUserId, viewUserId) ==
            // WhetherTypeEnum.YES.getName() ||
            // appChattingRecordsMapper.isContinuousChat(currentUserId, viewUserId))
            // {//如果是好友或者有连续聊天，那就改成私信类型
            // viewUserInfoVo.setChatBtnType(WhetherTypeEnum.YES.getName());
            // }
            // }
            // 获取用户亲密度
            AppUserIntimacy userIntimacy = appUserIntimacyMapper.getUserIntimacy(currentUserId, viewUserId);
            if (null == userIntimacy) {// 为空就是0
                viewUserInfoVo.setIntimacyValue(new BigDecimal("0"));
            } else {
                viewUserInfoVo.setIntimacyValue(userIntimacy.getIntimacyValue());
            }
        } else {
            subjoinParameter.put("isFollowTarget", WhetherTypeEnum.NO.getName());// 当前用户是否关注了对方:0否,1是

            subjoinParameter.put("isFollowMy", WhetherTypeEnum.NO.getName());// 对方是否关注了当前用户:0否,1是

            subjoinParameter.put("isShieldTarget", WhetherTypeEnum.NO.getName());
            subjoinParameter.put("isShieldMy", WhetherTypeEnum.NO.getName());
            viewUserInfoVo.setIntimacyValue(new BigDecimal("0"));

        }

        viewUserInfoVo.setSubjoinParameter(subjoinParameter);

        viewUserInfoVo.setIsOnline(appCommonService.getUserIsOnline(viewUserId, user.getSex(), user.getIsOnline()));

        if (!currentUserId.equals(viewUserId) && isAddViewRecords == WhetherTypeEnum.YES.getName()) {// 不是自己看自己，那就异步添加查看用户记录
            // 判断用户是否开启隐身访问
            boolean enabled = appVipService.enableVipPrivileges(VipPrivilegesConstants.HIDE_VISIT, currentUserId);
            AppViewUserRecords viewUserRecords = new AppViewUserRecords();
            viewUserRecords.setBeViewUserId(viewUserId);
            viewUserRecords.setViewUserId(currentUserId);
            viewUserRecords.setCreateTime(new Date());
            viewUserRecords.setIsRead(enabled ? WhetherTypeEnum.YES.getName() : WhetherTypeEnum.NO.getName());
            clientAsyncFactory.asyncAddViewUserRecords(viewUserRecords);
        }

        viewUserInfoVo.setUseInColorNickName(appUserPersonalDressingMapper.getUserUseInPersonalDressingInfo(
                AppPersonalDressingCategoryEnums.COLOR_NICK_NAME.getId(), viewUserId));
        viewUserInfoVo.setUseInMount(appUserPersonalDressingMapper
                .getUserUseInPersonalDressingInfo(AppPersonalDressingCategoryEnums.MOUNT.getId(), viewUserId));
        viewUserInfoVo.setUseInChatFrame(appUserPersonalDressingMapper
                .getUserUseInPersonalDressingInfo(AppPersonalDressingCategoryEnums.CHAT_FRAME.getId(), viewUserId));
        viewUserInfoVo.setUseInMicFrame(appUserPersonalDressingMapper
                .getUserUseInPersonalDressingInfo(AppPersonalDressingCategoryEnums.MIC_FRAME.getId(), viewUserId));

        viewUserInfoVo.setUseInPendant(appUserPersonalDressingMapper
                .getUserUseInPersonalDressingInfo(AppPersonalDressingCategoryEnums.PENDANT.getId(), viewUserId));
        viewUserInfoVo.setUseInFamousBrand(appUserPersonalDressingMapper
                .getUserUseInPersonalDressingInfo(AppPersonalDressingCategoryEnums.FAMOUS_BRAND.getId(), viewUserId));

        try {
            viewUserInfoVo.setUserTitleNobility(appTitleNobilityService.getUserTitleNobilityInfo(user, false));
        } catch (Exception e) {
        }

        viewUserInfoVo.setJoinCharRoom(appChatRoomUserMapper.getUserJoinCharRoomInfo(viewUserInfoVo.getUserId()));

        viewUserInfoVo.setIsPhoneAuth(
                StringUtils.isBlank(user.getPhone()) ? WhetherTypeEnum.NO.getName() : WhetherTypeEnum.YES.getName());
        // 获取用户魅力信息
        viewUserInfoVo.setUserCharmGrade(appTitleNobilityService.getUserTitleCharmInfo(user, false));
        // 财富信息
        viewUserInfoVo.setUserTopUpGrade(appTitleNobilityService.getUserTitleTopUpInfo(user, false));

        //从app_community.picture_url字段中获取最新的3个图片url地址
        List<String> recentPostImages = appCommunityMapper.getUserTopThreePostImages(viewUserId);
        viewUserInfoVo.setRecentPostImages(recentPostImages);
        viewUserInfoVo.setIsNewUser(UserUtil.isNewUser(user.getCreatedTime(),redisCache.getCacheObject(CacheConstants.NEW_USER_DAYS)));
        viewUserInfoVo.setCurrentIcoUrl(appTitleNobilityService.getCurrentIcoUrl(viewUserId).getCurrentIcoUrl());
        viewUserInfoVo.setContributeCurrentIcoUrl(appTitleNobilityService.getCurrentIcoUrl(viewUserId).getContributeCurrentIcoUrl());

        // 获取用户会员配置
        if (!currentUserId.equals(viewUserId) && user.getIsVip() == WhetherTypeEnum.YES.getName()) {
            AppVipPrivilegesConfig config = appVipPrivilegesConfigMapper.getVipPrivilegesConfigByUserId(viewUserId);
            viewUserInfoVo.setHideCharm(config.getHideCharm());
            viewUserInfoVo.setHideWealth(config.getHideWealth());
            viewUserInfoVo.setHideGifts(config.getHideGifts());
            viewUserInfoVo.setHideSentGifts(config.getHideSentGifts());
        }

        return viewUserInfoVo;
    }

    /**
     * 关注或取消关注
     *
     * @param toUserId 到用户id
     * @param user     用户
     * @return {@link AjaxResult }
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult followOrCancelFollow(Long toUserId, AppUserEntity user) {
        Long userId = user.getId();
        if (null == toUserId || toUserId.intValue() < 1) {
            return AjaxResult.error("参数【toUserId】为空");
        }
        RLock lock = redisson.getLock("app:followOrCancelFollow:" + userId + ":" + toUserId);
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(60, TimeUnit.SECONDS);

        if (userId.equals(toUserId)) {
            lock.unlock();
            return AjaxResult.error("不能对自己操作");
        }

        // 判断当前用户有没有拉黑对方
        AppBlacklist blacklistByUser = appBlacklistMapper.selectAppBlacklistByUserIdAndToUserId(userId, toUserId);
        if (null != blacklistByUser) {
            lock.unlock();
            return AjaxResult.error("操作失败,您已将对方加入黑名单");
        }

        // 判断当前对方有没有拉黑当前用户
        AppBlacklist blacklistByReceiveUser = appBlacklistMapper.selectAppBlacklistByUserIdAndToUserId(toUserId,
                userId);
        if (null != blacklistByReceiveUser) {
            lock.unlock();
            return AjaxResult.error("操作失败,对方已将您加入黑名单");
        }

        AppUserEntity receiveUser = appUserMapper.selectAppUserById(toUserId);
        if (null == receiveUser) {
            lock.unlock();
            return AjaxResult.error("操作失败,对方账号不存在");
        }
        Map<String, Object> result = new HashMap<>();
        AppUserFollow followTarget = appUserFollowMapper.selectAppUserFollowByUserIdAndBeUserId(userId, toUserId);
        if (null == followTarget) {// 为空就是关注
            followTarget = new AppUserFollow();
            followTarget.setBeUserId(toUserId);
            followTarget.setUserId(userId);
            followTarget.setCreateTime(new Date());
            followTarget.setUpdateTime(followTarget.getCreateTime());
            appUserFollowMapper.insertAppUserFollow(followTarget);

            // 判断该用户今日有没有完成关注6个异性用户任务
            if (appUserFollowMapper.isDoneFollowSixUser(userId)) {
                taskToDo(userId, AppTaskTypeEnums.TYPE12, AppTaskTypeEnums.TYPE12.getPoints());
            }
            // 发送系统消息
            AppSysMsg appSysMsg = new AppSysMsg();
            appSysMsg.setUserId(toUserId)
                    .setMsgType((long) AppSysMsgTypeEnums.TYPE1.getId())
                    .setIsRead(0)
                    .setTitle(AppSysMsgTypeEnums.TYPE1.getDesc())
                    .setContent(null == user.getNickName() ? user.getRecodeCode() : user.getNickName() + "关注了您")
                    .setCreateTime(new Date());
            appSysMsgService.save(appSysMsg);

            result.put("type", 1);// type为1就是关注
            lock.unlock();
            return AjaxResult.success("关注成功", result);
        } else {// 不为空就是取消关注
            appUserFollowMapper.deleteAppUserFollowById(followTarget.getId());
            result.put("type", 2);// type为2就是取消
            lock.unlock();
            return AjaxResult.success("已取消关注", result);
        }
    }

    /**
     * 获取当前用户详情-原始数据转换成VO返回给前端
     *
     * @param user 用户
     * @return {@link AppUserInfoVo }
     */
    public AppUserInfoVo getPresentUserInfo(AppUserEntity user) {

        AppUserInfoVo userInfoVo = new AppUserInfoVo();
        BeanUtils.copyProperties(user, userInfoVo);
        if (null != userInfoVo.getSex() && userInfoVo.getSex().intValue() == -1) {
            userInfoVo.setSex(null);
        }
        if (StringUtils.isBlank(userInfoVo.getBirthday())) {
            userInfoVo.setAge(null);
        }
        if (null != userInfoVo.getAge() && userInfoVo.getAge().intValue() == -1) {
            userInfoVo.setAge(null);
        }
        if (!StringUtils.isBlank(user.getPhotoAlbum())) {
            String[] photoAlbum = JSON.parseObject(user.getPhotoAlbum(), String[].class);
            userInfoVo.setPhotoAlbum(photoAlbum);
        } else {
            userInfoVo.setPhotoAlbum(new String[]{});
        }
        if (null != userInfoVo.getPurchaseSituation() && userInfoVo.getPurchaseSituation().intValue() == -1) {
            userInfoVo.setPurchaseSituation(null);
        }
        if (null != userInfoVo.getCarPurchaseSituation() && userInfoVo.getCarPurchaseSituation().intValue() == -1) {
            userInfoVo.setCarPurchaseSituation(null);
        }
        userInfoVo.setUserId(user.getId());
        // if(null!=user.getSex()&&user.getSex().intValue()==AppUserSexTypeEnums.TYPE1.getId()){
        // userInfoVo.setIsTx(WhetherTypeEnum.YES.getName());
        // }else{
        // userInfoVo.setIsTx(user.getIsTx());
        // }

        boolean isTx = false;

        if (appGuildMapper.userIsGuild(userInfoVo.getUserId())) {
            isTx = true;
        }
        // Long userChatRoomId =
        // appChatRoomUserMapper.getHallOwnerChatRoomId(userInfoVo.getUserId());
        // if (null == userChatRoomId) {
        // userChatRoomId =
        // appChatRoomUserMapper.getUserAdminChatRoomId(userInfoVo.getUserId());
        // if (null == userChatRoomId) {
        // userChatRoomId =
        // appChatRoomUserMapper.getUserEmceeChatRoomId(userInfoVo.getUserId());
        // }
        // }
        // if (null == userChatRoomId) {
        // isTx = false;
        // } else {
        // isTx = true;
        // }
        AppUserEntity userInfo = appUserMapper.selectAppUserById(userInfoVo.getUserId());
        BeanUtils.copyProperties(userInfo, userInfoVo);
        // 是否设置了密码
        userInfoVo.setIsSetPassword(!StringUtils.isBlank(user.getPassword()) ? WhetherTypeEnum.YES.getName()
                : WhetherTypeEnum.NO.getName());
        // 是否绑定了手机
        userInfoVo.setIsBindingPhone(
                !StringUtils.isBlank(user.getPhone()) ? WhetherTypeEnum.YES.getName() : WhetherTypeEnum.NO.getName());

        // 获取当前用户被查看的数量
        userInfoVo.setBeViewCount(appViewUserRecordsMapper.getUserBeViewCount(user.getId()));

        // 获取当前用户粉丝数量
        userInfoVo.setVermicelliCount(appUserFollowMapper.getUserVermicelliCount(user.getId()));

        // 获取当前用户关注数量
        userInfoVo.setFollowCount(appUserFollowMapper.getUserFollowCount(user.getId()));

        // 获取当前用户好友数量
        userInfoVo.setFriendCount(appUserFollowMapper.getUserFriendCount(user.getId()));

        userInfoVo.setUseInColorNickName(appUserPersonalDressingMapper.getUserUseInPersonalDressingInfo(
                AppPersonalDressingCategoryEnums.COLOR_NICK_NAME.getId(), user.getId()));
        userInfoVo.setUseInMount(appUserPersonalDressingMapper
                .getUserUseInPersonalDressingInfo(AppPersonalDressingCategoryEnums.MOUNT.getId(), user.getId()));
        userInfoVo.setUseInChatFrame(appUserPersonalDressingMapper
                .getUserUseInPersonalDressingInfo(AppPersonalDressingCategoryEnums.CHAT_FRAME.getId(), user.getId()));
        userInfoVo.setUseInMicFrame(appUserPersonalDressingMapper
                .getUserUseInPersonalDressingInfo(AppPersonalDressingCategoryEnums.MIC_FRAME.getId(), user.getId()));

        userInfoVo.setUseInPendant(appUserPersonalDressingMapper
                .getUserUseInPersonalDressingInfo(AppPersonalDressingCategoryEnums.PENDANT.getId(), user.getId()));
        userInfoVo.setUseInFamousBrand(appUserPersonalDressingMapper
                .getUserUseInPersonalDressingInfo(AppPersonalDressingCategoryEnums.FAMOUS_BRAND.getId(), user.getId()));

        userInfoVo.setIsPhoneAuth(
                StringUtils.isBlank(user.getPhone()) ? WhetherTypeEnum.NO.getName() : WhetherTypeEnum.YES.getName());

        userInfoVo.setUserStatus(null);

        userInfoVo.setJoinCharRoom(appChatRoomUserMapper.getUserJoinCharRoomInfo(user.getId()));
        userInfoVo.setGuildId(appGuildMapper.getUserJoinGuildId(user.getId()));
        userInfoVo.setGoldIcon(appTopUpGradeConfigMapper.getIconByUserId(user.getId()));
        userInfoVo.setCharmIcon(appCharmGradeConfigMapper.getIconByUserId(user.getId()));

        userInfoVo.setIsNewUser(UserUtil.isNewUser(user.getCreatedTime(), redisCache.getCacheObject(CacheConstants.NEW_USER_DAYS)));

        // 先判断公会状态
        boolean isInGuild = appGuildMapper.getUserJoinGuildId(user.getId()) != null;

        if (isInGuild) {
            // 加入公会永久不显示
            userInfoVo.setIsShow(0);
        } else {
            // 未加入公会时判断时间
            boolean isOver24Hours = ChronoUnit.HOURS.between(user.getCreatedTime(), LocalDateTime.now()) > 24;
            userInfoVo.setIsShow(isOver24Hours ? 1 : 0);
        }

        return userInfoVo;
    }

    /**
     * 绑定手机号(只有未绑定过才可以操作)
     *
     * @param phone
     * @param code
     * @return
     */
    public AjaxResult bindingPhone(String phone, String code, AppUserEntity user) {

        RLock lock = redisson.getLock("app:bindingPhone:" + user.getId());
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(60, TimeUnit.SECONDS);

        if (!StringUtils.isBlank(user.getPhone())) {
            lock.unlock();
            return AjaxResult.error("您已绑定手机号,无需重复操作");
        }

        if (StringUtils.isBlank(phone)) {
            lock.unlock();
            return AjaxResult.error("手机号不能为空");
        }
        if (!Validator.isMobile(phone)) {
            lock.unlock();
            return AjaxResult.error("手机号格式错误");
        }
        if (StringUtils.isBlank(code)) {
            lock.unlock();
            return AjaxResult.error("验证码不能为空");
        }

        // 校验验证码
        String cacheCode = redisCache.getCacheObject(SmsSendTypeEnum.TYPE3.getRegionName() + phone);
        // 万能验证码
        if (redisCache.hasKey("loginCode")) {
            String loginCode = redisCache.getCacheObject("loginCode").toString();
            if (!StringUtils.equals(loginCode, code)) {
                if (StringUtils.isBlank(cacheCode)) {
                    return AjaxResult.error("验证码无效");
                }
                if (!StringUtils.equals(code, cacheCode)) {
                    return AjaxResult.error("验证码错误");
                }
            }
        } else {
            if (StringUtils.isBlank(cacheCode)) {
                return AjaxResult.error("验证码无效");
            }
            if (!StringUtils.equals(code, cacheCode)) {
                return AjaxResult.error("验证码错误");
            }
        }
        // 判断该手机号是否已被绑定
        AppUserEntity appUser = appUserMapper.selectAppUserByPhone(phone);
        if (null != appUser) {
            lock.unlock();
            return AjaxResult.error("该手机号已被其他账号绑定");
        }
        AppUserEntity userUpd = new AppUserEntity();
        userUpd.setId(user.getId());
        // 设置手机号
        userUpd.setPhone(phone);
        appUserMapper.updateAppUser(userUpd);
        // 查看是否需要完善资料
        AppLoginResultVo loginResult = new AppLoginResultVo();
        loginResult.setToken(user.getToken());
        loginResult.setIsPerfectInfo(user.getIsPerfectInfo());// 是否完善了资料
        lock.unlock();
        return AjaxResult.success("绑定成功", loginResult);
    }

    /**
     * 语音视频认证
     * <AUTHOR>
     * @date 2025/4/2 下午4:31
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult authentication(AuthenticationVo vo, AppUserEntity user) {
        AppUserEntity appUser = appUserMapper.selectById(user.getId());

        if (!StringUtils.isBlank(vo.getVideoUrl())) {
            appUser.setVideo(vo.getVideoUrl());
            appUser.setVideoCheckStatus(CheckStatusConstants.CHECKING);
        }

        if (!StringUtils.isBlank(vo.getVoiceUrl())) {
            appUser.setVoiceSignature(vo.getVoiceUrl());
            appUser.setVoiceCheckStatus(CheckStatusConstants.CHECKING);
        }

        appUserMapper.updateById(appUser);
        return AjaxResult.success("修改成功", appUser.getId());
    }

    /**
     * 语音认证提示词
     * <AUTHOR>
     * @date 2025/4/3 上午10:46
     */
    public AjaxResult getVoiceCheckTips() {
        // 从字典中获取客服的微信号和二维码
        List<SysDictData> customerService = sysDictTypeService.selectDictDataByType("voice_check_tips");
        if (CollectionUtils.isEmpty(customerService)) {
            return AjaxResult.error("系统未配置语音认证提示词");
        }
        SysDictData sysDictData = customerService.get(new Random().nextInt(customerService.size()));
        return AjaxResult.success("查询成功", sysDictData);
    }

    /**
     * 修改用户资料
     *
     * @param vo 用户信息更新VO对象
     * @param user 当前用户实体
     * @return 操作结果
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult updUserInfo(AppUpdUserInfoVo vo, AppUserEntity user) {
        // 获取分布式锁防止并发更新
        RLock lock = redisson.getLock("app:updateUserInfo:" + user.getId());
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        
        try {
            lock.lock(30, TimeUnit.SECONDS);
            
            // 只修改定位数据
            if (Boolean.TRUE.equals(vo.getIsUpdateLocation()) && vo.getIsUpdateLocation().intValue() == WhetherTypeEnum.YES.getName()) {
                return updateLocationInfo(vo, user.getId());
            } else {
                // 修改用户资料
                return updateUserProfile(vo, user);
            }
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
    
    /**
     * 更新用户定位信息
     *
     * @param vo 用户更新VO
     * @param userId 用户ID
     * @return 操作结果
     */
    private AjaxResult updateLocationInfo(AppUpdUserInfoVo vo, Long userId) {
        // 验证位置参数
        if (!isValidLocationData(vo)) {
            return AjaxResult.error("位置信息参数不完整或超出长度限制");
        }
        
        // 更新位置信息
        AppUserEntity userUpd = new AppUserEntity();
        userUpd.setId(userId);
        userUpd.setLongitude(vo.getLongitude());
        userUpd.setLatitude(vo.getLatitude());
        userUpd.setProvince(vo.getProvince());
        userUpd.setCity(vo.getCity());
        userUpd.setArea(vo.getArea());

        appUserMapper.updateAppUser(userUpd);
        return AjaxResult.success();
    }
    
    /**
     * 验证位置数据是否合法
     *
     * @param vo 用户更新VO
     * @return 是否合法
     */
    private boolean isValidLocationData(AppUpdUserInfoVo vo) {
        // 验证经纬度和地区信息
        if (StringUtils.isBlank(vo.getLatitude()) || vo.getLatitude().length() > 400 ||
            StringUtils.isBlank(vo.getLongitude()) || vo.getLongitude().length() > 400 ||
            StringUtils.isBlank(vo.getProvince()) || vo.getProvince().length() > 400 ||
            StringUtils.isBlank(vo.getCity()) || vo.getCity().length() > 400 ||
            StringUtils.isBlank(vo.getArea()) || vo.getArea().length() > 400) {
            return false;
        }
        return true;
    }
    
    /**
     * 更新用户个人资料
     *
     * @param vo 用户更新VO
     * @param user 当前用户
     * @return 操作结果
     */
    private AjaxResult updateUserProfile(AppUpdUserInfoVo vo, AppUserEntity user) {
        // 设置默认值
        setDefaultValues(vo);
        
        // 验证基本信息
        AjaxResult validationResult = validateUserInfo(vo, user);
        if (!Integer.valueOf(HttpStatus.SUCCESS).equals(validationResult.get("code"))) {
            return validationResult;
        }
        
        // 检查敏感词
        AjaxResult sensitiveResult = checkSensitiveWords(vo);
        if (!Integer.valueOf(HttpStatus.SUCCESS).equals(sensitiveResult.get("code"))) {
            return sensitiveResult;
        }
        
        // 检查并处理昵称修改
        handleNicknameChange(user, vo);
        
        // 检查是否需要发送系统动态
        checkAndSendSystemPost(user, vo);
        
        // 处理头像为空的情况
        handleEmptyAvatar(vo, user);
        
        // 更新用户信息
        updateUserEntity(vo, user);
        
        // 处理任务完成情况
        processUserTasks(vo, user);
        
        // 返回更新结果，带上语音是否修改的标志
        boolean isVoiceChanged = isVoiceSignatureChanged(user, vo);
        return AjaxResult.success("资料修改成功", isVoiceChanged);
    }
    
    /**
     * 设置VO对象的默认值
     *
     * @param vo 用户更新VO
     */
    private void setDefaultValues(AppUpdUserInfoVo vo) {
        // 初始化默认值
        if (null == vo.getNoCheckHeadPortrait() || null == WhetherTypeEnum.getEnum(vo.getNoCheckHeadPortrait())) {
            vo.setNoCheckHeadPortrait(WhetherTypeEnum.NO.getName());
        }
        
        if (null == vo.getPurchaseSituation()) {
            vo.setPurchaseSituation(-1);
        }
        if (null == vo.getCarPurchaseSituation()) {
            vo.setCarPurchaseSituation(-1);
        }
        
        // 设置空字段的默认值
        if (StringUtils.isBlank(vo.getPersonalSignature())) {
            vo.setPersonalSignature("");
        }
        if (StringUtils.isBlank(vo.getConstellation())) {
            vo.setConstellation("");
        }
        if (StringUtils.isBlank(vo.getHeight())) {
            vo.setHeight("");
        }
        if (StringUtils.isBlank(vo.getWeight())) {
            vo.setWeight("");
        }
        if (StringUtils.isBlank(vo.getLocation())) {
            vo.setLocation("");
        }
        if (StringUtils.isBlank(vo.getAnnualIncome())) {
            vo.setAnnualIncome("");
        }
        if (StringUtils.isBlank(vo.getSelfIntroduction())) {
            vo.setSelfIntroduction("");
        }
        if (StringUtils.isBlank(vo.getLabel())) {
            vo.setLabel("");
        }
        if (StringUtils.isBlank(vo.getVoiceSignature())) {
            vo.setVoiceSignature("");
        }
        
        // 处理相册为空的情况
        if (null == vo.getPhotoAlbum() || vo.getPhotoAlbum().length == 0) {
            vo.setPhotoAlbum(new String[]{});
        }
    }
    
    /**
     * 验证用户提交的信息
     *
     * @param vo 用户更新VO
     * @param user 当前用户
     * @return 验证结果
     */
    private AjaxResult validateUserInfo(AppUpdUserInfoVo vo, AppUserEntity user) {
        // 验证性别设置
        if (user.getIsPerfectInfo().intValue() == WhetherTypeEnum.NO.getName()) {
            if (null == vo.getSex()) {
                return AjaxResult.error("请选择性别");
            }
            if (null == AppUserSexTypeEnums.getEnum(vo.getSex())) {
                return AjaxResult.error("参数【sex】错误");
            }
        } else {
            // 完善了资料就不可以修改性别
            vo.setSex(null);
        }
        
        // 验证昵称
        if (StringUtils.isBlank(vo.getNickName())) {
            return AjaxResult.error("请填写昵称");
        }
        if (vo.getNickName().length() > 8) {
            return AjaxResult.error("昵称最长8位");
        }
        
        // 检查昵称唯一性
        boolean isExistNiceName = appUserMapper.isExistByNiceName(vo.getNickName(), user.getId());
        if (isExistNiceName) {
            return AjaxResult.error("昵称已存在");
        }
        
        // 验证相册数量
        if (null != vo.getPhotoAlbum() && vo.getPhotoAlbum().length > 8) {
            return AjaxResult.error("相册最多上传8张照片");
        }
        
        // 验证购房购车情况
        // if (null != vo.getPurchaseSituation() && null == WhetherTypeEnum.getEnum(vo.getPurchaseSituation())) {
        //     return AjaxResult.error("参数【purchaseSituation】错误");
        // }
        // if (null != vo.getCarPurchaseSituation() && null == WhetherTypeEnum.getEnum(vo.getCarPurchaseSituation())) {
        //     return AjaxResult.error("参数【carPurchaseSituation】错误");
        // }
        
        // 校验生日和年龄
        if (StringUtils.isNotBlank(vo.getBirthday())) {
            try {
                if (new Date().before(sdf.parse(vo.getBirthday()))) {
                    return AjaxResult.error("出生日期不能晚于当前时间");
                }
            } catch (ParseException e) {
                return AjaxResult.error("出生日期格式错误");
            }
        }
        
        return AjaxResult.success();
    }
    
    /**
     * 检查是否包含敏感词
     *
     * @param vo 用户更新VO
     * @return 检查结果
     */
    private AjaxResult checkSensitiveWords(AppUpdUserInfoVo vo) {
        // 检查昵称敏感词
        if (!StringUtils.isBlank(vo.getNickName()) && appCommonService.isContainsSensitiveLexicon(vo.getNickName())) {
            return AjaxResult.error("昵称中包含违禁词");
        }
        
        // 检查个性签名敏感词
        if (!StringUtils.isBlank(vo.getPersonalSignature()) && 
            appCommonService.isContainsSensitiveLexicon(vo.getPersonalSignature())) {
            return AjaxResult.error("个性签名中包含违禁词");
        }
        
        // 检查自我介绍敏感词
        if (!StringUtils.isBlank(vo.getSelfIntroduction())) {
            try {
                Map<String, Object> selfIntroductionMap = JSON.parseObject(vo.getSelfIntroduction(), Map.class);
                if (null != selfIntroductionMap) {
                    String selfIntroduction = MapUtil.getStr(selfIntroductionMap, "selfIntroduction");
                    if (!StringUtils.isBlank(selfIntroduction) && 
                        appCommonService.isContainsSensitiveLexicon(selfIntroduction)) {
                        return AjaxResult.error("自我介绍中包含违禁词");
                    }
                }
            } catch (Exception e) {
                // 解析异常，忽略
            }
        }
        
        return AjaxResult.success();
    }
    
    /**
     * 处理昵称变更
     *
     * @param user 用户实体
     * @param vo 用户更新VO
     */
    private void handleNicknameChange(AppUserEntity user, AppUpdUserInfoVo vo) {
        if (user != null && !StringUtils.isBlank(user.getNickName()) && 
            !user.getNickName().equals(vo.getNickName())) {
            // 判断该用户有没有完成修改昵称任务
            AppTaskTypeEnums taskTypeEnums = AppTaskTypeEnums.TYPE5;
            taskToDo(user.getId(), taskTypeEnums, taskTypeEnums.getPoints());
        }
    }
    
    /**
     * 检查资料变更并发送系统动态
     *
     * @param user 当前用户
     * @param vo 用户更新VO
     * @return 是否有变更
     */
    private void checkAndSendSystemPost(AppUserEntity user, AppUpdUserInfoVo vo) {
        // 判断是否修改了头像、相册和个性签名
        boolean isHeadPortraitChanged = isHeadPortraitChanged(user, vo);
        boolean isPhotoAlbumChanged = isPhotoAlbumChanged(user, vo);
        boolean isPersonalSignatureChanged = isPersonalSignatureChanged(user, vo);
        boolean isNickNameChanged = isNickNameChanged(user, vo);
        
        // 检查是否有修改并需要发送动态
        if ((isHeadPortraitChanged || isPhotoAlbumChanged || isPersonalSignatureChanged || isNickNameChanged) && vo.getSend() != null) {
            AjaxResult ajaxResult = sendSystemPost(user, vo, isHeadPortraitChanged, isPhotoAlbumChanged, isPersonalSignatureChanged);
            sendContentCheck(user, vo, isHeadPortraitChanged, isPhotoAlbumChanged, isPersonalSignatureChanged, isNickNameChanged, ajaxResult);
        }
    }

    /**
     * 判断头像是否变更
     */
    private boolean isHeadPortraitChanged(AppUserEntity user, AppUpdUserInfoVo vo) {
        return user.getHeadPortrait() != null && !user.getHeadPortrait().equals(vo.getHeadPortrait());
    }
    
    /**
     * 判断相册是否变更
     */
    private boolean isPhotoAlbumChanged(AppUserEntity user, AppUpdUserInfoVo vo) {
        // 检查vo的相册中是否有照片
        if (vo.getPhotoAlbum().length > 0 && user.getPhotoAlbum() != null) {
            try {
                String[] userPhotos = user.getPhotoAlbum().split(",");
                
                // 比较相册长度
                if (vo.getPhotoAlbum().length != userPhotos.length) {
                    return true;
                }
                
                // 检查是否有新增的照片
                for (String photoAlbum : vo.getPhotoAlbum()) {
                    if (!user.getPhotoAlbum().contains(photoAlbum)) {
                        return true;
                    }
                }
            } catch (Exception e) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断昵称是否变更
     * <AUTHOR>
     * @date 2025/4/29 下午8:15
     */
    private boolean isNickNameChanged(AppUserEntity user, AppUpdUserInfoVo vo) {
        return user.getNickName()!= null && !user.getNickName().equals(vo.getNickName());
    }
    
    /**
     * 判断个性签名是否变更
     */
    private boolean isPersonalSignatureChanged(AppUserEntity user, AppUpdUserInfoVo vo) {
        return user.getPersonalSignature() != null && !user.getPersonalSignature().equals(vo.getPersonalSignature());
    }
    
    /**
     * 判断语音签名是否变更
     */
    private boolean isVoiceSignatureChanged(AppUserEntity user, AppUpdUserInfoVo vo) {
        // 如果用户从未设置过语音签名，且vo中有新语音签名
        if (StringUtils.isBlank(user.getVoiceSignature()) && !StringUtils.isBlank(vo.getVoiceSignature())) {
            return true;
        }

        // 如果用户已有语音签名，且vo中的语音签名不同
        if (!StringUtils.isBlank(user.getVoiceSignature()) && !StringUtils.isBlank(vo.getVoiceSignature())) {
            return !user.getVoiceSignature().equals(vo.getVoiceSignature());
        }

        // 其他情况视为未变更
        return false;
    }

    /**
     * 准备发送腾讯云检测
     * <AUTHOR>
     * @date 2025/4/29 下午8:17
     */
    private void sendContentCheck(AppUserEntity user, AppUpdUserInfoVo vo, boolean isHeadPortraitChanged,
                                  boolean isPhotoAlbumChanged, boolean isPersonalSignatureChanged, boolean isNickNameChanged,
                                  AjaxResult ajaxResult) {
        // 获取帖子id
        Long bizId = null;
        if (ajaxResult != null && ajaxResult.get(CODE_TAG).equals(HttpStatus.SUCCESS)) {
            Map<String, Long> map = (Map<String, Long>) ajaxResult.get(DATA_TAG);
            bizId = map.get("id");
        }

        // 检测头像
        if (isHeadPortraitChanged) {
            appContentCheckService.checkContentForPersonalData(bizId, user.getId(),
                    CheckCategoryConstants.HEAD_PORTRAIT, ContentCheckType.IMAGE, vo.getHeadPortrait());
        }

        // 检测相册 只检测新增的
        if (isPhotoAlbumChanged) {
            for (String photo : vo.getPhotoAlbum()) {
                if (!user.getPhotoAlbum().contains(photo)) {
                    appContentCheckService.checkContentForPersonalData(bizId, user.getId(),
                            CheckCategoryConstants.PHOTO_ALBUM, ContentCheckType.IMAGE, photo);
                }
            }
        }

        // 检测昵称
        if (isNickNameChanged) {
            appContentCheckService.checkContentForPersonalData(null, user.getId(),
                    CheckCategoryConstants.NICKNAME, ContentCheckType.TEXT, vo.getNickName());
        }

        // 检测个性签名
        if (isPersonalSignatureChanged) {
            appContentCheckService.checkContentForPersonalData(null, user.getId(),
                    CheckCategoryConstants.PERSONAL_SIGNATURE, ContentCheckType.TEXT, vo.getPersonalSignature());
        }
    }
    
    /**
     * 发送系统动态
     */
    private AjaxResult sendSystemPost(AppUserEntity user, AppUpdUserInfoVo vo,
                               boolean isHeadPortraitChanged, boolean isPhotoAlbumChanged, 
                               boolean isPersonalSignatureChanged) {
        if (vo.getSend() == 0){
            return null;
        }

        AppReleasePostVo appReleasePostVo = new AppReleasePostVo();
        
        // 构建发布内容
        StringBuilder contentBuilder = new StringBuilder();
        
        if (isHeadPortraitChanged) {
            contentBuilder.append("我发布了新的头像");
            appReleasePostVo.setPictureUrl(new String[]{vo.getHeadPortrait()});
            if (isPhotoAlbumChanged || isPersonalSignatureChanged) {
                contentBuilder.append("；");
            }
        }
        
        if (isPhotoAlbumChanged) {
            contentBuilder.append("我发布了一组照片");
            
            // 找出新增的照片
            String[] differentPhotos = findDifferentPhotos(user.getPhotoAlbum(), vo.getPhotoAlbum());
            
            // 如果头像也变化了，将新照片与头像合并
            if (isHeadPortraitChanged && appReleasePostVo.getPictureUrl() != null) {
                String[] existingPictures = appReleasePostVo.getPictureUrl();
                String[] combinedPictures = new String[existingPictures.length + differentPhotos.length];
                
                // 复制现有图片（包括头像）
                System.arraycopy(existingPictures, 0, combinedPictures, 0, existingPictures.length);
                // 添加新的照片
                System.arraycopy(differentPhotos, 0, combinedPictures, existingPictures.length, differentPhotos.length);
                
                appReleasePostVo.setPictureUrl(combinedPictures);
            } else {
                // 没有头像变化，直接设置照片
                appReleasePostVo.setPictureUrl(differentPhotos);
            }
            
            if (isPersonalSignatureChanged) {
                contentBuilder.append("；");
            }
        }
        
        if (isPersonalSignatureChanged) {
            contentBuilder.append("我发布了新的个性签名:" + vo.getPersonalSignature());
            appReleasePostVo.setPersonalSignature(vo.getPersonalSignature());
        }
        
        // 设置发布内容和属性
        appReleasePostVo.setContent(contentBuilder.toString());
        if (isHeadPortraitChanged) {
            appReleasePostVo.setHeadPortrait(vo.getHeadPortrait());
        }
        
        // 发布系统动态
        appReleasePostVo.setSystemSend(true);
        return appCommunityService.releasePost(appReleasePostVo, user.getId());
    }
    
    /**
     * 查找新增的照片
     */
    private String[] findDifferentPhotos(String oldPhotoString, String[] newPhotoArray) {
        // 将旧相册字符串解析为数组
        String[] oldPhotoArray = new String[0];
        if (oldPhotoString != null && !oldPhotoString.isEmpty()) {
            try {
                oldPhotoArray = JSON.parseObject(oldPhotoString, String[].class);
            } catch (Exception e) {
                // 解析异常，使用空数组
            }
        }
        
        // 准备一个集合存储不同的元素（新相册中有，但旧相册中没有的）
        List<String> differentPhotos = new ArrayList<>();
        
        // 如果新相册不为空，遍历新相册查找差异
        if (newPhotoArray != null && newPhotoArray.length > 0) {
            for (String newPhoto : newPhotoArray) {
                boolean existsInOld = false;
                // 检查新照片是否存在于旧相册中
                for (String oldPhoto : oldPhotoArray) {
                    if (oldPhoto.equals(newPhoto)) {
                        existsInOld = true;
                        break;
                    }
                }
                // 如果是新增的照片，加入差异列表
                if (!existsInOld) {
                    differentPhotos.add(newPhoto);
                }
            }
        }
        
        return differentPhotos.toArray(new String[0]);
    }
    
    /**
     * 处理头像为空的情况
     */
    private void handleEmptyAvatar(AppUpdUserInfoVo vo, AppUserEntity user) {
        if (ObjectUtil.isEmpty(vo.getHeadPortrait())) {
            // 默认注册头像
            List<SysDictData> registrationDefaultAvatar = redisCache.getCacheObject("sys_dict:registration_default_avatar");
            if (ObjectUtil.isNotEmpty(registrationDefaultAvatar)) {
                registrationDefaultAvatar.stream()
                    .filter(item -> item.getDictValue().equals(
                        ObjectUtil.isNotEmpty(vo.getSex()) ? vo.getSex().toString() : "1"))
                    .findFirst()
                    .ifPresent(item -> {
                        if (item.getImg() != null) {
                            vo.setHeadPortrait(item.getImg());
                        }
                    });
            }
        }
    }
    
    /**
     * 更新用户实体
     */
    private void updateUserEntity(AppUpdUserInfoVo vo, AppUserEntity user) {
        // 计算年龄
        Long age = -1L;
        try {
            if (StringUtils.isNotBlank(vo.getBirthday())) {
                age = Long.valueOf(DateUtils.getAgeByBirthTime(vo.getBirthday()));
            }
        } catch (Exception e) {
            // 年龄计算异常，使用默认值
        }
        
        // 处理渠道码
        String channelCode = user.getUpChannelCode();
        if (StringUtils.isNotBlank(vo.getRecodeCode()) && user.getUpChannelCode().equals("--")) {
            // 判断渠道码是否以小写字母开头
            char firstChar = vo.getRecodeCode().charAt(0);
            if (Character.isLowerCase(firstChar)) {
                channelCode = vo.getRecodeCode();
                appLoginService.handleChannelBinding(user.getId(), channelCode);
            }
        }
        
        // 创建更新实体
        AppUserEntity userUpd = new AppUserEntity();
        userUpd.setId(user.getId());
        userUpd.setHeadPortrait(vo.getHeadPortrait());
        userUpd.setNickName(vo.getNickName());
        userUpd.setPhotoAlbum(null != vo.getPhotoAlbum() ? JSON.toJSONString(vo.getPhotoAlbum()) : "[]");
        userUpd.setBirthday(vo.getBirthday());
        userUpd.setAge(age);
        userUpd.setSex(vo.getSex());
        
        // 如果用户性别为女性，自动开启提现权限
        if (vo.getSex() != null && vo.getSex() == AppUserSexTypeEnums.TYPE1.getId()) {
            userUpd.setIsTx(WhetherTypeEnum.YES.getName());
        }
        
        userUpd.setPersonalSignature(vo.getPersonalSignature());
        userUpd.setEducationBackground(vo.getEducationBackground());
        userUpd.setUpChannelCode(channelCode);
        userUpd.setConstellation(vo.getConstellation());
        userUpd.setHeight(vo.getHeight());
        userUpd.setWeight(vo.getWeight());
        userUpd.setLocation(vo.getLocation());
        userUpd.setAnnualIncome(vo.getAnnualIncome());
        userUpd.setPurchaseSituation(vo.getPurchaseSituation());
        userUpd.setCarPurchaseSituation(vo.getCarPurchaseSituation());
        userUpd.setSelfIntroduction(vo.getSelfIntroduction());
        userUpd.setLabel(vo.getLabel());
        userUpd.setVideo(vo.getVideo());
        
        // 处理语音签名变更
        if (isVoiceSignatureChanged(user, vo)) {
            userUpd.setVoiceSignature(vo.getVoiceSignature());
            userUpd.setVoiceCheckStatus(CheckStatusConstants.CHECKING);
        }
        
        // 判断是否需要更新资料完善状态
        if (user.getIsPerfectInfo() == WhetherTypeEnum.NO.getName()) {
            userUpd.setPerfectInfoTime(new Date());
            userUpd.setIsPerfectInfo(WhetherTypeEnum.YES.getName());
        }
        
        // 执行更新
        appUserMapper.updateAppUser(userUpd);
    }
    
    /**
     * 处理用户任务完成情况
     */
    private void processUserTasks(AppUpdUserInfoVo vo, AppUserEntity user) {
        // 判断语音签名任务
        if (!StringUtils.isBlank(vo.getVoiceSignature())) {
            taskToDo(user.getId(), AppTaskTypeEnums.TYPE3, AppTaskTypeEnums.TYPE3.getPoints());
        }
        
        // 判断头像任务
        if (!StringUtils.isBlank(vo.getHeadPortrait())) {
            taskToDo(user.getId(), AppTaskTypeEnums.TYPE1, AppTaskTypeEnums.TYPE1.getPoints());
        }
        
        // 判断相册任务
        if (ObjectUtil.isNotEmpty(vo.getPhotoAlbum()) && vo.getPhotoAlbum().length >= 3) {
            taskToDo(user.getId(), AppTaskTypeEnums.TYPE2, AppTaskTypeEnums.TYPE2.getPoints());
        }
        
        // 判断个性签名任务
        if (ObjectUtil.isNotEmpty(vo.getPersonalSignature())) {
            taskToDo(user.getId(), AppTaskTypeEnums.TYPE4, AppTaskTypeEnums.TYPE4.getPoints());
        }
    }

    /**
     * 记录每日任务或新手任务
     *
     * @param userId        用户uid
     * @param taskTypeEnums 任务类型枚举
     */
    private void taskToDo(Long userId, AppTaskTypeEnums taskTypeEnums, BigDecimal goldNum) {
        int isComplete = appCompleteTaskRecordMapper.isCompleteByTaskTypeAndUserId((long) taskTypeEnums.getType(),
                userId, taskTypeEnums.isBeginnerTask());
        if (isComplete == WhetherTypeEnum.NO.getName()) {
            // 1 未完成的话，那就添加一条完成任务记录
            AppCompleteTaskRecord completeTaskRecord = new AppCompleteTaskRecord();
            completeTaskRecord.setUserId(userId);
            completeTaskRecord.setTaskType((long) taskTypeEnums.getType());
            completeTaskRecord.setPoints(goldNum);
            completeTaskRecord.setCreateTime(new Date());
            appCompleteTaskRecordMapper.insertAppCompleteTaskRecord(completeTaskRecord);

            // 2 给用户加金币
            appUserMapper.addUserGoldBalance(userId, goldNum);

            // 3 记录当前用户账单
            AppUserGoldBill userGoldBill = new AppUserGoldBill();
            userGoldBill.setUserId(userId);

            AppGoldBillTypeEnums type = taskTypeEnums.isBeginnerTask() ? AppGoldBillTypeEnums.TYPE34
                    : AppGoldBillTypeEnums.TYPE35;
            // 账单类型
            userGoldBill.setBillType((long) type.getId());
            userGoldBill.setObjectId(completeTaskRecord.getId());// 产生账单的相关id为签到的id
            userGoldBill.setAmount(goldNum);// 金额为每日签到获得的金币数量
            userGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
            appUserGoldBillMapper.insertAppUserGoldBill(userGoldBill);

            // 4 生成系统通知
            AppSysMsg appSysMsg = new AppSysMsg();
            appSysMsg.setUserId(userId)
                    .setMsgType((long) AppSysMsgTypeEnums.TYPE1.getId())
                    .setIsRead(0)
                    .setTitle(type.getName())
                    .setContent("恭喜您完成了【" + taskTypeEnums.getName() + "】任务，获得了【" + goldNum.intValue() + "】金币奖励")
                    .setCreateTime(new Date());
            appSysMsgService.save(appSysMsg);
        }

    }

    /**
     * 实名认证回调
     *
     * @param certifyId
     * @return
     */
    public AjaxResult realNameAuthCallback(AppUserEntity user, Long userId, String certifyId) {

        RLock lock = redisson.getLock("app:real:name:auth:callback:" + userId);
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(-1, TimeUnit.SECONDS);
        if (StringUtils.isBlank(certifyId)) {
            lock.unlock();
            return AjaxResult.error("参数【certifyId】为空");
        }

        if (user.getIsRealNameAuth().intValue() == WhetherTypeEnum.YES.getName()) {
            lock.unlock();
            return AjaxResult.error("操作失败,您已完成实名认证");
        }
        Map<String, Object> realNameAuthInfo = redisCache.getCacheObject("realNameAuth-" + certifyId);
        if (null == realNameAuthInfo) {
            lock.unlock();
            return AjaxResult.error("实名认证已过期,请重新发起实名认证");
        }

        String certNo = MapUtil.getStr(realNameAuthInfo, "certNo");
        // 校验身份证是否合法
        if (!IdcardUtil.isValidCard(certNo)) {
            lock.unlock();
            return AjaxResult.error("身份证号不合法");
        }

        Date time = new Date();

        // 判断是否已成年
        int age = IdcardUtil.getAgeByIdCard(certNo, time);
        if (age < 18) {
            lock.unlock();
            return AjaxResult.error("未成年暂无法认证");
        }
        // int idCardSex = IdcardUtil.getGenderByIdCard(certNo);
        // if (idCardSex == 0) {// 如果身份证为女，账号性别也必须为女
        // if (user.getSex().intValue() != AppUserSexTypeEnums.TYPE1.getId()) {
        // lock.unlock();
        // return AjaxResult.error("身份证与账号性别不符");
        // }
        // } else {// 如果身份证为男，账号性别也必须为男
        // if (user.getSex().intValue() != AppUserSexTypeEnums.TYPE0.getId()) {
        // lock.unlock();
        // return AjaxResult.error("身份证与账号性别不符");
        // }
        // }
        if (appUserMapper.getIdNumberAuthCount(certNo) >= 3) {// 一个身份证最多认证两个账号
            return AjaxResult.error("同一身份证最多认证3个账号");
        }

        try {
            DescribeFaceVerifyResponseBody.DescribeFaceVerifyResponseBodyResultObject resultObject = authCardUtil
                    .getAuthResult(certifyId);
            if (!resultObject.getPassed().equals("T") || !resultObject.getSubCode().equals("200")) {
                lock.unlock();
                return AjaxResult.error("实名认证失败,人脸认证未通过");
            }

            AppUserEntity userUpd = new AppUserEntity();
            userUpd.setId(user.getId());
            userUpd.setIsRealPersonAuth(WhetherTypeEnum.YES.getName());
            userUpd.setIsRealNameAuth(WhetherTypeEnum.YES.getName());
            userUpd.setRealName(MapUtil.getStr(realNameAuthInfo, "certName"));
            userUpd.setIdNumber(certNo);
            // 实名认证后自动开通提现权限
            userUpd.setIsTx(WhetherTypeEnum.YES.getName());
            appUserMapper.updateAppUser(userUpd);
            try {
                redisCache.deleteObject("realNameAuth-" + certifyId);
            } catch (Exception e) {
            }
            lock.unlock();
            return AjaxResult.success("实名认证成功");
        } catch (Exception e) {
            lock.unlock();
            return AjaxResult.error("实名认证失败," + e.getMessage());
        }

    }

    /**
     * 获取certifyId
     *
     * @param certName
     * @param certNo
     * @param metaInfo
     * @return
     */
    public AjaxResult getCertifyId(AppUserEntity user, String certName, String certNo, String metaInfo) {
        // 参数校验
        if (StringUtils.isBlank(certName)) {
            return AjaxResult.error("请填写姓名");
        }
        if (StringUtils.isBlank(certNo)) {
            return AjaxResult.error("请填写身份证号");
        }
        // 校验身份证是否合法
        if (!IdcardUtil.isValidCard(certNo)) {
            return AjaxResult.error("身份证号不合法");
        }
        if (StringUtils.isBlank(metaInfo)) {
            return AjaxResult.error("参数【metaInfo】为空");
        }

        if (user.getIsRealNameAuth().intValue() == WhetherTypeEnum.YES.getName()) {
            return AjaxResult.error("操作失败,您已完成实名认证");
        }

        Date time = new Date();

        // 判断是否已成年
        int age = IdcardUtil.getAgeByIdCard(certNo, time);
        if (age < 18) {
            return AjaxResult.error("未成年暂无法认证");
        }
        // int idCardSex = IdcardUtil.getGenderByIdCard(certNo);
        // if (idCardSex == 0) {// 如果身份证为女，账号性别也必须为女
        // if (user.getSex().intValue() != AppUserSexTypeEnums.TYPE1.getId()) {
        // return AjaxResult.error("身份证与账号性别不符");
        // }
        // } else {// 如果身份证为男，账号性别也必须为男
        // if (user.getSex().intValue() != AppUserSexTypeEnums.TYPE0.getId()) {
        // return AjaxResult.error("身份证与账号性别不符");
        // }
        // }
        if (appUserMapper.getIdNumberAuthCount(certNo) >= 3) {// 一个身份证最多认证两个账号
            return AjaxResult.error("同一身份证最多认证3个账号");
        }

        try {
            InitFaceVerifyResponseBody responseBody = authCardUtil.getCertifyId(user.getId()
                    .toString(), certName, certNo, metaInfo, StringUtils.getRandomStringByLength(32));
            // 认证信息存入缓存,30分钟有效
            Map<String, Object> result = new HashMap<>();
            result.put("certName", certName);
            result.put("certNo", certNo);
            result.put("isSuccess", false);
            redisCache.setCacheObject("realNameAuth-" + responseBody.getResultObject()
                    .getCertifyId(), result, 30, TimeUnit.MINUTES);
            return AjaxResult.success("获取成功", responseBody.getResultObject());
        } catch (Exception e) {
            return AjaxResult.error("初始化失败," + e.getMessage());
        }
    }

    /**
     * 注销账号
     *
     * @param code
     * @return
     */
    public AjaxResult cancelAccount(AppUserEntity user, String token, String code) {
        if (StringUtils.isBlank(code)) {
            return AjaxResult.error("验证码不能为空");
        }
        Long userId = user.getId();
        RLock lock = redisson.getLock("app:cancelAccount:" + userId);
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(60, TimeUnit.SECONDS);

        String phone = user.getPhone();

        String cacheCode = redisCache.getCacheObject(SmsSendTypeEnum.TYPE6.getRegionName() + phone);
        if (StringUtils.isBlank(cacheCode)) {
            lock.unlock();
            return AjaxResult.error("验证码无效");
        }
        if (!StringUtils.equals(code, cacheCode)) {
            lock.unlock();
            return AjaxResult.error("验证码错误");
        }

        tokenService.removeToken(token);
        AppUserEntity userUpd = new AppUserEntity();
        userUpd.setId(userId);
        userUpd.setToken("");
        userUpd.setUserStatus(AppUserStatusTypeEnums.TYPE3.getCode());// 用户状态改为已注销
        appUserMapper.updateAppUser(userUpd);
        try {
            redisCache.deleteObject(SmsSendTypeEnum.TYPE6.getRegionName() + phone);
        } catch (Exception e) {
        }

        // 移除锁
        lock.unlock();
        return AjaxResult.success("账号注销成功");
    }

    /**
     * 获取用户金币账单列表
     *
     * @param page
     * @param size
     * @return
     */
    public TableDataInfo getUserGoldBillList(int page, int size, Long userId) {
        Page p = PageHelperUtils.startPage(page, size, true);
        List<AppUserGoldBillVo> list = appUserGoldBillMapper.getUserGoldBillList(userId, null, null, null, null);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        } else {
            list.forEach(item -> {
                item.setUserId(null);
                AppGoldBillTypeEnums goldBillTypeEnums = AppGoldBillTypeEnums.getEnum(item.getBillType().intValue());
                if (goldBillTypeEnums.getId() == AppGoldBillTypeEnums.TYPE31.getId()
                        || goldBillTypeEnums.getId() == AppGoldBillTypeEnums.TYPE30.getId()
                        || goldBillTypeEnums.getId() == AppGoldBillTypeEnums.TYPE2.getId()
                        || goldBillTypeEnums.getId() == AppGoldBillTypeEnums.TYPE9.getId()
                        || goldBillTypeEnums.getId() == AppGoldBillTypeEnums.TYPE10.getId()
                        || goldBillTypeEnums.getId() == AppGoldBillTypeEnums.TYPE17.getId()
                        || goldBillTypeEnums.getId() == AppGoldBillTypeEnums.TYPE18.getId()) {// 送礼给用户、给用户打招呼、给用户发消息、红包
                    // 拼接用户昵称
                    String nickName = appUserMapper.getNickNameByUserId(item.getObjectId());
                    if (StringUtils.isBlank(nickName)) {
                        nickName = "未知用户";
                    }
                    // 拼接账单类型名称
                    item.setBillTypeName(StringUtils.format(goldBillTypeEnums.getDesc(), nickName));
                } else if (goldBillTypeEnums.getId() == AppGoldBillTypeEnums.TYPE3.getId()
                        || goldBillTypeEnums.getId() == AppGoldBillTypeEnums.TYPE4.getId()) {// 语音和视频通话相关,拼接用户昵称
                    String nickName = appCommunicateTelephoneRecordsMapper
                            .getCommunicateTelephoneUserNickNameById(item.getObjectId());
                    if (StringUtils.isBlank(nickName)) {
                        nickName = "未知用户";
                    }
                    // 拼接账单类型名称
                    item.setBillTypeName(StringUtils.format(goldBillTypeEnums.getDesc(), nickName));
                } else if (goldBillTypeEnums.getId() == AppGoldBillTypeEnums.TYPE20.getId()) {// 钻石兑换金币,拼接钻石
                    // 拼接账单类型名称
                    item.setBillTypeName(StringUtils.format(goldBillTypeEnums.getDesc(), item.getRemarksMsg()));
                } else if (goldBillTypeEnums.getId() == AppGoldBillTypeEnums.TYPE24.getId()) {
                    GameConfigEntity gameConfig = gameConfigService.getGameConfigById(item.getObjectId());
                    String billName = "";
                    if (gameConfig != null && StringUtils.isNotEmpty(gameConfig.getName())) {
                        billName = gameConfig.getName() + item.getRemarksMsg();
                    }
                    item.setBillTypeName(billName);
                } else if (goldBillTypeEnums.getId() == AppGoldBillTypeEnums.TYPE34.getId()
                        || goldBillTypeEnums.getId() == AppGoldBillTypeEnums.TYPE35.getId()) {
                    // 查询完成任务表中的记录类型
                    final AppCompleteTaskRecord appCompleteTaskRecord = appCompleteTaskRecordMapper
                            .selectAppCompleteTaskRecordById(item.getObjectId());
                    if (appCompleteTaskRecord != null) {
                        String name = goldBillTypeEnums.getName() + "-"
                                + AppTaskTypeEnums.getEnum(appCompleteTaskRecord.getTaskType()
                                .intValue()).getName();
                        item.setBillTypeName(name);
                    } else {
                        item.setBillTypeName(goldBillTypeEnums.getName());
                    }
                } else {
                    // 获取账单类型名称
                    item.setBillTypeName(goldBillTypeEnums.getDesc());
                }
            });
        }

        return AjaxResult.getDataTable(list, p.getTotal());
    }

    /**
     * 获取用户流水列表
     * <AUTHOR>
     * @date 2025/5/8 下午8:18
     */
    public TableDataInfo getFlowingWater(Integer page, Integer size, String startDate, String endDate, Long userId) {
        BigDecimal totalFlowingWater = appUserPointsBillMapper.getTotalFlowingWater(userId, startDate, endDate);
        Page p = PageHelperUtils.startPage(page, size, true);
        List<AppUserPointsBillVo> flowingWater = appUserPointsBillMapper.getFlowingWaterByUserId(userId, startDate, endDate);
        return AjaxResult.getDataTable(flowingWater, p.getTotal(), totalFlowingWater);
    }

    /**
     * 获取用户钻石账单列表
     *
     * @param page
     * @param size
     * @return
     */
    public TableDataInfo getUserPointsBillList(int page, int size, Long userId) {
        Page p = PageHelperUtils.startPage(page, size, true);
        List<AppUserPointsBillVo> list = appUserPointsBillMapper.getPointsBillsByUserId(userId);
        log.info("获取用户钻石账单列表,list:" + list);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        } else {
            list.forEach(item -> {
                AppPointsBillTypeEnums billTypeEnums = AppPointsBillTypeEnums.getEnum(item.getBillType().intValue());
                // 收到礼物、收到用户打招呼、收到用户发消息、礼物收益、充值收益,拼接用户昵称
                if (billTypeEnums.getId() == AppPointsBillTypeEnums.TYPE13.getId()
                        || billTypeEnums.getId() == AppPointsBillTypeEnums.TYPE19.getId()
                        || billTypeEnums.getId() == AppPointsBillTypeEnums.TYPE12.getId()
                        || billTypeEnums.getId() == AppPointsBillTypeEnums.TYPE14.getId()) {
                    String nickName = appUserMapper.getNickNameByUserId(item.getObjectId());

                    if (StringUtils.isBlank(nickName)) {
                        nickName = "未知用户";
                    }
                    // 拼接账单类型名称
                    item.setBillTypeName(StringUtils.format(billTypeEnums.getDesc(), nickName));
                } else if (billTypeEnums.getId() == AppPointsBillTypeEnums.TYPE6.getId()
                        || billTypeEnums.getId() == AppPointsBillTypeEnums.TYPE7.getId()
                        || billTypeEnums.getId() == AppPointsBillTypeEnums.TYPE9.getId()) {// 语音和视频通话相关,拼接用户昵称
                    // 拼接账单类型名称
                    item.setBillTypeName(item.getRemarksMsg());
                } else if (billTypeEnums.getId() == AppPointsBillTypeEnums.TYPE4.getId()
                        || billTypeEnums.getId() == AppPointsBillTypeEnums.TYPE3.getId()) {// 支付宝提现,拼接实际到账金额
                    BigDecimal actualAmount = appWithdrawRecordsMapper.getActualAmount(item.getObjectId());
                    String actualAmountStr = "未知";
                    if (null != actualAmount) {
                        actualAmountStr = actualAmount.stripTrailingZeros().toPlainString();
                    }
                    // 拼接账单类型名称
                    item.setBillTypeName(StringUtils.format(billTypeEnums.getDesc(), actualAmountStr));
                } else if (billTypeEnums.getId() == AppPointsBillTypeEnums.TYPE15.getId()) {// 兑换金币,拼接金币数量
                    // 拼接账单类型名称
                    item.setBillTypeName(StringUtils.format(billTypeEnums.getDesc(), item.getRemarksMsg()));
                } else if (billTypeEnums.getId() == AppPointsBillTypeEnums.TYPE16.getId()
                        || billTypeEnums.getId() == AppPointsBillTypeEnums.TYPE17.getId()) {
                    // 拼接账单类型名称
                    if (StringUtils.isBlank(item.getRemarksMsg())) {
                        item.setBillTypeName(billTypeEnums.getDesc());
                    } else {
                        item.setBillTypeName(billTypeEnums.getDesc() + "," + item.getRemarksMsg());
                    }
                } else if (billTypeEnums.getId() == AppPointsBillTypeEnums.TYPE21.getId()
                        || billTypeEnums.getId() == AppPointsBillTypeEnums.TYPE22.getId()) {
                    // 拼接账单类型名称
                    item.setBillTypeName(item.getRemarksMsg());
                } else if (billTypeEnums.getId() == AppPointsBillTypeEnums.TYPE23.getId()
                        || billTypeEnums.getId() == AppPointsBillTypeEnums.TYPE25.getId()
                        || billTypeEnums.getId() == AppPointsBillTypeEnums.TYPE5.getId()
                        || billTypeEnums.getId() == AppPointsBillTypeEnums.TYPE8.getId()) {
                    item.setBillTypeName(item.getRemarksMsg());
                } else {
                    // 获取账单类型名称
                    item.setBillTypeName(billTypeEnums.getDesc());
                }

            });
        }

        return AjaxResult.getDataTable(list, p.getTotal());
    }

    /**
     * 搜索用户
     *
     * @param page
     * @param size
     * @param searchUserParameterVo
     * @return
     */
    public TableDataInfo searchUser(int page, int size, AppSearchUserParameterVo searchUserParameterVo,
                                    AppUserEntity user) {
        if (null != searchUserParameterVo.getQueryType()
                && null == AppSearchUserTypeEnums.getEnum(searchUserParameterVo.getQueryType())) {
            return AjaxResult.getDataTableError("参数【queryType】错误");
        }
        if (StringUtils.isBlank(searchUserParameterVo.getKeyword())) {
            searchUserParameterVo.setKeyword(null);
        } else {
            searchUserParameterVo.setQueryType(null);
        }
        if (null != searchUserParameterVo.getQueryType() && searchUserParameterVo.getQueryType()
                .intValue() == AppSearchUserTypeEnums.TYPE2.getId()) {
            if (StringUtils.isBlank(searchUserParameterVo.getLatitude())
                    || StringUtils.isBlank(searchUserParameterVo.getLongitude())) {
                return AjaxResult.getDataTableError("未开启定位");
            }
        }
        searchUserParameterVo.setAdminQuery(false);
        searchUserParameterVo.setBindRecodeUserId(null);
        searchUserParameterVo.setIsRealNameAuth(null);
        searchUserParameterVo.setOrderType(null);
        searchUserParameterVo.setIsQueryTopUp(null);
        searchUserParameterVo.setQueryBeginTime(null);
        searchUserParameterVo.setQueryEndTime(null);
        searchUserParameterVo.setUserStatus(null);
        searchUserParameterVo.setIsVirtual(null);
        searchUserParameterVo.setIsRealPersonOrderBy(true);

        if (StringUtils.isBlank(searchUserParameterVo.getKeyword()) && null == searchUserParameterVo.getSex()) {// 搜索关键字为空时并且没有传性别，默认根据当前用户性别取反查询
            Integer sex = null;
            if (null != user.getSex() && user.getSex().intValue() != -1) {
                sex = user.getSex();
            }
            if (null != sex) {
                if (sex.intValue() == AppUserSexTypeEnums.TYPE1.getId()) {
                    sex = AppUserSexTypeEnums.TYPE0.getId();
                } else {
                    sex = AppUserSexTypeEnums.TYPE1.getId();
                }
                searchUserParameterVo.setSex(sex);
            }
        }

        searchUserParameterVo.setUserId(user.getId());

        // 获取客服用户id，查询时不查客服
        List<Long> serviceUserIds = appMessageService.getServiceUserIdList();
        if (!CollectionUtils.isEmpty(serviceUserIds)) {
            searchUserParameterVo.setServiceUserId(serviceUserIds.get(0));
        }
        searchUserParameterVo.setIsQueryOnline(null);
        searchUserParameterVo.setUserIdList(null);

        Page p = PageHelperUtils.startPage(page, size, true);
        List<AppSearchUserInfoResultVo> list = appUserMapper.searchUser(searchUserParameterVo);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        } else {
            list.forEach(item -> {
                item.setPushId(null);
                item.setBindRecodeUserTime(null);
                item.setRealName(null);
                item.setIdNumber(null);
                item.setLastLoginIp(null);
                item.setUserStatus(null);
                item.setGoldBalance(null);
                item.setIsTx(null);
                item.setPointsBalance(null);
                item.setUserPhone(null);
                item.setIsVirtual(null);
                item.setCreateTime(null);
                if (null != item.getSex() && item.getSex().intValue() == -1) {
                    item.setSex(null);
                }
                if (StringUtils.isBlank(item.getBirthday())) {
                    item.setAge(null);
                }
                if (null != item.getAge() && item.getAge().intValue() == -1) {
                    item.setAge(null);
                }
                if (!StringUtils.isBlank(item.getPhotoAlbumStr())) {
                    item.setPhotoAlbum(JSONArray.parse(item.getPhotoAlbumStr()));
                    item.setPhotoAlbumStr(null);
                } else {
                    item.setPhotoAlbum(new JSONArray());
                }
                if (null != item.getPurchaseSituation() && item.getPurchaseSituation().intValue() == -1) {
                    item.setPurchaseSituation(null);
                }
                if (null != item.getCarPurchaseSituation() && item.getCarPurchaseSituation().intValue() == -1) {
                    item.setCarPurchaseSituation(null);
                }
                item.setIsOnline(appCommonService.getUserIsOnline(item.getUserId(), item.getSex(), item.getIsOnline()));
                item.setChatBtnType(WhetherTypeEnum.YES.getName());
            });
        }

        return AjaxResult.getDataTable(list, p.getTotal());
    }

    /**
     * 获取查看用户记录列表
     * @param queryType 查询类型:0谁看过我,1我看过谁
     * <AUTHOR>
     * @date 2025/5/14 下午2:56
     */
    public TableDataInfo getViewUserRecordsList(int page, int size, Integer queryType, Long userId) {
        if (null == queryType) {
            return AjaxResult.getDataTableError("参数【queryType】为空");
        }
        if (null == WhetherTypeEnum.getEnum(queryType)) {
            return AjaxResult.getDataTableError("参数【queryType】错误");
        }

        List<AppViewUserInfoVo> recordList;
        Page p = PageHelperUtils.startPage(page, size, true);
        if (queryType == 0) {
            recordList = appViewUserRecordsMapper.getBeViewUserRecordsList(userId);
        } else {
            recordList = appViewUserRecordsMapper.getViewUserRecordsList(userId);
        }

        if (CollectionUtils.isEmpty(recordList)) {
            return AjaxResult.getDataTable(new ArrayList<>(), p.getTotal());
        }

        recordList.forEach(item -> {
            if (null != item.getSex() && item.getSex() == -1) {
                item.setSex(null);
            }
            if (StringUtils.isBlank(item.getBirthday())) {
                item.setAge(null);
            }
            if (null != item.getAge() && item.getAge().intValue() == -1) {
                item.setAge(null);
            }

            item.setUseInColorNickName(appUserPersonalDressingMapper.getUserUseInPersonalDressingInfo(
                    AppPersonalDressingCategoryEnums.COLOR_NICK_NAME.getId(), item.getUserId()));
        });

        return AjaxResult.getDataTable(recordList, p.getTotal());
    }

    /**
     * 清空访客记录
     * @param queryType 查询类型:0谁看过我,1我看过谁
     * <AUTHOR>
     * @date 2025/4/24 下午3:56
     */
    public AjaxResult clearViewUserRecordsList(Integer queryType, Long userId) {
        // 清空访客的时候需要会员
        AppUserEntity user = appUserMapper.selectById(userId);
        if (user.getIsVip() == WhetherTypeEnum.NO.getName()) {
            return AjaxResult.error("您不是会员，无法清空访客记录");
        }

        if (queryType == 0) {
            appViewUserRecordsMapper.clearBeViewUserRecords(userId);
        } else {
            appViewUserRecordsMapper.clearViewUserRecords(userId);
        }
        return AjaxResult.success();
    }

    /**
     * 随机获取三位用户
     */
    public AjaxResult randomGetThreeUserList(Long userId) {
        List<AppViewUserInfoVo> list = appUserMapper.randomGetThreeUserList(userId);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        } else {
            list.forEach(item -> {
                if (null != item.getSex() && item.getSex().intValue() == -1) {
                    item.setSex(null);
                }
                if (StringUtils.isBlank(item.getBirthday())) {
                    item.setAge(null);
                }
                if (null != item.getAge() && item.getAge().intValue() == -1) {
                    item.setAge(null);
                }
                if (!StringUtils.isBlank(item.getPhotoAlbumStr())) {
                    String[] photoAlbum = JSON.parseObject(item.getPhotoAlbumStr(), String[].class);
                    item.setPhotoAlbum(photoAlbum);
                    item.setPhotoAlbumStr(null);
                } else {
                    item.setPhotoAlbum(new String[]{});
                }
                if (null != item.getPurchaseSituation() && item.getPurchaseSituation().intValue() == -1) {
                    item.setPurchaseSituation(null);
                }
                if (null != item.getCarPurchaseSituation() && item.getCarPurchaseSituation().intValue() == -1) {
                    item.setCarPurchaseSituation(null);
                }
                item.setIsOnline(appCommonService.getUserIsOnline(item.getUserId(), item.getSex(), item.getIsOnline()));
            });
        }
        return AjaxResult.success(list);
    }

    /**
     * 获取用户粉丝列表
     *
     * @param page
     * @param size
     * @return
     */
    public TableDataInfo getUserVermicelliList(int page, int size, Long userId) {

        Page p = PageHelperUtils.startPage(page, size, true);
        List<AppViewUserInfoVo> list = appUserFollowMapper.getUserVermicelliList(userId);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        } else {
            list.forEach(item -> {
                if (null != item.getSex() && item.getSex().intValue() == -1) {
                    item.setSex(null);
                }
                if (StringUtils.isBlank(item.getBirthday())) {
                    item.setAge(null);
                }
                if (null != item.getAge() && item.getAge().intValue() == -1) {
                    item.setAge(null);
                }
                if (!StringUtils.isBlank(item.getPhotoAlbumStr())) {
                    String[] photoAlbum = JSON.parseObject(item.getPhotoAlbumStr(), String[].class);
                    item.setPhotoAlbum(photoAlbum);
                    item.setPhotoAlbumStr(null);
                } else {
                    item.setPhotoAlbum(new String[]{});
                }
                if (null != item.getPurchaseSituation() && item.getPurchaseSituation().intValue() == -1) {
                    item.setPurchaseSituation(null);
                }
                if (null != item.getCarPurchaseSituation() && item.getCarPurchaseSituation().intValue() == -1) {
                    item.setCarPurchaseSituation(null);
                }
                item.setIsOnline(appCommonService.getUserIsOnline(item.getUserId(), item.getSex(), item.getIsOnline()));
                // item.setCurrentIcoUrl(appTitleNobilityService.getUserCurrentIcoUrl(item.getUserId(),
                // item.getSex()));
                item.setCurrentIcoUrl(appTitleNobilityService.getCurrentIcoUrl(item.getUserId()).getCurrentIcoUrl());
                item.setContributeCurrentIcoUrl(appTitleNobilityService.getCurrentIcoUrl(item.getUserId())
                        .getContributeCurrentIcoUrl());

                item.setUseInColorNickName(appUserPersonalDressingMapper.getUserUseInPersonalDressingInfo(
                        AppPersonalDressingCategoryEnums.COLOR_NICK_NAME.getId(), item.getUserId()));

                // 附加参数
                Map<String, Object> subjoinParameter = new HashMap<>();
                AppUserFollow followTarget = appUserFollowMapper.selectAppUserFollowByUserIdAndBeUserId(userId,
                        item.getUserId());
                subjoinParameter.put("isFollowTarget",
                        null == followTarget ? WhetherTypeEnum.NO.getName() : WhetherTypeEnum.YES.getName());// 当前用户是否关注了对方:0否,1是
                item.setSubjoinParameter(subjoinParameter);
            });
        }

        return AjaxResult.getDataTable(list, p.getTotal());
    }

    /**
     * 获取用户亲密列表
     *
     * @param page
     * @param size
     * @return
     */
    public TableDataInfo getUserIntimacyList(int page, int size, Long userId) {

        Page p = PageHelperUtils.startPage(page, size, true);
        List<AppViewUserInfoVo> list = appUserIntimacyMapper.getUserIntimacyList(userId);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        } else {
            list.forEach(item -> {
                if (null != item.getSex() && item.getSex().intValue() == -1) {
                    item.setSex(null);
                }
                if (StringUtils.isBlank(item.getBirthday())) {
                    item.setAge(null);
                }
                if (null != item.getAge() && item.getAge().intValue() == -1) {
                    item.setAge(null);
                }
                if (!StringUtils.isBlank(item.getPhotoAlbumStr())) {
                    String[] photoAlbum = JSON.parseObject(item.getPhotoAlbumStr(), String[].class);
                    item.setPhotoAlbum(photoAlbum);
                    item.setPhotoAlbumStr(null);
                } else {
                    item.setPhotoAlbum(new String[]{});
                }
                if (null != item.getPurchaseSituation() && item.getPurchaseSituation().intValue() == -1) {
                    item.setPurchaseSituation(null);
                }
                if (null != item.getCarPurchaseSituation() && item.getCarPurchaseSituation().intValue() == -1) {
                    item.setCarPurchaseSituation(null);
                }
                item.setIsOnline(appCommonService.getUserIsOnline(item.getUserId(), item.getSex(), item.getIsOnline()));

                item.setUseInColorNickName(appUserPersonalDressingMapper.getUserUseInPersonalDressingInfo(
                        AppPersonalDressingCategoryEnums.COLOR_NICK_NAME.getId(), item.getUserId()));
                // item.setCurrentIcoUrl(appTitleNobilityService.getUserCurrentIcoUrl(item.getUserId(),
                // item.getSex()));
                item.setCurrentIcoUrl(appTitleNobilityService.getCurrentIcoUrl(item.getUserId()).getCurrentIcoUrl());
                item.setContributeCurrentIcoUrl(appTitleNobilityService.getCurrentIcoUrl(item.getUserId())
                        .getContributeCurrentIcoUrl());

                // 附加参数
                Map<String, Object> subjoinParameter = new HashMap<>();
                AppUserFollow followTarget = appUserFollowMapper.selectAppUserFollowByUserIdAndBeUserId(userId,
                        item.getUserId());
                subjoinParameter.put("isFollowTarget",
                        null == followTarget ? WhetherTypeEnum.NO.getName() : WhetherTypeEnum.YES.getName());// 当前用户是否关注了对方:0否,1是

                AppUserFollow followMy = appUserFollowMapper.selectAppUserFollowByUserIdAndBeUserId(item.getUserId(),
                        userId);
                subjoinParameter.put("isFollowMy",
                        null == followMy ? WhetherTypeEnum.NO.getName() : WhetherTypeEnum.YES.getName());// 对方是否关注了当前用户:0否,1是

                item.setSubjoinParameter(subjoinParameter);
            });
        }

        return AjaxResult.getDataTable(list, p.getTotal());
    }

    /**
     * 获取用户关注列表
     *
     * @param page
     * @param size
     * @return
     */
    public TableDataInfo getUserFollowList(int page, int size, Long userId) {

        Page p = PageHelperUtils.startPage(page, size, true);
        List<AppViewUserInfoVo> list = appUserFollowMapper.getUserFollowList(userId);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        } else {
            list.forEach(item -> {
                if (null != item.getSex() && item.getSex().intValue() == -1) {
                    item.setSex(null);
                }
                if (StringUtils.isBlank(item.getBirthday())) {
                    item.setAge(null);
                }
                if (null != item.getAge() && item.getAge().intValue() == -1) {
                    item.setAge(null);
                }
                if (!StringUtils.isBlank(item.getPhotoAlbumStr())) {
                    String[] photoAlbum = JSON.parseObject(item.getPhotoAlbumStr(), String[].class);
                    item.setPhotoAlbum(photoAlbum);
                    item.setPhotoAlbumStr(null);
                } else {
                    item.setPhotoAlbum(new String[]{});
                }
                if (null != item.getPurchaseSituation() && item.getPurchaseSituation().intValue() == -1) {
                    item.setPurchaseSituation(null);
                }
                if (null != item.getCarPurchaseSituation() && item.getCarPurchaseSituation().intValue() == -1) {
                    item.setCarPurchaseSituation(null);
                }
                item.setIsOnline(appCommonService.getUserIsOnline(item.getUserId(), item.getSex(), item.getIsOnline()));
                item.setUseInColorNickName(appUserPersonalDressingMapper.getUserUseInPersonalDressingInfo(
                        AppPersonalDressingCategoryEnums.COLOR_NICK_NAME.getId(), item.getUserId()));
                // item.setCurrentIcoUrl(appTitleNobilityService.getUserCurrentIcoUrl(item.getUserId(),
                // item.getSex()));
                item.setCurrentIcoUrl(appTitleNobilityService.getCurrentIcoUrl(item.getUserId()).getCurrentIcoUrl());
                item.setContributeCurrentIcoUrl(appTitleNobilityService.getCurrentIcoUrl(item.getUserId())
                        .getContributeCurrentIcoUrl());

                // 附加参数
                Map<String, Object> subjoinParameter = new HashMap<>();
                AppUserFollow followMy = appUserFollowMapper.selectAppUserFollowByUserIdAndBeUserId(item.getUserId(),
                        userId);
                subjoinParameter.put("isFollowMy",
                        null == followMy ? WhetherTypeEnum.NO.getName() : WhetherTypeEnum.YES.getName());// 对方是否关注了当前用户:0否,1是
                item.setSubjoinParameter(subjoinParameter);
            });
        }

        return AjaxResult.getDataTable(list, p.getTotal());
    }

    /**
     * 处理用户好友列表
     *
     * @param list
     * @return
     */
    public List<AppViewUserInfoVo> handleUserFriendList(List<AppViewUserInfoVo> list) {
        list.forEach(item -> {
            if (null != item.getSex() && item.getSex().intValue() == -1) {
                item.setSex(null);
            }
            if (StringUtils.isBlank(item.getBirthday())) {
                item.setAge(null);
            }
            if (null != item.getAge() && item.getAge().intValue() == -1) {
                item.setAge(null);
            }
            if (!StringUtils.isBlank(item.getPhotoAlbumStr())) {
                String[] photoAlbum = JSON.parseObject(item.getPhotoAlbumStr(), String[].class);
                item.setPhotoAlbum(photoAlbum);
                item.setPhotoAlbumStr(null);
            } else {
                item.setPhotoAlbum(new String[]{});
            }
            if (null != item.getPurchaseSituation() && item.getPurchaseSituation().intValue() == -1) {
                item.setPurchaseSituation(null);
            }
            if (null != item.getCarPurchaseSituation() && item.getCarPurchaseSituation().intValue() == -1) {
                item.setCarPurchaseSituation(null);
            }
            item.setIsOnline(appCommonService.getUserIsOnline(item.getUserId(), item.getSex(), item.getIsOnline()));
            // item.setCurrentIcoUrl(appTitleNobilityService.getUserCurrentIcoUrl(item.getUserId(),
            // item.getSex()));
            item.setCurrentIcoUrl(appTitleNobilityService.getCurrentIcoUrl(item.getUserId()).getCurrentIcoUrl());
            item.setContributeCurrentIcoUrl(appTitleNobilityService.getCurrentIcoUrl(item.getUserId())
                    .getContributeCurrentIcoUrl());

            item.setUseInColorNickName(appUserPersonalDressingMapper.getUserUseInPersonalDressingInfo(
                    AppPersonalDressingCategoryEnums.COLOR_NICK_NAME.getId(), item.getUserId()));
        });

        return list;
    }

    /**
     * 获取用户好友列表
     *
     * @return
     */
    public AjaxResult getUserFriendList(Long userId) {
        List<AppViewUserInfoVo> list = appUserFollowMapper.getUserFriendList(userId);
        if (CollectionUtils.isEmpty(list)) {
            return AjaxResult.success(new ArrayList<>());
        } else {
            Callable<List<AppViewUserInfoVo>> callable = () -> handleUserFriendList(list);
            FutureTask<List<AppViewUserInfoVo>> callableTask = new FutureTask<>(callable);
            executorService.submit(callableTask);
            try {
                return AjaxResult.success(callableTask.get());
            } catch (Exception e) {
                return AjaxResult.error("线程处理异常");
            }
        }
    }

    /**
     * 解除拉黑
     *
     * @param toUserId
     * @return
     */
    public AjaxResult relieveShield(Long userId, Long toUserId) {
        if (null == toUserId || toUserId.intValue() < 1) {
            return AjaxResult.error("参数【toUserId】为空");
        }
        RLock lock = redisson.getLock("app:relieveShield:" + userId);
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(60, TimeUnit.SECONDS);

        if (userId.equals(toUserId)) {
            lock.unlock();
            return AjaxResult.error("无法对自己操作");
        }

        // 判断是否为客服
        if (appMessageService.isServiceUser(userId)) {
            lock.unlock();
            return AjaxResult.error("当前账号为客服，无法操作");
        }
        if (appMessageService.isServiceUser(toUserId)) {
            lock.unlock();
            return AjaxResult.error("无法对客服操作");
        }

        // 判断当前用户是否拉黑了对方
        AppBlacklist blacklist = appBlacklistMapper.selectAppBlacklistByUserIdAndToUserId(userId, toUserId);
        if (null == blacklist) {
            lock.unlock();
            return AjaxResult.success("操作成功");
        }

        // 有拉黑记录就删除
        appBlacklistMapper.deleteAppBlacklistById(blacklist.getId());

        lock.unlock();
        return AjaxResult.success("操作成功");
    }

    /**
     * 拉黑用户
     *
     * @param toUserId
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult shieldUser(Long userId, Long toUserId) {
        if (null == toUserId || toUserId.intValue() < 1) {
            return AjaxResult.error("参数【toUserId】为空");
        }
        RLock lock = redisson.getLock("app:shieldUser:" + userId);
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(60, TimeUnit.SECONDS);

        if (userId.equals(toUserId)) {
            lock.unlock();
            return AjaxResult.error("无法对自己操作");
        }

        // 判断是否为客服
        if (appMessageService.isServiceUser(userId)) {
            lock.unlock();
            return AjaxResult.error("当前账号为客服，无法操作");
        }
        if (appMessageService.isServiceUser(toUserId)) {
            lock.unlock();
            return AjaxResult.error("无法对客服操作");
        }

        // 判断当前用户是否已拉黑对方
        AppBlacklist blacklist = appBlacklistMapper.selectAppBlacklistByUserIdAndToUserId(userId, toUserId);
        if (null != blacklist) {
            lock.unlock();
            return AjaxResult.success("操作成功");
        }
        // 没有拉黑记录就入库
        blacklist = new AppBlacklist();
        blacklist.setUserId(userId);
        blacklist.setToUserId(toUserId);
        blacklist.setCreateTime(new Date());
        appBlacklistMapper.insertAppBlacklist(blacklist);

        // 判断当前用户是否有对方聊天对话框
        AppPrivateLetterList privateLetter = appPrivateLetterListMapper
                .selectAppPrivateLetterListByUserIdAndReceiveUserId(toUserId, userId);
        if (null != privateLetter) {// 有就删除
            appPrivateLetterListMapper.deleteAppPrivateLetterListById(privateLetter.getId());
        }
        // 如果我关注了对方，那就删除关注
        AppUserFollow followTarget = appUserFollowMapper.selectAppUserFollowByUserIdAndBeUserId(userId, toUserId);
        if (null != followTarget) {
            appUserFollowMapper.deleteAppUserFollowById(followTarget.getId());
        }
        // 如果对方关注了我，那就删除关注
        AppUserFollow followMy = appUserFollowMapper.selectAppUserFollowByUserIdAndBeUserId(toUserId, userId);
        if (null != followMy) {
            appUserFollowMapper.deleteAppUserFollowById(followMy.getId());
        }

        lock.unlock();
        return AjaxResult.success("操作成功");
    }

    /**
     * 获取用户黑名单列表
     *
     * @param page
     * @param size
     * @return
     */
    public TableDataInfo getUserBlackList(int page, int size, Long userId) {

        Page p = PageHelperUtils.startPage(page, size, true);
        List<AppViewUserInfoVo> list = appBlacklistMapper.getUserBlackList(userId);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        } else {
            list.forEach(item -> {
                if (null != item.getSex() && item.getSex().intValue() == -1) {
                    item.setSex(null);
                }
                if (StringUtils.isBlank(item.getBirthday())) {
                    item.setAge(null);
                }
                if (null != item.getAge() && item.getAge().intValue() == -1) {
                    item.setAge(null);
                }
                if (!StringUtils.isBlank(item.getPhotoAlbumStr())) {
                    String[] photoAlbum = JSON.parseObject(item.getPhotoAlbumStr(), String[].class);
                    item.setPhotoAlbum(photoAlbum);
                    item.setPhotoAlbumStr(null);
                } else {
                    item.setPhotoAlbum(new String[]{});
                }
                if (null != item.getPurchaseSituation() && item.getPurchaseSituation().intValue() == -1) {
                    item.setPurchaseSituation(null);
                }
                if (null != item.getCarPurchaseSituation() && item.getCarPurchaseSituation().intValue() == -1) {
                    item.setCarPurchaseSituation(null);
                }
                item.setIsOnline(appCommonService.getUserIsOnline(item.getUserId(), item.getSex(), item.getIsOnline()));
                // item.setCurrentIcoUrl(appTitleNobilityService.getUserCurrentIcoUrl(item.getUserId(),
                // item.getSex()));
                item.setCurrentIcoUrl(appTitleNobilityService.getCurrentIcoUrl(item.getUserId()).getCurrentIcoUrl());
                item.setContributeCurrentIcoUrl(appTitleNobilityService.getCurrentIcoUrl(item.getUserId())
                        .getContributeCurrentIcoUrl());

                item.setUseInColorNickName(appUserPersonalDressingMapper.getUserUseInPersonalDressingInfo(
                        AppPersonalDressingCategoryEnums.COLOR_NICK_NAME.getId(), item.getUserId()));
            });
        }

        return AjaxResult.getDataTable(list, p.getTotal());
    }

    /**
     * 修改用户通话配置
     *
     * @param vo     入参
     * @param userId 用户id
     * @return {@link AjaxResult }
     */
    public AjaxResult updUserCommunicateTelephoneConfig(AppUpdUserCommunicateTelephoneConfigVo vo, Long userId) {
        // 委托给核心服务处理业务逻辑
        return appUserCommunicateTelephoneConfigService.updUserCommunicateTelephoneConfig(vo, userId);
    }

    /**
     * 处理用户守护列表
     *
     * @param list
     * @return
     */
    public List<AppUserProtectionResultVo> handleUserProtectionResultList(List<AppUserProtectionResultVo> list) {
        for (AppUserProtectionResultVo item : list) {
            item.setUserPhone(null);
            if (null != item.getSex() && item.getSex().intValue() == -1) {
                item.setSex(null);
            }
            if (StringUtils.isBlank(item.getBirthday())) {
                item.setAge(null);
            }
            if (null != item.getAge() && item.getAge().intValue() == -1) {
                item.setAge(null);
            }
            item.setIsOnline(appCommonService.getUserIsOnline(item.getUserId(), item.getSex(), item.getIsOnline()));
            // item.setCurrentIcoUrl(appTitleNobilityService.getUserCurrentIcoUrl(item.getUserId(),
            // item.getSex()));
            item.setCurrentIcoUrl(appTitleNobilityService.getCurrentIcoUrl(item.getUserId()).getCurrentIcoUrl());
            item.setContributeCurrentIcoUrl(appTitleNobilityService.getCurrentIcoUrl(item.getUserId())
                    .getContributeCurrentIcoUrl());

            item.setUseInColorNickName(appUserPersonalDressingMapper.getUserUseInPersonalDressingInfo(
                    AppPersonalDressingCategoryEnums.COLOR_NICK_NAME.getId(), item.getUserId()));
        }
        return list;
    }

    /**
     * 获取用户守护列表
     *
     * @param userId     用户id
     * @param viewUserId 被查看的用户id,不传就是查当前用户
     * @return
     */
    public AjaxResult getUserProtectionResultList(Long userId, Long viewUserId) {
        if (null == viewUserId || viewUserId.intValue() <= 0) {
            viewUserId = userId;
        }
        List<AppUserProtectionResultVo> list = appUserMapper.getUserProtectionResultList(viewUserId);
        if (CollectionUtils.isEmpty(list)) {
            return AjaxResult.success(new ArrayList<>());
        } else {
            Callable<List<AppUserProtectionResultVo>> callable = () -> handleUserProtectionResultList(list);
            FutureTask<List<AppUserProtectionResultVo>> callableTask = new FutureTask<>(callable);
            executorService.submit(callableTask);
            try {
                List<AppUserProtectionResultVo> resultList = callableTask.get();
                int ranking = 1;
                for (AppUserProtectionResultVo item : resultList) {
                    item.setRanking(ranking);
                    ranking = ranking + 1;
                }
                return AjaxResult.success(resultList);
            } catch (Exception e) {
                return AjaxResult.error("线程处理异常");
            }
        }
    }

    public Integer getCanTransfer(Long userId) {
        AppUserEntity appUserEntity = appUserMapper.selectAppUserById(userId);
        return appUserEntity.getCanTransfer();
    }

    /**
     * 获取通话金额配置
     * <AUTHOR>
     * @date 2025/4/7 下午2:23
     */
    public AjaxResult getVoiceGold(Long userId) {
        // 获取用户通话配置
        AppUserCommunicateTelephoneConfig config = appUserCommunicateTelephoneConfigMapper.getUserCommunicateTelephoneConfigByUserId(userId);
        if (config == null) {
            return AjaxResult.error("获取用户语言通话配置失败");
        }

        // 从字典中获取最高可以设置的价格
        List<SysDictData> voicePriceList = sysDictTypeService.selectDictDataByType("app_voice_gold");
        if (CollectionUtils.isEmpty(voicePriceList)) {
            return AjaxResult.error("系统未配置语音通话价格");
        }

        // 根据用户累计通话时长来显示最高语音通话价格
        Integer voiceTotalTime = config.getVoiceTotalTime();
        voicePriceList.forEach(item -> {
            item.setCssClass(Integer.parseInt(item.getRemark()) * 60 <= voiceTotalTime ? "show" : null);
            // 把图片的值修改为价格 供前端使用
            item.setImg(String.valueOf(Double.parseDouble(item.getDictValue())/ 10));
        });
        return AjaxResult.success(voicePriceList);
    }

    /**
     * 获取通话金额配置
     * <AUTHOR>
     * @date 2025/4/7 下午2:23
     */
    public AjaxResult getVideoGold(Long userId) {
        // 获取用户通话配置
        // AppUserCommunicateTelephoneConfig config = appUserCommunicateTelephoneConfigMapper.getUserCommunicateTelephoneConfigByUserId(userId);
        // if (config == null) {
        //     return AjaxResult.error("获取用户视频通话配置失败");
        // }

        // 从字典中获取最高可以设置的价格
        List<SysDictData> videoPriceList = sysDictTypeService.selectDictDataByType("app_video_gold");
        if (CollectionUtils.isEmpty(videoPriceList)) {
            return AjaxResult.error("系统未配置视频通话价格");
        }

        // 根据用户累计通话时长来显示最高视频通话价格
        // Integer videoTotalTime = config.getVideoTotalTime();
        // videoPriceList.forEach(item -> {
        //     item.setCssClass(Integer.parseInt(item.getRemark()) * 60 <= videoTotalTime ? "show" : null);
        //     // 把图片的值修改为价格 供前端使用
        //     item.setImg(String.valueOf(Double.parseDouble(item.getDictValue())/ 10));
        // });
        return AjaxResult.success(videoPriceList);
    }

    public AjaxResult getTags() {
        Object interestTags = redisCache.getCacheObject("sys_dict:app_interest_tags");
        Object selfDescriptionTags = redisCache.getCacheObject("sys_dict:app_self_description_tags");
        Object likeTypeTags = redisCache.getCacheObject("sys_dict:app_like_type_tags");
        Object userLabelTags = redisCache.getCacheObject("sys_dict:app_user_label_config");
        Map<String, Object> map = new HashMap<>();
        map.put("interestTags", interestTags);
        map.put("selfDescriptionTags", selfDescriptionTags);
        map.put("likeTypeTags", likeTypeTags);
        map.put("userLabelTags", userLabelTags);
        return AjaxResult.success(map);
    }

    /**
     * 用户是否能关注对方
     *
     * @param userId  用户id
     * @param toUserId 被关注用户id
     * @return {@link boolean }
     */
    private boolean isCanFollow(Long userId, Long toUserId) {
        // 同一个用户不可以相互关注
        if (userId.equals(toUserId)) {
            return false;
        }
        // 判断是否为客服
        if (appMessageService.isServiceUser(userId)) {
            return true;
        }
        if (appMessageService.isServiceUser(toUserId)) {
            return true;
        }

        return true;
    }

    /**
     * 用户是否可以给对方打招呼(收费是否判断)
     *
     * @param userId  用户id
     * @param toUserId 对方用户id
     * @return {@link boolean }
     */
    private boolean isCanHellowFollow(Long userId, Long toUserId) {

        if (userId.equals(toUserId)) {
            return false;
        }

        // 判断是否为客服
        if (appMessageService.isServiceUser(userId)) {
            return true;
        }
        if (appMessageService.isServiceUser(toUserId)) {
            return true;
        }

        return true;
    }


}
