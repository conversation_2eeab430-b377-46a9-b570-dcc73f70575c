package com.hzy.core.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.hzy.core.config.RedisCache;
import com.hzy.core.entity.*;
import com.hzy.core.enums.AppPointsBillTypeEnums;
import com.hzy.core.enums.AppUserSexTypeEnums;
import com.hzy.core.enums.ChannelCommissionEnums;
import com.hzy.core.enums.ChannelCommissionTypeEnums;
import com.hzy.core.enums.WhetherTypeEnum;
import com.hzy.core.mapper.*;
import com.hzy.core.model.vo.AjaxResult;
import com.hzy.core.model.vo.admin.ChannelCommissionRecordVo;
import com.hzy.core.page.TableDataInfo;
import com.hzy.core.service.ChannelCommissionRecordService;
import com.hzy.core.service.CoreAppUserService;
import com.hzy.core.utils.GenChanelIdUtil;
import com.hzy.core.utils.PageHelperUtils;
import com.hzy.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 渠道佣金记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Slf4j
@Service
public class ChannelCommissionRecordServiceImpl extends ServiceImpl<ChannelCommissionRecordMapper, ChannelCommissionRecord> implements ChannelCommissionRecordService {

    @Resource
    private RedisCache redisCache;
    @Resource
    private CoreAppUserService coreAppUserService;
    @Resource
    private AppConfigMapper appConfigMapper;
    @Resource
    private AppUserMapper appUserMapper;
    @Resource
    private AppUserPointsBillMapper appUserPointsBillMapper;
    @Resource
    private ChannelCommissionRecordMapper channelCommissionRecordMapper;
    @Autowired
    private AppGuildMemberMapper appGuildMemberMapper;

    /**
     * 充值-渠道佣金记录
     *
     * @param user  充值的用户
     * @param order 订单
     */
    @Override
    public void saveChannelCommissionRecords(AppUserEntity user, AppOrder order) {
        if (user.getUpChannelCode() == null)   {
            return;
        }

        // 上级渠道的用户渠道等级
        int levelByCode = GenChanelIdUtil.getLevelByCode(user.getUpChannelCode());


        // 根据上级渠道级别执行不同的业务流程
        switch (levelByCode) {
            case 1:
                // 一级渠道分佣
                this.commissionRecord(ChannelCommissionEnums.CZ_B2A, user, order, ChannelCommissionTypeEnums.CASH);
                break;
            case 2:
                // 二级用户分佣
                this.commissionRecord(ChannelCommissionEnums.CZ_C2B, user, order, ChannelCommissionTypeEnums.POINTS);

                // 获取上级用户
                Optional<AppUserEntity> upUser = coreAppUserService.lambdaQuery()
                        .eq(AppUserEntity::getChannelCode, user.getUpChannelCode())
                        .oneOpt();
                // A级渠道分佣
                if (upUser.isPresent()) {
                    String ratio3 = redisCache.getCacheObject(ChannelCommissionEnums.CZ_C2A.getRedisKey()).toString();

                    // 佣金
                    BigDecimal bigDecimal3 = order.getOrderPrice()
                            .multiply(new BigDecimal(ratio3))
                            .setScale(2, RoundingMode.HALF_UP);

                    ChannelCommissionRecord channelCommissionRecord3 = new ChannelCommissionRecord();
                    channelCommissionRecord3.setChannelCode(upUser.get().getUpChannelCode())
                            .setRechargeUserCode(user.getChannelCode())
                            .setUserId(user.getId().intValue())
                            .setMoney(bigDecimal3)
                            .setRatio(Float.valueOf(ratio3))
                            .setOrderNo(order.getOrderNo())
                            .setOrderPrice(order.getOrderPrice())
                            .setType(ChannelCommissionTypeEnums.CASH.getType());

                    List<ChannelCommissionRecord> record3 = this.lambdaQuery()
                            .eq(ChannelCommissionRecord::getChannelCode, upUser.get().getUpChannelCode())
                            .eq(ChannelCommissionRecord::getRechargeUserCode, user.getChannelCode())
                            .eq(ChannelCommissionRecord::getOrderNo, order.getOrderNo())
                            .eq(ChannelCommissionRecord::getType, ChannelCommissionTypeEnums.CASH.getType())
                            .list();

                    if (record3.isEmpty()) {
                        this.save(channelCommissionRecord3);
                    }
                }

                break;
            default:
                // 三级游客分佣
                this.commissionRecord(ChannelCommissionEnums.CZ_D2C, user, order, ChannelCommissionTypeEnums.POINTS);
        }

    }

    /**
     * 生成渠道分佣记录
     *
     * @param redisKey keyName
     * @param user     用户
     * @param order    订单
     * @param type     渠道分佣类型
     */
    private void commissionRecord(ChannelCommissionEnums redisKey, AppUserEntity user, AppOrder order, ChannelCommissionTypeEnums type) {
        String ratio = redisCache.getCacheObject(redisKey.getRedisKey()).toString();

        // 佣金
        BigDecimal bigDecimal = order.getOrderPrice().multiply(new BigDecimal(ratio)).setScale(2, RoundingMode.HALF_UP);

        ChannelCommissionRecord channelCommissionRecord = new ChannelCommissionRecord();
        channelCommissionRecord.setChannelCode(user.getUpChannelCode())
                .setRechargeUserCode(user.getChannelCode())
                .setUserId(user.getId().intValue())
                .setMoney(bigDecimal)
                .setRatio(Float.valueOf(ratio))
                .setOrderNo(order.getOrderNo())
                .setOrderPrice(order.getOrderPrice())
                .setFlag(1)
                .setType(type.getType());

        List<ChannelCommissionRecord> record = this.lambdaQuery()
                .eq(ChannelCommissionRecord::getChannelCode, user.getUpChannelCode())
                .eq(ChannelCommissionRecord::getRechargeUserCode, user.getChannelCode())
                .eq(ChannelCommissionRecord::getOrderNo, order.getOrderNo())
                .eq(ChannelCommissionRecord::getType, type.getType())
                .eq(ChannelCommissionRecord::getFlag, 1)
                .list();

        if (record.isEmpty()) {
            this.save(channelCommissionRecord);
            this.addPointsByChannelCode(user, channelCommissionRecord.getId()
                    .longValue(), type,order.getOrderPrice(), bigDecimal, AppPointsBillTypeEnums.TYPE21);
        }
    }

    @Override
    public void saveChannelGiftRecords(AppUserEntity user, AppGift gift, UserGiveGiftEntity giftRecord) {
        if (user.getUpChannelCode() == null || user.getUpChannelCode().equals("重复注册"))   {
            return;
        }

        // 上级渠道的用户渠道等级
        int levelByCode = GenChanelIdUtil.getLevelByCode(user.getUpChannelCode());
        log.info("是否执行了异步任务现成,记录分佣记录");

        // 根据上级渠道级别执行不同的业务流程
        switch (levelByCode) {
            case 1:
                // 一级渠道分佣
                this.giftCommissionRecord(ChannelCommissionEnums.XF_B2A, user, gift, giftRecord, ChannelCommissionTypeEnums.CASH);
                break;
            case 2:
                // 二级用户分佣
                this.giftCommissionRecord(ChannelCommissionEnums.XF_C2B, user, gift, giftRecord, ChannelCommissionTypeEnums.POINTS);
                break;
            default:
                // 三级游客分佣
                this.giftCommissionRecord(ChannelCommissionEnums.XF_D2C, user, gift, giftRecord, ChannelCommissionTypeEnums.POINTS);

        }
    }

    @Override
    public TableDataInfo getRecordList(Integer pageNum, Integer pageSize, ChannelCommissionRecordVo channelCommissionRecord) {
        BigDecimal total = channelCommissionRecordMapper.getRecordList(channelCommissionRecord).stream()
                .map(ChannelCommissionRecord::getOrderPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        Page<ChannelCommissionRecord> p = PageHelperUtils.startPage(pageNum, pageSize, true);

        final List<ChannelCommissionRecordVo> list = channelCommissionRecordMapper.getRecordList(channelCommissionRecord);

        return AjaxResult.getDataTable(list, p.getTotal(), total);
    }
    @Override
    public TableDataInfo getRecordListOnlyOnce(Integer pageNum, Integer pageSize, ChannelCommissionRecordVo channelCommissionRecord) {
        BigDecimal total = channelCommissionRecordMapper.getRecordListOnlyOnce(channelCommissionRecord).stream()
                .map(ChannelCommissionRecord::getOrderPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        Page<ChannelCommissionRecord> p = PageHelperUtils.startPage(pageNum, pageSize, true);

        final List<ChannelCommissionRecordVo> list = channelCommissionRecordMapper.getRecordListOnlyOnce(channelCommissionRecord);

        return AjaxResult.getDataTable(list, p.getTotal(), total);
    }

    /**
     * 生成渠道分佣记录
     *
     * @param redisKey    keyName
     * @param user        用户
     * @param order       订单
     * @param giftRecord 消费礼物给出记录
     * @param type        分佣类型
     */
    private void giftCommissionRecord(ChannelCommissionEnums redisKey, AppUserEntity user, AppGift order, UserGiveGiftEntity giftRecord, ChannelCommissionTypeEnums type) {
        String ratio = redisCache.getCacheObject(redisKey.getRedisKey()).toString();

        // 佣金,礼物金额是金币,需要转化为对应的金额
        BigDecimal bigDecimal = giftRecord.getGiftGoldTotalAmount()
                .multiply(new BigDecimal(ratio))
                .multiply(appConfigMapper.getAppConfig().getOneGoldEqRmb())
                .setScale(2, RoundingMode.HALF_UP);

        ChannelCommissionRecord channelCommissionRecord = new ChannelCommissionRecord();
        channelCommissionRecord.setChannelCode(user.getUpChannelCode())
                .setRechargeUserCode(user.getChannelCode())
                .setUserId(user.getId().intValue())
                .setMoney(bigDecimal)
                .setRatio(Float.valueOf(ratio))
                .setOrderNo(giftRecord.getId().toString())
                .setOrderPrice(giftRecord.getGiftGoldTotalAmount())
                .setType(type.getType())
                .setFlag(2);

        List<ChannelCommissionRecord> record = this.lambdaQuery()
                .eq(ChannelCommissionRecord::getChannelCode, user.getUpChannelCode())
                .eq(ChannelCommissionRecord::getRechargeUserCode, user.getChannelCode())
                .eq(ChannelCommissionRecord::getOrderNo, giftRecord.getId().toString())
                .eq(ChannelCommissionRecord::getType, type.getType())
                .eq(ChannelCommissionRecord::getFlag, 2)
                .list();

        if (record.isEmpty()) {
            this.save(channelCommissionRecord);
            this.addPointsByChannelCode(user, channelCommissionRecord.getId()
                    .longValue(), type,giftRecord.getGiftGoldTotalAmount(), bigDecimal, AppPointsBillTypeEnums.TYPE22);
        }

    }

    /**
     * 添加钻石明细记录
     *
     * @param user          用户
     * @param recordId      记录id
     * @param type          类型
     * @param bigDecimal    计算收益比例后的到账钻石数
     * @param billTypeEnums 账单类型
     */
    private void addPointsByChannelCode(AppUserEntity user, Long recordId, ChannelCommissionTypeEnums type,BigDecimal total, BigDecimal bigDecimal, AppPointsBillTypeEnums billTypeEnums) {
        if (type.getType() == ChannelCommissionTypeEnums.POINTS.getType()) {
            Long userId = appUserMapper.getUserIdByChannelCode(user.getUpChannelCode());
            if (userId == null) {
                return;
            }
            appUserMapper.addUserPointsBalanceByChannelCode(user.getUpChannelCode(), bigDecimal);

            // 记录接收用户账单
            AppUserPointsBill userGoldBillRecipient = new AppUserPointsBill();
            userGoldBillRecipient.setUserId(userId);
            userGoldBillRecipient.setCreateTime(new Date());
            userGoldBillRecipient.setBillType((long) billTypeEnums.getId());
            userGoldBillRecipient.setObjectId(recordId);
            userGoldBillRecipient.setAmount(bigDecimal);
            userGoldBillRecipient.setTotalAmount(total);
            userGoldBillRecipient.setIsDel(WhetherTypeEnum.NO.getName());
            userGoldBillRecipient.setRemarksMsg(StringUtils.format(billTypeEnums.getDesc(), user.getNickName()));
            userGoldBillRecipient.setGuildId(appGuildMemberMapper.getGuildIdByUserId(userId));
            appUserPointsBillMapper.insertAppUserPointsBill(userGoldBillRecipient);
        }
    }

    @Override
    public void saveChannelTelRecords(AppUserEntity user, AppCommunicateTelephoneRecords records) {
        if (user.getUpChannelCode() == null)   {
            return;
        }

        // 上级渠道的用户渠道等级
        int levelByCode = GenChanelIdUtil.getLevelByCode(user.getUpChannelCode());
        log.info("是否执行了异步任务现成,记录分佣记录");

        // 根据上级渠道级别执行不同的业务流程
        switch (levelByCode) {
            case 1:
                // 一级渠道分佣
                this.telCommissionRecord(ChannelCommissionEnums.XF_B2A, user, records, ChannelCommissionTypeEnums.CASH);
                break;
            case 2:
                // 二级用户分佣
                this.telCommissionRecord(ChannelCommissionEnums.XF_C2B, user, records, ChannelCommissionTypeEnums.POINTS);
                break;
            default:
                // 三级游客分佣
                this.telCommissionRecord(ChannelCommissionEnums.XF_D2C, user, records, ChannelCommissionTypeEnums.POINTS);

        }


    }

    /**
     * 生成渠道分佣记录
     *
     * @param redisKey keyName
     * @param user     用户
     * @param records  记录
     * @param type     分佣类型
     */
    private void telCommissionRecord(ChannelCommissionEnums redisKey, AppUserEntity user, AppCommunicateTelephoneRecords records, ChannelCommissionTypeEnums type) {
        String ratio = redisCache.getCacheObject(redisKey.getRedisKey()).toString();

        // 佣金,礼物金额是金币,需要转化为对应的金额
        BigDecimal bigDecimal = records.getConsumptionGold()
                .multiply(new BigDecimal(ratio))
                .multiply(appConfigMapper.getAppConfig().getOneGoldEqRmb())
                .setScale(2, RoundingMode.HALF_UP);

        ChannelCommissionRecord channelCommissionRecord = new ChannelCommissionRecord();
        channelCommissionRecord.setChannelCode(user.getUpChannelCode())
                .setRechargeUserCode(user.getChannelCode())
                .setUserId(user.getId().intValue())
                .setMoney(bigDecimal)
                .setRatio(Float.valueOf(ratio))
                .setOrderNo(records.getId().toString())
                .setOrderPrice(records.getConsumptionGold())
                .setType(type.getType())
                .setFlag(2);

        List<ChannelCommissionRecord> record = this.lambdaQuery()
                .eq(ChannelCommissionRecord::getChannelCode, user.getUpChannelCode())
                .eq(ChannelCommissionRecord::getRechargeUserCode, user.getChannelCode())
                .eq(ChannelCommissionRecord::getOrderNo, records.getId().toString())
                .eq(ChannelCommissionRecord::getType, type.getType())
                .eq(ChannelCommissionRecord::getFlag, 2)
                .list();

        if (record.isEmpty()) {
            this.save(channelCommissionRecord);
            this.addPointsByChannelCode(user, channelCommissionRecord.getId()
                    .longValue(), type,records.getConsumptionGold(), bigDecimal, AppPointsBillTypeEnums.TYPE24);
        }

    }

    @Override
    public void saveChannelGiftRecordsForFemale(AppUserEntity receiveUser, AppGift gift, UserGiveGiftEntity giftRecord) {
        // 1. 检查接收用户是否为女性
        if (receiveUser.getSex() == null || receiveUser.getSex() != AppUserSexTypeEnums.TYPE1.getId()) {
            log.info("接收用户{}不是女性，不进行渠道分佣", receiveUser.getId());
            return;
        }

        // 2. 检查是否有上级渠道
        if (receiveUser.getUpChannelCode() == null || receiveUser.getUpChannelCode().equals("重复注册")) {
            log.info("接收用户{}没有上级渠道，不进行渠道分佣", receiveUser.getId());
            return;
        }

        log.info("开始为女性用户{}的上级渠道{}进行礼物分佣", receiveUser.getId(), receiveUser.getUpChannelCode());

        // 3. 查找上级渠道用户
        Optional<AppUserEntity> upChannelUser = coreAppUserService.lambdaQuery()
                .eq(AppUserEntity::getChannelCode, receiveUser.getUpChannelCode())
                .oneOpt();

        if (!upChannelUser.isPresent()) {
            log.warn("找不到上级渠道用户，渠道码：{}", receiveUser.getUpChannelCode());
            return;
        }

        // 4. 计算固定5%的积分分佣
        BigDecimal commissionRatio = new BigDecimal("0.05").multiply(new BigDecimal("0.1")); // 固定5%
        BigDecimal giftPoints = giftRecord.getGiftGoldTotalAmount(); // 礼物积分
        BigDecimal commissionAmount = giftPoints.multiply(commissionRatio).setScale(2, RoundingMode.HALF_UP);

        // 5. 创建渠道佣金记录
        ChannelCommissionRecord channelCommissionRecord = new ChannelCommissionRecord();
        channelCommissionRecord.setChannelCode(receiveUser.getUpChannelCode())
                .setRechargeUserCode(receiveUser.getChannelCode())
                .setUserId(receiveUser.getId().intValue())
                .setMoney(commissionAmount)
                .setRatio(commissionRatio.floatValue())
                .setOrderNo(giftRecord.getId().toString())
                .setOrderPrice(giftPoints)
                .setType(ChannelCommissionTypeEnums.POINTS.getType()) // 使用积分类型
                .setFlag(2); // 使用新的标志位区分新逻辑

        // 6. 检查是否已存在相同记录，避免重复
        List<ChannelCommissionRecord> existingRecords = this.lambdaQuery()
                .eq(ChannelCommissionRecord::getChannelCode, receiveUser.getUpChannelCode())
                .eq(ChannelCommissionRecord::getRechargeUserCode, receiveUser.getChannelCode())
                .eq(ChannelCommissionRecord::getOrderNo, giftRecord.getId().toString())
                .eq(ChannelCommissionRecord::getType, ChannelCommissionTypeEnums.POINTS.getType())
                .eq(ChannelCommissionRecord::getFlag, 2)
                .list();

        if (existingRecords.isEmpty()) {
            // 7. 保存佣金记录
            this.save(channelCommissionRecord);

            // 8. 给上级渠道用户增加积分
            this.addPointsByChannelCode(receiveUser, channelCommissionRecord.getId().longValue(), 
                    ChannelCommissionTypeEnums.POINTS, giftPoints, commissionAmount, AppPointsBillTypeEnums.TYPE22);
            
            log.info("为女性用户{}的上级渠道{}成功添加礼物分佣记录，分佣积分：{}", 
                    receiveUser.getId(), receiveUser.getUpChannelCode(), commissionAmount);
        } else {
            log.info("渠道分佣记录已存在，跳过：{}", giftRecord.getId());
        }
    }

    @Override
    public void saveChannelCommissionRecordsForMale(AppUserEntity user, AppOrder order) {
        // 1. 检查充值用户是否为男性
        if (user.getSex() == null || user.getSex() != AppUserSexTypeEnums.TYPE0.getId()) {
            log.info("充值用户{}不是男性，不进行特殊渠道分佣", user.getId());
            return;
        }

        // 2. 检查是否有上级渠道
        if (user.getUpChannelCode() == null) {
            log.info("充值用户{}没有上级渠道，不进行渠道分佣", user.getId());
            return;
        }

        log.info("开始为男性用户{}的上级渠道{}进行充值分佣", user.getId(), user.getUpChannelCode());

        // 3. 查找上级渠道用户
        Optional<AppUserEntity> upChannelUser = coreAppUserService.lambdaQuery()
                .eq(AppUserEntity::getChannelCode, user.getUpChannelCode())
                .oneOpt();

        if (!upChannelUser.isPresent()) {
            log.warn("找不到上级渠道用户，渠道码：{}", user.getUpChannelCode());
            return;
        }

        // 4. 计算固定30%的金币收益分佣
        BigDecimal commissionRatio = new BigDecimal("0.30"); // 固定30%
        BigDecimal orderPrice = order.getOrderPrice(); // 订单金额
        BigDecimal commissionAmount = orderPrice.multiply(commissionRatio).setScale(2, RoundingMode.HALF_UP);

        // 5. 创建渠道佣金记录
        ChannelCommissionRecord channelCommissionRecord = new ChannelCommissionRecord();
        channelCommissionRecord.setChannelCode(user.getUpChannelCode())
                .setRechargeUserCode(user.getChannelCode())
                .setUserId(user.getId().intValue())
                .setMoney(commissionAmount)
                .setRatio(commissionRatio.floatValue())
                .setOrderNo(order.getOrderNo())
                .setOrderPrice(orderPrice)
                .setType(ChannelCommissionTypeEnums.POINTS.getType()) // 使用积分类型
                .setFlag(1); // 使用新的标志位区分男性充值分佣逻辑

        // 6. 检查是否已存在相同记录，避免重复
        List<ChannelCommissionRecord> existingRecords = this.lambdaQuery()
                .eq(ChannelCommissionRecord::getChannelCode, user.getUpChannelCode())
                .eq(ChannelCommissionRecord::getRechargeUserCode, user.getChannelCode())
                .eq(ChannelCommissionRecord::getOrderNo, order.getOrderNo())
                .eq(ChannelCommissionRecord::getType, ChannelCommissionTypeEnums.POINTS.getType())
                .eq(ChannelCommissionRecord::getFlag, 1)
                .list();

        if (existingRecords.isEmpty()) {
            // 7. 保存佣金记录
            this.save(channelCommissionRecord);

            // 8. 给上级渠道用户增加积分
            this.addPointsByChannelCode(user, channelCommissionRecord.getId().longValue(), 
                    ChannelCommissionTypeEnums.POINTS, orderPrice, commissionAmount, AppPointsBillTypeEnums.TYPE21);
            
            log.info("为男性用户{}的上级渠道{}成功添加充值分佣记录，分佣金额：{}", 
                    user.getId(), user.getUpChannelCode(), commissionAmount);
        } else {
            log.info("渠道分佣记录已存在，跳过：{}", order.getOrderNo());
        }
    }

    @Override
    public void saveChannelTelRecordsForFemale(AppUserEntity receiveUser, AppCommunicateTelephoneRecords telRecords) {
        // 1. 检查接收用户是否为女性
        if (receiveUser.getSex() == null || receiveUser.getSex() != AppUserSexTypeEnums.TYPE1.getId()) {
            log.info("接收用户{}不是女性，不进行电话收益渠道分佣", receiveUser.getId());
            return;
        }

        // 2. 检查是否有上级渠道
        if (receiveUser.getUpChannelCode() == null || receiveUser.getUpChannelCode().equals("重复注册")) {
            log.info("接收用户{}没有上级渠道，不进行电话收益渠道分佣", receiveUser.getId());
            return;
        }

        // 3. 检查是否产生了电话收益（消费金币大于0）
        if (telRecords.getConsumptionGold() == null || telRecords.getConsumptionGold().compareTo(BigDecimal.ZERO) <= 0) {
            log.info("通话记录{}没有产生收益，不进行电话收益渠道分佣", telRecords.getId());
            return;
        }

        log.info("开始为女性用户{}的上级渠道{}进行电话收益分佣", receiveUser.getId(), receiveUser.getUpChannelCode());

        // 4. 查找上级渠道用户
        Optional<AppUserEntity> upChannelUser = coreAppUserService.lambdaQuery()
                .eq(AppUserEntity::getChannelCode, receiveUser.getUpChannelCode())
                .oneOpt();

        if (upChannelUser.isEmpty()) {
            log.warn("找不到上级渠道用户，渠道码：{}", receiveUser.getUpChannelCode());
            return;
        }

        // 5. 计算固定5%的电话收益分佣
        BigDecimal commissionRatio = new BigDecimal("0.05").multiply(new BigDecimal("0.1")); // 固定5%
        BigDecimal telConsumptionGold = telRecords.getConsumptionGold(); // 电话消费金币
        BigDecimal commissionAmount = telConsumptionGold.multiply(commissionRatio).setScale(2, RoundingMode.HALF_UP);

        // 6. 创建渠道佣金记录
        ChannelCommissionRecord channelCommissionRecord = new ChannelCommissionRecord();
        channelCommissionRecord.setChannelCode(receiveUser.getUpChannelCode())
                .setRechargeUserCode(receiveUser.getChannelCode())
                .setUserId(receiveUser.getId().intValue())
                .setMoney(commissionAmount)
                .setRatio(commissionRatio.floatValue())
                .setOrderNo(telRecords.getId().toString())
                .setOrderPrice(telConsumptionGold)
                .setType(ChannelCommissionTypeEnums.POINTS.getType()) // 使用积分类型
                .setFlag(2); // 使用新的标志位区分电话收益分佣逻辑

        // 7. 检查是否已存在相同记录，避免重复
        List<ChannelCommissionRecord> existingRecords = this.lambdaQuery()
                .eq(ChannelCommissionRecord::getChannelCode, receiveUser.getUpChannelCode())
                .eq(ChannelCommissionRecord::getRechargeUserCode, receiveUser.getChannelCode())
                .eq(ChannelCommissionRecord::getOrderNo, telRecords.getId().toString())
                .eq(ChannelCommissionRecord::getType, ChannelCommissionTypeEnums.POINTS.getType())
                .eq(ChannelCommissionRecord::getFlag, 2)
                .list();

        if (existingRecords.isEmpty()) {
            // 8. 保存佣金记录
            this.save(channelCommissionRecord);

            // 9. 给上级渠道用户增加积分
            this.addPointsByChannelCode(receiveUser, channelCommissionRecord.getId().longValue(), 
                    ChannelCommissionTypeEnums.POINTS, telConsumptionGold, commissionAmount, AppPointsBillTypeEnums.TYPE24);
            
            log.info("为女性用户{}的上级渠道{}成功添加电话收益分佣记录，分佣积分：{}", 
                    receiveUser.getId(), receiveUser.getUpChannelCode(), commissionAmount);
        } else {
            log.info("电话收益渠道分佣记录已存在，跳过：{}", telRecords.getId());
        }
    }

}
