<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppFamilyMapper">

    <resultMap type="AppFamily" id="AppFamilyResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="userId" column="user_id"/>
        <result property="isDel" column="is_del"/>
        <result property="peopleNum" column="people_num"/>
        <result property="maxPeopleNum" column="max_people_num"/>
        <result property="groupNumber" column="group_number"/>
        <result property="familyName" column="family_name"/>
        <result property="greeting" column="greeting"/>
        <result property="headPortrait" column="head_portrait"/>
    </resultMap>

    <sql id="selectAppFamilyVo">
        select id,
        create_time,
        update_time,
        create_by,
        update_by,
        user_id,
        is_del,
        people_num,
        max_people_num,
        group_number,
        family_name,
        greeting,
        head_portrait
        from app_family
    </sql>

    <select id="selectAppFamilyList" parameterType="AppFamily" resultMap="AppFamilyResult">
        <include refid="selectAppFamilyVo"/>
        where is_del=false
        <if test="userId != null ">and user_id = #{userId}</if>
        <if test="peopleNum != null ">and people_num = #{peopleNum}</if>
        <if test="maxPeopleNum != null ">and max_people_num = #{maxPeopleNum}</if>
        <if test="groupNumber != null  and groupNumber != ''">and group_number = #{groupNumber}</if>
        <if test="familyName != null  and familyName != ''">and family_name like concat('%', #{familyName}, '%')
        </if>
        <if test="greeting != null  and greeting != ''">and greeting = #{greeting}</if>
        <if test="headPortrait != null  and headPortrait != ''">and head_portrait = #{headPortrait}</if>
        order by create_time desc
    </select>

    <select id="selectAppFamilyById" parameterType="Long" resultMap="AppFamilyResult">
        <include refid="selectAppFamilyVo"/>
        where id = #{id} and is_del=false
    </select>

    <select id="getFamilyById" parameterType="Long" resultMap="AppFamilyResult">
        <include refid="selectAppFamilyVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppFamily" parameterType="AppFamily" useGeneratedKeys="true" keyProperty="id">
        insert into app_family
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="userId != null">user_id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="peopleNum != null">people_num,</if>
            <if test="maxPeopleNum != null">max_people_num,</if>
            <if test="groupNumber != null">group_number,</if>
            <if test="familyName != null">family_name,</if>
            <if test="greeting != null">greeting,</if>
            <if test="headPortrait != null">head_portrait,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="userId != null">#{userId},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="peopleNum != null">#{peopleNum},</if>
            <if test="maxPeopleNum != null">#{maxPeopleNum},</if>
            <if test="groupNumber != null">#{groupNumber},</if>
            <if test="familyName != null">#{familyName},</if>
            <if test="greeting != null">#{greeting},</if>
            <if test="headPortrait != null">#{headPortrait},</if>
        </trim>
    </insert>

    <update id="updateAppFamily" parameterType="AppFamily">
        update app_family
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="peopleNum != null">people_num = #{peopleNum},</if>
            <if test="maxPeopleNum != null">max_people_num = #{maxPeopleNum},</if>
            <if test="groupNumber != null">group_number = #{groupNumber},</if>
            <if test="familyName != null">family_name = #{familyName},</if>
            <if test="greeting != null">greeting = #{greeting},</if>
            <if test="headPortrait != null">head_portrait = #{headPortrait},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppFamilyById" parameterType="Long">
        delete
        from app_family
        where id = #{id}
    </delete>

    <delete id="deleteAppFamilyByIds" parameterType="String">
        delete from app_family where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectAppFamilyByUserId" parameterType="Long" resultMap="AppFamilyResult">
        <include refid="selectAppFamilyVo"/>
        where user_id = #{userId} and is_del=false
        order by create_time desc limit 0,1
    </select>

    <select id="getUserCreateFamilyId" parameterType="Long" resultType="java.lang.Long">
        select id
        from app_family
        where user_id = #{userId}
        and is_del = false
        order by create_time desc limit 0,1
    </select>

    <select id="getHotFamilyList" parameterType="Long" resultType="com.hzy.core.model.vo.app.AppHotFamilyListVo">
        select f.id as familyId,
        f.user_id as userId,
        f.people_num as peopleNum,
        f.max_people_num as maxPeopleNum,
        f.group_number as groupNumber,
        f.family_name as familyName,
        f.greeting as greeting,
        f.head_portrait as headPortrait,
        if(f.user_id = #{userId}, 1, (select if(count(*) > 0, 1, 0)
        from app_family_member fm
        where fm.family_id = f.id
        and fm.user_id = #{userId})) as isJoin
        from app_family f
        where f.is_del = false
        order by f.people_num desc, create_time desc
    </select>

    <update id="addPeopleNum" parameterType="java.lang.Long">
        update app_family
        set people_num = people_num + #{num},
        update_time=now()
        where id = #{id}
    </update>

    <update id="subtractPeopleNum" parameterType="java.lang.Long">
        update app_family
        set people_num = people_num - #{num},
        update_time=now()
        where id = #{id} and people_num>=#{num}
    </update>
</mapper>