<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppTopUpRewardsMapper">

    <resultMap type="AppTopUpRewards" id="AppTopUpRewardsResult">
        <result property="id" column="id"/>
        <result property="amount" column="amount"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isEnable" column="is_enable"/>
        <result property="colorNickNameId" column="color_nick_name_id"/>
        <result property="mountId" column="mount_id"/>
        <result property="chatFrameId" column="chat_frame_id"/>
        <result property="micFrame" column="mic_frame"/>
        <result property="pendantId" column="pendant_id"/>
        <result property="famousBrandId" column="famous_brand_id"/>
    </resultMap>

    <sql id="selectAppTopUpRewardsVo">
        select id, amount, create_time, update_time, create_by, update_by, is_enable, color_nick_name_id, mount_id,
        chat_frame_id, mic_frame, pendant_id, famous_brand_id from app_top_up_rewards
    </sql>

    <select id="selectAppTopUpRewardsList" parameterType="AppTopUpRewards" resultMap="AppTopUpRewardsResult">
        <include refid="selectAppTopUpRewardsVo"/>
        <where>
            <if test="amount != null ">and amount = #{amount}</if>
            <if test="isEnable != null ">and is_enable = #{isEnable}</if>
            <if test="colorNickNameId != null ">and color_nick_name_id = #{colorNickNameId}</if>
            <if test="mountId != null ">and mount_id = #{mountId}</if>
            <if test="chatFrameId != null ">and chat_frame_id = #{chatFrameId}</if>
            <if test="micFrame != null ">and mic_frame = #{micFrame}</if>
            <if test="pendantId != null ">and pendant_id = #{pendantId}</if>
            <if test="famousBrandId != null ">and famous_brand_id = #{famousBrandId}</if>
        </where>
    </select>

    <select id="selectAppTopUpRewardsById" parameterType="Long" resultMap="AppTopUpRewardsResult">
        <include refid="selectAppTopUpRewardsVo"/>
        order by id desc limit 0,1
    </select>

    <insert id="insertAppTopUpRewards" parameterType="AppTopUpRewards" useGeneratedKeys="true" keyProperty="id">
        insert into app_top_up_rewards
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="amount != null">amount,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isEnable != null">is_enable,</if>
            <if test="colorNickNameId != null">color_nick_name_id,</if>
            <if test="mountId != null">mount_id,</if>
            <if test="chatFrameId != null">chat_frame_id,</if>
            <if test="micFrame != null">mic_frame,</if>
            <if test="pendantId != null">pendant_id,</if>
            <if test="famousBrandId != null">famous_brand_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="amount != null">#{amount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isEnable != null">#{isEnable},</if>
            <if test="colorNickNameId != null">#{colorNickNameId},</if>
            <if test="mountId != null">#{mountId},</if>
            <if test="chatFrameId != null">#{chatFrameId},</if>
            <if test="micFrame != null">#{micFrame},</if>
            <if test="pendantId != null">#{pendantId},</if>
            <if test="famousBrandId != null">#{famousBrandId},</if>
        </trim>
    </insert>

    <update id="updateAppTopUpRewards" parameterType="AppTopUpRewards">
        update app_top_up_rewards
        <trim prefix="SET" suffixOverrides=",">
            <if test="amount != null">amount = #{amount},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isEnable != null">is_enable = #{isEnable},</if>
            <if test="colorNickNameId != null">color_nick_name_id = #{colorNickNameId},</if>
            <if test="mountId != null">mount_id = #{mountId},</if>
            <if test="chatFrameId != null">chat_frame_id = #{chatFrameId},</if>
            <if test="micFrame != null">mic_frame = #{micFrame},</if>
            <if test="pendantId != null">pendant_id = #{pendantId},</if>
            <if test="famousBrandId != null">famous_brand_id = #{famousBrandId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppTopUpRewardsById" parameterType="Long">
        delete from app_top_up_rewards where id = #{id}
    </delete>

    <delete id="deleteAppTopUpRewardsByIds" parameterType="String">
        delete from app_top_up_rewards where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>