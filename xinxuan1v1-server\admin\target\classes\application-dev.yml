# Spring配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: **************************************************************************************************************************************************************************
        username: nanjing
        password: hDTa2XJTXXjLbRmt
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 50
      # 最大连接池数量
      maxActive: 1000
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: lk81unvUOxEqw
        login-password: Ch2Po4mIyRdcB
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: false
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

  #全局返回时间格式
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages

  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 50MB
      # 设置总上传的文件大小
      max-request-size: 200MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: ************
#    host: 127.0.0.1
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 6
    # 密码
    password:
    # 连接超时时间
    timeout: 10000
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 500
        # 连接池的最大数据库连接数
        max-active: 500
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

## MyBatis配置
#mybatis:
#  # 搜索指定包别名
#  typeAliasesPackage: com.hzy.**.domain
#  # 配置mapper的扫描，找到所有的mapper.xml映射文件
#  mapperLocations: classpath*:mapper/**/*Mapper.xml
#  # 加载全局的配置文件
#  configLocation: classpath:mybatis/mybatis-config.xml


# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  reasonable: false
  supportMethodsArguments: true
  params: count=countSql

# 日志配置
logging:
  # 日志存储路径
  file:
    path: ${hzy.rootPath}/logs/${hzy.prefix}-dev
  # 日志存储路径
  config: classpath:logback-spring.xml
  level:
    com.common: debug
    org.springframework: warn
    mybatis: warn
    com.ahhy.mapper: debug
#    com.hzy: info
    com.hzy.system.service.: debug

# knife开源的swagger ui配置
knife4j:
  # 开启增强配置
  enable: true
  # 是否是生产环境，如果是生产环境会默认关闭swagger
  production: false
  # 配置认证功能
  basic:
    # 是否开启认证
    enable: false

#通用配置
common-config:
  #内部请求长连接api密钥
  websocketApiKey: hSQUeFMtdrOcbWYgNTmZVJ86BqRIwfEz
  #内部请求客户端api密钥
  clientApiKey: M7lVn31Ia2GByQzuoDxmZ9ibte5TgkEX
  #websocket地址
  websocketUrl: http://127.0.0.1:5010
  #客户端地址
  clientUrl: http://**************:5000/client
  localhostClientUrl: http://127.0.0.1:5000/client
  #渠道页下载地址前缀
  channelDownloadUrl: https://i.yunduoyy.com/?code=

lykj:
  admin:
    img-host: http://localhost/images
    img-upload-path: D:\images\
    upload-secret: 8c76f778-66a4-4f74-9b4f-09079a7d607d
    download-url: https://yunduoyy.com/dl
# 阿里云系统通知推送配置
aliyun:
  push:
    env: DEV
    open: true