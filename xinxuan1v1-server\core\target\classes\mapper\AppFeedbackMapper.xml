<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppFeedbackMapper">

    <resultMap type="AppFeedback" id="AppFeedbackResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="typeContent" column="type_content"/>
        <result property="content" column="content"/>
        <result property="imgUrl" column="img_url"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="selectAppFeedbackVo">
        select id,
        user_id,
        type_content,
        content,
        img_url,
        create_time,
        update_time,
        create_by,
        update_by
        from app_feedback
    </sql>

    <select id="selectAppFeedbackList" parameterType="AppFeedback" resultMap="AppFeedbackResult">
        select f.id,
        f.user_id,
        f.type_content,
        f.content,
        f.img_url,
        f.create_time,
        f.update_time,
        f.create_by,
        f.update_by,
        u.phone as userPhone,
        u.recode_code as userRecodeCode,
        u.nick_name as userNickName,
        u.head_portrait as userHeadPortrait
        from app_feedback f,
        app_user u
        where f.user_id=u.id
        <if test="userId != null ">and f.user_id = #{userId}</if>
        <if test="typeContent != null  and typeContent != ''">
            and f.type_content like concat('%', #{typeContent}, '%')
        </if>
        <if test="userPhone != null  and userPhone != ''">
            and u.phone like concat('%', #{userPhone}, '%')
        </if>
        <!--        <if test="userRecodeCode != null  and userRecodeCode != ''">-->
        <!--            and u.recode_code like concat('%', #{userRecodeCode}, '%')-->
        <!--        </if>-->
        <if test="userRecodeCode != null  and userRecodeCode != ''">
            and u.recode_code = #{userRecodeCode}
        </if>
        <if test="userNickName != null  and userNickName != ''">
            and u.nick_name like concat('%', #{userNickName}, '%')
        </if>
        order by f.create_time desc
    </select>

    <select id="selectAppFeedbackById" parameterType="Long" resultMap="AppFeedbackResult">
        <include refid="selectAppFeedbackVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppFeedback" parameterType="AppFeedback" useGeneratedKeys="true" keyProperty="id">
        insert into app_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="typeContent != null">type_content,</if>
            <if test="content != null">content,</if>
            <if test="imgUrl != null">img_url,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="typeContent != null">#{typeContent},</if>
            <if test="content != null">#{content},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateAppFeedback" parameterType="AppFeedback">
        update app_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="typeContent != null">type_content = #{typeContent},</if>
            <if test="content != null">content = #{content},</if>
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppFeedbackById" parameterType="Long">
        delete
        from app_feedback
        where id = #{id}
    </delete>

    <delete id="deleteAppFeedbackByIds" parameterType="String">
        delete from app_feedback where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>