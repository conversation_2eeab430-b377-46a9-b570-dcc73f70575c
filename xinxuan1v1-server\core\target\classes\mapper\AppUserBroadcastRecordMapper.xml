<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppUserBroadcastRecordMapper">
    
    <resultMap type="com.hzy.core.entity.AppUserBroadcastRecord" id="AppUserBroadcastRecordResult">
        <id property="id" column="id"/>
        <result property="broadcastId" column="broadcast_id"/>
        <result property="userId" column="user_id"/>
        <result property="isRead" column="is_read"/>
        <result property="readTime" column="read_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>
    
    <sql id="selectAppUserBroadcastRecordVo">
        SELECT id, broadcast_id, user_id, is_read, read_time, create_by, create_time, update_by, update_time, remark
        FROM app_user_broadcast_record
    </sql>
    
    <!-- 查询用户接收的广播记录列表 -->
    <select id="selectUserRecordList" parameterType="Long" resultMap="AppUserBroadcastRecordResult">
        <include refid="selectAppUserBroadcastRecordVo"/>
        WHERE user_id = #{userId}
        ORDER BY create_time DESC
    </select>
    
    <!-- 检查用户是否已接收过指定广播 -->
    <select id="checkUserReceived" resultMap="AppUserBroadcastRecordResult">
        <include refid="selectAppUserBroadcastRecordVo"/>
        WHERE user_id = #{userId}
        AND broadcast_id = #{broadcastId}
        LIMIT 1
    </select>
    
    <!-- 批量插入用户广播接收记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO app_user_broadcast_record (broadcast_id, user_id, is_read, create_by, create_time, remark)
        VALUES
        <foreach collection="records" item="record" separator=",">
            (#{record.broadcastId}, #{record.userId}, #{record.isRead}, #{record.createBy}, NOW(), #{record.remark})
        </foreach>
    </insert>
    
    <!-- 统计广播的接收人数 -->
    <select id="countReceivedUsers" parameterType="Long" resultType="int">
        SELECT COUNT(1)
        FROM app_user_broadcast_record
        WHERE broadcast_id = #{broadcastId}
    </select>
    
    <!-- 统计广播的已读人数 -->
    <select id="countReadUsers" parameterType="Long" resultType="int">
        SELECT COUNT(1)
        FROM app_user_broadcast_record
        WHERE broadcast_id = #{broadcastId}
        AND is_read = 1
    </select>

    <!-- 根据广播ID和用户ID查询记录 -->
    <select id="getRecordByBroadcastIdAndUserId">
        <include refid="selectAppUserBroadcastRecordVo"/>
        WHERE broadcast_id = #{broadcastId}
        AND user_id = #{userId}
    </select>
    
    <!-- 直接标记广播为已读状态，仅当记录存在、属于用户且未读时更新 -->
    <update id="updateToReadDirectly">
        UPDATE app_user_broadcast_record
        SET is_read = 1, read_time = NOW()
        WHERE broadcast_id = #{broadcastId}
        AND user_id = #{userId}
        AND is_read = 0
    </update>
</mapper> 