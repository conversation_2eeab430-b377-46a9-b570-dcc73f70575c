<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.SysAppRoomTotalFlowMapper">
    <select id="getRoomTotalFlowList" resultType="com.hzy.core.entity.AppChatRoomListEntity">
        select cr.id as chatRoomId,
        cr.start_time as startTime,
        cr.name,
        crc.category_name as categoryName,
        if(cr.type=5,cr.user_id,cr.hall_owner_user_id) as createUserId,
        cr.password as password,
        cr.greeting_text as greetingText,
        cr.notice_text as noticeText,
        cr.avatar_url as avatarUrl,
        cr.type as chatRoomType,
        cr.third_id as thirdId,
        cr.status as `status`,
        cr.heat,
        cr.motorcade_type as motorcadeType,
        cr.father_id as fatherId,
        cr.label_json as labelJson,
        cr.background_url as backgroundUrl,
        cr.wheat_serving_type as wheatServingType,
        cr.is_hide_gift_gold as isHideGiftGold,
        cr.is_hot as isHot,
        cr.guild_id as guildId,
        cr.hall_owner_user_id,
        cr.hall_owner_commission_scale as hallOwnerCommissionScale,
        cr.gift_income_scale as giftIncomeScale,
        cr.create_time as createdTime,
        cr.update_time as updatedTime,
        COALESCE((select count(*)
        from app_chat_room_user cru
        where cru.is_del = false
        and cru.chat_room_id = cr.id
        and cru.is_join = true) ,0)as onlineUserCount,
        COALESCE((
        SELECT SUM(gift_gold_total_amount)
        FROM user_give_gift agg
        WHERE agg.chat_room_id = cr.id
        AND (
        CASE
        WHEN #{giftType} IS NOT NULL THEN agg.gift_type = #{giftType}
        ELSE TRUE END)), 0 ) AS totalFlow
        from app_chat_room cr
        left join app_chat_room_category crc ON cr.type = crc.id
        where cr.is_del = false
        <if test="keyWord != null">
            and (cr.name like concat('%',#{keyWord},'%') or cr.third_id = #{keyWord})
        </if>
        <if test="createUserId != null">
            and (if(cr.type=5, cr.user_id, cr.hall_owner_user_id) = #{createUserId})
        </if>
        <if test="chatRoomId !=null ">
            and cr.id = #{chatRoomId}
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(cr.update_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(cr.update_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        <if test="giftType != null">
            AND EXISTS (
            SELECT 1
            FROM user_give_gift agg
            WHERE agg.chat_room_id = cr.id
            AND agg.gift_type = #{giftType}
            )
        </if>
        <if test="orderType ==0">
            order by totalFlow desc
        </if>
        <if test="orderType ==1">
            order by totalFlow asc
        </if>
        <if test="orderType ==null">
            order by updatedTime desc
        </if>
    </select>
    <select id="getTotalFlow" resultType="java.math.BigDecimal" parameterType="java.lang.String">
        SELECT SUM(gift_gold_total_amount) AS totalFlow
        FROM user_give_gift agg
        left join app_chat_room as cr ON cr.id = agg.chat_room_id
        WHERE agg.chat_room_id = cr.id
        and cr.is_del = false
        <if test="keyWord != null">
            and (cr.name like concat('%',#{keyWord},'%') or cr.third_id = #{keyWord})
        </if>
        <if test="createUserId != null">
            and (if(cr.type=5, cr.user_id, cr.hall_owner_user_id) = #{createUserId})
        </if>
        <if test="chatRoomId !=null ">
            and cr.id = #{chatRoomId}
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(cr.update_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(cr.update_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        <if test="giftType != null">
            and agg.gift_type = #{giftType}
        </if>
    </select>
</mapper>