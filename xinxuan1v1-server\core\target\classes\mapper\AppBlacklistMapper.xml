<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppBlacklistMapper">

    <resultMap type="AppBlacklist" id="AppBlacklistResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="toUserId" column="to_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAppBlacklistVo">
        select id, user_id, to_user_id, create_time, update_time
        from app_blacklist
    </sql>

    <select id="selectAppBlacklistList" parameterType="AppBlacklist" resultMap="AppBlacklistResult">
        <include refid="selectAppBlacklistVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="toUserId != null ">and to_user_id = #{toUserId}</if>
        </where>
    </select>

    <select id="selectAppBlacklistById" parameterType="Long" resultMap="AppBlacklistResult">
        <include refid="selectAppBlacklistVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppBlacklist" parameterType="AppBlacklist" useGeneratedKeys="true" keyProperty="id">
        insert into app_blacklist
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="toUserId != null">to_user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="toUserId != null">#{toUserId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateAppBlacklist" parameterType="AppBlacklist">
        update app_blacklist
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="toUserId != null">to_user_id = #{toUserId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppBlacklistById" parameterType="Long">
        delete
        from app_blacklist
        where id = #{id}
    </delete>

    <delete id="deleteAppBlacklistByIds" parameterType="String">
        delete from app_blacklist where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectAppBlacklistByUserIdAndToUserId" parameterType="java.lang.Long" resultMap="AppBlacklistResult">
        select id, user_id, to_user_id, create_time, update_time
        from app_blacklist
        where user_id = #{userId}
        and to_user_id = #{toUserId}
    </select>


    <select id="getUserBlackList" resultType="com.hzy.core.model.vo.app.AppViewUserInfoVo">
        select u.id as userId,
        u.recode_code as recodeCode,
        u.is_online as isOnline,
        u.last_operating_time as lastOperatingTime,
        u.photo_album as photoAlbumStr,
        u.nick_name as nickName,
        u.is_real_person_auth as isRealPersonAuth,
        u.is_real_name_auth as isRealNameAuth,
        if(u.phone!=''and u.phone is not null, 1, 0) as isPhoneAuth,
        u.birthday,
        u.age,
        u.sex,
        u.personal_signature as personalSignature,
        u.head_portrait as headPortrait,
        u.education_background as educationBackground,
        u.constellation,
        u.height,
        u.weight,
        u.location,
        u.annual_income as annualIncome,
        u.purchase_situation as purchaseSituation,
        u.car_purchase_situation as carPurchaseSituation,
        u.self_introduction as selfIntroduction,
        u.label,
        u.voice_signature as voiceSignature,
        u.longitude,
        u.latitude,
        u.province,
        u.city,
        u.area
        from app_blacklist bl,
        app_user u
        where bl.to_user_id = u.id
        and bl.user_id = #{userId}
        order by bl.id desc
    </select>
</mapper>