<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppFrontPageMapper">

    <resultMap type="AppFrontPage" id="AppFrontPageResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="userId" column="user_id"/>
        <result property="content" column="content"/>
        <result property="isDel" column="is_del"/>
        <result property="chatRoomId" column="chat_room_id"/>
        <result property="price" column="price"/>
    </resultMap>

    <sql id="selectAppFrontPageVo">
        select id, create_time, user_id, content, is_del, chat_room_id, price from app_front_page
    </sql>

    <select id="selectAppFrontPageList" parameterType="AppFrontPage" resultMap="AppFrontPageResult">
        <include refid="selectAppFrontPageVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="content != null  and content != ''">and content = #{content}</if>
            <if test="isDel != null ">and is_del = #{isDel}</if>
            <if test="chatRoomId != null ">and chat_room_id = #{chatRoomId}</if>
            <if test="price != null ">and price = #{price}</if>
        </where>
    </select>

    <select id="selectAppFrontPageById" parameterType="Long" resultMap="AppFrontPageResult">
        <include refid="selectAppFrontPageVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppFrontPage" parameterType="AppFrontPage" useGeneratedKeys="true" keyProperty="id">
        insert into app_front_page
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="content != null">content,</if>
            <if test="isDel != null">is_del,</if>
            <if test="chatRoomId != null">chat_room_id,</if>
            <if test="price != null">price,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="content != null">#{content},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="chatRoomId != null">#{chatRoomId},</if>
            <if test="price != null">#{price},</if>
        </trim>
    </insert>

    <update id="updateAppFrontPage" parameterType="AppFrontPage">
        update app_front_page
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="chatRoomId != null">chat_room_id = #{chatRoomId},</if>
            <if test="price != null">price = #{price},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteAppFrontPageById" parameterType="Long">
        update app_front_page set is_del=true where id = #{id}
    </update>

    <delete id="deleteAppFrontPageByIds" parameterType="String">
        delete from app_front_page where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getNewFrontPageInfo" resultType="com.hzy.core.model.vo.app.AppFrontPageVo">
        select
        id as frontPageId,
        create_time as frontPageCreateTime,
        user_id as frontPageUserId,
        content as frontPageContent,
        chat_room_id as frontPageChatRoomId,
        price as frontPagePrice
        from app_front_page
        where is_del=false
        order by id desc
        limit 0,1
    </select>


    <select id="getFrontPageList" resultType="com.hzy.core.model.vo.app.AppFrontPageVo">
        select
        id as frontPageId,
        create_time as frontPageCreateTime,
        user_id as frontPageUserId,
        content as frontPageContent,
        chat_room_id as frontPageChatRoomId,
        price as frontPagePrice
        from app_front_page
        where is_del=false
        order by id desc
    </select>
</mapper>