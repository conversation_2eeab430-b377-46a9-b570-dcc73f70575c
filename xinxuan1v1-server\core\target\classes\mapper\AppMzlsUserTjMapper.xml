<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppMzlsUserTjMapper">

    <resultMap type="AppMzlsUserTj" id="AppMzlsUserTjResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="jjcId" column="jjc_id"/>
        <result property="srSum" column="sr_sum"/>
        <result property="zcSum" column="zc_sum"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAppMzlsUserTjVo">
        select id, user_id, jjc_id, sr_sum, zc_sum, create_time, update_time,game_id,`type` from app_mzls_user_tj
    </sql>
    <insert id="insertAppMzlsUserTj">
        INSERT INTO app_mzls_user_tj
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="jjcId != null">jjc_id,</if>
            <if test="srSum != null">sr_sum,</if>
            <if test="zcSum != null">zc_sum,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="gameId != null">game_id,</if>
            <if test="userLoginIp != null">user_login_ip,</if>
            <if test="userLoginDeviceId != null">device_id,</if>
            <if test="type != null">`type`,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="jjcId != null">#{jjcId},</if>
            <if test="srSum != null">#{srSum},</if>
            <if test="zcSum != null">#{zcSum},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="gameId != null">#{gameId},</if>
            <if test="userLoginIp != null">#{userLoginIp},</if>
            <if test="userLoginDeviceId != null">#{userLoginDeviceId},</if>
            <if test="type != null">#{type},</if>
        </trim>
    </insert>
    <update id="updateAppMzlsUserTj">
        update app_mzls_user_tj
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="jjcId != null">jjc_id = #{jjcId},</if>
            <if test="srSum != null">sr_sum = #{srSum},</if>
            <if test="zcSum != null">zc_sum = #{zcSum},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userLoginDeviceId != null">device_id=#{userLoginDeviceId},</if>
            <if test="userLoginIp != null">user_login_ip=#{userLoginIp},</if>
            <if test="type != null">`type`=#{type},</if>
        </trim>
        where id = #{id}
    </update>

    <!--<select id="selectAppMzlsUserTjList" resultType="com.hzy.core.entity.AppMzlsUserTj">
        select t.id, t.user_id, t.jjc_id, t.sr_sum, t.zc_sum, t.create_time,t.update_time,
        u.recode_code as recodeCode,
        u.nick_name nickName,
        u.phone as phone,
        u.head_portrait as headPortrait
        from app_mzls_user_tj t
        LEFT JOIN app_user u on(u.id=t.user_id)

        where 1=1
        <if test="recodeCode!=null and recodeCode!=''">
            and u.recode_code  like concat('%', #{recodeCode}, '%')
        </if>
        <if test="nickName!=null and nickName!=''">
            and u.nick_name  like concat('%', #{nickName}, '%')
        </if>
        <if test="phone!=null and phone!=''">
            and u.phone  like concat('%', #{phone}, '%')
        </if>

        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(t.create_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(t.create_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        order by  t.id desc
    </select>-->

    <select id="selectAppMzlsUserTjList" parameterType="AppMzlsUserTj" resultMap="AppMzlsUserTjResult">
        select t.id, t.user_id, t.jjc_id, t.sr_sum, t.zc_sum, t.create_time,t.update_time,
        t.game_id,
        u.recode_code as recodeCode,
        u.nick_name nickName,
        u.phone as phone,
        u.head_portrait as headPortrait,
        gc.name as gameName,
        t.device_id as userLoginDeviceId,
        t.user_login_ip as userLoginIp,
        t.type as `type`
        from app_mzls_user_tj t
        LEFT JOIN app_user u on(u.id=t.user_id)
        LEFT JOIN game_config as gc ON gc.id=t.game_id
        where 1=1
        <if test="gameId!=null">
            and t.game_id =#{gameId}
        </if>
          <if test="type!=null">
            and t.type =#{type}
        </if>
        <if test="userId!=null">
            and u.id = #{userId}
        </if>
        <if test="recodeCode!=null">
            and u.recode_code = #{recodeCode}
        </if>
        <if test="nickName!=null and nickName!=''">
            and u.nick_name like concat('%', #{nickName}, '%')
        </if>
        <if test="phone!=null and phone!=''">
            and u.phone like concat('%', #{phone}, '%')
        </if>
        <if test="userLoginDeviceId!=null and userLoginDeviceId!=''">
            and t.device_id = #{userLoginDeviceId}
        </if>
        <if test="userLoginIp!=null and userLoginIp!=''">
            and t.user_login_ip =#{userLoginIp}
        </if>

        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(t.update_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(t.update_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        order by t.update_time desc, t.create_time desc
    </select>


    <select id="selectAppMzlsUserTjTotal" parameterType="AppMzlsUserTj"
            resultType="com.hzy.core.model.vo.admin.InPutUserVo">
        select
        SUM(IFNULL(t.sr_sum, 0)) AS incomeTotal,
        SUM(IFNULL(t.zc_sum, 0)) AS expenseTotal
        from app_mzls_user_tj t
        LEFT JOIN app_user u on(u.id=t.user_id)

        where 1=1
        <if test="gameId!=null">
            and t.game_id =#{gameId}
        </if>
        <if test="type!=null">
            and t.type =#{type}
        </if>
        <if test="userId!=null">
            and u.id = #{userId}
        </if>
        <if test="nickName!=null and nickName!=''">
            and u.nick_name like concat('%', #{nickName}, '%')
        </if>
        <if test="phone!=null and phone!=''">
            and u.phone like concat('%', #{phone}, '%')
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(t.update_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(t.update_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        order by t.id desc
    </select>


    <select id="selectAppMzlsUserTjById" parameterType="Long" resultMap="AppMzlsUserTjResult">
        <include refid="selectAppMzlsUserTjVo"/>
        where id = #{id}
    </select>


    <delete id="deleteAppMzlsUserTjById" parameterType="Long">
        delete from app_mzls_user_tj where id = #{id}
    </delete>

    <delete id="deleteAppMzlsUserTjByIds" parameterType="String">
        delete from app_mzls_user_tj where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getUserAppMzlsUserTj" parameterType="Long" resultMap="AppMzlsUserTjResult">
        <include refid="selectAppMzlsUserTjVo"/>
        where user_id = #{userId}
        and game_id = #{gameId}
        and `type` = #{type}
        order by id desc limit 0,1
    </select>

    <select id="summery" resultType="com.hzy.core.model.vo.TjSummery">
        select sum(amut.sr_sum) as totalIncome, sum(amut.zc_sum) as totalSpend
        from app_mzls_user_tj amut
        where 1=1
        <if test="gameId!=null">
            and amut.game_id =#{gameId}
        </if>
          <if test="type!=null">
            and amut.type =#{type}
        </if>
        <if test="recodeCode!=null and recodeCode!=''">
            and u.recode_code like concat('%', #{recodeCode}, '%')
        </if>
        <if test="nickName!=null and nickName!=''">
            and u.nick_name like concat('%', #{nickName}, '%')
        </if>
        <if test="phone!=null and phone!=''">
            and u.phone like concat('%', #{phone}, '%')
        </if>

        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(t.create_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(t.create_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
    </select>

</mapper>