<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.candy.CandyGrabRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hzy.core.entity.candy.CandyGrabRecord">
        <id column="grab_id" property="grabId" />
        <result column="user_id" property="userId" />
        <result column="activity_id" property="activityId" />
        <result column="period_no" property="periodNo" />
        <result column="candy_cost" property="candyCost" />
        <result column="gold_cost" property="goldCost" />
        <result column="is_gold_exchange" property="isGoldExchange" />
        <result column="result_candy" property="resultCandy" />
        <result column="bonus_gold" property="bonusGold" />
        <result column="is_min_winner" property="isMinWinner" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        grab_id, user_id, activity_id, period_no, candy_cost, gold_cost, is_gold_exchange, result_candy, bonus_gold, is_min_winner, status, create_time, update_time
    </sql>
    <select id="getUserListByActivityId" resultType="com.hzy.core.model.vo.app.CandyInfoVo$UserInfo">
        SELECT
            au.id AS userId,
            au.nick_name AS nickName,
            au.head_portrait AS avatarUrl
        FROM
            candy_grab_record cgr
            LEFT JOIN app_user au ON cgr.user_id = au.id
        WHERE
            cgr.activity_id = #{activityId}
        ORDER BY
            cgr.create_time DESC
    </select>
    <select id="getRecordAndUserInfoByActivityId" resultType="com.hzy.core.model.vo.app.CandyGrabRecordAndUserInfoVo">
        SELECT
            au.id AS userId,
            au.nick_name AS nickName,
            au.head_portrait AS avatarUrl,
            cgr.grab_id AS grabId,
            cgr.activity_id AS activityId,
            cgr.period_no AS periodNo,
            cgr.candy_cost AS candyCost,
            cgr.gold_cost AS goldCost,
            cgr.is_gold_exchange AS isGoldExchange,
            cgr.result_candy AS resultCandy,
            cgr.bonus_gold AS bonusGold,
            cgr.is_min_winner AS isMinWinner,
            cgr.status AS status,
            cgr.create_time AS createTime,
            cgr.update_time AS updateTime
        FROM
            candy_grab_record cgr
            LEFT JOIN app_user au ON cgr.user_id = au.id
        WHERE
            cgr.activity_id = #{activityId}
        ORDER BY
            cgr.create_time DESC
    </select>
    <select id="selectRecordList" resultType="com.hzy.core.model.dto.app.CandyGrabRecordDto">
        SELECT
            cgr.grab_id AS grabId,
            au.recode_code AS userId,
            au.nick_name AS nickName,
            cgr.activity_id AS activityId,
            cgr.period_no AS periodNo,
            cgr.candy_cost AS candyCost,
            cgr.gold_cost AS goldCost,
            cgr.is_gold_exchange AS isGoldExchange,
            cgr.result_candy AS resultCandy,
            cgr.bonus_gold AS bonusGold,
            cgr.is_min_winner AS isMinWinner,
            cgr.status AS status,
            cgr.create_time AS createTime,
            cgr.update_time AS updateTime
        FROM
            candy_grab_record cgr
        left join app_user au on cgr.user_id = au.id
        WHERE
            1 = 1
        <if test="activityId != null">
            AND cgr.activity_id = #{activityId}
        </if>
        <if test="userId != null">
            AND cgr.user_id = #{userId}
        </if>
        <if test="periodNo != null">
            AND cgr.period_no = #{periodNo}
        </if>
        <if test="status != null">
            AND cgr.status = #{status}
        </if>
        <if test="isMinWinner != null">
            AND cgr.is_min_winner = #{isMinWinner}
        </if>
        <if test="recodeCode != null">
            AND au.recode_code = #{recodeCode}
        </if>
        <if test="startTime != null and endTime != null">
            AND cgr.create_time BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="startTime != null and endTime == null">
            AND cgr.create_time &gt;= #{startTime}
        </if>
        <if test="startTime == null and endTime != null">
            AND cgr.create_time &lt;= #{endTime}
        </if>
        ORDER BY
            cgr.grab_id DESC
    </select>

</mapper>
