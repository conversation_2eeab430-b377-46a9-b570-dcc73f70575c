<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppForbiddenEquipmentMapper">

    <resultMap type="AppForbiddenEquipment" id="AppForbiddenEquipmentResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="equipmentId" column="equipment_id"/>
        <result property="equipmentType" column="equipment_type"/>
        <result property="createTime" column="create_time"/>
        <result property="endTime" column="end_time"/>
    </resultMap>

    <sql id="selectAppForbiddenEquipmentVo">
        select id, user_id, equipment_id, equipment_type, create_time, end_time from app_forbidden_equipment
    </sql>

    <select id="selectAppForbiddenEquipmentList" parameterType="AppForbiddenEquipment"
            resultMap="AppForbiddenEquipmentResult">
        <include refid="selectAppForbiddenEquipmentVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="equipmentId != null  and equipmentId != ''">and equipment_id = #{equipmentId}</if>
            <if test="equipmentType != null ">and equipment_type = #{equipmentType}</if>
            <if test="endTime != null ">and end_time = #{endTime}</if>
        </where>
    </select>

    <select id="selectAppForbiddenEquipmentById" parameterType="Long" resultMap="AppForbiddenEquipmentResult">
        <include refid="selectAppForbiddenEquipmentVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppForbiddenEquipment" parameterType="AppForbiddenEquipment" useGeneratedKeys="true"
            keyProperty="id">
        insert into app_forbidden_equipment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="equipmentId != null">equipment_id,</if>
            <if test="equipmentType != null">equipment_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="endTime != null">end_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="equipmentId != null">#{equipmentId},</if>
            <if test="equipmentType != null">#{equipmentType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="endTime != null">#{endTime},</if>
        </trim>
    </insert>

    <update id="updateAppForbiddenEquipment" parameterType="AppForbiddenEquipment">
        update app_forbidden_equipment
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="equipmentId != null">equipment_id = #{equipmentId},</if>
            <if test="equipmentType != null">equipment_type = #{equipmentType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppForbiddenEquipmentById" parameterType="Long">
        delete from app_forbidden_equipment where id = #{id}
    </delete>

    <delete id="deleteAppForbiddenEquipmentByIds" parameterType="String">
        delete from app_forbidden_equipment where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="isForbiddenEquipment" resultType="java.lang.Boolean">
        select if(count(*)>0,1,0) from
        app_forbidden_equipment
        where equipment_id=#{equipmentId}
        and equipment_type=#{equipmentType}
        and now() &lt;= end_time
    </select>

    <update id="relieveForbidden">
        delete from app_forbidden_equipment
        where equipment_id=#{equipmentId}
        and equipment_type=#{equipmentType}
    </update>
</mapper>