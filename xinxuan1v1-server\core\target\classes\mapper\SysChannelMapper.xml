<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.SysChannelMapper">

    <resultMap type="SysChannel" id="SysChannelResult">
        <result property="id" column="id"/>
        <result property="channelName" column="channel_name"/>
        <result property="parentId" column="parent_id"/>
        <result property="createdBy" column="create_by"/>
        <result property="updatedBy" column="update_by"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedTime" column="updated_time"/>
        <result property="deleted" column="deleted"/>
        <result property="chatRoomId" column="chat_room_id"/>
    </resultMap>

    <sql id="selectSysChannelVo">
        select id, channel_name, parent_id, created_by, updated_by,
        from sys_channel
    </sql>


    <select id="checkChannelNameUnique" resultType="com.hzy.core.entity.SysChannel">
        select id, channel_name, parent_id
        from sys_channel
        where channel_name = #{channelName}
          and parent_id = #{parentId}
    </select>
    <select id="hasChildByChannelId" resultType="java.lang.Integer">
        select count(1)
        from sys_channel
        where parent_id = #{channelId}
          and deleted = 0
    </select>


    <select id="checkChannelExistUser" resultType="com.hzy.core.entity.SysChannel">
        select id
        from channel_user_relation as cur
        where channel_id = #{channelId}
          and cur.deleted = 0
    </select>
    <select id="selectSysChannelList" resultType="com.hzy.core.entity.SysChannel">
        select
        sc.id,
        sc.channel_name,
        sc.parent_id,
        sc.created_by,
        sc.updated_by,
        sc.created_time,
        sc.updated_time,
        sc.code,
        sc.invite_code as inviteCodeUrl,
        sc.sys_user_id as sysUserId,
        (SELECT count(DISTINCT cur.user_id)
        FROM channel_user_relation cur
        RIGHT JOIN app_user as au ON au.id = cur.user_id
        WHERE cur.channel_id = sc.id
        AND cur.deleted=0 AND au.up_channel_code IS NOT NULL) AS sum
        from sys_channel as sc
        where
        sc.deleted=0
        <if test="channelName != null and channelName != ''">
            AND channel_name like concat('%', #{channelName}, '%')
        </if>

        <if test="parentId != null ">
            AND parent_id = #{parentId}
        </if>
        <if test="sysUserId != null ">
            AND sys_user_id = #{sysUserId}
        </if>
        <if test="sysUserIds != null ">
            AND sys_user_id in
            <foreach collection="sysUserIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>


    </select>


    <select id="selectSysChannelById" resultType="com.hzy.core.entity.SysChannel">
        select
        id,
        channel_name as channelName,
        parent_id as parentId,
        created_by as createdBy,
        updated_by as updatedBy,
        created_time as createdTime,
        updated_time as updatedTime,
        code as code,
        chat_room_id as chatRoomId
        from sys_channel
        where id = #{id}
        <if test="channelName != null and channelName !='' ">
            and channel_name like concat('%', #{channelName}, '%')
        </if>
    </select>
    <select id="queryChannelById" resultType="com.hzy.core.entity.SysChannel">
        select id,
               channel_name as channelName,
               parent_id    as parentId,
               created_by   as createdBy,
               updated_by   as updatedBy,
               created_time as createdTime,
               updated_time as updatedTime,
               code         as code,
               chat_room_id as chatRoomId
        from sys_channel
        where id = #{id}
    </select>
    <select id="selectSysChannelByName" resultType="com.hzy.core.entity.SysChannel"
            parameterType="com.hzy.core.entity.SysChannel">
        select
        id,
        channel_name as channelName,
        parent_id as parentId,
        created_by as createdBy,
        updated_by as updatedBy,
        created_time as createdTime,
        updated_time as updatedTime,
        invite_code as intervalCodeUrl,
        code as code,
        chat_room_id as chatRoomId
        from sys_channel
        where channel_name = #{channelName}
        and deleted=0
        <if test="parentId != null ">
            and parent_id = #{parentId}
        </if>
    </select>
    <select id="selectSysChannelByInviteCode" resultType="com.hzy.core.entity.SysChannel"
            parameterType="com.hzy.core.entity.SysChannel">
        select id,
               channel_name as channelName,
               parent_id    as parentId,
               created_by   as createdBy,
               updated_by   as updatedBy,
               created_time as createdTime,
               updated_time as updatedTime,
               invite_code  as intervalCodeUrl,
               code         as code,
               chat_room_id as chatRoomId
        from sys_channel
        where code = #{inviteCodeUrl}
          and deleted = 0
    </select>
    <select id="selectSysChannelName"
            parameterType="com.hzy.core.entity.SysChannel" resultType="com.hzy.core.entity.SysChannel">
        select id,
               channel_name as channelName,
               parent_id    as parentId,
               created_by   as createdBy,
               updated_by   as updatedBy,
               created_time as createdTime,
               updated_time as updatedTime,
               invite_code  as intervalCodeUrl,
               code         as code,
               chat_room_id as chatRoomId
        from sys_channel
        where channel_name = #{channelName}
          and deleted = 0
    </select>


    <insert id="insertSysChannel" parameterType="string" useGeneratedKeys="true" keyProperty="id">
        insert into sys_channel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="channelName != null and channelName != ''">channel_name,</if>
            <if test="inviteCodeUrl != null and inviteCodeUrl !=''">invite_code,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="deleted != null">deleted,</if>
            <if test="chatRoomId != null">chat_room_id,</if>
            <if test="code != null">code,</if>
            <if test="sysUserId != null">sys_user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="channelName != null and channelName != ''">#{channelName},</if>
            <if test="inviteCodeUrl != null and inviteCodeUrl !=''">#{inviteCodeUrl},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
            <if test="deleted != null">#{deleted},</if>
            <if test="chatRoomId != null">#{chat_room_id},</if>
            <if test="code != null">#{code},</if>
            <if test="sysUserId != null">#{sysUserId},</if>
        </trim>
    </insert>


    <update id="deleteSysChannelById" parameterType="Long">
        update sys_channel
        set deleted=1
        where id = #{id}
    </update>

    <update id="deleteSysChannelByIds" parameterType="String">
        update sys_channel set deleted=1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>

    </update>
    <update id="updateSysChannel">
        UPDATE sys_channel
        <set>
            <trim suffixOverrides=",">
                <if test="channelName != null and channelName != ''">
                    channel_name = #{channelName},
                </if>
                <if test="createdBy != null">
                    created_by = #{createdBy},
                </if>
                <if test="updatedBy != null">
                    updated_by = #{updatedBy},
                </if>
                <if test="updatedTime != null">
                    updated_time = #{updatedTime}
                </if>
            </trim>
        </set>
        WHERE id = #{id}
    </update>
</mapper>