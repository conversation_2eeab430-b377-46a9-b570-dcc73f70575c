<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.candy.CandyRankingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hzy.core.entity.candy.CandyRanking">
        <id column="ranking_id" property="rankingId" />
        <result column="user_id" property="userId" />
        <result column="month" property="month" />
        <result column="total_candy" property="totalCandy" />
        <result column="ranking" property="ranking" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ranking_id, user_id, month, total_candy, ranking, create_time, update_time
    </sql>
    
    <!-- 查询糖果排行榜列表 -->
    <select id="selectRankingList" resultType="com.hzy.core.model.dto.app.CandyRankingDto">
        SELECT
            cr.ranking_id AS rankingId,
            cr.user_id AS userId,
            au.recode_code AS recodeCode,
            au.nick_name AS nickName,
            cr.month AS month,
            cr.total_candy AS totalCandy,
            cr.ranking AS ranking,
            cr.create_time AS createTime,
            cr.update_time AS updateTime
        FROM
            candy_ranking cr
        LEFT JOIN app_user au ON cr.user_id = au.id
        WHERE
            1 = 1
        <if test="userId != null">
            AND cr.user_id = #{userId}
        </if>
        <if test="month != null and month != ''">
            AND cr.month = #{month}
        </if>
        <if test="ranking != null">
            AND cr.ranking = #{ranking}
        </if>
        <if test="recodeCode != null and recodeCode != ''">
            AND au.recode_code = #{recodeCode}
        </if>
        <if test="nickName != null and nickName != ''">
            AND au.nick_name LIKE CONCAT('%', #{nickName}, '%')
        </if>
        ORDER BY
            cr.ranking ASC
    </select>

</mapper>
