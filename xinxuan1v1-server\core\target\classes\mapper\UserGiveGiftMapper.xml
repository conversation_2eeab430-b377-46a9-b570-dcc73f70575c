<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.UserGiveGiftMapper">
    <select id="selectGiveGiftPage" resultType="com.hzy.core.model.vo.admin.UserGiveGiftVo"
            parameterType="com.hzy.core.model.dto.admin.QueryUserGiveGiftDTO">
        SELECT
        gift.id AS id,
        gift.user_id AS userId,
        au.nick_name AS username,
        au.recode_code AS recodeCode,
        gift.to_user_id to_userId,
        au2.nick_name to_username,
        au2.recode_code AS toRecodeCode,
        gift.hall_owner_user_id hall_user_id,
        au3.nick_name hallUsername,
        room.id AS chatRoomId,
        room.name AS chatRoomName,
        gift.gift_id AS giftId,
        ag.gift_name AS giftName,
        gift.gift_num,
        gift.gift_gold_total_amount,
        gift.gift_gold_unit_amount,
        gift.receive_point_total_amount receivePointTotalAmount,
        gift.hall_point_total_amount hallPointTotalAmount,
        gift.profit_amount as profitAmount,
        gift.created_time AS createTime,
        gift.gift_type AS giftType
        FROM
        user_give_gift gift
        LEFT JOIN app_user au ON au.id = gift.user_id
        left join app_user au2 on au2.id = gift.to_user_id
        left join app_user au3 on au3.id = gift.hall_owner_user_id
        LEFT JOIN app_chat_room room ON room.id = gift.chat_room_id
        LEFT JOIN app_gift ag ON ag.id = gift.gift_id
        WHERE gift.deleted = 0
        <if test="queryDTO.userName != null and queryDTO.userName != ''">
            AND au.nick_name = #{queryDTO.userName}
        </if>
        <if test="queryDTO.recodeCode != null and queryDTO.recodeCode != ''">
            AND au.recode_code = #{queryDTO.recodeCode}
        </if>
        <if test="queryDTO.type == 1">
            AND gift.chat_room_id IS NULL
        </if>
        <if test="queryDTO.type == 2">
            AND gift.chat_room_id IS not NULL
        </if>
        <if test="queryDTO.toUserName != null and queryDTO.toUserName != ''">
            AND au2.nick_name = #{queryDTO.toUserName}
        </if>
        <if test="queryDTO.toRecodeCode != null and queryDTO.toRecodeCode != ''">
            AND au2.recode_code = #{queryDTO.toRecodeCode}
        </if>
        <if test="queryDTO.chatRoomId != null">
            AND gift.chat_room_id = #{queryDTO.chatRoomId}
        </if>
        <if test="queryDTO.toUserId != null">
            AND gift.to_user_id = #{queryDTO.toUserId}
        </if>
        <if test="queryDTO.userId != null">
            AND gift.user_id = #{queryDTO.userId}
        </if>

        <if test="queryDTO.chatRoomName != null">
            AND room.name = #{queryDTO.chatRoomName}
        </if>
        <if test="queryDTO.queryBeginTime != null and queryDTO.queryBeginTime != ''"><!-- 开始时间检索 -->
            AND date_format(gift.created_time,'%y%m%d') &gt;= date_format(#{queryDTO.queryBeginTime},'%y%m%d')
        </if>
        <if test="queryDTO.queryEndTime != null and queryDTO.queryEndTime != ''"><!-- 结束时间检索 -->
            AND date_format(gift.created_time,'%y%m%d') &lt;= date_format(#{queryDTO.queryEndTime},'%y%m%d')
        </if>
        <if test="queryDTO.giftType != null">
            AND gift_type = #{queryDTO.giftType}
        </if>
        order by gift.updated_time desc,gift.created_time desc
    </select>

    <select id="getSumAmount" resultType="com.hzy.core.model.vo.admin.GiveGiftTotalVO">
        SELECT IFNULL(SUM(gift.gift_gold_total_amount), 0) AS giftTotalAmount,
        IFNULL(SUM(gift.receive_point_total_amount), 0) AS receiveTotalAmount,
        IFNULL(SUM(gift.hall_point_total_amount), 0) AS hallTotalAmount,
        IFNULL(SUM(gift.profit_amount), 0) AS profitTotalAmount
        FROM
        user_give_gift gift
        LEFT JOIN
        app_user au ON au.id = gift.user_id
        LEFT JOIN
        app_user au2 ON au2.id = gift.to_user_id
        LEFT JOIN
        app_chat_room room ON room.id = gift.chat_room_id
        LEFT JOIN
        app_gift ag ON ag.id = gift.gift_id
        WHERE
        gift.deleted = 0
        <if test="toUserName != null and toUserName != ''">
            AND au2.nick_name = #{toUserName}
        </if>
        <if test="toUserId != null">
            AND gift.to_user_id = #{toUserId}
        </if>
        <if test="toRecodeCode != null and toRecodeCode != ''">
            AND au2.recode_code = #{toRecodeCode}
        </if>
        <if test="userName != null and userName != ''">
            AND au.nick_name = #{userName}
        </if>
        <if test="userId != null">
            AND gift.user_id = #{userId}
        </if>
        <if test="recodeCode != null and recodeCode != ''">
            AND au.recode_code = #{recodeCode}
        </if>
        <if test="chatRoomId != null">
            AND gift.chat_room_id = #{chatRoomId}
        </if>
        <if test="chatRoomName != null and chatRoomName != ''">
            AND room.name = #{chatRoomName}
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            AND date_format(gift.created_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            AND date_format(gift.created_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        <if test="giftType != null">
            AND gift_type = #{giftType}
        </if>
        <if test="type == 1">
            AND gift.chat_room_id IS NULL
        </if>
        <if test="type == 2">
            AND gift.chat_room_id IS not NULL
        </if>

    </select>


    <!--<select id="getSumAmount" resultType="java.math.BigDecimal" parameterType="com.hzy.core.model.dto.admin.QueryUserGiveGiftDTO">
        SELECT IFNULL(SUM(gift.gift_gold_unit_amount), 0) AS totalAmount
        FROM
        user_give_gift gift
        LEFT JOIN
        app_user au ON au.id = gift.user_id
        LEFT JOIN
        app_chat_room room ON room.id = gift.chat_room_id
        LEFT JOIN
        app_gift ag ON ag.id = gift.gift_id
        WHERE
        gift.deleted = 0
        <if test="userName != null">
            AND au.nick_name = #{userName}
        </if>
        <if test="userId != null">
            AND gift.user_id = #{userId}
        </if>
        <if test="chatRoomId != null">
            AND gift.chat_room_id = #{chatRoomId}
        </if>
        <if test="chatRoomName != null">
            AND room.name = #{chatRoomName}
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">&lt;!&ndash; 开始时间检索 &ndash;&gt;
            AND date_format(gift.created_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">&lt;!&ndash; 结束时间检索 &ndash;&gt;
            AND date_format(gift.created_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
    </select>-->

    <insert id="saveGiveGift" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_give_gift(user_id, to_user_id,
                                   hall_owner_user_id,
                                   gift_id,
                                   gift_name,
                                   gift_type,
                                   gift_gold_unit_amount,
                                   gift_num,
                                   luck_gift_id,
                                   luck_gift_name,
                                   gift_gold_total_amount,
                                   receive_point_total_amount,
                                   hall_point_total_amount,
                                   profit_amount,
                                   chat_room_id,
                                   created_time,
                                   deleted,
                                   guild_id)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.userId},
             #{item.toUserId},
             #{item.hallOwnerUserId},
             #{item.giftId},
             #{item.giftName},
             2,
             #{item.giftGoldUnitAmount},
             #{item.giftNum},
             #{luck_gift_id},
             #{luck_gift_name},
             #{item.giftGoldTotalAmount},
             #{item.receivePointTotalAmount},
             #{item.hallPointTotalAmount},
             #{item.profitAmount},
             #{item.chatRoomId},
             #{item.createdTime},
             0,
             #{item.guildId})
        </foreach>
    </insert>
    <select id="homeGiftTop" resultType="com.hzy.core.model.vo.app.AppGiftTopVo">
        SELECT ugg.id,
            ugg.user_id,
               au.nick_name      as user_name,
               au.head_portrait  AS user_tx,
               ugg.to_user_id,
               au2.nick_name     as to_user_name,
               au2.head_portrait AS to_user_tx,
               ugg.gift_name,
               ag.img_url as gift_img,
               ugg.gift_num,
               ugg.chat_room_id,
               ugg.created_time,
               gift_gold_unit_amount as amount
        FROM user_give_gift ugg
                 LEFT JOIN
             app_user au
             ON
                 ugg.user_id = au.id
                 LEFT JOIN
             app_user au2
             ON
                 ugg.to_user_id = au2.id
        left join app_gift ag on ugg.gift_id = ag.id
        WHERE ugg.gift_num > 0
          and ugg.deleted =0
          and ugg.gift_gold_unit_amount >= #{amount}
        ORDER BY ugg.id desc limit 0,20;
    </select>

    <select id="selectLuckRecordList" resultType="com.hzy.core.model.vo.admin.LuckRecordListVo">
        SELECT au.nick_name,
               au.phone,
               ou.nick_name                    as to_nick_name,
               ou.phone                        as to_phone,
               ag.masonry_price * ugg.gift_num AS sum_price,
               ugg.*
        FROM user_give_gift ugg
                 left join app_gift ag
                           on
                               ugg.luck_gift_id = ag.id
                 left join app_user au
                           on
                               ugg.user_id = au.id
                 left join app_user ou
                           on
                               ugg.to_user_id = ou.id
        WHERE ugg.luck_gift_id IS NOT NULL
        order by ugg.id desc
    </select>
</mapper>
