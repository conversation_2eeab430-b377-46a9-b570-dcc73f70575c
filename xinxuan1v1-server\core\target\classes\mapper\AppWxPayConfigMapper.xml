<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppWxPayConfigMapper">

    <resultMap type="AppWxPayConfig" id="AppWxPayConfigResult">
        <result property="id" column="id"/>
        <result property="isDel" column="is_del"/>
        <result property="isEnable" column="is_enable"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="appId" column="app_id"/>
        <result property="mchId" column="mch_id"/>
        <result property="mchKey" column="mch_key"/>
        <result property="name" column="name"/>
    </resultMap>

    <resultMap type="com.hzy.core.model.vo.app.AppWxPayConfigVo" id="AppWxPayConfigVoResult">
        <result property="id" column="id"/>
        <result property="isDel" column="is_del"/>
        <result property="isEnable" column="is_enable"/>
        <result property="appId" column="app_id"/>
        <result property="mchId" column="mch_id"/>
        <result property="mchKey" column="mch_key"/>
    </resultMap>

    <select id="getEnableWxPayConfig" resultMap="AppWxPayConfigVoResult">
        select id, is_del, is_enable,app_id, mch_id, mch_key from
        app_wx_pay_config
        where is_del=false and is_enable=true
        order by id desc limit 0,1
    </select>

    <select id="isHaveEnableWxPayConfig" resultType="java.lang.Integer">
        select 1 from
        app_wx_pay_config
        where is_del=false and is_enable=true
        order by id desc limit 0,1
    </select>

    <sql id="selectAppWxPayConfigVo">
        select id, is_del, is_enable, create_time, update_time, create_by, update_by, app_id, mch_id, mch_key,`name`
        from
        app_wx_pay_config
    </sql>

    <select id="selectAppWxPayConfigList" parameterType="AppWxPayConfig" resultMap="AppWxPayConfigResult">
        <include refid="selectAppWxPayConfigVo"/>
        where is_del=false
        <if test="isEnable != null ">and is_enable = #{isEnable}</if>
        <if test="appId != null  and appId != ''">and app_id like concat('%', #{appId}, '%')</if>
        <if test="mchId != null  and mchId != ''">and mch_id like concat('%', #{mchId}, '%')</if>
        <if test="name != null  and name != ''">and `name` like concat('%', #{name}, '%')</if>
        order by is_enable desc, id desc
    </select>

    <select id="selectAppWxPayConfigById" parameterType="Long" resultMap="AppWxPayConfigResult">
        <include refid="selectAppWxPayConfigVo"/>
        where id = #{id} and is_del=false
    </select>

    <select id="getAppWxPayConfigById" parameterType="Long" resultMap="AppWxPayConfigResult">
        <include refid="selectAppWxPayConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppWxPayConfig" parameterType="AppWxPayConfig" useGeneratedKeys="true" keyProperty="id">
        insert into app_wx_pay_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="isDel != null">is_del,</if>
            <if test="isEnable != null">is_enable,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="appId != null">app_id,</if>
            <if test="mchId != null">mch_id,</if>
            <if test="mchKey != null">mch_key,</if>
            <if test="name != null">`name`,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="isDel != null">#{isDel},</if>
            <if test="isEnable != null">#{isEnable},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="appId != null">#{appId},</if>
            <if test="mchId != null">#{mchId},</if>
            <if test="mchKey != null">#{mchKey},</if>
            <if test="name != null">#{name},</if>
        </trim>
    </insert>

    <update id="updateAppWxPayConfig" parameterType="AppWxPayConfig">
        update app_wx_pay_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="isEnable != null">is_enable = #{isEnable},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="appId != null">app_id = #{appId},</if>
            <if test="mchId != null">mch_id = #{mchId},</if>
            <if test="mchKey != null">mch_key = #{mchKey},</if>
            <if test="name != null">`name` = #{name},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteAppWxPayConfigById">
        update app_wx_pay_config set is_del=true,update_by=#{updateBy},update_time=now() where id = #{id}
    </update>

    <delete id="deleteAppWxPayConfigByIds" parameterType="String">
        delete from app_wx_pay_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="forbiddenAllWxPayConfig">
        update app_wx_pay_config set is_enable=0,update_by=#{updateBy},update_time=now()
    </update>

</mapper>