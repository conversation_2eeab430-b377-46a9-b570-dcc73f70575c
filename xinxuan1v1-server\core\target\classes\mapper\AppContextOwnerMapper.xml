<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppContextOwnerMapper">

    <resultMap type="AppContextOwner" id="AppContextOwnerResult">
        <result property="id" column="id"/>
        <result property="chatRoomId" column="chat_room_id"/>
        <result property="userId" column="user_id"/>
        <result property="dateStr" column="date_str"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="selectAppContextOwnerVo">
        select id,
        chat_room_id,
        user_id,
        date_str,
        create_time,
        update_time,
        create_by,
        update_by
        from app_context_owner
    </sql>

    <select id="getContextOwnerByChatRoomIdAndDateStr" resultMap="AppContextOwnerResult">
        select id,
        chat_room_id,
        user_id,
        date_str,
        create_time,
        update_time,
        create_by,
        update_by
        from app_context_owner
        where chat_room_id = #{chatRoomId}
        and date_str = #{dateStr}
        order by id desc limit 0,1
    </select>

    <select id="selectAppContextOwnerList" parameterType="AppContextOwner" resultMap="AppContextOwnerResult">
        select co.id,
        co.chat_room_id,
        co.user_id,
        co.date_str,
        co.create_time,
        co.update_time,
        co.create_by,
        co.update_by,
        if(u.nick_name is null or u.nick_name='',u.phone,u.nick_name) as userNickName
        from app_context_owner co,
        app_user u
        where u.id=co.user_id
        <if test="chatRoomId != null ">and co.chat_room_id = #{chatRoomId}</if>
        <if test="userId != null ">and co.user_id = #{userId}</if>
        <if test="dateStr != null  and dateStr != ''">and co.date_str = #{dateStr}</if>
        <if test="queryDateMonth != null  and queryDateMonth != ''">and DATE_FORMAT(co.date_str,'%Y-%m')=
            #{queryDateMonth}
        </if>
        order by co.date_str asc
    </select>

    <select id="selectAppContextOwnerById" parameterType="Long" resultMap="AppContextOwnerResult">
        <include refid="selectAppContextOwnerVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppContextOwner" parameterType="AppContextOwner" useGeneratedKeys="true" keyProperty="id">
        insert into app_context_owner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="chatRoomId != null">chat_room_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="dateStr != null">date_str,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="chatRoomId != null">#{chatRoomId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="dateStr != null">#{dateStr},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateAppContextOwner" parameterType="AppContextOwner">
        update app_context_owner
        <trim prefix="SET" suffixOverrides=",">
            <if test="chatRoomId != null">chat_room_id = #{chatRoomId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="dateStr != null">date_str = #{dateStr},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppContextOwnerById" parameterType="Long">
        delete
        from app_context_owner
        where id = #{id}
    </delete>

    <delete id="deleteAppContextOwnerByIds" parameterType="String">
        delete from app_context_owner where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>