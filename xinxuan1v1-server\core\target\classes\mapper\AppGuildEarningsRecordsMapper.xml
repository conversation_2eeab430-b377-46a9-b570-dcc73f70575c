<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppGuildEarningsRecordsMapper">

    <resultMap type="AppGuildEarningsRecords" id="AppGuildEarningsRecordsResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="guildId" column="guild_id"/>
        <result property="amount" column="amount"/>
        <result property="triggerObecjtId" column="trigger_obecjt_id"/>
        <result property="receiveObjectId" column="receive_object_id"/>
    </resultMap>

    <sql id="selectAppGuildEarningsRecordsVo">
        select id, create_time, guild_id, amount, trigger_obecjt_id, receive_object_id from app_guild_earnings_records
    </sql>

    <select id="selectAppGuildEarningsRecordsList" parameterType="AppGuildEarningsRecords"
            resultMap="AppGuildEarningsRecordsResult">
        <include refid="selectAppGuildEarningsRecordsVo"/>
        <where>
            <if test="guildId != null ">and guild_id = #{guildId}</if>
            <if test="amount != null ">and amount = #{amount}</if>
            <if test="triggerObecjtId != null ">and trigger_obecjt_id = #{triggerObecjtId}</if>
            <if test="receiveObjectId != null ">and receive_object_id = #{receiveObjectId}</if>
        </where>
    </select>

    <select id="selectAppGuildEarningsRecordsById" parameterType="Long" resultMap="AppGuildEarningsRecordsResult">
        <include refid="selectAppGuildEarningsRecordsVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppGuildEarningsRecords" parameterType="AppGuildEarningsRecords" useGeneratedKeys="true"
            keyProperty="id">
        insert into app_guild_earnings_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="guildId != null">guild_id,</if>
            <if test="amount != null">amount,</if>
            <if test="triggerObecjtId != null">trigger_obecjt_id,</if>
            <if test="receiveObjectId != null">receive_object_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="guildId != null">#{guildId},</if>
            <if test="amount != null">#{amount},</if>
            <if test="triggerObecjtId != null">#{triggerObecjtId},</if>
            <if test="receiveObjectId != null">#{receiveObjectId},</if>
        </trim>
    </insert>

    <update id="updateAppGuildEarningsRecords" parameterType="AppGuildEarningsRecords">
        update app_guild_earnings_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="guildId != null">guild_id = #{guildId},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="triggerObecjtId != null">trigger_obecjt_id = #{triggerObecjtId},</if>
            <if test="receiveObjectId != null">receive_object_id = #{receiveObjectId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppGuildEarningsRecordsById" parameterType="Long">
        delete from app_guild_earnings_records where id = #{id}
    </delete>

    <delete id="deleteAppGuildEarningsRecordsByIds" parameterType="String">
        delete from app_guild_earnings_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>