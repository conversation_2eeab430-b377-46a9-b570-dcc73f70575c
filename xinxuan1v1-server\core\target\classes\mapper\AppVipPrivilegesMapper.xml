<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppVipPrivilegesMapper">


    <select id="getVipPrivilegesList" resultType="com.hzy.core.entity.AppVipPrivileges">
        select * from app_vip_privileges
        <where>
            <if test="isAll == false">
                and status = true
            </if>
        </where>
        order by sort_order
    </select>

    <select id="existsVipPrivilegesByKey" resultType="java.lang.Boolean">
        select if(count(id) > 0, true, false)
        from app_vip_privileges
        where privilege_key = #{privilegeKey}
    </select>
</mapper>