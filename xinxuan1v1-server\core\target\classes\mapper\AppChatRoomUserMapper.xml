<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppChatRoomUserMapper">

    <resultMap type="AppChatRoomUser" id="AppChatRoomUserResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="lastJoinTime" column="last_join_time"/>
        <result property="chatRoomId" column="chat_room_id"/>
        <result property="isAdmin" column="is_admin"/>
        <result property="isOwner" column="is_owner"/>
        <result property="isDel" column="is_del"/>
        <result property="isWheatServing" column="is_wheat_serving"/>
        <result property="isBlanking" column="is_blanking"/>
        <result property="isBannedToPost" column="is_banned_to_post"/>
        <result property="nextJoinRoomTime" column="next_join_room_time"/>
        <result property="isKickOut" column="is_kick_out"/>
        <result property="isJoin" column="is_join"/>
        <result property="wheatBit" column="wheat_bit"/>
        <result property="isEmcee" column="is_emcee"/>
        <result property="giftGoldSum" column="gift_gold_sum"/>
        <result property="fatherId" column="father_id"/>
        <result property="addEmceeTime" column="add_emcee_time"/>
    </resultMap>

    <sql id="selectAppChatRoomUserVo">
        select id,
               create_time,
               update_time,
               user_id,
               last_join_time,
               chat_room_id,
               is_admin,
               is_del,
               is_wheat_serving,
               is_blanking,
               is_banned_to_post,
               next_join_room_time,
               is_kick_out,
               is_join,
               wheat_bit,
               is_emcee,
               gift_gold_sum,
               father_id,
               add_emcee_time
        from app_chat_room_user
    </sql>

    <select id="selectAppChatRoomUserList" parameterType="AppChatRoomUser" resultMap="AppChatRoomUserResult">
        <include refid="selectAppChatRoomUserVo"/>
        where is_del = false
        <if test="userId != null ">and user_id = #{userId}</if>
        <if test="lastJoinTime != null ">and last_join_time = #{lastJoinTime}</if>
        <if test="chatRoomId != null ">and chat_room_id = #{chatRoomId}</if>
        <if test="isAdmin != null ">and is_admin = #{isAdmin}</if>
        <if test="isWheatServing != null ">and is_wheat_serving = #{isWheatServing}</if>
        <if test="isBlanking != null ">and is_blanking = #{isBlanking}</if>
        <if test="isBannedToPost != null ">and is_banned_to_post = #{isBannedToPost}</if>
        <if test="nextJoinRoomTime != null ">and next_join_room_time = #{nextJoinRoomTime}</if>
        <if test="isKickOut != null ">and is_kick_out = #{isKickOut}</if>
        <if test="isJoin != null ">and is_join = #{isJoin}</if>
        <if test="wheatBit != null ">and wheat_bit = #{wheatBit}</if>
        <if test="isEmcee != null ">and is_emcee = #{isEmcee}</if>
        <if test="giftGoldSum != null ">and gift_gold_sum = #{giftGoldSum}</if>
        <if test="fatherId != null ">and father_id = #{fatherId}</if>
        order by id desc
    </select>

    <select id="selectAppChatRoomUserById" parameterType="Long" resultMap="AppChatRoomUserResult">
        <include refid="selectAppChatRoomUserVo"/>
        where id = #{id} and is_del = false
    </select>

    <insert id="insertAppChatRoomUser" parameterType="AppChatRoomUser" useGeneratedKeys="true" keyProperty="id">
        insert into app_chat_room_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="lastJoinTime != null">last_join_time,</if>
            <if test="chatRoomId != null">chat_room_id,</if>
            <if test="isAdmin != null">is_admin,</if>
            <if test="isDel != null">is_del,</if>
            <if test="isWheatServing != null">is_wheat_serving,</if>
            <if test="isBlanking != null">is_blanking,</if>
            <if test="isBannedToPost != null">is_banned_to_post,</if>
            <if test="nextJoinRoomTime != null">next_join_room_time,</if>
            <if test="isKickOut != null">is_kick_out,</if>
            <if test="isJoin != null">is_join,</if>
            <if test="wheatBit != null">wheat_bit,</if>
            <if test="isEmcee != null">is_emcee,</if>
            <if test="giftGoldSum != null">gift_gold_sum,</if>
            <if test="fatherId != null">father_id,</if>
            <if test="addEmceeTime != null">add_emcee_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="lastJoinTime != null">#{lastJoinTime},</if>
            <if test="chatRoomId != null">#{chatRoomId},</if>
            <if test="isAdmin != null">#{isAdmin},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="isWheatServing != null">#{isWheatServing},</if>
            <if test="isBlanking != null">#{isBlanking},</if>
            <if test="isBannedToPost != null">#{isBannedToPost},</if>
            <if test="nextJoinRoomTime != null">#{nextJoinRoomTime},</if>
            <if test="isKickOut != null">#{isKickOut},</if>
            <if test="isJoin != null">#{isJoin},</if>
            <if test="wheatBit != null">#{wheatBit},</if>
            <if test="isEmcee != null">#{isEmcee},</if>
            <if test="giftGoldSum != null">#{giftGoldSum},</if>
            <if test="fatherId != null">#{fatherId},</if>
            <if test="addEmceeTime != null">#{addEmceeTime},</if>
        </trim>
    </insert>

    <update id="updateAppChatRoomUser" parameterType="AppChatRoomUser">
        update app_chat_room_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="lastJoinTime != null">last_join_time = #{lastJoinTime},</if>
            <if test="chatRoomId != null">chat_room_id = #{chatRoomId},</if>
            <if test="isAdmin != null">is_admin = #{isAdmin},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="isWheatServing != null">is_wheat_serving = #{isWheatServing},</if>
            <if test="isBlanking != null">is_blanking = #{isBlanking},</if>
            <if test="isBannedToPost != null">is_banned_to_post = #{isBannedToPost},</if>
            <if test="nextJoinRoomTime != null">next_join_room_time = #{nextJoinRoomTime},</if>
            <if test="isKickOut != null">is_kick_out = #{isKickOut},</if>
            <if test="isJoin != null">is_join = #{isJoin},</if>
            <if test="wheatBit != null">wheat_bit = #{wheatBit},</if>
            <if test="isEmcee != null">is_emcee = #{isEmcee},</if>
            <if test="giftGoldSum != null">gift_gold_sum = #{giftGoldSum},</if>
            <if test="fatherId != null">father_id = #{fatherId},</if>
            <if test="addEmceeTime != null">add_emcee_time = #{addEmceeTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppChatRoomUserById" parameterType="Long">
        delete
        from app_chat_room_user
        where id = #{id}
    </delete>

    <delete id="deleteAppChatRoomUserByIds" parameterType="String">
        delete from app_chat_room_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="getChatRoomWheatServingUser" parameterType="java.lang.Long" resultMap="AppChatRoomUserResult">
        select id,
               create_time,
               update_time,
               user_id,
               last_join_time,
               chat_room_id,
               is_admin,
               is_del,
               is_wheat_serving,
               is_blanking,
               is_banned_to_post,
               next_join_room_time,
               is_kick_out,
               is_join,
               wheat_bit,
               is_emcee,
               gift_gold_sum,
               father_id,
               add_emcee_time
        from app_chat_room_user
        where is_del = false
          and chat_room_id = #{chatRoomId}
          and is_wheat_serving = true
          and is_join = true
          and wheat_bit!=-1
        order by last_join_time asc, id desc
    </select>


    <select id="getChatRoomWheatServingUserAvatar" parameterType="java.lang.Long" resultType="java.lang.String">
        select u.head_portrait
        from app_chat_room_user ru
                 LEFT JOIN app_user u on (u.id = ru.user_id)
        where ru.is_del = false
          and ru.chat_room_id = #{chatRoomId}
          and ru.is_wheat_serving = true
          and ru.is_join = true
          and ru.wheat_bit > -1
        order by ru.wheat_bit asc, ru.id desc limit 0,5
    </select>


    <select id="getChatRoomWheatServingUserCount" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(*)
        from app_chat_room_user
        where is_del = false
          and chat_room_id = #{chatRoomId}
          and is_wheat_serving = true
          and is_join = true
          and wheat_bit!=-1
    </select>

    <select id="selectAppChatRoomUserByChatRoomIdAndUserId" parameterType="java.lang.Long"
            resultMap="AppChatRoomUserResult">
        select id,
               create_time,
               update_time,
               user_id,
               last_join_time,
               chat_room_id,
               is_admin,
               is_owner,
               is_del,
               is_wheat_serving,
               is_blanking,
               is_banned_to_post,
               next_join_room_time,
               is_kick_out,
               is_join,
               wheat_bit,
               is_emcee,
               gift_gold_sum,
               father_id,
               add_emcee_time
        from app_chat_room_user
        where is_del = false
          and chat_room_id = #{chatRoomId}
          and user_id = #{userId}
        order by last_join_time desc, id desc limit 0,1
    </select>


    <update id="clearKickOutInfo" parameterType="java.lang.Long">
        update app_chat_room_user
        set is_kick_out= false,
            next_join_room_time=null
        where id = #{id}
          and is_kick_out = true
    </update>

    <update id="closeAllOnlineUserByChatRoomId" parameterType="java.lang.Long">
        update app_chat_room_user
        set is_wheat_serving= false,
            is_join= false,
            wheat_bit= -1,
            update_time=now()
        where chat_room_id = #{chatRoomId}
    </update>

    <update id="closeMotorcadeAllOnlineUserByChatRoomId" parameterType="java.lang.Long">
        update app_chat_room_user
        set is_wheat_serving= false,
            is_join= false,
            wheat_bit= -1,
            update_time=now()
        where father_id = #{chatRoomId}
    </update>


    <update id="clearAllUserGiftGoldSumByChatRoomId" parameterType="java.lang.Long">
        update app_chat_room_user
        set gift_gold_sum=0
        where chat_room_id = #{chatRoomId}
    </update>


    <update id="clearMotorcadeAllUserGiftGoldSumByChatRoomId" parameterType="java.lang.Long">
        update app_chat_room_user
        set gift_gold_sum=0
        where father_id = #{chatRoomId}
    </update>

    <update id="addUserGiftGold">
        update app_chat_room_user
        set gift_gold_sum=gift_gold_sum + #{giftGold}
        where chat_room_id = #{chatRoomId}
          and user_id = #{userId}
          and is_wheat_serving = true
          and is_join = true
          and wheat_bit!=-1
    </update>

    <update id="addAdmin">
        update app_chat_room_user
        set is_Admin= #{isAdmin}
        where chat_room_id = #{chatRoomId}
        and user_id = #{userId}
        and is_del = 0
    </update>

    <select id="getChatRoomOnlineUserListByThree" parameterType="java.lang.Long"
            resultType="com.hzy.core.model.vo.app.AppViewUserInfoVo">

        select u.id                                                                      as userId,
               u.recode_code                                                             as recodeCode,
               u.last_operating_time                                                     as lastOperatingTime,
               u.photo_album                                                             as photoAlbumStr,
               u.nick_name                                                               as nickName,
               u.phone                                                                   as userPhone,
               u.birthday,
               u.is_real_person_auth                                                     as isRealPersonAuth,
               u.is_real_name_auth                                                       as isRealNameAuth,
               if(u.phone!=''and u.phone is not null, 1, 0)                              as isPhoneAuth,
               u.age,
               u.sex,
               u.personal_signature                                                      as personalSignature,
               u.head_portrait                                                           as headPortrait,
               u.education_background                                                    as educationBackground,
               u.constellation,
               u.height,
               u.weight,
               u.location,
               u.annual_income                                                           as annualIncome,
               u.purchase_situation                                                      as purchaseSituation,
               u.car_purchase_situation                                                  as carPurchaseSituation,
               u.self_introduction                                                       as selfIntroduction,
               u.label,
               u.voice_signature                                                         as voiceSignature,
               u.longitude,
               u.latitude,
               u.province,
               u.city,
               u.area,
               CONCAT('{"isAdmin":', (cru.is_admin), ',"isEmcee":', (cru.is_emcee), '}') as subjoinParameter
        from app_chat_room_user cru
                 LEFT JOIN app_user u on (u.id = cru.user_id)
        where cru.chat_room_id = #{chatRoomId}
          and cru.is_join = true
        order by cru.last_join_time desc, cru.id desc limit 0,3
    </select>

    <select id="getChatRoomOnlineUserList" resultType="com.hzy.core.model.vo.app.AppViewUserInfoVo">
        select
        u.id as userId,
        u.user_status as userInfoStatus,
        u.recode_code as recodeCode,
        u.last_operating_time as lastOperatingTime,
        u.gold_balance as ugb,
        u.photo_album as photoAlbumStr,
        u.nick_name as nickName,
        u.phone as userPhone,
        u.birthday,
        u.age,
        u.sex,
        u.personal_signature as personalSignature,
        u.head_portrait as headPortrait,
        u.education_background as educationBackground,
        u.constellation,
        u.height,
        u.weight,
        u.location,
        u.annual_income as annualIncome,
        u.purchase_situation as purchaseSituation,
        u.car_purchase_situation as carPurchaseSituation,
        u.self_introduction as selfIntroduction,
        u.label,
        u.voice_signature as voiceSignature,
        u.longitude,
        u.latitude,
        u.province,
        u.city,
        u.area,
        u.created_time as createdTime,
        CONCAT('{"isAdmin":',(cru.is_admin) ,',"isEmcee":', (cru.is_emcee),',"isOwner":', (cru.is_owner),'}' ) as
        subjoinParameter
        from app_chat_room_user cru
        LEFT JOIN app_user u on(u.id=cru.user_id)
        where cru.chat_room_id = #{chatRoomId}
        <if test="!isQueryAllStatus">
            and cru.is_join = true
        </if>
        <if test="queryType!=null">
            <if test="queryType==0">
                and cru.is_admin=true
            </if>
            <if test="queryType==1">
                and cru.is_emcee=true
            </if>
        </if>
        -- order by cru.last_join_time desc, cru.id desc
        order by cru.is_owner desc, cru.is_admin desc
    </select>

    <select id="getUserNickNameById" resultType="java.lang.String" parameterType="java.lang.Long">
        select ifnull(u.nick_name, u.recode_code)
        from app_chat_room_user ru,
             app_user u
        where ru.id = #{id}
          and u.id = ru.user_id
    </select>


    <select id="getWheatBitList" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select wheat_bit
        from app_chat_room_user
        where is_del = false
          and chat_room_id = #{chatRoomId}
          and is_wheat_serving = true
          and is_join = true
          and wheat_bit!=-1
    </select>


    <select id="getOneWheatBitUserId" parameterType="java.lang.Long" resultType="java.lang.Long">
        select user_id
        from app_chat_room_user
        where is_del = false
          and chat_room_id = #{chatRoomId}
          and is_wheat_serving = true
          and is_join = true
          and wheat_bit = 1
    </select>

    <select id="getChatRoomOnlineUserCount" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(*)
        from app_chat_room_user
        where is_del = false
          and chat_room_id = #{chatRoomId}
          and is_join = true
    </select>

    <select id="getUserJoinCharRoomInfo" parameterType="java.lang.Long"
            resultType="com.hzy.core.model.vo.app.AppChatRoomListVo">
        select cr.id                      as chatRoomId,
               cr.start_time              as startTime,
               cr.name,
               cr.greeting_text           as greetingText,
               cr.notice_text             as noticeText,
               cr.avatar_url              as avatarUrl,
               cr.type                    as chatRoomType,
               cr.third_id                as thirdId,
               cr.status                  as `status`,
               cr.father_id               as fatherId,
               cr.label_json              as labelJson,
               cr.background_url          as backgroundUrl,
               cr.wheat_serving_type      as wheatServingType,
               cr.is_hide_gift_gold       as isHideGiftGold,
               (select count(*)
                from app_chat_room_user cru
                where cru.is_del = false
                  and cru.chat_room_id = cr.id
                  and cru.is_join = true) as onlineUserCount
        from app_chat_room cr
                 LEFT JOIN app_user u on (u.id = cr.user_id and u.user_status = true)
                 left join app_chat_room_user cru
                           on (cru.is_del = false and cru.user_id = #{userId} and cru.chat_room_id = cr.id
                               and cru.is_join = true)
        where cr.status = true
          and cr.is_del = false
          and cru.id is not null
        order by cru.last_join_time desc, cr.start_time desc, cr.id desc limit 0,1
    </select>


    <select id="userIsEmcee" parameterType="java.lang.Long" resultType="java.lang.Boolean">
        select if(count(*) > 0, 1, 0)
        from app_chat_room_user ru,
             app_chat_room cr
        where ru.is_del = false
          and ru.user_id = #{userId}
          and is_emcee = 1
          and ru.father_id = 0
          and cr.id = ru.chat_room_id
          and cr.type in (1, 2, 3, 7, 8)
    </select>

    <select id="userIsAdmin" parameterType="java.lang.Long" resultType="java.lang.Boolean">
        select if(count(*) > 0, 1, 0)
        from app_chat_room_user ru,
             app_chat_room cr
        where ru.is_del = false
          and ru.user_id = #{userId}
          and ru.is_admin = 1
          and ru.father_id = 0
          and cr.id = ru.chat_room_id
          and cr.type in (1, 2, 3, 7, 8)
    </select>

    <select id="getUserAdminChatRoomId" parameterType="java.lang.Long" resultType="java.lang.Long">
        select ru.chat_room_id
        from app_chat_room_user ru,
             app_chat_room cr
        where ru.is_del = false
          and ru.user_id = #{userId}
          and ru.is_admin = 1
          and ru.father_id = 0
          and ru.chat_room_id = cr.id
          and cr.type not in (4, 5)
        order by ru.id desc limit 0,1
    </select>

    <select id="getUserEmceeChatRoomId" parameterType="java.lang.Long" resultType="java.lang.Long">
        select ru.chat_room_id
        from app_chat_room_user ru,
             app_chat_room cr
        where ru.is_del = false
          and ru.user_id = #{userId}
          and is_emcee = 1
          and ru.father_id = 0
          and ru.chat_room_id = cr.id
          and cr.type not in (4, 5)
        order by ru.id desc limit 0,1
    </select>


    <select id="isJoin" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select if(count(*) > 0, 1, 0)
        from app_chat_room_user ru,
             app_chat_room cr
        where ru.is_del = false
          and ru.chat_room_id = #{chatRoomId}
          and ru.user_id = #{userId}
          and ru.is_join = true
          and cr.id = ru.chat_room_id
          and cr.status!=2
    </select>

    <select id="getUserMotorcadeInfo" parameterType="java.lang.Long"
            resultMap="AppChatRoomUserResult">
        select ru.id,
               ru.create_time,
               ru.update_time,
               ru.user_id,
               ru.last_join_time,
               ru.chat_room_id,
               ru.is_admin,
               ru.is_del,
               ru.is_wheat_serving,
               ru.is_blanking,
               ru.is_banned_to_post,
               ru.next_join_room_time,
               ru.is_kick_out,
               ru.is_join,
               ru.wheat_bit,
               ru.is_emcee,
               ru.gift_gold_sum,
               ru.father_id,
               ru.add_emcee_time
        from app_chat_room_user ru,
             app_chat_room cr
        where ru.is_del = false
          and ru.user_id = #{userId}
          and ru.father_id = #{chatRoomId}
          and cr.id = ru.chat_room_id
          and cr.status!=2
        order by ru.last_join_time desc, ru.id desc limit 0, 1
    </select>


    <select id="getUserUnderWayMotorcadeInfo" parameterType="java.lang.Long"
            resultMap="AppChatRoomUserResult">
        select ru.id,
               ru.create_time,
               ru.update_time,
               ru.user_id,
               ru.last_join_time,
               ru.chat_room_id,
               ru.is_admin,
               ru.is_del,
               ru.is_wheat_serving,
               ru.is_blanking,
               ru.is_banned_to_post,
               ru.next_join_room_time,
               ru.is_kick_out,
               ru.is_join,
               ru.wheat_bit,
               ru.is_emcee,
               ru.gift_gold_sum,
               ru.father_id,
               ru.add_emcee_time
        from app_chat_room_user ru,
             app_chat_room cr
        where ru.is_del = false
          and ru.user_id = #{userId}
          and ru.father_id!=0
        and ru.is_join = true
        and cr.id=ru.chat_room_id
        and cr.status!=2
        order by ru.last_join_time desc, ru.id desc limit 0, 1
    </select>


    <select id="getHallOwnerChatRoomId" parameterType="java.lang.Long" resultType="java.lang.Long">
        select cr.id
        from app_chat_room cr
        where cr.hall_owner_user_id = #{userId}
        order by cr.id desc limit 0,1
    </select>
    <select id="getAdminOrOwnerUserList" resultType="com.hzy.core.entity.AppChatRoomUser">
        select ru.id,
        ru.create_time as createTime,
        ru.update_time as updatedTime,
        ru.user_id as userId,
        ru.last_join_time as lastJoinTime,
        ru.chat_room_id as chatRoomId,
        ru.is_admin as isAdmin,
        ru.is_del as isDel,
        ru.is_wheat_serving as isWheatServing,
        ru.is_blanking as isBlanking,
        ru.is_banned_to_post as isBannedToPost,
        ru.next_join_room_time as nextJoinRoomTime,
        ru.is_kick_out as isKickOut,
        ru.is_join as isJoin,
        ru.wheat_bit as wheatBit,
        ru.is_emcee as isEmcee,
        ru.gift_gold_sum as giftGoldSum,
        ru.father_id as fatherId,
        ru.add_emcee_time as addEmceeTime,
        ru.is_owner as isOwner,
        cr.name as chatRoomName,
        cr.avatar_url as chatRoomAvatarUrl,
        cr.notice_text as noticeText,
        cr.background_url as backgroundUrl,
        cr.wheat_serving_type as wheatServingType,
        cr.type as type,
        cr.status as status,
        u.nick_name as nickName,
        cr.hall_owner_user_id as hallOwnerUserId,
        COALESCE(onlineUserCount.onlineUserCount, 0) AS onlineUserCount
        FROM
        app_chat_room AS cr
        LEFT JOIN (
        SELECT
        chat_room_id,
        COUNT(DISTINCT user_id) AS onlineUserCount
        FROM
        app_chat_room_user
        WHERE
        last_join_time > NOW() - INTERVAL '5' MINUTE
        GROUP BY
        chat_room_id
        ) AS onlineUserCount ON cr.id = onlineUserCount.chat_room_id
        LEFT JOIN app_chat_room_user AS ru ON cr.id = ru.chat_room_id
        left join app_user as u on ru.user_id=u.id
        <where>
            <if test="userId != null ">
                ru.user_id = #{userId}
            </if>
            <if test="isEmcee != null ">
                and ru.is_emcee = #{isEmcee}
            </if>
            <if test="isAdmin != null ">
                and ru.is_admin = #{isAdmin}
            </if>
        </where>

    </select>
    <select id="getRoomInfoByChatRoomIdAndUserId" resultType="com.hzy.core.entity.AppChatRoomUser">
        <include refid="selectAppChatRoomUserVo"/>
        where user_id = #{userId}
        <if test="chatRoomId != null ">
            and chat_room_id not in (#{chatRoomId})
        </if>
        and is_join = true
        and is_del = false
    </select>

    <select id="isRoomAdminOrOwner" resultType="java.lang.Boolean">
        SELECT IF(COUNT(*) > 0, 1, 0)
        FROM (
            -- 检查用户是否是房间管理员
            SELECT 1
            FROM app_chat_room_user ru
            WHERE ru.is_del = false
              AND ru.user_id = #{userId}
              AND ru.chat_room_id = #{chatRoomId}
              AND ru.is_admin = 1
            UNION ALL
            -- 检查用户是否是房间房主
            SELECT 1
            FROM app_chat_room cr
            WHERE cr.is_del = false
              AND cr.id = #{chatRoomId}
              AND cr.hall_owner_user_id = #{userId}
        ) AS result
    </select>

</mapper>