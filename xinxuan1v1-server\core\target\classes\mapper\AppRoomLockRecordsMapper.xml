<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppRoomLockRecordsMapper">

    <resultMap type="AppRoomLockRecords" id="AppRoomLockRecordsResult">
        <result property="id" column="id"/>
        <result property="chatRoomId" column="chat_room_id"/>
        <result property="type" column="type"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectAppRoomLockRecordsVo">
        select id, chat_room_id, type, create_time from app_room_lock_records
    </sql>

    <select id="selectAppRoomLockRecordsList" parameterType="AppRoomLockRecords" resultMap="AppRoomLockRecordsResult">
        <include refid="selectAppRoomLockRecordsVo"/>
        <where>
            <if test="chatRoomId != null ">and chat_room_id = #{chatRoomId}</if>
            <if test="type != null ">and type = #{type}</if>
            <if test="queryBeginTime != null and queryBeginTime != ''">
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
            </if>
            <if test="queryEndTime != null and queryEndTime != ''">
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
            </if>
        </where>
        order by id desc
    </select>

    <select id="selectAppRoomLockRecordsById" parameterType="Long" resultMap="AppRoomLockRecordsResult">
        <include refid="selectAppRoomLockRecordsVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppRoomLockRecords" parameterType="AppRoomLockRecords" useGeneratedKeys="true" keyProperty="id">
        insert into app_room_lock_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="chatRoomId != null">chat_room_id,</if>
            <if test="type != null">type,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="chatRoomId != null">#{chatRoomId},</if>
            <if test="type != null">#{type},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateAppRoomLockRecords" parameterType="AppRoomLockRecords">
        update app_room_lock_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="chatRoomId != null">chat_room_id = #{chatRoomId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppRoomLockRecordsById" parameterType="Long">
        delete from app_room_lock_records where id = #{id}
    </delete>

    <delete id="deleteAppRoomLockRecordsByIds" parameterType="String">
        delete from app_room_lock_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>