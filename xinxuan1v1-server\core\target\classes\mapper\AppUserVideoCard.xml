<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppUserVideoCardMapper">

    <select id="getMyVideoCardList" resultType="com.hzy.core.entity.AppUserVideoCard">
        select * from app_user_video_card
        where user_id = #{userId}
        and status = 1
        order by id
    </select>

    <update id="expiredVideoCard">
        update app_user_video_card
        set status = 3, update_time = NOW()
        where status = 1
        and expire_time &lt; CURDATE();
    </update>

    <select id="getUserTodayVideoCard" resultType="com.hzy.core.entity.AppUserVideoCard">
        select * from app_user_video_card
        where user_id = #{userId}
        and TO_DAYS(create_time) = TO_DAYS(NOW())
    </select>

    <select id="getUserVideoCardPage" resultType="com.hzy.core.entity.AppUserVideoCard">
        select
            au.id AS user_id,
            au.nick_name,
            au.recode_code,
            au.phone,
            au.created_time as createTime,
            COALESCE(SUM(IF(auvc.status = 1, auvc.amount, 0)), 0) AS totalAmount
        from app_user au
        left join app_user_video_card auvc on au.id = auvc.user_id
        <where>
            <if test="phone != null and phone != ''">
                and au.phone = #{phone}
            </if>
            <if test="nickname != null and nickname != ''">
                and au.nick_name = #{nickname}
            </if>
            <if test="recodeCode != null and recodeCode != ''">
                and au.recode_code = #{recodeCode}
            </if>
        </where>
        group by au.id
        order by totalAmount desc
    </select>

    <select id="getUserVideoCardCount" resultType="java.lang.Integer">
        select IFNULL(SUM(amount), 0)
        from app_user_video_card
        where user_id = #{userId}
        and status = 1
    </select>
</mapper>