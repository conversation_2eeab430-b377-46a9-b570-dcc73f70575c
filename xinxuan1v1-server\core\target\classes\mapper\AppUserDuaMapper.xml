<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppUserDuaMapper">

    <select id="getAppUserDua" resultType="com.hzy.core.entity.AppUserDua">
        select * from app_user_dua
        where date = #{date}
        and gender = #{gender}
    </select>

    <select id="getAppUserDuaCount" resultType="java.lang.Integer">
        select IFNULL(SUM(`count`), 0) from app_user_dua
        <where>
            <if test="date!= null">
                and date = #{date}
            </if>
        </where>
    </select>

    <select id="getAppUserDuaList" resultType="com.hzy.core.entity.AppUserDua">
        select * from app_user_dua
        <where>
            <if test="date != null">
                and date = #{date}
            </if>
            <if test="gender != null">
                and gender = #{gender}
            </if>
        </where>
        order by id desc
    </select>

</mapper>