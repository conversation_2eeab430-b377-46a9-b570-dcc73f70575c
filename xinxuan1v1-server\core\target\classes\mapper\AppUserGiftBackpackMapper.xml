<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppUserGiftBackpackMapper">

    <resultMap type="AppUserGiftBackpack" id="AppUserGiftBackpackResult">
        <result property="id" column="id"/>
        <result property="isDel" column="is_del"/>
        <result property="createdTime" column="create_time"/>
        <result property="updatedTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="giftId" column="gift_id"/>
        <result property="sumNum" column="gift_num"/>
        <result property="sendUserId" column="send_user_id"/>
        <result property="sendUserGoldBillId" column="send_user_gold_bill_id"/>
        <result property="oldSumNum" column="old_sum_num"/>
        <result property="objectId" column="object_id"/>
        <result property="isReissue" column="is_reissue"/>
        <result property="giftNum" column="gift_num"/>
    </resultMap>

    <sql id="selectAppUserGiftBackpackVo">
        select id, is_del, create_time, update_time, create_by, update_by, user_id, gift_id, gift_num, send_user_id,
        send_user_gold_bill_id, old_sum_num, object_id, is_reissue from app_user_gift_backpack
    </sql>
    <update id="deductBackpackStock">
        update app_user_gift_backpack
        set gift_num = gift_num - #{num}
        where user_id = #{userId}
        and gift_num &gt;= #{num}
    </update>

    <select id="getUserReceiveGiftList"
            resultType="com.hzy.core.model.vo.app.AppUserReceiveGiftVo">
        select g.id as giftId,
        g.gift_name as giftName,
        g.img_url as imgUrl,
        g.give_gif_img_url as giveGifImgUrl,
        g.effect_picture_url as effectPictureUrl,
        sum(gb.gift_num) as sumCount
        from app_user u,
        app_user_gift_backpack gb,
        app_gift g,
        app_gift_category ca
        where u.user_status = true
        and gb.is_del = false
        and gb.user_id = u.id
        and gb.gift_id = g.id
        and u.id = #{userId}
        and gb.gift_num > 0
        and ca.id=g.category_id
        and gb.user_id!=gb.send_user_id
        <if test="notCategoryId!=null and notCategoryId.size()>0">
            and ca.id not in
            <foreach item="itemId" collection="notCategoryId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        group by g.id
        order by sumCount desc,gb.id desc
    </select>

    <select id="getUserReceiveGiftCount" parameterType="java.lang.Long"
            resultType="java.lang.Integer">
        select ifnull(sum(gb.gift_num), 0)
        from app_user u,
        app_user_gift_backpack gb,
        app_gift g,
        app_gift_category ca
        where u.user_status = true
        and gb.is_del = false
        and gb.user_id = u.id
        and gb.gift_id = g.id
        and u.id = #{userId}
        and gb.gift_num > 0
        and ca.id=g.category_id
        and gb.user_id!=gb.send_user_id
        <if test="notCategoryId!=null and notCategoryId.size()>0">
            and ca.id not in
            <foreach item="itemId" collection="notCategoryId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
    </select>


    <select id="getUserGiftList" resultType="com.hzy.core.model.vo.admin.AdminAppUserGiftVo"
            parameterType="com.hzy.core.model.vo.admin.AdminAppUserGiftVo">
        select g.id as giftId,
        g.gift_name as giftName,
        g.img_url as imgUrl,
        g.give_gif_img_url as giveGifImgUrl,
        g.effect_picture_url as effectPictureUrl,
        gb.created_time as giveTime,
        receiveUser.head_portrait as receiveUserHeadPortrait,
        receiveUser.nick_name as receiveUserNickName,
        receiveUser.phone as receiveUserPhone,
        receiveUser.id as receiveUserId,
        receiveUser.recode_code as receiveUserRecodeCode,
        -- sendUser.head_portrait as sendUserHeadPortrait,
        -- sendUser.nick_name as sendUserNickName,
        -- sendUser.phone as sendUserPhone,
        -- sendUser.id as sendUserId,
        -- sendUser.recode_code as sendUserRecodeCode,
        gb.gift_num as giveNum,
        ifnull((select ifnull(abs( bi.amount),0) from app_user_gold_bill bi where
        bi.id=gb.send_user_gold_bill_id),g.masonry_price) as giftPrice
        from
        app_user_gift_backpack gb
        LEFT JOIN app_user receiveUser on(gb.user_id = receiveUser.id)
        LEFT JOIN app_user sendUser on(gb.send_user_id = sendUser.id)
        LEFT JOIN app_gift g on(gb.gift_id = g.id)
        LEFT JOIN app_gift_category ca on(ca.id=g.category_id)
        where 1=1
        <if test="sendUserId != null ">and gb.send_user_id = #{sendUserId}</if>
        <if test="receiveUserId != null ">and gb.user_id = #{receiveUserId}</if>
        <if test="giftName != null  and giftName != ''">
            and g.gift_name like concat('%', #{giftName}, '%')
        </if>

        <if test="receiveUserPhone != null  and receiveUserPhone != ''">
            and receiveUser.phone like concat('%', #{receiveUserPhone}, '%')
        </if>
        <if test="receiveUserRecodeCode != null  and receiveUserRecodeCode != ''">
            and receiveUser.recode_code like concat('%', #{receiveUserRecodeCode}, '%')
        </if>
        <if test="receiveUserNickName != null  and receiveUserNickName != ''">
            and receiveUser.nick_name like concat('%', #{receiveUserNickName}, '%')
        </if>

        <if test="sendUserPhone != null  and sendUserPhone != ''">
            and sendUser.phone like concat('%', #{sendUserPhone}, '%')
        </if>
        <if test="sendUserRecodeCode != null  and sendUserRecodeCode != ''">
            and sendUser.recode_code like concat('%', #{sendUserRecodeCode}, '%')
        </if>
        <if test="sendUserNickName != null  and sendUserNickName != ''">
            and sendUser.nick_name like concat('%', #{sendUserNickName}, '%')
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(gb.created_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(gb.created_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        <if test="notCategoryId!=null and notCategoryId.size()>0">
            and ca.id not in
            <foreach item="itemId" collection="notCategoryId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        and 1=if(gb.gift_num > 0,1,0)
        and 1=if(gb.user_id=gb.send_user_id,0,1)
        order by gb.id desc
    </select>

    <select id="selectGiftPriceList"
            parameterType="com.hzy.core.model.dto.AppUserGiftDTO"
            resultType="com.hzy.core.model.vo.admin.AdminAppUserGiftVo">
        SELECT ug.id as id,
        ug.gift_id as giftId,
        ug.gift_name as giftName,
        ug.gift_num as giftNum,
        ug.gift_gold_total_amount as giftPrice,
        ug.created_time as createTime,
        u.id as userId,
        u.recode_code as recodeCode,
        u.phone as phone,
        u.nick_name as nickname,
        ag.img_url as imgUrl
        FROM user_give_gift ug
        <if test="type == 0">
            LEFT JOIN app_user u ON u.id = ug.user_id
        </if>
        <if test="type == 1">
            LEFT JOIN app_user u ON u.id = ug.to_user_id
        </if>
        LEFT JOIN app_gift ag ON ag.id = ug.gift_id
        WHERE
        u.id = #{userId}
        AND ug.deleted = 0

        <if test="userId != null">
            and u.id = #{userId}
        </if>

        <if test="recodeCode != null">
            and u.recode_code = #{recodeCode}
        </if>

        <if test="phone != null  and phone != ''">
            and u.phone like concat('%', #{phone}, '%')
        </if>

        <if test="username != null  and username != ''">
            and u.nick_name like concat('%', #{username}, '%')
        </if>

        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(ug.create_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>

        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(ug.create_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>

        ORDER BY ug.updated_time desc, ug.created_time desc
    </select>


    <select id="selectSendGiftPrice"
            parameterType="com.hzy.core.model.dto.AppUserGiftDTO"
            resultType="com.hzy.core.model.vo.admin.AdminAppUserGiftVo">
        SELECT ug.id as id,
        ug.gift_id as giftId,
        ug.gift_name as giftName,
        ug.gift_num as giftNum,
        ug.gift_gold_unit_amount as giftPrice,
        ug.gift_gold_unit_amount as totalGiftPrice,
        ug.created_time as createTime,
        u.id as userId,
        u.recode_code as recodeCode,
        u.phone as phone,
        u.nick_name as nickname,
        ag.img_url as imgUrl
        FROM user_give_gift ug
        LEFT JOIN app_user u ON u.id = ug.to_user_id
        LEFT JOIN app_gift ag ON ag.id = ug.gift_id
        WHERE
        ug.deleted = 0
        <if test="userId != null">
            and ug.user_id = #{userId}
        </if>

        <if test="toUserId != null">
            and ug.to_user_id = #{toUserId}
        </if>

        <if test="recodeCode != null">
            and u.recode_code = #{recodeCode}
        </if>

        <if test="phone != null  and phone != ''">
            and u.phone like concat('%', #{phone}, '%')
        </if>

        <if test="nickname != null  and nickname != ''">
            and u.nick_name like concat('%', #{nickname}, '%')
        </if>

        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(ug.create_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>

        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(ug.create_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>

        ORDER BY ug.updated_time desc, ug.created_time desc
    </select>


    <select id="selectReceiveGiftPrice"
            parameterType="com.hzy.core.model.dto.AppUserGiftDTO"
            resultType="com.hzy.core.model.vo.admin.AdminAppUserGiftVo">
        SELECT ug.id as id,
        ug.gift_id as giftId,
        ug.gift_name as giftName,
        ug.gift_num as giftNum,
        ug.gift_gold_unit_amount as giftPrice,
        ug.gift_gold_total_amount as totalGiftPrice,
        ug.created_time as createTime,
        u.id as userId,
        u.recode_code as recodeCode,
        u.phone as phone,
        u.nick_name as nickname,
        ag.img_url as imgUrl
        FROM user_give_gift ug
        LEFT JOIN app_user u ON u.id = ug.user_id
        LEFT JOIN app_gift ag ON ag.id = ug.gift_id
        WHERE
        ug.deleted = 0
        <if test="userId != null">
            AND ug.to_user_id = #{userId}
        </if>

        <if test="toUserId != null">
            and ug.to_user_id = #{toUserId}
        </if>

        <if test="recodeCode != null">
            and u.recode_code = #{recodeCode}
        </if>

        <if test="phone != null  and phone != ''">
            and u.phone like concat('%', #{phone}, '%')
        </if>

        <if test="nickname != null  and nickname != ''">
            and u.nick_name like concat('%', #{nickname}, '%')
        </if>

        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(ug.created_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>

        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(ug.created_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        ORDER BY ug.updated_time desc, ug.created_time desc
    </select>


    <select id="getUserGiftListSum" resultType="java.math.BigDecimal"
            parameterType="com.hzy.core.model.vo.admin.AdminAppUserGiftVo">
        select
        sum(ifnull((select ifnull(abs( bi.amount),0) from app_user_gold_bill bi where
        bi.id=gb.send_user_gold_bill_id),g.masonry_price))
        from
        app_user_gift_backpack gb
        LEFT JOIN app_user receiveUser on(gb.user_id = receiveUser.id)
        LEFT JOIN app_user sendUser on(gb.send_user_id = sendUser.id)
        LEFT JOIN app_gift g on(gb.gift_id = g.id)
        LEFT JOIN app_gift_category ca on(ca.id=g.category_id)
        where 1=1
        <if test="sendUserId != null ">and gb.send_user_id = #{sendUserId}</if>
        <if test="receiveUserId != null ">and gb.user_id = #{receiveUserId}</if>
        <if test="giftName != null  and giftName != ''">
            and g.gift_name like concat('%', #{giftName}, '%')
        </if>
        <if test="receiveUserPhone != null  and receiveUserPhone != ''">
            and receiveUser.phone like concat('%', #{receiveUserPhone}, '%')
        </if>
        <if test="receiveUserRecodeCode != null  and receiveUserRecodeCode != ''">
            and receiveUser.recode_code like concat('%', #{receiveUserRecodeCode}, '%')
        </if>
        <if test="receiveUserNickName != null  and receiveUserNickName != ''">
            and receiveUser.nick_name like concat('%', #{receiveUserNickName}, '%')
        </if>

        <if test="sendUserPhone != null  and sendUserPhone != ''">
            and sendUser.phone like concat('%', #{sendUserPhone}, '%')
        </if>
        <if test="sendUserRecodeCode != null  and sendUserRecodeCode != ''">
            and sendUser.recode_code like concat('%', #{sendUserRecodeCode}, '%')
        </if>
        <if test="sendUserNickName != null  and sendUserNickName != ''">
            and sendUser.nick_name like concat('%', #{sendUserNickName}, '%')
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(gb.create_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(gb.create_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        <if test="notCategoryId!=null and notCategoryId.size()>0">
            and ca.id not in
            <foreach item="itemId" collection="notCategoryId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        and 1=if(gb.gift_num > 0,1,0)
        and 1=if(gb.user_id=gb.send_user_id,0,1)
    </select>


    <select id="getUserGiftSum" resultType="java.math.BigDecimal"
            parameterType="com.hzy.core.model.dto.AppUserGiftDTO">
        SELECT SUM(IFNULL(ug.gift_gold_total_amount, 0)) AS totalAmount
        FROM user_give_gift ug
        LEFT JOIN app_user u ON u.id = ug.to_user_id
        LEFT JOIN app_gift ag ON ag.id = ug.gift_id
        WHERE
        ug.deleted = 0
        <if test="userId != null">
            AND ug.user_id = #{userId}
        </if>

        <if test="toUserId != null">
            and ug.to_user_id = #{toUserId}
        </if>

        <if test="recodeCode != null">
            and u.recode_code = #{recodeCode}
        </if>

        <if test="phone != null  and phone != ''">
            and u.phone like concat('%', #{phone}, '%')
        </if>

        <if test="nickname != null  and nickname != ''">
            and u.nick_name like concat('%', #{nickname}, '%')
        </if>

        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(ug.create_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>

        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(ug.create_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
    </select>

    <select id="getReceiveUserGiftSum" resultType="java.math.BigDecimal"
            parameterType="com.hzy.core.model.dto.AppUserGiftDTO">
        SELECT SUM(IFNULL(ug.gift_gold_total_amount, 0)) AS totalAmount
        FROM user_give_gift ug
        LEFT JOIN app_user u ON u.id = ug.user_id
        LEFT JOIN app_gift ag ON ag.id = ug.gift_id
        WHERE
        ug.deleted = 0
        <if test="userId != null">
            AND ug.to_user_id = #{userId}
        </if>

        <if test="toUserId != null">
            and ug.to_user_id = #{toUserId}
        </if>

        <if test="recodeCode != null">
            and u.recode_code = #{recodeCode}
        </if>

        <if test="phone != null  and phone != ''">
            and u.phone like concat('%', #{phone}, '%')
        </if>

        <if test="nickname != null  and nickname != ''">
            and u.nick_name like concat('%', #{nickname}, '%')
        </if>

        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(ug.created_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>

        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(ug.created_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
    </select>


    <select id="getUserBackPackCategoryGiftList" resultType="com.hzy.core.model.vo.app.AppGiftVo">
        select g.id as giftId,
        g.gift_name as giftName,
        g.masonry_price as masonryPrice,
        g.img_url as imgUrl,
        g.give_gif_img_url as giveGifImgUrl,
        g.effect_picture_url as effectPictureUrl,
        sum(gb.gift_num) as sumCount
        from app_user u,
        app_user_gift_backpack gb,
        app_gift g,
        app_gift_category ca
        where u.user_status = true
        and gb.is_del = false
        and gb.user_id = u.id
        and gb.gift_id = g.id
        and u.id = #{userId}
        and gb.gift_num > 0
        and ca.id=g.category_id
        and gb.user_id=gb.send_user_id
        <if test="notCategoryId!=null and notCategoryId.size()>0">
            and ca.id not in
            <foreach item="itemId" collection="notCategoryId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        group by g.id
        order by g.masonry_price asc,gb.id desc
    </select>


    <select id="getUserBackPackCategoryGiftBackpackListByGiftId" resultMap="AppUserGiftBackpackResult">
        select gb.id,
        gb.is_del,
        gb.create_time,
        gb.update_time,
        gb.create_by,
        gb.update_by,
        gb.user_id,
        gb.gift_id,
        gb.gift_num,
        gb.send_user_id,
        gb.send_user_gold_bill_id
        from app_user u,
        app_user_gift_backpack gb,
        app_gift g,
        app_gift_category ca
        where u.user_status = true
        and gb.is_del = false
        and gb.user_id = u.id
        and gb.gift_id = g.id
        and u.id = #{userId}
        and gb.gift_num > 0
        and ca.id=g.category_id
        and gb.user_id=gb.send_user_id
        <if test="giftId!=null">
            and g.id=#{giftId}
        </if>
        <if test="notCategoryId!=null and notCategoryId.size()>0">
            and ca.id not in
            <foreach item="itemId" collection="notCategoryId" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
        order by gb.id desc
    </select>

    <select id="getUserWinningRecord" resultType="com.hzy.core.model.vo.app.AppGiftVo">
        select g.id as giftId,
        g.gift_name as giftName,
        g.masonry_price as masonryPrice,
        g.img_url as imgUrl,
        g.give_gif_img_url as giveGifImgUrl,
        g.effect_picture_url as effectPictureUrl,
        gb.create_time as addTime,
        gb.old_sum_num as sumCount
        from app_user_gift_backpack gb
        left join app_gift g on gb.gift_id = g.id
        LEFT JOIN game_pool_gift_relation ci on ci.bind_gift_id = gb.gift_id
        left join game_upgrade_pool gmuc on ci.pool_id = gmuc.id
        left join game_config gc on gmuc.game_id = gc.id
        where gb.is_del = false
        and gb.user_id = #{userId}
        and gc.id=#{gameId}
        and gb.gift_id = g.id
        and gb.send_user_gold_bill_id = -2
        order by gb.id desc
    </select>


    <select id="getMzlsKjjlList" resultType="com.hzy.core.model.vo.admin.AdminMzlsKjjlVo">
        select
        gb.id as id,
        g.id as giftId,
        g.gift_name as giftName,
        g.img_url as giftUrl,
        g.masonry_price as giftPrice,
        gb.old_sum_num as sumNum,
        gb.user_id as userId,
        u.recode_code as recodeCode,
        u.phone as phone,
        u.nick_name as nickName,
        u.head_portrait as avatar,
        ci.pool_id as jjcId,
        gb.create_time as createTIme
        from
        app_user_gift_backpack gb
        LEFT JOIN app_gift g on(g.id=gb.gift_id)
        LEFT JOIN app_user u on(u.id=gb.user_id)
        LEFT JOIN game_pool_gift_relation ci on(ci.bind_gift_id=gb.gift_id)
        left join game_upgrade_pool gmuc on ci.pool_id = gmuc.id
        left join game_config gc on gmuc.game_id = gc.id
        where gb.send_user_gold_bill_id=-2
        <if test="gameId!=null">
            and gc.id=#{gameId}
        </if>
        <if test="jjcId!=null">
            and ci.pool_id=#{jjcId}
        </if>
        <if test="recodeCode!=null and recodeCode!=''">
            and u.recode_code like concat('%', #{recodeCode}, '%')
        </if>
        <if test="phone!=null and phone!=''">
            and u.phone like concat('%', #{phone}, '%')
        </if>
        <if test="nickName!=null and nickName!=''">
            and u.nick_name like concat('%', #{nickName}, '%')
        </if>
        order by gb.id desc
    </select>


    <select id="getBxKjjlList" resultType="com.hzy.core.model.vo.admin.AdminBxVo">
        select
        gb.id as id,
        g.id as giftId,
        g.gift_name as giftName,
        g.img_url as giftUrl,
        g.masonry_price as giftPrice,
        gb.old_sum_num as sumNum,
        gb.user_id as userId,
        u.recode_code as recodeCode,
        u.phone as phone,
        u.nick_name as nickName,
        u.head_portrait as avatar,
        ci.gitt_id as jjcId,
        gb.create_time as createTIme
        from
        app_user_gift_backpack gb
        LEFT JOIN app_gift g on(g.id=gb.gift_id)
        LEFT JOIN app_user u on(u.id=gb.user_id)
        LEFT JOIN app_chest_gift_config ci on(ci.bind_gift_id=gb.gift_id)
        where gb.object_id=-1
        <if test="jjcId!=null">
            and ci.gitt_id=#{jjcId}
        </if>
        <if test="recodeCode!=null and recodeCode!=''">
            and u.recode_code like concat('%', #{recodeCode}, '%')
        </if>
        <if test="phone!=null and phone!=''">
            and u.phone like concat('%', #{phone}, '%')
        </if>
        <if test="nickName!=null and nickName!=''">
            and u.nick_name like concat('%', #{nickName}, '%')
        </if>
        order by gb.id desc
    </select>
    <select id="getUserBackPackCategoryGifts" resultType="com.hzy.core.model.vo.app.AppGiftVo">
        SELECT
        ag.id AS giftId,
        ag.gift_name AS giftName,
        ag.masonry_price AS masonryPrice,
        ag.img_url AS imgUrl,
        ag.give_gif_img_url AS giveGifImgUrl,
        ag.effect_picture_url AS effectPictureUrl,
        augb.gift_num sumCount
        FROM
        app_user_gift_backpack augb
        LEFT JOIN app_gift ag ON augb.gift_id = ag.id
        WHERE
        augb.user_id = #{userId}
        AND augb.gift_num > 0
        AND augb.deleted = FALSE
        AND ag.is_del = FALSE
        ORDER BY
        ag.masonry_price DESC
    </select>



    <select id="getUserReceiveGiftAmount" parameterType="Long" resultType="BigDecimal">
        SELECT
        SUM(IFNULL(ag.masonry_price, 0)) AS priceTotal

        FROM
        app_user_gift_backpack ab
        left join app_gift ag ON ag.id = ab.gift_id
        WHERE
        ab.user_id = #{userId}
        AND ab.deleted = 0
    </select>

    <select id="getBackPackList" parameterType="Long" resultType="com.hzy.core.model.vo.app.BackPackVo">
        SELECT
        ab.id AS id,
        ab.gift_id AS giftId,
        ag.gift_name AS giftName,
        ag.masonry_price as masonryPrice,
        ab.gift_num AS giftNum,
        ag.img_url AS imgUrl
        FROM app_user_gift_backpack ab
        INNER JOIN app_gift ag ON ag.id = ab.gift_id
        where ab.user_id = #{userId} AND ag.category_id = -1 AND ab.gift_num > 0
    </select>
    <select id="getUserReceiveGiftList2" resultType="com.hzy.core.model.vo.app.AppUserReceiveGiftVo"
            parameterType="java.lang.Long">
        SELECT
            ugg.gift_id       AS giftId,
            ugg.gift_name     AS giftName,
            MAX(ag.img_url)   AS imgUrl,
            SUM(ugg.gift_num) AS sumCount,
            MAX(u.nick_name)  AS nickName,
            MAX(u.head_portrait) AS headImgUrl,
            MAX(ugg.created_time) AS createTime,
            MAX(ugg.gift_gold_unit_amount) AS giftPrice
        FROM user_give_gift AS ugg
                 LEFT JOIN app_gift ag ON ag.id = ugg.gift_id
                 LEFT JOIN app_user u ON u.id = ugg.to_user_id
        WHERE ugg.to_user_id = #{viewUserId}
        GROUP BY ugg.gift_id, ugg.gift_name
        ORDER BY MAX(ugg.created_time) DESC, MAX(ugg.gift_gold_unit_amount) DESC;
    </select>
    <select id="getUserReceiveGiftList3" resultType="com.hzy.core.model.vo.app.AppUserReceiveGiftVo">
        SELECT ugg.gift_id       AS giftId,
               ugg.gift_name as giftName,
               ag.img_url as imgUrl,
               ugg.gift_num AS sumCount,
               u.nick_name as nickName,
               u.head_portrait as headImgUrl,
               ugg.created_time as createTime,
               ugg.gift_gold_unit_amount as giftPrice
        FROM user_give_gift as ugg
                 left join
             app_gift ag ON ag.id = ugg.gift_id
                 left join
             app_user u on u.id = ugg.to_user_id
        WHERE ugg.user_id = #{viewUserId}
        order by ugg.created_time desc ,ugg.gift_gold_unit_amount desc
    </select>
    <select id="getUserSendGiftList" resultType="com.hzy.core.model.vo.app.AppUserReceiveGiftVo">
        SELECT ugg.gift_id       AS giftId,
               ugg.gift_name as giftName,
               ag.img_url as imgUrl,
               ugg.gift_num as sumCount,
               u.nick_name as nickName,
               u.head_portrait as headImgUrl,
               ugg.created_time as createTime,
               ugg.gift_gold_unit_amount as giftPrice
        FROM user_give_gift as ugg
                 left join
             app_gift ag ON ag.id = ugg.gift_id
                 left join
             app_user u on u.id = ugg.to_user_id
        WHERE ugg.user_id = #{viewUserId}
        order by ugg.created_time desc ,ugg.gift_gold_unit_amount desc

    </select>
</mapper>
