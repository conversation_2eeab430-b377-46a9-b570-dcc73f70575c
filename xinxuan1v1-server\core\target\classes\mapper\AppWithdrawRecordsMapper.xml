<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppWithdrawRecordsMapper">

    <resultMap type="AppWithdrawRecords" id="AppWithdrawRecordsResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="withdrawPoints" column="withdraw_points"/>
        <result property="serviceCharge" column="service_charge"/>
        <result property="actualAmount" column="actual_amount"/>
        <result property="onePointsEqRmb" column="one_points_eq_rmb"/>
        <result property="alipayName" column="alipay_name"/>
        <result property="alipayAccount" column="alipay_account"/>
        <result property="alipayResult" column="alipay_result"/>
        <result property="status" column="status"/>
        <result property="refuseMsg" column="refuse_msg"/>
        <result property="withdrawAmount" column="withdraw_amount"/>
    </resultMap>

    <sql id="selectAppWithdrawRecordsVo">
        select id,
        user_id,
        create_time,
        update_time,
        withdraw_points,
        service_charge,
        actual_amount,
        one_points_eq_rmb,
        alipay_name,
        alipay_account,
        alipay_result,
        status,
        refuse_msg,
        withdraw_amount
        from app_withdraw_records
    </sql>

    <select id="selectAppWithdrawRecordsByIds" resultType="com.hzy.core.entity.AppWithdrawRecords">
        select wr.id,
               wr.user_id,
               wr.create_time,
               wr.update_time,
               wr.withdraw_points,
               wr.service_charge,
               wr.actual_amount,
               wr.one_points_eq_rmb,
               wr.alipay_name,
               wr.alipay_account,
               wr.alipay_result,
               wr.status,
               wr.refuse_msg,
               wr.withdraw_amount,
               u.phone as userPhone,
               u.recode_code as userRecodeCode,
               u.id as userId,
               u.nick_name as userNickName,
               u.head_portrait as userHeadPortrait
        from app_withdraw_records wr
            left join app_user u on wr.user_id = u.id
        where wr.id in
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectAppWithdrawRecordsList" parameterType="AppWithdrawRecords" resultMap="AppWithdrawRecordsResult">
        select wr.id,
        wr.user_id,
        wr.create_time,
        wr.update_time,
        wr.withdraw_points,
        wr.service_charge,
        wr.actual_amount,
        wr.one_points_eq_rmb,
        wr.alipay_name,
        wr.alipay_account,
        wr.alipay_result,
        wr.status,
        wr.refuse_msg,
        wr.withdraw_amount,
        u.phone as userPhone,
        u.recode_code as userRecodeCode,
        u.id as userId,
        u.nick_name as userNickName,
        u.head_portrait as userHeadPortrait
        from app_withdraw_records wr,
        app_user u
        where wr.user_id=u.id
        <if test="status != null ">and wr.status = #{status}</if>
        <if test="userId != null ">and wr.user_id = #{userId}</if>
        <if test="alipayName != null  and alipayName != ''">
            and wr.alipay_name like concat('%', #{alipayName}, '%')
        </if>
        <if test="alipayAccount != null  and alipayAccount != ''">
            and wr.alipay_account like concat('%',#{alipayAccount}, '%')
        </if>
        <if test="userPhone != null  and userPhone != ''">
            and u.phone like concat('%', #{userPhone}, '%')
        </if>
        <if test="userRecodeCode != null  and userRecodeCode != ''">
            and u.recode_code like concat('%', #{userRecodeCode}, '%')
        </if>
        <if test="userId != null ">
            and u.id like concat('%', #{userId}, '%')
        </if>
        <if test="userNickName != null  and userNickName != ''">
            and u.nick_name like concat('%', #{userNickName}, '%')
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(wr.create_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(wr.create_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        order by wr.id desc
    </select>

    <select id="selectAppWithdrawRecordsById" parameterType="Long" resultMap="AppWithdrawRecordsResult">
        <include refid="selectAppWithdrawRecordsVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppWithdrawRecords" parameterType="AppWithdrawRecords" useGeneratedKeys="true" keyProperty="id">
        insert into app_withdraw_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="withdrawPoints != null">withdraw_points,</if>
            <if test="serviceCharge != null">service_charge,</if>
            <if test="actualAmount != null">actual_amount,</if>
            <if test="onePointsEqRmb != null">one_points_eq_rmb,</if>
            <if test="alipayName != null">alipay_name,</if>
            <if test="alipayAccount != null">alipay_account,</if>
            <if test="alipayResult != null">alipay_result,</if>
            <if test="status != null">status,</if>
            <if test="refuseMsg != null">refuse_msg,</if>
            <if test="withdrawAmount != null">withdraw_amount,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="withdrawPoints != null">#{withdrawPoints},</if>
            <if test="serviceCharge != null">#{serviceCharge},</if>
            <if test="actualAmount != null">#{actualAmount},</if>
            <if test="onePointsEqRmb != null">#{onePointsEqRmb},</if>
            <if test="alipayName != null">#{alipayName},</if>
            <if test="alipayAccount != null">#{alipayAccount},</if>
            <if test="alipayResult != null">#{alipayResult},</if>
            <if test="status != null">#{status},</if>
            <if test="refuseMsg != null">#{refuseMsg},</if>
            <if test="withdrawAmount != null">#{withdrawAmount},</if>
        </trim>
    </insert>

    <update id="updateAppWithdrawRecords" parameterType="AppWithdrawRecords">
        update app_withdraw_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="withdrawPoints != null">withdraw_points = #{withdrawPoints},</if>
            <if test="serviceCharge != null">service_charge = #{serviceCharge},</if>
            <if test="actualAmount != null">actual_amount = #{actualAmount},</if>
            <if test="onePointsEqRmb != null">one_points_eq_rmb = #{onePointsEqRmb},</if>
            <if test="alipayName != null">alipay_name = #{alipayName},</if>
            <if test="alipayAccount != null">alipay_account = #{alipayAccount},</if>
            <if test="alipayResult != null">alipay_result = #{alipayResult},</if>
            <if test="status != null">status = #{status},</if>
            <if test="refuseMsg != null">refuse_msg = #{refuseMsg},</if>
            <if test="withdrawAmount != null">withdraw_amount = #{withdrawAmount},</if>
        </trim>
        where id = #{id}
    </update>


    <delete id="deleteAppWithdrawRecordsById" parameterType="Long">
        delete
        from app_withdraw_records
        where id = #{id}
    </delete>

    <delete id="deleteAppWithdrawRecordsByIds" parameterType="String">
        delete from app_withdraw_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getActualAmount" resultType="java.math.BigDecimal" parameterType="java.lang.Long">
        select actual_amount
        from app_withdraw_records
        where id = #{id}
    </select>

</mapper>