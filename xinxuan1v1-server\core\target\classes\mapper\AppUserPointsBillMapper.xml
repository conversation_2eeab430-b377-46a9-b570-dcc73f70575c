<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppUserPointsBillMapper">

    <resultMap type="AppUserPointsBill" id="AppUserPointsBillResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="createTime" column="created_time"/>
        <result property="updateTime" column="updated_time"/>
        <result property="createBy" column="created_by"/>
        <result property="updateBy" column="updated_by"/>
        <result property="billType" column="bill_type"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="amount" column="amount"/>
        <result property="objectId" column="object_id"/>
        <result property="isDel" column="deleted"/>
        <result property="remarksMsg" column="remarks_msg"/>
        <result property="guildId" column="guild_id"/>
    </resultMap>

    <sql id="selectAppUserPointsBillVo">
        select id, user_id, created_time,total_amount, updated_time, created_by, updated_by, bill_type, amount, object_id, deleted,
        remarks_msg, guild_id from app_user_points_bill
    </sql>

    <insert id="insertAppUserPointsBill" parameterType="AppUserPointsBill" useGeneratedKeys="true" keyProperty="id">
        insert into app_user_points_bill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="createTime != null">created_time,</if>
            <if test="updateTime != null">updated_time,</if>
            <if test="createBy != null">created_by,</if>
            <if test="updateBy != null">updated_by,</if>
            <if test="billType != null">bill_type,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="amount != null">amount,</if>
            <if test="objectId != null">object_id,</if>
            <if test="isDel != null">deleted,</if>
            <if test="remarksMsg != null">remarks_msg,</if>
            <if test="chatRoomId != null">chat_room_id,</if>
            <if test="giftIncomeScale != null">gift_income_scale,</if>
            <if test="guildId != null">guild_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="billType != null">#{billType},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="amount != null">#{amount},</if>
            <if test="objectId != null">#{objectId},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="remarksMsg != null">#{remarksMsg},</if>
            <if test="chatRoomId != null">#{chatRoomId},</if>
            <if test="giftIncomeScale != null">#{giftIncomeScale},</if>
            <if test="guildId != null">#{guildId},</if>
        </trim>
    </insert>

    <select id="getUserPointsBillList" resultType="com.hzy.core.model.vo.app.AppUserPointsBillVo">
        select b.id as pointsBillId,
        b.created_time as createTime,
        b.object_id as objectId,
        b.bill_type as billType,
        b.total_amount as totalAmount,
        b.amount,
        b.user_id as userId,
        b.remarks_msg as remarksMsg,
        b.guild_id as guildId
        from app_user_points_bill b
        where 1=1
        <if test="queryType!=null">
            and b.bill_type=#{queryType}
        </if>
        <if test="isShow == null">
            and b.bill_type not in (23, 25)
        </if>
        <if test="queryUserId!=null">
            and b.user_id=#{queryUserId}
        </if>
        <if test="userId!=null">
            and b.user_id=#{userId}
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(b.created_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(b.created_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        order by b.id desc
    </select>

    <select id="getPointsBillsByUserId" resultType="com.hzy.core.model.vo.app.AppUserPointsBillVo">
        select id as pointsBillId, created_time as createTime, object_id, bill_type,
            total_amount, amount, user_id, remarks_msg, guild_id as guildId
        from app_user_points_bill
        where user_id = #{userId}
        and amount != 0
        order by id desc
    </select>

    <select id="getFlowingWaterByUserId" resultType="com.hzy.core.model.vo.app.AppUserPointsBillVo">
        select id as pointsBillId, created_time as createTime, object_id, bill_type,
               total_amount, amount, user_id, remarks_msg as billTypeName, guild_id as guildId
        from app_user_points_bill
        where user_id = #{userId}
          and gift_income_scale = 0
        <if test="startDate != null and startDate != ''">
            and date_format(created_time,'%y%m%d') &gt;= date_format(#{startDate},'%y%m%d')
        </if>
        <if test="endDate != null and endDate != ''">
            and date_format(created_time,'%y%m%d') &lt;= date_format(#{endDate},'%y%m%d')
        </if>
        order by id desc
    </select>

    <select id="getTotalFlowingWater" resultType="java.math.BigDecimal">
        select sum(total_amount) as totalAmount
        from app_user_points_bill
        where user_id = #{userId}
        and gift_income_scale = 0
        <if test="startDate != null and startDate != ''">
            and date_format(created_time,'%y%m%d') &gt;= date_format(#{startDate},'%y%m%d')
        </if>
        <if test="endDate != null and endDate != ''">
            and date_format(created_time,'%y%m%d') &lt;= date_format(#{endDate},'%y%m%d')
        </if>
        order by id desc
    </select>

    <update id="updateAppUserPointBill" parameterType="AppUserPointsBill">
        update app_user_points_bill
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="createTime != null">created_time = #{createTime},</if>
            <if test="updateTime != null">updated_time = #{updateTime},</if>
            <if test="createBy != null">created_by = #{createBy},</if>
            <if test="updateBy != null">updated_by = #{updateBy},</if>
            <if test="billType != null">bill_type = #{billType},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="objectId != null">object_id = #{objectId},</if>
            <if test="isDel != null">deleted = #{isDel},</if>
            <if test="remarksMsg != null">remarks_msg = #{remarksMsg},</if>
            <if test="userContributionValue != null">user_contribution_value = #{userContributionValue},</if>
            <if test="toUserCharmValue != null">to_user_charm_value = #{toUserCharmValue},</if>
        </trim>
        where id = #{id}
    </update>
    <!-- 主播营收 -->
    <select id="getRevenueData" resultType="com.hzy.core.model.vo.admin.RevenueDataVo">
        SELECT
            DATE_FORMAT(b.created_time, '%Y-%m-%d') AS date,
            u.recode_code AS recodeCode,
            u.id AS userId,
            u.nick_name AS nickName,
            ag.guild_name as guildName,
            SUM(CASE WHEN b.bill_type IN (5, 6, 7, 8, 9, 12) THEN b.total_amount ELSE 0 END) AS totalFlow,
            SUM(CASE WHEN b.bill_type IN (5) THEN b.total_amount ELSE 0 END) AS roomFlow,
            SUM(CASE WHEN b.bill_type IN (8) THEN b.total_amount ELSE 0 END) AS privateLetterFlow,
            SUM(CASE WHEN b.bill_type IN (12) THEN COALESCE(b.total_amount, 0) ELSE 0 END) AS privateChatFlow,
            SUM(CASE WHEN b.bill_type IN (6, 7, 9) THEN b.total_amount ELSE 0 END) AS callFlow,
            SUM(CASE WHEN b.bill_type IN (6) THEN b.total_amount ELSE 0 END) AS voiceCallFlow,
            SUM(CASE WHEN b.bill_type IN (7, 9) THEN b.total_amount ELSE 0 END) AS videoCallFlow
        FROM
        app_user_points_bill b
        left JOIN
        app_user u ON b.user_id = u.id
        left JOIN
        app_guild ag ON b.guild_id = ag.id
        WHERE
        b.deleted = 0
        AND b.bill_type IN (5, 6, 7, 8, 9, 12)
        <if test="recodeCode != null and recodeCode != ''">
            AND u.recode_code = #{recodeCode}
        </if>
        <if test="brokerId != null">
            AND agm.sys_user_id = #{brokerId}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE_FORMAT(b.created_time, '%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE_FORMAT(b.created_time, '%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test="guildIds != null and queryNoGuild == 0">
            	AND b.guild_id in
				<foreach collection="guildIds" item="id" separator="," open="(" close=")">
					#{id}
				</foreach>
        </if>
        <if test="guildIds != null and queryNoGuild == 1">
            AND b.guild_id IS NULL
        </if>
        GROUP BY
        DATE_FORMAT(b.created_time, '%Y-%m-%d'), u.recode_code, u.nick_name
        ORDER BY
        date DESC
    </select>
    <!-- 房间营收 -->
    <select id="getRoomRevenueData" resultType="com.hzy.core.model.vo.admin.RoomRevenueDataVo">
        SELECT
            DATE_FORMAT(ugg.created_time, '%Y-%m-%d') AS date,
            acr.third_id AS roomCode,
            acr.id AS roomId,
            acr.name AS name,
            ag.guild_name,
            SUM(ugg.gift_gold_total_amount) AS totalFlow,
            IFNULL(
                TIMESTAMPDIFF(
                    SECOND,
                    acr.start_time,
                    CASE
                        WHEN acr.last_end_time IS NULL THEN NOW()
                        ELSE acr.last_end_time
                    END
                ) / 3600.0,
                0
            ) AS duration
        FROM
            app_chat_room acr
        left JOIN
            user_give_gift ugg ON acr.id = ugg.chat_room_id
        left JOIN
            app_guild_member agm ON acr.hall_owner_user_id = agm.user_id
        left JOIN
            app_guild ag ON ugg.guild_id = ag.id
        left join
            app_user u ON ag.user_id = u.id
        WHERE
            acr.is_del = 0
            AND ugg.deleted = 0
            <if test="roomType == 1">
                AND LENGTH(acr.third_id) &lt; 6
            </if>
            <if test="roomType == 2">
                AND LENGTH(acr.third_id) &gt;= 6
            </if>
            <if test="guildLeaderCode != null and guildLeaderCode != ''">
                AND u.recode_code = #{guildLeaderCode}
            </if>
            <if test="guildLeaderName != null and guildLeaderName != ''">
                AND u.nick_name = #{guildLeaderName}
            </if>
            <if test="roomCode != null and roomCode != ''">
                AND acr.third_id = #{roomCode}
            </if>
            <if test="roomId != null and roomId != ''">
                AND acr.id = #{roomId}
            </if>
            <if test="brokerId != null">
                AND agm.sys_user_id = #{brokerId}
            </if>
            <if test="roomName != null and roomName != ''">
                AND acr.name LIKE CONCAT('%', #{roomName}, '%')
            </if>
            <if test="startDate != null and startDate != ''">
                AND DATE_FORMAT(ugg.created_time, '%Y-%m-%d') &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE_FORMAT(ugg.created_time, '%Y-%m-%d') &lt;= #{endDate}
            </if>
            <if test="guildIds != null and queryNoGuild == 0">
            	AND ugg.id in
				<foreach collection="guildIds" item="id" separator="," open="(" close=")">
					#{id}
				</foreach>
        	</if>
        <if test="guildIds != null and queryNoGuild == 1">
            AND ugg.id IS NULL
        </if>
        <if test="giftType != null">
            and ugg.gift_type = #{giftType}
        </if>
        GROUP BY
            DATE_FORMAT(ugg.created_time, '%Y-%m-%d'), acr.third_id, acr.name
        ORDER BY
            date DESC, totalFlow DESC
    </select>

    <!-- 公会营收 -->
    <select id="getGuildRevenueData" resultType="com.hzy.core.model.vo.admin.GuildRevenueDataVo">
        SELECT
            DATE_FORMAT(revenue_date, '%Y-%m-%d') AS date,
            roomCode,
            roomName,
            guildName,
            SUM(roomTotalFlow) AS roomTotalFlow,
            SUM(userTotalFlow) AS userTotalFlow
        FROM (
             <!-- 房间流水数据（来自user_give_gift表） -->
            SELECT
                ugg.created_time AS revenue_date,
                acr.third_id AS roomCode,
                acr.name AS roomName,
                ag.guild_name AS guildName,
                SUM(ugg.gift_gold_total_amount) AS roomTotalFlow,
                0 AS userTotalFlow
            FROM
                user_give_gift ugg
            left JOIN
                app_chat_room acr ON ugg.chat_room_id = acr.id
            left JOIN
                app_guild_member agm ON acr.hall_owner_user_id = agm.user_id
            left JOIN
                app_guild ag ON agm.guild_id = ag.id
            WHERE
                ugg.deleted = 0
                AND acr.is_del = 0
                AND ag.is_del = 0
                <if test="guildId != null and guildId != ''">
                    AND ag.id = #{guildId}
                </if>
                <if test="guildName != null and guildName != ''">
                    AND ag.guild_name LIKE CONCAT('%', #{guildName}, '%')
                </if>
                <if test="startDate != null and startDate != ''">
                    AND DATE_FORMAT(ugg.created_time, '%Y-%m-%d') &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate != ''">
                    AND DATE_FORMAT(ugg.created_time, '%Y-%m-%d') &lt;= #{endDate}
                </if>
            GROUP BY
                DATE_FORMAT(ugg.created_time, '%Y-%m-%d'), acr.third_id, ag.guild_name
                
            UNION ALL            
            <!-- 主播流水数据（来自app_user_points_bill表） -->
            SELECT
                b.created_time AS revenue_date, 
                acr.third_id AS roomCode,
                acr.name AS roomName,
                ag.guild_name AS guildName,
                0 AS roomTotalFlow,
                SUM(CASE WHEN b.bill_type = 5 THEN b.amount ELSE 0 END) AS userTotalFlow
            FROM
                app_user_points_bill b
            left JOIN
                app_chat_room acr ON b.chat_room_id = acr.id
            left JOIN
                app_guild_member agm ON acr.hall_owner_user_id = agm.user_id
            left JOIN
                app_guild ag ON agm.guild_id = ag.id
            WHERE
                b.deleted = 0
                AND ag.is_del = 0
                AND b.bill_type = 5
                <if test="guildId != null and guildId != ''">
                    AND ag.id = #{guildId}
                </if>
                <if test="guildName != null and guildName != ''">
                    AND ag.guild_name LIKE CONCAT('%', #{guildName}, '%')
                </if>
                <if test="startDate != null and startDate != ''">
                    AND DATE_FORMAT(b.created_time, '%Y-%m-%d') &gt;= #{startDate}
                </if>
                <if test="endDate != null and endDate != ''">
                    AND DATE_FORMAT(b.created_time, '%Y-%m-%d') &lt;= #{endDate}
                </if>
            GROUP BY
                DATE_FORMAT(b.created_time, '%Y-%m-%d'), acr.third_id, ag.guild_name
        ) AS combined_data
        GROUP BY
            date, roomCode, guildName
        ORDER BY
            date DESC, roomTotalFlow DESC, userTotalFlow DESC
    </select>

    <!-- getUserPointDetail --> 

    <select id="getUserPointDetail" resultType="com.hzy.core.model.vo.admin.UserPointDetailVo">
        SELECT
        b.id,
        b.user_id as userId,
        b.created_time as createTime,
        b.total_amount as totalAmount,
        b.amount as amount,
        b.bill_type as billType,
        b.object_id as objectId,
        b.remarks_msg as remarksMsg,
        b.chat_room_id as chatRoomId,
        b.guild_id as guildId,
        u.recode_code as recodeCode,
        u.nick_name as nickName,
        acr.name as chatRoomName,
        acr.third_id as thirdId
        FROM
        app_user_points_bill b
        LEFT JOIN
        app_user u ON b.user_id = u.id
        LEFT JOIN
        app_chat_room acr ON b.chat_room_id = acr.id
        WHERE
        b.deleted = 0
        <foreach collection="queryType" item="type" open="AND b.bill_type IN (" close=")" separator=",">
            #{type}
        </foreach>
        <if test="roomId != null and roomId != ''">
            AND acr.id = #{roomId}
        </if>
        <if test="userId != null and userId != ''">
            AND b.user_id = #{userId}
        </if>
        <if test="recodeCode != null and recodeCode != ''">
            AND u.recode_code = #{recodeCode}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE_FORMAT(b.created_time, '%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE_FORMAT(b.created_time, '%Y-%m-%d') &lt;= #{endDate}
        </if>
        ORDER BY b.created_time desc
    </select>

    <!-- getTotalFlowSum --> 

    <select id="getTotalFlowSum" resultType="com.hzy.core.model.vo.admin.TotalFlowVo">
        SELECT
            COALESCE(SUM(CASE WHEN b.bill_type IN (5, 6, 7, 8, 9, 12) THEN COALESCE(b.total_amount, 0) ELSE 0 END), 0) AS totalFlow,
            COALESCE(SUM(CASE WHEN b.bill_type IN (5) THEN COALESCE(b.total_amount, 0) ELSE 0 END), 0) AS roomFlow,
            COALESCE(SUM(CASE WHEN b.bill_type IN (8) THEN COALESCE(b.total_amount, 0) ELSE 0 END), 0) AS privateLetterFlow,
            COALESCE(SUM(CASE WHEN b.bill_type IN (12) THEN COALESCE(b.total_amount, 0) ELSE 0 END), 0) AS privateChatFlow,
            COALESCE(SUM(CASE WHEN b.bill_type IN (6, 7, 9) THEN COALESCE(b.total_amount, 0) ELSE 0 END), 0) AS callFlow,
            COALESCE(SUM(CASE WHEN b.bill_type IN (6) THEN COALESCE(b.total_amount, 0) ELSE 0 END), 0) AS voiceCallFlow,
            COALESCE(SUM(CASE WHEN b.bill_type IN (7, 9) THEN COALESCE(b.total_amount, 0) ELSE 0 END), 0) AS videoCallFlow
        FROM
            app_user_points_bill b
        left JOIN
            app_user u ON b.user_id = u.id
        left JOIN
            app_guild ag ON b.guild_id = ag.id
        WHERE
            b.deleted = 0
        <if test="guildIds != null and queryNoGuild == 0">
            AND b.guild_id in
            <foreach collection="guildIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="guildIds != null and queryNoGuild == 1">
            AND b.guild_id IS NULL
        </if>
        <if test="roomId != null and roomId != ''">
                AND b.chat_room_id = #{roomId}
            </if>
            <if test="recodeCode != null and recodeCode != ''">
                AND u.recode_code = #{recodeCode}
            </if>
        <if test="brokerId != null">
            AND agm.sys_user_id = #{brokerId}
        </if>
        <if test="startDate != null and startDate != ''">
                AND DATE_FORMAT(b.created_time, '%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
                AND DATE_FORMAT(b.created_time, '%Y-%m-%d') &lt;= #{endDate}
        </if>
    </select>
    
    <!-- getRoomTotalFlowSum --> 
    <select id="getRoomTotalFlowSum" resultType="java.math.BigDecimal">
        SELECT
            SUM(ugg.gift_gold_total_amount) AS totalFlow
        FROM
            app_chat_room acr
        left JOIN
            user_give_gift ugg ON acr.id = ugg.chat_room_id
        left JOIN
            app_guild_member agm ON acr.hall_owner_user_id = agm.user_id
        left JOIN
            app_guild ag ON ugg.guild_id = ag.id
        left join
            app_user u ON ag.user_id = u.id
        WHERE
            acr.is_del = 0 AND ugg.deleted = 0
        <if test="roomType == 1">
            AND LENGTH(acr.third_id) &lt; 6
        </if>
        <if test="roomType == 2">
            AND LENGTH(acr.third_id) &gt;= 6
        </if>
        <if test="guildLeaderCode != null and guildLeaderCode != ''">
            AND u.recode_code = #{guildLeaderCode}
        </if>
        <if test="guildLeaderName != null and guildLeaderName != ''">
            AND u.nick_name = #{guildLeaderName}
        </if>
        <if test="roomCode != null and roomCode != ''">
            AND acr.third_id = #{roomCode}
        </if>
        <if test="roomId != null and roomId != ''">
            AND acr.id = #{roomId}
        </if>
        <if test="brokerId != null">
            AND agm.sys_user_id = #{brokerId}
        </if>
        <if test="roomName != null and roomName != ''">
            AND acr.name LIKE CONCAT('%', #{roomName}, '%')
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE_FORMAT(ugg.created_time, '%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE_FORMAT(ugg.created_time, '%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test="guildIds != null and queryNoGuild == 0">
            AND ugg.id in
            <foreach collection="guildIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="guildIds != null and queryNoGuild == 1">
            AND ugg.id IS NULL
        </if>
        <if test="giftType != null">
            and ugg.gift_type = #{giftType}
        </if>
    </select>

    <select id="getRoomTotalFlowSum2" resultType="java.math.BigDecimal">
        SELECT
        SUM(CASE WHEN b.bill_type IN (5, 6, 7, 8) THEN b.total_amount ELSE 0 END) AS totalFlow
        FROM
        app_user_points_bill b
        WHERE
        b.deleted = 0
        <if test="roomId != null and roomId != ''">
            AND b.chat_room_id = #{roomId}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE_FORMAT(b.created_time, '%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE_FORMAT(b.created_time, '%Y-%m-%d') &lt;= #{endDate}
        </if>
    </select>

    <select id="getUserPointsBillTotal" resultType="com.hzy.core.model.vo.app.AppUserPointsBillTotalVo">
        select IFNULL(sum(aupb.amount),0) as amountTotal
        from app_user_points_bill aupb
        left join app_user au on aupb.user_id = au.id
        <where>
            <if test="queryType!=null">
                and aupb.bill_type=#{queryType}
            </if>
            <if test="userId!=null">
                and aupb.user_id=#{userId}
            </if>
            <if test="recodeCode != null and recodeCode != ''">
                and au.recode_code = #{recodeCode}
            </if>
            <if test="queryBeginTime != null and queryBeginTime != ''">
                and date_format(aupb.create_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
            </if>
            <if test="queryEndTime != null and queryEndTime != ''">
                and date_format(aupb.create_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
            </if>
        </where>
    </select>

    <select id="getCorporateData" resultType="com.hzy.core.model.vo.admin.CorporateDataVo">
        SELECT
        DATE_FORMAT(b.created_time, '%Y-%m-%d') AS date,
        MAX(b.created_time) AS last_record_time,
        SUM(IF(b.bill_type IN (5, 6, 7, 8, 9, 12), b.total_amount, 0)) AS totalFlow,
        u.recode_code AS recodeCode,
        u.nick_name AS nickName,
        ag.guild_name AS guildName,
        b.gift_income_scale AS giftIncomeScale
        FROM
        app_user_points_bill b
        left JOIN
        app_user u ON b.user_id = u.id
        left JOIN
        app_guild ag ON b.guild_id = ag.id
        WHERE
        b.deleted = false
        AND b.gift_income_scale = #{giftIncomeScale}
        <if test="guildIds != null">
            AND ag.id in
            <foreach collection="guildIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="recodeCode != null and recodeCode != ''">
            AND u.recode_code = #{recodeCode}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE_FORMAT(b.created_time, '%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE_FORMAT(b.created_time, '%Y-%m-%d') &lt;= #{endDate}
        </if>
        group by b.user_id, DATE_FORMAT(b.created_time, '%Y-%m-%d')
        order by date desc, last_record_time desc
    </select>

    <select id="getCorporateTotalFlow" resultType="java.math.BigDecimal">
        SELECT
        COALESCE(SUM(IF(b.bill_type IN (5, 6, 7, 8, 9, 12), COALESCE(b.total_amount, 0), 0)), 0) AS totalFlow
        FROM
        app_user_points_bill b
        left JOIN
        app_user u ON b.user_id = u.id
        left JOIN
        app_guild ag ON b.guild_id = ag.id
        WHERE
        b.deleted = false
        AND b.gift_income_scale = #{giftIncomeScale}
        <if test="guildIds != null">
            AND ag.id in
            <foreach collection="guildIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="recodeCode != null and recodeCode != ''">
            AND u.recode_code = #{recodeCode}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE_FORMAT(b.created_time, '%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE_FORMAT(b.created_time, '%Y-%m-%d') &lt;= #{endDate}
        </if>
    </select>


    <select id="getNoGuildTotalFlowSum" resultType="com.hzy.core.model.vo.admin.TotalFlowVo">
    SELECT
            COALESCE(SUM(CASE WHEN b.bill_type IN (5, 6, 7, 8, 9, 12) THEN COALESCE(b.total_amount, 0) ELSE 0 END), 0) AS totalFlow,
            COALESCE(SUM(CASE WHEN b.bill_type IN (5) THEN COALESCE(b.total_amount, 0) ELSE 0 END), 0) AS roomFlow,
            COALESCE(SUM(CASE WHEN b.bill_type IN (8) THEN COALESCE(b.total_amount, 0) ELSE 0 END), 0) AS privateLetterFlow,
            COALESCE(SUM(CASE WHEN b.bill_type IN (12) THEN COALESCE(b.total_amount, 0) ELSE 0 END), 0) AS privateChatFlow,
            COALESCE(SUM(CASE WHEN b.bill_type IN (6, 7, 9) THEN COALESCE(b.total_amount, 0) ELSE 0 END), 0) AS callFlow,
            COALESCE(SUM(CASE WHEN b.bill_type IN (6) THEN COALESCE(b.total_amount, 0) ELSE 0 END), 0) AS voiceCallFlow,
            COALESCE(SUM(CASE WHEN b.bill_type IN (7, 9) THEN COALESCE(b.total_amount, 0) ELSE 0 END), 0) AS videoCallFlow
        FROM
            app_user_points_bill b
        left JOIN
            app_user u ON b.user_id = u.id
        left JOIN
            app_guild_member agm ON u.id = agm.user_id
        WHERE
            b.deleted = 0
            AND agm.id IS NULL
        <if test="roomId != null and roomId != ''">
                AND b.chat_room_id = #{roomId}
            </if>
            <if test="recodeCode != null and recodeCode != ''">
                AND u.recode_code = #{recodeCode}
            </if>
        <if test="startDate != null and startDate != ''">
                AND DATE_FORMAT(b.created_time, '%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
                AND DATE_FORMAT(b.created_time, '%Y-%m-%d') &lt;= #{endDate}
        </if>
    
    </select>

    <!-- getNoGuildRevenueData --> 

    <select id="getNoGuildRevenueData"  resultType="com.hzy.core.model.vo.admin.RevenueDataVo">
        SELECT
            DATE_FORMAT(b.created_time, '%Y-%m-%d') AS date,
            u.recode_code AS recodeCode,
            u.id AS userId,
            u.nick_name AS nickName,
            SUM(CASE WHEN b.bill_type IN (5, 6, 7, 8, 9, 12) THEN b.total_amount ELSE 0 END) AS totalFlow,
            SUM(CASE WHEN b.bill_type IN (5) THEN b.total_amount ELSE 0 END) AS roomFlow,
            SUM(CASE WHEN b.bill_type IN (8) THEN b.total_amount ELSE 0 END) AS privateLetterFlow,
            SUM(CASE WHEN b.bill_type IN (12) THEN b.total_amount ELSE 0 END) AS privateChatFlow,
            SUM(CASE WHEN b.bill_type IN (6, 7, 9) THEN b.total_amount ELSE 0 END) AS callFlow,
            SUM(CASE WHEN b.bill_type IN (6) THEN b.total_amount ELSE 0 END) AS voiceCallFlow,
            SUM(CASE WHEN b.bill_type IN (7, 9) THEN b.total_amount ELSE 0 END) AS videoCallFlow
        FROM
        app_user_points_bill b
        left JOIN
        app_user u ON b.user_id = u.id
        left JOIN
        app_guild_member agm ON u.id = agm.user_id
        WHERE
        b.deleted = 0
        AND b.bill_type IN (5, 6, 7, 8, 9, 12)
        AND agm.id IS NULL
        <if test="recodeCode != null and recodeCode != ''">
            AND u.recode_code = #{recodeCode}
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE_FORMAT(b.created_time, '%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE_FORMAT(b.created_time, '%Y-%m-%d') &lt;= #{endDate}
        </if>
        GROUP BY
        DATE_FORMAT(b.created_time, '%Y-%m-%d'), u.recode_code, u.nick_name
        ORDER BY
        date DESC

    </select>
    
    <!-- getNoGuildRoomTotalFlowSum --> 
    <select id="getNoGuildRoomTotalFlowSum" resultType="java.math.BigDecimal">
        SELECT
            SUM(ugg.gift_gold_total_amount) AS totalFlow
        FROM
            app_chat_room acr
        left JOIN
            user_give_gift ugg ON acr.id = ugg.chat_room_id
        left JOIN
            app_guild_member agm ON acr.hall_owner_user_id = agm.user_id
        left join
            app_user u ON agm.user_id = u.id
        WHERE
            acr.is_del = 0 AND ugg.deleted = 0
            AND ugg.guild_id IS NULL
        <if test="roomType == 1">
            AND LENGTH(acr.third_id) &lt; 6
        </if>
        <if test="roomType == 2">
            AND LENGTH(acr.third_id) &gt;= 6
        </if>
        <if test="guildLeaderCode != null and guildLeaderCode != ''">
            AND u.recode_code = #{guildLeaderCode}
        </if>
        <if test="guildLeaderName != null and guildLeaderName != ''">
            AND u.nick_name = #{guildLeaderName}
        </if>
        <if test="roomCode != null and roomCode != ''">
            AND acr.third_id = #{roomCode}
        </if>
        <if test="roomId != null and roomId != ''">
            AND acr.id = #{roomId}
        </if>
        <if test="brokerId != null">
            AND agm.sys_user_id = #{brokerId}
        </if>
        <if test="roomName != null and roomName != ''">
            AND acr.name LIKE CONCAT('%', #{roomName}, '%')
        </if>
        <if test="startDate != null and startDate != ''">
            AND DATE_FORMAT(ugg.created_time, '%Y-%m-%d') &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND DATE_FORMAT(ugg.created_time, '%Y-%m-%d') &lt;= #{endDate}
        </if>
        <if test="giftType != null">
            and ugg.gift_type = #{giftType}
        </if>
    </select>
    
    <!-- getNoGuildRoomRevenueData --> 
    <select id="getNoGuildRoomRevenueData" resultType="com.hzy.core.model.vo.admin.RoomRevenueDataVo">
        SELECT
            DATE_FORMAT(ugg.created_time, '%Y-%m-%d') AS date,
            acr.third_id AS roomCode,
            acr.id AS roomId,
            acr.name AS name,
            SUM(ugg.gift_gold_total_amount) AS totalFlow,
            IFNULL(
                TIMESTAMPDIFF(
                    SECOND,
                    acr.start_time,
                    CASE
                        WHEN acr.last_end_time IS NULL THEN NOW()
                        ELSE acr.last_end_time
                    END
                ) / 3600.0,
                0
            ) AS duration
        FROM
            app_chat_room acr
        left JOIN
            user_give_gift ugg ON acr.id = ugg.chat_room_id
        left JOIN
            app_guild_member agm ON acr.hall_owner_user_id = agm.user_id
        left join
            app_user u ON agm.user_id = u.id
        WHERE
            acr.is_del = 0
            AND ugg.deleted = 0
            AND ugg.guild_id IS NULL
            <if test="roomType == 1">
                AND LENGTH(acr.third_id) &lt; 6
            </if>
            <if test="roomType == 2">
                AND LENGTH(acr.third_id) &gt;= 6
            </if>
            <if test="guildLeaderCode != null and guildLeaderCode != ''">
                AND u.recode_code = #{guildLeaderCode}
            </if>
            <if test="guildLeaderName != null and guildLeaderName != ''">
                AND u.nick_name = #{guildLeaderName}
            </if>
            <if test="roomCode != null and roomCode != ''">
                AND acr.third_id = #{roomCode}
            </if>
            <if test="roomId != null and roomId != ''">
                AND acr.id = #{roomId}
            </if>
            <if test="brokerId != null">
                AND agm.sys_user_id = #{brokerId}
            </if>
            <if test="roomName != null and roomName != ''">
                AND acr.name LIKE CONCAT('%', #{roomName}, '%')
            </if>
            <if test="startDate != null and startDate != ''">
                AND DATE_FORMAT(ugg.created_time, '%Y-%m-%d') &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE_FORMAT(ugg.created_time, '%Y-%m-%d') &lt;= #{endDate}
            </if>
            <if test="giftType != null">
                and ugg.gift_type = #{giftType}
            </if>
        GROUP BY
            DATE_FORMAT(ugg.created_time, '%Y-%m-%d'), acr.third_id, acr.name
        ORDER BY
            date DESC, totalFlow DESC
    </select>
    
    <select id="getMlTopThree" resultType="com.hzy.core.model.vo.app.AppUserGoldBillGetTopThreeVo">
        SELECT user_id,
               nick_name,
               head_portrait,
               SUM(amount) AS total_amount,
               guild_id
        FROM app_user_points_bill ab
                 left join app_user au on au.id = ab.user_id
        WHERE
            DATE (ab.created_time) = CURDATE()
        GROUP BY
            user_id
        ORDER BY
            total_amount DESC
            LIMIT 3;
    </select>
    
    </mapper>