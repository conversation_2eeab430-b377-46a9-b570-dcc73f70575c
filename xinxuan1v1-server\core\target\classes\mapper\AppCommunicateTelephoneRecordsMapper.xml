<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppCommunicateTelephoneRecordsMapper">

    <resultMap type="AppCommunicateTelephoneRecords" id="AppCommunicateTelephoneRecordsResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="initiateUserId" column="initiate_user_id"/>
        <result property="receiveUserId" column="receive_user_id"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
        <result property="connectTime" column="connect_time"/>
        <result property="hangUpTime" column="hang_up_time"/>
        <result property="hangUpUserId" column="hang_up_user_id"/>
        <result property="consumptionGold" column="consumption_gold"/>
        <result property="useVideoCard" column="use_video_card"/>
        <result property="videoCardAmount" column="video_card_amount"/>
    </resultMap>

    <sql id="selectAppCommunicateTelephoneRecordsVo">
        select id,
               create_time,
               update_time,
               initiate_user_id,
               receive_user_id,
               `type`,
               `status`,
               connect_time,
               hang_up_time,
               hang_up_user_id,
               consumption_gold,
               use_video_card,
               video_card_amount
        from app_communicate_telephone_records
    </sql>

    <select id="selectAppCommunicateTelephoneRecordsList" parameterType="AppCommunicateTelephoneRecords"
            resultMap="AppCommunicateTelephoneRecordsResult">
        <include refid="selectAppCommunicateTelephoneRecordsVo"/>
        <where>
            <if test="initiateUserId != null ">and initiate_user_id = #{initiateUserId}</if>
            <if test="receiveUserId != null ">and receive_user_id = #{receiveUserId}</if>
            <if test="type != null ">and `type` = #{type}</if>
            <if test="status != null ">and `status` = #{status}</if>
            <if test="connectTime != null ">and connect_time = #{connectTime}</if>
            <if test="hangUpTime != null ">and hang_up_time = #{hangUpTime}</if>
            <if test="hangUpUserId != null ">and hang_up_user_id = #{hangUpUserId}</if>
            <if test="consumptionGold != null ">and consumption_gold = #{consumptionGold}</if>
            <if test="useVideoCard != null ">and use_video_card = #{useVideoCard}</if>
            <if test="videoCardAmount != null ">and video_card_amount = #{videoCardAmount}</if>
        </where>
    </select>

    <select id="selectAppCommunicateTelephoneRecordsById" parameterType="Long"
            resultMap="AppCommunicateTelephoneRecordsResult">
        <include refid="selectAppCommunicateTelephoneRecordsVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppCommunicateTelephoneRecords" parameterType="AppCommunicateTelephoneRecords"
            useGeneratedKeys="true" keyProperty="id">
        insert into app_communicate_telephone_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="initiateUserId != null">initiate_user_id,</if>
            <if test="receiveUserId != null">receive_user_id,</if>
            <if test="type != null">`type`,</if>
            <if test="status != null">`status`,</if>
            <if test="connectTime != null">connect_time,</if>
            <if test="hangUpTime != null">hang_up_time,</if>
            <if test="hangUpUserId != null">hang_up_user_id,</if>
            <if test="consumptionGold != null">consumption_gold,</if>
            <if test="useVideoCard != null">use_video_card,</if>
            <if test="videoCardAmount != null">video_card_amount,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="initiateUserId != null">#{initiateUserId},</if>
            <if test="receiveUserId != null">#{receiveUserId},</if>
            <if test="type != null">#{type},</if>
            <if test="status != null">#{status},</if>
            <if test="connectTime != null">#{connectTime},</if>
            <if test="hangUpTime != null">#{hangUpTime},</if>
            <if test="hangUpUserId != null">#{hangUpUserId},</if>
            <if test="consumptionGold != null">#{consumptionGold},</if>
            <if test="useVideoCard != null">#{useVideoCard},</if>
            <if test="videoCardAmount != null">#{videoCardAmount},</if>
        </trim>
    </insert>

    <update id="updateAppCommunicateTelephoneRecords" parameterType="AppCommunicateTelephoneRecords">
        update app_communicate_telephone_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="initiateUserId != null">initiate_user_id = #{initiateUserId},</if>
            <if test="receiveUserId != null">receive_user_id = #{receiveUserId},</if>
            <if test="type != null">`type` = #{type},</if>
            <if test="status != null">`status` = #{status},</if>
            <if test="connectTime != null">connect_time = #{connectTime},</if>
            <if test="hangUpTime != null">hang_up_time = #{hangUpTime},</if>
            <if test="hangUpUserId != null">hang_up_user_id = #{hangUpUserId},</if>
            <if test="consumptionGold != null">consumption_gold = #{consumptionGold},</if>
            <if test="useVideoCard != null">use_video_card = #{useVideoCard},</if>
            <if test="videoCardAmount != null">video_card_amount = #{videoCardAmount},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppCommunicateTelephoneRecordsById" parameterType="Long">
        delete
        from app_communicate_telephone_records
        where id = #{id}
    </delete>

    <delete id="deleteAppCommunicateTelephoneRecordsByIds" parameterType="String">
        delete from app_communicate_telephone_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="isBusy" resultType="java.lang.Boolean" parameterType="java.lang.Long">
        select if(count(*) > 0, 1, 0)
        from app_communicate_telephone_records
        where (initiate_user_id = #{userId} or receive_user_id = #{userId})
          and `status` in (0, 1)
    </select>

    <select id="getIsBusyRecords" resultMap="AppCommunicateTelephoneRecordsResult" parameterType="java.lang.Long">
        <include refid="selectAppCommunicateTelephoneRecordsVo"/>
        where (initiate_user_id = #{userId} or receive_user_id = #{userId})
        and `status` = 1
        order by id desc limit 0,1
    </select>


    <select id="getAllCommunicateTelephoneInRecords" resultMap="AppCommunicateTelephoneRecordsResult">
        <include refid="selectAppCommunicateTelephoneRecordsVo"/>
        where `status`=1
    </select>


    <select id="getCommunicateTelephoneUserNickNameById" resultType="java.lang.String" parameterType="java.lang.Long">
        select ifnull(u.nick_name, u.recode_code)
        from app_user u,
             app_communicate_telephone_records ctr
        where ctr.id = #{id}
          and ctr.receive_user_id = u.id
    </select>

    <select id="getCommunicateTelephoneUserIdById" resultType="java.lang.Long" parameterType="java.lang.Long">
        select u.id
        from app_user u,
             app_communicate_telephone_records ctr
        where ctr.id = #{id}
          and ctr.receive_user_id = u.id
    </select>

    <select id="getUserCommunicateTelephoneRecordsList"
            parameterType="com.hzy.core.model.vo.admin.AdminAppUserCommunicateTelephoneRecordsVo"
            resultType="com.hzy.core.model.vo.admin.AdminAppUserCommunicateTelephoneRecordsVo">
        select ctr.id as communicateTelephoneId,
        ctr.type as `type`,
        ctr.status as `status`,
        ctr.connect_time as connectTime,
        ctr.hang_up_time as hangUpTime,
        ctr.consumption_gold as consumptionGold,
        receiveUser.head_portrait as receiveUserHeadPortrait,
        receiveUser.nick_name as receiveUserNickName,
        receiveUser.phone as receiveUserPhone,
        receiveUser.id as receiveUserId,
        receiveUser.recode_code as receiveUserRecodeCode,
        sendUser.head_portrait as sendUserHeadPortrait,
        sendUser.nick_name as sendUserNickName,
        sendUser.phone as sendUserPhone,
        sendUser.id as sendUserId,
        sendUser.recode_code as sendUserRecodeCode
        from
        app_communicate_telephone_records ctr
        LEFT JOIN app_user receiveUser on(receiveUser.id=ctr.receive_user_id)
        LEFT JOIN app_user sendUser on(sendUser.id=ctr.initiate_user_id)
        where
        1=1
        <if test="type != null ">and ctr.type = #{type}</if>
        <if test="status != null ">and ctr.status = #{status}</if>
        <if test="receiveUserId != null ">and receiveUser.id = #{receiveUserId}</if>
        <if test="receiveUserPhone != null  and receiveUserPhone != ''">
            and receiveUser.phone like concat('%', #{receiveUserPhone}, '%')
        </if>
        <if test="receiveUserRecodeCode != null  and receiveUserRecodeCode != ''">
            and receiveUser.recode_code like concat('%', #{receiveUserRecodeCode}, '%')
        </if>
        <if test="receiveUserNickName != null  and receiveUserNickName != ''">
            and receiveUser.nick_name like concat('%', #{receiveUserNickName}, '%')
        </if>
        <if test="sendUserId != null ">and sendUser.id = #{sendUserId}</if>
        <if test="sendUserPhone != null  and sendUserPhone != ''">
            and sendUser.phone like concat('%', #{sendUserPhone}, '%')
        </if>
        <if test="sendUserRecodeCode != null  and sendUserRecodeCode != ''">
            and sendUser.recode_code like concat('%', #{sendUserRecodeCode}, '%')
        </if>
        <if test="sendUserNickName != null  and sendUserNickName != ''">
            and sendUser.nick_name like concat('%', #{sendUserNickName}, '%')
        </if>
        order by ctr.id desc
    </select>
    <select id="getRecentCallLogs" resultType="com.hzy.core.model.vo.app.RecentCallLogsVo">
        SELECT r.*,
               CASE
                   WHEN r.initiate_user_id = #{userId} THEN u2.id
                   ELSE u1.id
                   END as userId,
               CASE
                   WHEN r.initiate_user_id = #{userId} THEN u2.head_portrait
                   ELSE u1.head_portrait
                   END as img,
               CASE
                   WHEN r.initiate_user_id = #{userId} THEN u2.nick_name
                   ELSE u1.nick_name
                   END as nick_name
        FROM app_communicate_telephone_records r
                 LEFT JOIN app_user u1 ON r.initiate_user_id = u1.id
                 LEFT JOIN app_user u2 ON r.receive_user_id = u2.id
        WHERE r.initiate_user_id = #{userId}
           OR r.receive_user_id = #{userId}
        ORDER BY r.id DESC
    </select>

    <select id="getOperationalDataDto" resultType="com.hzy.core.model.dto.admin.OperationalDataDto">
        SELECT
            -- 1. 通话人数 (本周与被统计人通话的不同用户数)
            COUNT(DISTINCT IF(connect_time IS NOT NULL, initiate_user_id, NULL))         AS communicationNumber,

            -- 2. 来电接听率 (本周成功接听通话次数/本周通话来电次数)
            IF(COUNT(*) = 0, 0, SUM(IF(connect_time IS NOT NULL, 1, 0)) / COUNT(*))      AS communicationAnswerRatio,

            -- 3. 人均通话时长 (本周通话总时长/本周通话成功人数)
            IF(COUNT(IF(connect_time IS NOT NULL, initiate_user_id, NULL)) = 0, 0,
               SUM(IF(connect_time IS NOT NULL AND hang_up_time IS NOT NULL,
                      TIMESTAMPDIFF(SECOND, connect_time, hang_up_time), 0)
               ) / COUNT(DISTINCT IF(connect_time IS NOT NULL, initiate_user_id, NULL))) AS communicationTimePerPerson,

            -- 4. 60s挂断率 (通话60s内挂断次数/通话总次数)
            IF(SUM(IF(connect_time IS NOT NULL AND hang_up_time IS NOT NULL, 1, 0)) = 0, 0,
               SUM(IF(connect_time IS NOT NULL AND hang_up_time IS NOT NULL AND
                      TIMESTAMPDIFF(SECOND, connect_time, hang_up_time) &lt; 60, 1, 0)
               ) / SUM(IF(connect_time IS NOT NULL AND hang_up_time IS NOT NULL, 1, 0))) AS hangUpRatio
        FROM app_communicate_telephone_records
        WHERE receive_user_id = #{userId} AND create_time BETWEEN #{startTime} AND #{endTime}
    </select>
</mapper>