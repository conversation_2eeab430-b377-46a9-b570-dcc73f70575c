<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppChattingRecordsMapper">

    <resultMap type="AppChattingRecords" id="AppChattingRecordsResult">
        <result property="id" column="id"/>
        <result property="sendUserId" column="send_user_id"/>
        <result property="receiveUserId" column="receive_user_id"/>
        <result property="content" column="content"/>
        <result property="sendTime" column="send_time"/>
        <result property="isRead" column="is_read"/>
        <result property="msgType" column="msg_type"/>
        <result property="isFamily" column="is_family"/>
        <result property="familyId" column="family_id"/>
    </resultMap>

    <sql id="selectAppChattingRecordsVo">
        select id,
        send_user_id,
        receive_user_id,
        content,
        send_time,
        is_read,
        msg_type,
        is_family,
        family_id
        from app_chatting_records
    </sql>

    <select id="selectAppChattingRecordsList" parameterType="AppChattingRecords" resultMap="AppChattingRecordsResult">
        <include refid="selectAppChattingRecordsVo"/>
        <where>
            <if test="sendUserId != null ">and send_user_id = #{sendUserId}</if>
            <if test="receiveUserId != null ">and receive_user_id = #{receiveUserId}</if>
            <if test="content != null  and content != ''">and content = #{content}</if>
            <if test="sendTime != null ">and send_time = #{sendTime}</if>
            <if test="isRead != null ">and is_read = #{isRead}</if>
            <if test="msgType != null ">and msg_type = #{msgType}</if>
            <if test="isFamily != null ">and is_family = #{isFamily}</if>
            <if test="familyId != null ">and family_id = #{familyId}</if>
        </where>
    </select>

    <select id="selectAppChattingRecordsById" parameterType="Long" resultMap="AppChattingRecordsResult">
        <include refid="selectAppChattingRecordsVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppChattingRecords" parameterType="AppChattingRecords" useGeneratedKeys="true" keyProperty="id">
        insert into app_chatting_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sendUserId != null">send_user_id,</if>
            <if test="receiveUserId != null">receive_user_id,</if>
            <if test="content != null">content,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="isRead != null">is_read,</if>
            <if test="msgType != null">msg_type,</if>
            <if test="isFamily != null">is_family,</if>
            <if test="familyId != null">family_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sendUserId != null">#{sendUserId},</if>
            <if test="receiveUserId != null">#{receiveUserId},</if>
            <if test="content != null">#{content},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="isRead != null">#{isRead},</if>
            <if test="msgType != null">#{msgType},</if>
            <if test="isFamily != null">#{isFamily},</if>
            <if test="familyId != null">#{familyId},</if>
        </trim>
    </insert>

    <update id="updateAppChattingRecords" parameterType="AppChattingRecords">
        update app_chatting_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="sendUserId != null">send_user_id = #{sendUserId},</if>
            <if test="receiveUserId != null">receive_user_id = #{receiveUserId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="isRead != null">is_read = #{isRead},</if>
            <if test="msgType != null">msg_type = #{msgType},</if>
            <if test="isFamily != null">is_family = #{isFamily},</if>
            <if test="familyId != null">family_id = #{familyId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppChattingRecordsById" parameterType="Long">
        delete
        from app_chatting_records
        where id = #{id}
    </delete>

    <delete id="deleteAppChattingRecordsByIds" parameterType="String">
        delete from app_chatting_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="getChattingRecordsListBySendUserIdAndReceiveUserId"
            resultType="com.hzy.core.model.vo.app.AppChattingRecordsVo" parameterType="java.lang.Long">
        select c1.id,
        c1.send_user_id as sendUserId,
        send_user.head_portrait as sendUserHeadIcon,
        send_user.nick_name as sendUserNickname,
        c1.receive_user_id as receiveUserId,
        receive_user.head_portrait as receiveUserHeadIcon,
        receive_user.nick_name as receiveUserNickname,
        c1.content,
        receive_user.is_real_person_auth as isRealPersonAuth,
        receive_user.is_real_name_auth as isRealNameAuth,
        if(receive_user.phone!=''and receive_user.phone is not null,1,0) as isPhoneAuth,
        c1.send_time as sendTime,
        c1.is_read as isRead,
        c1.msg_type as msgType
        from app_chatting_records c1
        LEFT JOIN app_user send_user on send_user.id = c1.send_user_id
        LEFT JOIN app_user receive_user on receive_user.id = c1.receive_user_id
        where ((c1.send_user_id = #{sendUserId} and c1.receive_user_id = #{receiveUserId})
        or (c1.send_user_id = #{receiveUserId} and c1.receive_user_id = #{sendUserId}))
        and c1.is_family = false
        and c1.is_del = false
        and (
            (c1.send_user_id = #{sendUserId} and (c1.sender_deleted = 0 OR c1.sender_deleted IS NULL)) or
            (c1.receive_user_id = #{sendUserId} and (c1.receiver_deleted = 0 OR c1.receiver_deleted IS NULL))
        )
        order by c1.id desc
    </select>


    <select id="getUserChattingRecordsList" resultType="com.hzy.core.model.vo.app.AppChattingRecordsVo">
        select c1.id,
        c1.send_user_id as sendUserId,
        c1.receive_user_id as receiveUserId,
        c1.content,
        c1.send_time as sendTime,
        c1.is_read as isRead,
        c1.msg_type as msgType
        from app_chatting_records c1
        where
        c1.is_family = false
        <if test="userId!=null">
            and (c1.send_user_id = #{userId} or c1.receive_user_id = #{userId})
            and (
                (c1.send_user_id = #{userId} and (c1.sender_deleted = 0 OR c1.sender_deleted IS NULL)) or
                (c1.receive_user_id = #{userId} and (c1.receiver_deleted = 0 OR c1.receiver_deleted IS NULL))
            )
        </if>
        <if test="content!=null and content!=''">
            and c1.content like concat('%', #{content}, '%')
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(c1.send_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(c1.send_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        order by c1.id desc
    </select>

    <select id="getUserChattingRecordsList2" resultType="com.hzy.core.model.vo.app.AppChattingRecordsVo">
        select c1.id,
        c1.send_user_id as sendUserId,
        c1.receive_user_id as receiveUserId,
        c1.content,
        c1.send_time as sendTime,
        c1.is_read as isRead,
        c1.msg_type as msgType
        from app_chatting_records c1
        where
        c1.msg_type = 5 and
        c1.is_family = false
        <if test="userId!=null">
            and (c1.send_user_id = #{userId} or c1.receive_user_id = #{userId})
        </if>
        <if test="content!=null and content!=''">
            and c1.content like concat('%', #{content}, '%')
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(c1.send_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(c1.send_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        order by c1.id desc
    </select>

    <update id="setRead" parameterType="java.lang.Long">
        update app_chatting_records
        set is_read=1
        where id = #{id}
    </update>

    <select id="getFamilyChattingRecordsList"
            resultType="com.hzy.core.model.vo.app.AppChattingRecordsVo" parameterType="java.lang.Long">
        select c1.id,
        c1.send_user_id as sendUserId,
        send_user.head_portrait as sendUserHeadIcon,
        send_user.nick_name as sendUserNickname,
        c1.receive_user_id as receiveUserId,
        receive_user.head_portrait as receiveUserHeadIcon,
        receive_user.nick_name as receiveUserNickname,
        c1.content,
        c1.send_time as sendTime,
        c1.is_read as isRead,
        c1.msg_type as msgType
        from app_chatting_records c1
        LEFT JOIN app_user send_user on send_user.id = c1.send_user_id
        LEFT JOIN app_user receive_user on receive_user.id = c1.receive_user_id
        where c1.receive_user_id = #{userId}
        and c1.is_family = true
        and c1.family_id=#{familyId}
        order by c1.id desc
    </select>


    <select id="isContinuousChat" parameterType="java.lang.Long" resultType="java.lang.Boolean">
        select if(count(DISTINCT cr1.id)>0,1,0)
        from app_chatting_records cr1,
        app_chatting_records cr2
        where cr1.send_user_id = cr2.receive_user_id
        and cr1.receive_user_id = cr2.send_user_id
        and cr1.is_family = false
        and cr2.is_family = false
        and cr1.send_user_id = #{userId}
        and cr2.send_user_id = #{toUserId}
    </select>


    <select id="getPrivateLetterNewMsg" parameterType="java.lang.Long" resultMap="AppChattingRecordsResult">
        select c1.id,
        c1.send_user_id,
        c1.receive_user_id,
        c1.content,
        c1.send_time,
        c1.is_read,
        c1.msg_type,
        c1.is_family,
        c1.family_id
        from app_chatting_records c1
        where ((c1.send_user_id = #{receiveUserId} and c1.receive_user_id =#{userId} )
        or (c1.send_user_id = #{userId} and c1.receive_user_id = #{receiveUserId}))
        and c1.is_family=false
        and c1.is_del=false
        order by c1.send_time desc
        limit 0,1
    </select>

    <select id="getPrivateLetterNewMsgUnreadCount" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select
        count(*)
        from app_chatting_records c1
        where c1.receive_user_id = #{receiveUserId} and c1.send_user_id =#{userId} and c1.is_read=0
        and c1.is_family=false
    </select>


    <select id="getFamilyNewMsgUnreadCount" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(*)
        from app_chatting_records c1
        where c1.family_id=#{familyId}
        and c1.is_read=0
        and c1.receive_user_id=#{receiveUserId}
        and c1.is_family=true
    </select>

    <select id="getFamilyNewMsg" parameterType="java.lang.Long" resultMap="AppChattingRecordsResult">
        select c1.id,
        c1.send_user_id,
        c1.receive_user_id,
        c1.content,
        c1.send_time,
        c1.is_read,
        c1.msg_type,
        c1.is_family,
        c1.family_id
        from app_chatting_records c1
        where c1.family_id=#{familyId}
        and c1.is_family=true
        and c1.receive_user_id=#{receiveUserId}
        order by c1.send_time desc
        limit 0,1
    </select>
    <select id="getChattingRecordsByReceiveUserId" resultType="java.lang.Long">
        select distinct send_user_id
        from app_chatting_records
        where receive_user_id=#{receiveUserId}
        and send_time &gt;= DATE_SUB(NOW(), INTERVAL 6 HOUR)
        and send_time &lt;= NOW()
    </select>

    <update id="setAllPrivateLetterMsgRead" parameterType="java.lang.Long">
        update app_chatting_records
        set is_read=1
        where is_read=0 and is_family=false and send_user_id=#{sendUserId} and receive_user_id=#{receiveUserId}
    </update>

    <update id="setAllFamilyMsgRead" parameterType="java.lang.Long">
        update app_chatting_records
        set is_read=1
        where is_read=0 and is_family=true and receive_user_id=#{userId} and family_id=#{familyId}
    </update>

    <select id="isHaveChat" resultType="java.lang.Boolean">
        select if(count(*) &gt; 0, 1, 0)
        from app_chatting_records
        where ((send_user_id = #{sendUserId} and receive_user_id = #{receiveUserId})
        or (send_user_id = #{receiveUserId} and receive_user_id = #{sendUserId}))
        and send_time &gt;= DATE_SUB(NOW(), INTERVAL 6 HOUR)
        and send_time &lt;= NOW()
    </select>

    <select id="getOperationalDataDto" resultType="com.hzy.core.model.dto.admin.OperationalDataDto">
        WITH
            -- 基础数据：本周所有互动记录（已过滤删除、时间范围）
            weekly_interactions AS (SELECT send_user_id, receive_user_id, send_time, DATE(send_time) AS interaction_date
                                    FROM app_chatting_records
                                    WHERE is_del = 0
                                      AND send_time BETWEEN #{startTime} AND #{endTime}
                                      AND #{userId} IN (send_user_id, receive_user_id)
                                      AND send_user_id != receive_user_id),

            -- 上周互动用户
            last_week_users AS (SELECT DISTINCT IF(send_user_id = #{userId}, receive_user_id, send_user_id) AS other_user_id
                                FROM app_chatting_records
                                WHERE is_del = 0
                                  AND send_time BETWEEN DATE_SUB(#{startTime}, INTERVAL 1 WEEK) AND DATE_SUB(#{endTime}, INTERVAL 1 WEEK)
                                  AND #{userId} IN (send_user_id, receive_user_id)
                                  AND send_user_id != receive_user_id),

            -- 本周互动用户
            current_week_users AS (SELECT DISTINCT IF(send_user_id = #{userId}, receive_user_id, send_user_id) AS other_user_id
                                   FROM weekly_interactions),

            -- 互动天数统计（按用户分组）
            interaction_days AS (SELECT IF(send_user_id = #{userId}, receive_user_id, send_user_id) AS other_user_id,
                                        COUNT(DISTINCT interaction_date) AS days_count
                                 FROM weekly_interactions
                                 GROUP BY IF(send_user_id = #{userId}, receive_user_id, send_user_id)),

            -- 每日首次消息（用于统计主动发起聊天的对象）
            daily_first_messages AS (SELECT send_user_id, receive_user_id,
                                            ROW_NUMBER() OVER (PARTITION BY DATE(send_time), LEAST(send_user_id, receive_user_id), GREATEST(send_user_id, receive_user_id)
                                                ORDER BY send_time) AS rn
                                     FROM app_chatting_records
                                     WHERE is_del = 0
                                       AND send_time BETWEEN #{startTime} AND #{endTime}
                                       AND #{userId} IN (send_user_id, receive_user_id)
                                       AND send_user_id != receive_user_id)

        SELECT
            -- 互动人数（本周与目标用户有收/发消息的不同用户数）
            (SELECT COUNT(DISTINCT other_user_id) FROM current_week_users)                 AS interactionNumber,

            -- 互动2天或以上的人数
            (SELECT COUNT(*) FROM interaction_days WHERE days_count >= 2)                  AS interactionTwoDaysNumber,

            -- 次周互动率（本周互动且上周也互动的人数 / 上周互动总人数）
            (SELECT COUNT(CASE WHEN c.other_user_id IS NOT NULL THEN 1 END) * 1.0 /
                    NULLIF(COUNT(l.other_user_id), 0)
             FROM last_week_users l
                      LEFT JOIN current_week_users c ON l.other_user_id = c.other_user_id) AS twoConsecutiveWeeksRatio,

            -- 目标用户主动发起聊天的对象人数
            (SELECT COUNT(DISTINCT receive_user_id)
             FROM daily_first_messages
             WHERE rn = 1 AND send_user_id = #{userId})                                               AS activeInteractionNumber
    </select>

    <select id="getDailyChatReplyStats" resultType="com.hzy.core.model.dto.admin.ChatReplyStatDTO">
        WITH FirstReceivedPerDay AS (
        -- 步骤1: 找出每天每个发送者发送给目标用户的【第一条】非家族消息
        SELECT
        send_user_id AS sender_id,
        DATE(send_time) AS message_date,
        MIN(send_time) AS first_receive_time
        FROM app_chatting_records
        WHERE receive_user_id = #{userId} -- 接收人为目标用户
        AND is_family = 0                   -- 非家族消息
        AND is_del = 0                      -- 未删除
        AND send_time >= #{startTime}         -- 大于等于开始日期
        AND send_time &lt; DATE_ADD(#{endTime}, INTERVAL 1 DAY) -- 小于结束日期的后一天 (确保包含endDate当天)
        GROUP BY sender_id, message_date
        ),
        SentReplies AS (
        -- 步骤2: 找出目标用户发送的所有【潜在回复】消息
        SELECT
        receive_user_id AS original_sender_id, -- 回复的目标（即原始发送者）
        send_time AS reply_send_time           -- 回复的发送时间
        FROM app_chatting_records
        WHERE send_user_id = #{userId}      -- 发送人为目标用户
        AND is_family = 0                   -- 非家族消息
        AND is_del = 0                      -- 未删除
        -- 检查时间范围稍宽，以捕捉在周期结束时收到但在12小时内回复的情况
        AND send_time >= #{startTime}
        AND send_time &lt; DATE_ADD(DATE_ADD(#{endTime}, INTERVAL 1 DAY), INTERVAL 12 HOUR)
        ),
        ValidRepliesToFirstMessage AS (
        -- 步骤3: 找出针对【第一条消息】的【有效回复】（12小时内）
        -- 这里的 DISTINCT 很重要，确保每个发送者每天只被计算一次回复
        SELECT DISTINCT
        fr.message_date,
        fr.sender_id
        FROM FirstReceivedPerDay fr
        INNER JOIN SentReplies sr ON fr.sender_id = sr.original_sender_id -- 回复给了原始发送者
        WHERE sr.reply_send_time > fr.first_receive_time                   -- 回复时间晚于收到时间
        AND sr.reply_send_time &lt;= TIMESTAMPADD(HOUR, 12, fr.first_receive_time) -- 回复在12小时内
        )
        -- 步骤4: 按天聚合统计总发信人数和有效回复人数，并计算回复率
        SELECT
        fr_grouped.message_date,
        fr_grouped.total_senders_count,
        IFNULL(vr_grouped.replied_senders_count, 0) AS replied_senders_count,
        -- 计算回复率，避免除以0错误
        IFNULL(vr_grouped.replied_senders_count * 1.0 / fr_grouped.total_senders_count, 0.0) AS reply_rate
        FROM (
        -- 计算每天的总发信人数（分母）
        SELECT
        message_date,
        COUNT(DISTINCT sender_id) AS total_senders_count
        FROM FirstReceivedPerDay
        GROUP BY message_date
        ) AS fr_grouped
        LEFT JOIN (
        -- 计算每天收到有效回复的发信人数（分子）
        SELECT
        message_date,
        COUNT(DISTINCT sender_id) AS replied_senders_count
        FROM ValidRepliesToFirstMessage
        GROUP BY message_date
        ) AS vr_grouped ON fr_grouped.message_date = vr_grouped.message_date
        ORDER BY fr_grouped.message_date; -- 按日期排序
    </select>

    <!-- 清除用户聊天记录（软删除方式） -->
    <update id="clearUserChattingRecords">
        UPDATE app_chatting_records 
        SET sender_deleted = CASE 
                WHEN send_user_id = #{userId} THEN 1 
                ELSE sender_deleted 
            END,
            receiver_deleted = CASE 
                WHEN receive_user_id = #{userId} THEN 1 
                ELSE receiver_deleted 
            END,
            sender_delete_time = CASE 
                WHEN send_user_id = #{userId} THEN NOW() 
                ELSE sender_delete_time 
            END,
            receiver_delete_time = CASE 
                WHEN receive_user_id = #{userId} THEN NOW() 
                ELSE receiver_delete_time 
            END
        WHERE (send_user_id = #{userId} OR receive_user_id = #{userId})
        AND is_del = 0
    </update>
</mapper>