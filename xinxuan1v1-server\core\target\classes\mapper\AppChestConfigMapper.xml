<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppChestConfigMapper">

    <resultMap type="AppChestConfig" id="AppChestConfigResult">
        <result property="id" column="id"/>
        <result property="prizePoolType" column="prize_pool_type"/>
    </resultMap>

    <sql id="selectAppChestConfigVo">
        select id, prize_pool_type from app_chest_config
    </sql>

    <select id="selectAppChestConfigList" parameterType="AppChestConfig" resultMap="AppChestConfigResult">
        <include refid="selectAppChestConfigVo"/>
        <where>
            <if test="prizePoolType != null ">and prize_pool_type = #{prizePoolType}</if>
        </where>
    </select>

    <select id="selectAppChestConfigById" parameterType="Long" resultMap="AppChestConfigResult">
        <include refid="selectAppChestConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppChestConfig" parameterType="AppChestConfig" useGeneratedKeys="true" keyProperty="id">
        insert into app_chest_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="prizePoolType != null">prize_pool_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="prizePoolType != null">#{prizePoolType},</if>
        </trim>
    </insert>

    <update id="updateAppChestConfig" parameterType="AppChestConfig">
        update app_chest_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="prizePoolType != null">prize_pool_type = #{prizePoolType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppChestConfigById" parameterType="Long">
        delete from app_chest_config where id = #{id}
    </delete>

    <delete id="deleteAppChestConfigByIds" parameterType="String">
        delete from app_chest_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>