package com.hzy.core.service.common;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.hzy.core.config.RedisCache;
import com.hzy.core.constant.CacheConstants;
import com.hzy.core.constant.RedisKeyConsts;
import com.hzy.core.entity.*;
import com.hzy.core.enums.*;
import com.hzy.core.mapper.*;
import com.hzy.core.model.vo.AjaxResult;
import com.hzy.core.model.vo.app.AppViewUserInfoVo;
import com.hzy.core.service.AppCommunicateTelephoneRecordsService;
import com.hzy.core.service.AppGiftIncomeConfigService;
import com.hzy.core.service.AppGuildMemberService;
import com.hzy.core.service.AppUserGoldBillService;
import com.hzy.core.utils.StringUtils;
import com.hzy.core.utils.UserUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 通用服务
 */
@Slf4j
@Service
public class CommonService {


    @Resource
    private AppUserPersonalDressingMapper appUserPersonalDressingMapper;

    @Resource
    private AppTitleNobilityService appTitleNobilityService;

    @Resource
    private AppConfigMapper appConfigMapper;

    @Resource
    private RedisCache redisCache;


    @Resource
    private AppUserMapper appUserMapper;


    @Resource
    private AppUserGoldBillMapper appUserGoldBillMapper;


    @Resource
    private AppUserPointsBillMapper appUserPointsBillMapper;

    @Resource
    private AppGuildMemberService appGuildMemberService;


    @Resource
    private AppCommunicateTelephoneRecordsMapper appCommunicateTelephoneRecordsMapper;
    @Resource
    private AppGuildApplyMapper appGuildApplyMapper;
    @Resource
    private AppGuildMemberMapper appGuildMemberMapper;
    @Resource
    private AppUserGoldBillService appUserGoldBillService;
    @Resource
    private AppGiftIncomeConfigService appGiftIncomeConfigService;
    @Resource
    private AppCommunicateTelephoneRecordsService appCommunicateTelephoneRecordsService;
    @Resource
    private AppVipPrivilegesConfigMapper appVipPrivilegesConfigMapper;

    /**
     * 强制结束通话
     *
     * @param userId
     * @return 返回双方用户id
     */
    public FinishRecordInfo finishCommunicateTelephone(Long userId) {
        FinishRecordInfo finishRecordInfo = new FinishRecordInfo();
        
        // 开始结束通话
        log.info("开始结束通话: 用户ID[{}]", userId);
        
        // 查询该用户通话中的记录
        AppCommunicateTelephoneRecords communicateTelephoneRecords = appCommunicateTelephoneRecordsMapper.getIsBusyRecords(userId);
        if (ObjectUtils.isNull(communicateTelephoneRecords)) {
            log.info("用户ID[{}] 没有找到通话中的记录", userId);
            return finishRecordInfo;
        }
        
        appCommunicateTelephoneRecordsService.getTelResult(userId, communicateTelephoneRecords);

        Long[] longs = {communicateTelephoneRecords.getReceiveUserId(), communicateTelephoneRecords.getInitiateUserId(), communicateTelephoneRecords.getId()};
        finishRecordInfo.setUserIds(longs);
        return finishRecordInfo;
    }

    public AppViewUserInfoVo getUserInfoByUserIdByInfo(Long userId) {

        AppUserEntity user = appUserMapper.selectAppUserById(userId);
        AppViewUserInfoVo viewUserInfoVo = new AppViewUserInfoVo();
        if (null != user) {
            BeanUtils.copyProperties(user, viewUserInfoVo);
            if (null != viewUserInfoVo.getSex() && viewUserInfoVo.getSex().intValue() == -1) {
                viewUserInfoVo.setSex(null);
            }
            if (StringUtils.isBlank(viewUserInfoVo.getBirthday())) {
                viewUserInfoVo.setAge(null);
            }
            if (null != viewUserInfoVo.getAge() && viewUserInfoVo.getAge().intValue() == -1) {
                viewUserInfoVo.setAge(null);
            }
            if (!StringUtils.isBlank(user.getPhotoAlbum())) {
                String[] photoAlbum = JSON.parseObject(user.getPhotoAlbum(), String[].class);
                viewUserInfoVo.setPhotoAlbum(photoAlbum);
            } else {
                viewUserInfoVo.setPhotoAlbum(new String[]{});
            }
            if (null != viewUserInfoVo.getPurchaseSituation() && viewUserInfoVo.getPurchaseSituation().intValue() == -1) {
                viewUserInfoVo.setPurchaseSituation(null);
            }
            if (null != viewUserInfoVo.getCarPurchaseSituation() && viewUserInfoVo.getCarPurchaseSituation().intValue() == -1) {
                viewUserInfoVo.setCarPurchaseSituation(null);
            }
            viewUserInfoVo.setUserId(user.getId());


        }


        return viewUserInfoVo;
    }

    /**
     * 根据用户id获取用户详情
     *
     * @param userId
     * @return
     */
    public AppViewUserInfoVo getUserInfoByUserId(Long userId) {
        // 先读缓存
        AppUserEntity user = appUserMapper.selectAppUserById(userId);
        AppViewUserInfoVo viewUserInfoVo = new AppViewUserInfoVo();
        if (null != user) {
            BeanUtils.copyProperties(user, viewUserInfoVo);
            if (null != viewUserInfoVo.getSex() && viewUserInfoVo.getSex().intValue() == -1) {
                viewUserInfoVo.setSex(null);
            }
            if (StringUtils.isBlank(viewUserInfoVo.getBirthday())) {
                viewUserInfoVo.setAge(null);
            }
            if (null != viewUserInfoVo.getAge() && viewUserInfoVo.getAge().intValue() == -1) {
                viewUserInfoVo.setAge(null);
            }
            if (!StringUtils.isBlank(user.getPhotoAlbum())) {
                String[] photoAlbum = JSON.parseObject(user.getPhotoAlbum(), String[].class);
                viewUserInfoVo.setPhotoAlbum(photoAlbum);
            } else {
                viewUserInfoVo.setPhotoAlbum(new String[]{});
            }
            if (null != viewUserInfoVo.getPurchaseSituation() && viewUserInfoVo.getPurchaseSituation().intValue() == -1) {
                viewUserInfoVo.setPurchaseSituation(null);
            }
            if (null != viewUserInfoVo.getCarPurchaseSituation() && viewUserInfoVo.getCarPurchaseSituation().intValue() == -1) {
                viewUserInfoVo.setCarPurchaseSituation(null);
            }
            viewUserInfoVo.setUserId(user.getId());

            viewUserInfoVo.setUseInColorNickName(appUserPersonalDressingMapper.getUserUseInPersonalDressingInfo(AppPersonalDressingCategoryEnums.COLOR_NICK_NAME.getId(), userId));
            viewUserInfoVo.setUseInMount(appUserPersonalDressingMapper.getUserUseInPersonalDressingInfo(AppPersonalDressingCategoryEnums.MOUNT.getId(), userId));
            viewUserInfoVo.setUseInChatFrame(appUserPersonalDressingMapper.getUserUseInPersonalDressingInfo(AppPersonalDressingCategoryEnums.CHAT_FRAME.getId(), userId));
            viewUserInfoVo.setUseInMicFrame(appUserPersonalDressingMapper.getUserUseInPersonalDressingInfo(AppPersonalDressingCategoryEnums.MIC_FRAME.getId(), userId));

            // viewUserInfoVo.setCurrentIcoUrl(appTitleNobilityService.getUserCurrentIcoUrl(userId, user.getSex()));
            // 魅力等级图标
            viewUserInfoVo.setCurrentIcoUrl(appTitleNobilityService.getCurrentIcoUrl(userId).getCurrentIcoUrl());
            // 贡献等级图标
            viewUserInfoVo.setContributeCurrentIcoUrl(appTitleNobilityService.getCurrentIcoUrl(userId).getContributeCurrentIcoUrl());
            viewUserInfoVo.setIsPhoneAuth(StringUtils.isBlank(user.getPhone()) ? WhetherTypeEnum.NO.getName() : WhetherTypeEnum.YES.getName());

            viewUserInfoVo.setUseInPendant(appUserPersonalDressingMapper.getUserUseInPersonalDressingInfo(AppPersonalDressingCategoryEnums.PENDANT.getId(), userId));
            viewUserInfoVo.setUseInFamousBrand(appUserPersonalDressingMapper.getUserUseInPersonalDressingInfo(AppPersonalDressingCategoryEnums.FAMOUS_BRAND.getId(), userId));

            viewUserInfoVo.setIsNewUser(UserUtil.isNewUser(user.getCreatedTime(), redisCache.getCacheObject(CacheConstants.NEW_USER_DAYS)));

            if (user.getIsVip() == WhetherTypeEnum.YES.getName()) {
                AppVipPrivilegesConfig config = appVipPrivilegesConfigMapper.getVipPrivilegesConfigByUserId(userId);
                viewUserInfoVo.setHideCharm(config.getHideCharm());
                viewUserInfoVo.setHideWealth(config.getHideWealth());
            }
        }


        return viewUserInfoVo;
    }

    /**
     * 从appconfig配置项中获取默认用户通话金额
     *
     * @param userId
     * @return
     */
    public AppUserCommunicateTelephoneConfig getUserCommunicateTelephoneConfig(Long userId) {
        AppConfig appConfig = getAppConfig();
        AppUserCommunicateTelephoneConfig communicateTelephoneConfig = new AppUserCommunicateTelephoneConfig();
        communicateTelephoneConfig.setUserId(userId);
        communicateTelephoneConfig.setIsEnableVideo(WhetherTypeEnum.YES.getName());// 默认启用视频接听
        communicateTelephoneConfig.setIsEnableVoice(WhetherTypeEnum.YES.getName());// 默认启用语音接听
        communicateTelephoneConfig.setVideoMinutesGold(appConfig.getVideoMinutesGold());
        communicateTelephoneConfig.setVoiceMinutesGold(appConfig.getVoiceMinutesGold());
        communicateTelephoneConfig.setSendMsgGoldPrice(appConfig.getSendMsgGoldPrice());
        communicateTelephoneConfig.setTemporarilyGoldPrice(appConfig.getTemporarilyGoldPrice());
        return communicateTelephoneConfig;
    }

    /**
     * 获取app配置信息
     *
     * @return
     */
    public AppConfig getAppConfig() {
        AppConfig config = null;
        try {
            config = redisCache.getCacheObject(RedisKeyConsts.appConfigKey);// 先读缓存
        } catch (Exception e) {
        }
        if (null == config) {// 缓存没有再查库
            config = appConfigMapper.getAppConfig();
            if (null != config) {// 如果不为空就保存到缓存
                try {
                    redisCache.setCacheObject(RedisKeyConsts.appConfigKey, config, 5, TimeUnit.MINUTES);
                } catch (Exception e) {
                }
            }
        }

        return config;
    }

    @Transactional
    public AjaxResult approve(Long userId, String status, Long guildId) {
        AppGuildApply appGuildApply = appGuildApplyMapper.getUserNewGuildApply(userId, guildId);
        if (null == appGuildApply) {
            return AjaxResult.error("记录不存在");
        }
        appGuildApply.setStatus(Long.valueOf(status));
        appGuildApply.setUpdateTime(LocalDateTime.now());
        appGuildApplyMapper.updateAppGuildApply(appGuildApply);
        if ("1".equals(status)) {
            AppGuildMember appGuildMember = new AppGuildMember();
            appGuildMember.setGuildId(appGuildApply.getGuildId());
            appGuildMember.setUserId(appGuildApply.getInitiateUserId());
            appGuildMember.setIsAdmin(WhetherTypeEnum.NO.getName());
            appGuildMember.setCreateTime(new Date());
            appGuildMemberMapper.insert(appGuildMember);
        }

        return AjaxResult.success();
    }

    @Transactional
    public AjaxResult refund(Long id, String status, Long guildId) {
        appGuildApplyMapper.updateRefund(id, status);
        // 删除 公会成员
        appGuildMemberMapper.deleteGuildMemberByUserIdAndGuildId(id, guildId);
        return AjaxResult.success();
    }

    @Data
    public static class FinishRecordInfo {
        private Long[] userIds;
        /**
         * 接听电话人的收益钻石
         */
        private BigDecimal amountOfIncome;
    }
}
