<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppOrderMapper">

    <resultMap type="AppOrder" id="AppOrderResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="payTime" column="pay_time"/>
        <result property="isDel" column="is_del"/>
        <result property="orderNo" column="order_no"/>
        <result property="payEndTime" column="pay_end_time"/>
        <result property="orderStatus" column="order_status"/>
        <result property="goodsId" column="goods_id"/>
        <result property="userId" column="user_id"/>
        <result property="orderPrice" column="order_price"/>
        <result property="payChannelType" column="pay_channel_type"/>
        <result property="goodsInfoJson" column="goods_info_json"/>
        <result property="orderType" column="order_type"/>
        <result property="buyNum" column="buy_num"/>
    </resultMap>

    <sql id="selectAppOrderVo">
        select id,
        create_time,
        update_time,
        pay_time,
        is_del,
        order_no,
        pay_end_time,
        order_status,
        goods_id,
        user_id,
        order_price,
        pay_channel_type,
        goods_info_json,
        order_type,
        buy_num
        from app_order
    </sql>

    <select id="selectAppOrderList" parameterType="AppOrder" resultMap="AppOrderResult">
        <include refid="selectAppOrderVo"/>
        where is_del=false
        <if test="payTime != null ">and pay_time = #{payTime}</if>
        <if test="isDel != null ">and is_del = #{isDel}</if>
        <if test="orderNo != null  and orderNo != ''">and order_no = #{orderNo}</if>
        <if test="payEndTime != null ">and pay_end_time = #{payEndTime}</if>
        <if test="orderStatus != null ">and order_status = #{orderStatus}</if>
        <if test="goodsId != null ">and goods_id = #{goodsId}</if>
        <if test="userId != null ">and user_id = #{userId}</if>
        <if test="orderPrice != null ">and order_price = #{orderPrice}</if>
        <if test="payChannelType != null ">and pay_channel_type = #{payChannelType}</if>
        <if test="goodsInfoJson != null  and goodsInfoJson != ''">and goods_info_json = #{goodsInfoJson}</if>
        <if test="orderType != null ">and order_type = #{orderType}</if>
        <if test="buyNum != null ">and buy_num = #{buyNum}</if>
        order by create_time desc
    </select>

    <select id="selectAppOrderById" resultMap="AppOrderResult">
        <include refid="selectAppOrderVo"/>
        where id = #{id} and is_del=false
    </select>

    <insert id="insertAppOrder" parameterType="AppOrder" useGeneratedKeys="true" keyProperty="id">
        insert into app_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="isDel != null">is_del,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="payEndTime != null">pay_end_time,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="goodsId != null">goods_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="orderPrice != null">order_price,</if>
            <if test="payChannelType != null">pay_channel_type,</if>
            <if test="goodsInfoJson != null">goods_info_json,</if>
            <if test="orderType != null">order_type,</if>
            <if test="buyNum != null">buy_num,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="payEndTime != null">#{payEndTime},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="goodsId != null">#{goodsId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="orderPrice != null">#{orderPrice},</if>
            <if test="payChannelType != null">#{payChannelType},</if>
            <if test="goodsInfoJson != null">#{goodsInfoJson},</if>
            <if test="orderType != null">#{orderType},</if>
            <if test="buyNum != null">#{buyNum},</if>
        </trim>
    </insert>

    <update id="updateAppOrder" parameterType="AppOrder">
        update app_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="payEndTime != null">pay_end_time = #{payEndTime},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="goodsId != null">goods_id = #{goodsId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="orderPrice != null">order_price = #{orderPrice},</if>
            <if test="payChannelType != null">pay_channel_type = #{payChannelType},</if>
            <if test="goodsInfoJson != null">goods_info_json = #{goodsInfoJson},</if>
            <if test="orderType != null">order_type = #{orderType},</if>
            <if test="buyNum != null">buy_num = #{buyNum},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppOrderById" parameterType="Long">
        delete
        from app_order
        where id = #{id}
    </delete>

    <delete id="deleteAppOrderByIds" parameterType="String">
        delete from app_order where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectAppOrderByOrderNo" parameterType="java.lang.String" resultMap="AppOrderResult">
        <include refid="selectAppOrderVo"/>
        where order_no = #{orderNo} and is_del=false
    </select>


    <select id="getTopUpRecordsList" resultType="com.hzy.core.model.vo.admin.AdminAppUserTopUpRecordsVo"
            parameterType="com.hzy.core.model.vo.admin.AdminAppUserTopUpRecordsVo">
        select
        u.phone as phone,
        u.recode_code as recodeCode,
        u.nick_name as nickName,
        u.head_portrait as headPortrait,
        u.id as userId,
        u.created_time as createTime,
        o.order_price as orderPrice,
        o.order_status as isPay,
        o.pay_time as payTime,
        o.pay_channel_type as payType,
        o.order_no as orderNo,
        ifnull(JSON_EXTRACT(o.goods_info_json, '$.goldNum'),0) as topUpNum,
        ifnull(JSON_EXTRACT(o.goods_info_json, '$.giveNum'),0) as giveNum
        from app_order o,
        app_user u
        where o.user_id=u.id
        <if test="isPay==1">
            and o.is_del=false
        </if>
        and o.order_type=1
        and o.order_status=#{isPay}
        <if test="payType!=null">
            and o.pay_channel_type=#{payType}
        </if>
        <if test="recodeCode!=null and recodeCode!=''">
            and u.recode_code like concat('%', #{recodeCode}, '%')
        </if>
        <if test="userId!=null">
            and u.id = #{userId}
        </if>
        <if test="phone!=null and phone!='' ">
            and u.phone like concat('%', #{phone}, '%')
        </if>
        <if test="nickName!=null and phone!='' ">
            and u.nick_name like concat('%', #{nickName}, '%')
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(o.pay_time,'%y%m%d%H%i%s') &gt;= date_format(#{queryBeginTime},'%y%m%d%H%i%s')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(o.pay_time,'%y%m%d%H%i%s') &lt;= date_format(#{queryEndTime},'%y%m%d%H%i%s')
        </if>
        <if test="createBeginTime != null and createBeginTime != ''">
            and date_format(u.created_time,'%y%m%d%H%i%s') &gt;= date_format(#{createBeginTime},'%y%m%d%H%i%s')
        </if>
        <if test="createEndTime != null and createEndTime != ''">
            and date_format(u.created_time,'%y%m%d%H%i%s') &lt;= date_format(#{createEndTime},'%y%m%d%H%i%s')
        </if>
        order by o.id desc
    </select>


    <!--<select id="getSumTopUpOrderPrice" resultType="java.math.BigDecimal"
            parameterType="com.hzy.common.vo.admin.AdminAppUserTopUpRecordsVo">
        select
        ifnull(sum(o.order_price),0),
        ifnull(JSON_EXTRACT(o.goods_info_json, '$.goldNum'),0) as topUpNum,
        ifnull(JSON_EXTRACT(o.goods_info_json, '$.giveNum'),0) as giveNum
        from app_order o,
        app_user u
        where o.user_id=u.id
        and o.is_del=false
        and o.order_type=1
        and o.order_status=1
        <if test="payType!=null">
            and o.pay_channel_type=#{payType}
        </if>
        <if test="recodeCode!=null and recodeCode!=''">
            and u.recode_code like concat('%', #{recodeCode}, '%')
        </if>
        <if test="userId!=null">
            and u.id = #{userId}
        </if>
        <if test="phone!=null and phone!='' ">
            and u.phone like concat('%', #{phone}, '%')
        </if>
        <if test="nickName!=null and phone!='' ">
            and u.nick_name like concat('%', #{nickName}, '%')
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(o.pay_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(o.pay_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
    </select>-->


    <select id="getUserSumTopUpOrderPrice" resultType="java.math.BigDecimal"
            parameterType="java.lang.Long">
        select ifnull(sum(o.order_price), 0)
        from app_order o
        where o.user_id = #{userId}
        and o.is_del = false
        and o.order_type = 1
        and o.order_status = 1
    </select>


    <select id="getUserIsTopUp" resultType="java.lang.Boolean"
            parameterType="java.lang.Long">
        select if(count(*) > 0, 1, 0)
        from app_order o
        where o.user_id = #{userId}
        and o.is_del = false
        and o.order_type = 1
        and o.order_status = 1
    </select>

    <select id="getSumTopUpOrderPrice" resultType="com.hzy.core.model.vo.admin.TopUpRecordsSummaryVO">
        select
        ifnull(sum(o.order_price),0) orderPriceTotal,
        ifnull(sum(JSON_EXTRACT(o.goods_info_json, '$.goldNum')),0) as topUpNumTotal,
        ifnull(sum(JSON_EXTRACT(o.goods_info_json, '$.giveNum')),0) as giveNumTotal
        from app_order o,
        app_user u
        where o.user_id=u.id
        and o.is_del=false
        and o.order_type=1
        <if test="payType!=null">
            and o.pay_channel_type=#{payType}
            and o.order_status=1
        </if>
        <if test="isPay==1">
            and o.order_status=1
        </if>
        <if test="isPay==0">
            and o.order_status=0
        </if>
        <if test="recodeCode!=null and recodeCode!=''">
            and u.recode_code like concat('%', #{recodeCode}, '%')
        </if>
        <if test="userId!=null">
            and u.id = #{userId}
        </if>
        <if test="phone!=null and phone!='' ">
            and u.phone like concat('%', #{phone}, '%')
        </if>
        <if test="nickName!=null and phone!='' ">
            and u.nick_name like concat('%', #{nickName}, '%')
        </if>
        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(o.pay_time,'%y%m%d%H%i%s') &gt;= date_format(#{queryBeginTime},'%y%m%d%H%i%s')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(o.pay_time,'%y%m%d%H%i%s') &lt;= date_format(#{queryEndTime},'%y%m%d%H%i%s')
        </if>
        <if test="createBeginTime != null and createBeginTime != ''">
            and date_format(u.created_time,'%y%m%d%H%i%s') &gt;= date_format(#{createBeginTime},'%y%m%d%H%i%s')
        </if>
        <if test="createEndTime != null and createEndTime != ''">
            and date_format(u.created_time,'%y%m%d%H%i%s') &lt;= date_format(#{createEndTime},'%y%m%d%H%i%s')
        </if>
    </select>

    <select id="selectAppOrderByIds" resultType="com.hzy.core.entity.AppOrder">
        SELECT
        ao.id,
        ao.user_id as userId,
        ao.buy_num as buyNum,
        ao.order_price as orderPrice,
        u.nick_name as nickName,
        ao.order_no as orderNo,
        ao.order_type as orderType,
        ao.order_status as orderStatus,
        ao.pay_channel_type as payChannelType,
        ao.pay_time as payTime,
        ao.pay_end_time as payEndTime,
        ao.goods_id as goodsId,
        ao.goods_info_json as goodsInfoJson,
        ao.order_type as orderType
        from app_order as ao
        left join app_user u on u.id=ao.user_id
        where ao.order_status=1
        and ao.order_type=1
        and ao.user_id in
        <foreach item="item" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectChannelUserOrderList" resultType="com.hzy.core.entity.AppOrder">
        SELECT
        ao.id,
        ao.user_id as userId,
        ao.buy_num as buyNum,
        ao.order_price as orderPrice,
        u.nick_name as nickName,
        ao.order_no as orderNo,
        ao.order_type as orderType,
        ao.order_status as orderStatus,
        ao.pay_channel_type as payChannelType,
        ao.pay_time as payTime,
        ao.pay_end_time as payEndTime,
        ao.goods_id as goodsId,
        ao.goods_info_json as goodsInfoJson
        from app_order as ao
        left join app_user u on u.id=ao.user_id
        where ao.order_status=1
        and ao.order_type=1
        and ao.user_id in
        <foreach item="item" collection="channelUserRelationList" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="nickName!=null and nickName!='' ">
            and u.nick_name like concat('%', #{nickName})
        </if>
        <if test="userId!=null">
            and u.id = #{userId}
        </if>

    </select>
    <select id="selectUserTotalFlow" resultType="java.math.BigDecimal">
        select ifnull(sum(o.order_price), 0)
        from app_order o
        where o.user_id = #{userId}
        and o.is_del = false
        and o.order_type = 1
        and o.order_status = 1
    </select>

</mapper>