spring:
  profiles:
    active: prod
  cache:
    type: redis

# 项目相关配置
hzy:
  # 前缀名称
  prefix: client
  # 名称
  name: Kita星球-客户端API
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2023
  # 项目根路径
  rootPath: /opt/xinxuan1v1

# 开发环境配置
server:
  port: 5000
  servlet:
    # 应用的访问路径
    context-path: /client
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 2000
    #server端的socket超时间，默认60s
    connection-timeout: 60000
    threads:
      # tomcat最大线程数，默认为200
      max: 2000
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 1000

mybatis-plus:
  # 如果是放在src/main/java目录下 classpath:/com/yourpackage/*/mapper/*Mapper.xml
  # 如果是放在resource目录 classpath:/mapper/*Mapper.xml
  mapper-locations: classpath:/mapper/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.hzy.core.entity
  configuration:
    #配置返回数据库(column下划线命名&&返回java实体是驼峰命名)，自动匹配无需as（没开启这个，SQL需要写as： select user_id as userId）
    map-underscore-to-camel-case: true
    cache-enabled: false
    #配置JdbcTypeForNull, oracle数据库必须配置
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: auto

# token配置
token:
  # 令牌自定义标识
  header: kita_token
  # 令牌密钥
  secret: yweuYuhr7263gyfhIfPofuu8ii
  # 令牌有效期（默认60天）
  expireDays: 60

#设备信息请求头
equipment-info:
  #设备类型:1:android,2:ios
  equipment-type-key: equipment_type
  #设备id
  equipment-id-key: equipment_id



# h5请求头key：用来区分是否是h5请求
h5-head-key: is_h5

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes:
  # 匹配链接
  urlPatterns: /*

#微信公众号配置
wx:
  app-id: wx198fe06ff9accc89
  # 请确保此 AppSecret 与微信小程序后台的完全一致
  app-secret: 11ca7a50e90d5192ee4aff143164b694
  mch-id: 111111
  mch-key: xxxxxx
  notify-url: ${common-config.clientUrl}/service/payCallback/weChat/{}
  # 商户证书文件路径
  # 请参考"商户证书"一节 https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=4_3
  key-path: xxxxx
  # 消息推送配置token
  token:
  # 消息推送配置AesKey
  aes-key:

# 支付相关配置
pay:
  # 微信支付相关配置
  wx:
    #支付回调通知
    notify-url-prefix: ${common-config.clientUrl}/service/payCallback/weChat/{}
    #证书路径
    key-path: ${hzy.rootPath}/apiclient_key.pem
    #支付公钥存放路径
    public-key-path: ${hzy.rootPath}/pub_key.pem
    #识别号
    serialNumber: 7285598DB98426E5BD9C6B9B058ED5D3439C9662

  # 支付宝支付相关配置
  ali:
    #支付宝证书存放的根路径
    key-root-path: ${hzy.rootPath}/ali_pay_key
    #回调地址
    notify-url-prefix: ${common-config.clientUrl}/service/payCallback/aliPay/{}

  #精秀第三方支付相关配置
  jx:
    #api网关
    api-url: https://gateway.jxpays.com/
    #回调地址
    notify-url: ${common-config.clientUrl}/service/payCallback/jxPayNew/{}

# 腾讯云短信服务配置
tencent:
  sms:
    # 腾讯云账户密钥对
    secret-id: AKIDc4b0Iav2yfATYOzNXS7XjfMo22nLeiX7
    secret-key: NVO8uW3YrwrY0zlIaeS2TyHUecNyKRJx
    # 短信应用ID
    sdk-app-id: 1400968359
    # 短信签名
    sign-name: 阜阳弘顺传媒
    # 模板ID配置
    template-id:
      # 验证码短信模板ID
      verification-code: 2372827
  # 内容审核
  content-check:
    content-callback-url: ${common-config.clientUrl}/app/api/contentCheck/checkContentCallBack
    stream-callback-url: ${common-config.clientUrl}/app/api/contentCheck/checkStreamCallBack

# 阿里云系统通知推送配置
aliyun:
  push:
    access-key-id: LTAI5tJSS7UupBPthByvtu7R
    access-key-secret: ******************************
    android-app-key: 335446530
    ios-app-key: 335460396


#阿里云相关配置
  # 是否发送短信
  sendSms: true
  accessKeyId: LTAI5tKSXXXurHMbeg9jEvNe
  accessKeySecret: ******************************
  # 短信签名
  signName: U乐
  #实人认证相关
  realPeopleAuth:
    #场景id
    sceneId: 1000012632