package com.hzy.task;

import com.alibaba.fastjson.JSONObject;
import com.hzy.core.config.RedisCache;
import com.hzy.core.constant.CacheConstants;
import com.hzy.core.constant.Constants;
import com.hzy.core.constant.RedisKeyConsts;
import com.hzy.core.entity.*;
import com.hzy.core.enums.*;
import com.hzy.core.mapper.*;
import com.hzy.core.model.bo.TelResult;
import com.hzy.core.model.vo.app.AppChatRoomPushWebSocketMsgVo;
import com.hzy.core.service.AppChattingRecordsService;
import com.hzy.core.service.AppCommunicateTelephoneRecordsService;
import com.hzy.core.service.AppUserCommunicateTelephoneConfigService;
import com.hzy.core.service.common.CommonService;
import com.hzy.core.utils.StringUtils;
import com.hzy.service.WsRoomService;
import com.hzy.webSocket.ChatRoomWebSocket;
import com.hzy.webSocket.PrivateLetterWebSocket;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * redis监听过期key实现
 */
@Configuration
public class RedisVerdueKeyListening extends KeyExpirationEventMessageListener {
    private static final Logger log = LoggerFactory.getLogger(RedisVerdueKeyListening.class);
    private final RedisTemplate<String, String> redisTemplate;

    @Resource
    private CommonService commonService;
    @Resource
    private AppOrderMapper appOrderMapper;
    @Resource
    private AppUserMapper appUserMapper;
    @Resource
    private ChatRoomWebSocket chatRoomWebSocket;
    @Resource
    private RedisCache redisCache;
    @Resource
    private AppChatRoomUserMapper appChatRoomUserMapper;
    @Resource
    private PrivateLetterWebSocket privateLetterWebSocket;
    @Resource
    private AppForbiddenEquipmentMapper appForbiddenEquipmentMapper;
    @Resource
    private AppCommunicateTelephoneRecordsMapper appCommunicateTelephoneRecordsMapper;
    @Resource
    private AppChatRoomMapper appChatRoomMapper;
    @Resource
    private AppGuildReturnRecordsMapper appGuildReturnRecordsMapper;
    @Resource
    private AppSkillOrderMapper appSkillOrderMapper;
    @Resource
    private AppUserGoldBillMapper appUserGoldBillMapper;
    @Resource
    private AppGuildMemberMapper appGuildMemberMapper;
    @Resource
    private Redisson redisson;
    @Resource
    private AppIpBannedMapper appIpBannedMapper;
    @Resource
    private AppUserCommunicateTelephoneConfigService appUserCommunicateTelephoneConfigService;
    @Resource
    private AppCommunicateTelephoneRecordsService appCommunicateTelephoneRecordsService;
    @Resource
    private AppChattingRecordsService appChattingRecordsService;
    @Resource
    private AppUserCommunicateTelephoneConfigMapper appUserCommunicateTelephoneConfigMapper;
    @Resource
    private WsRoomService wsRoomService;
    @Resource
    private AppUserVideoCardMapper appUserVideoCardMapper;
    @Resource
    private AppConfigMapper appConfigMapper;
    @Resource
    private AppUserPointsBillMapper appUserPointsBillMapper;

    public RedisVerdueKeyListening(RedisMessageListenerContainer listenerContainer, RedisTemplate<String, String> redisTemplate) {
        super(listenerContainer);
        this.redisTemplate = redisTemplate;
    }


    /**
     * 处理订单支付超时
     *
     * @param message
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onMessage(Message message, byte[] bytes) {
        // 过期的缓存key
        String expiredKey = message.toString();
        log.info("监听到过期的key:{}", expiredKey);
        if (expiredKey.startsWith(RedisKeyConsts.ORDER_PAY_TIME_OUT_LISTENING_KEY)) {
            // 截取前缀是否是订单支付超时的缓存
            order(expiredKey);
        } else if (expiredKey.startsWith(RedisKeyConsts.SKILL_ORDER_PAY_TIME_OUT_LISTENING_KEY)) {
            // 截取前缀是否是技能订单支付超时的缓存
            skill(expiredKey);
        } else if (expiredKey.startsWith(RedisKeyConsts.INITIATE_COMMUNICATE_TELEPHONE_TIME_OUT_LISTENING_KEY)) {
            // 截取前缀是否是通话邀请超时的缓存
            telephone(expiredKey);
        } else if (expiredKey.startsWith(Constants.APP_BANNED_IP_KEY)) {
            releaseIp(expiredKey);
        } else if (expiredKey.startsWith(Constants.APP_BANNED_EQUIPMENT_KEY)) {
            releaseEquipment(expiredKey);
        } else if (expiredKey.startsWith(Constants.APP_USER_BANNED_KEY)) {
            releaseUser(expiredKey);
        } else if (expiredKey.startsWith(Constants.APP_BANNED_CHAT_KEY)) {
            releaseCommunicateConfig(expiredKey);
        } else if (expiredKey.startsWith(Constants.APP_BANNED_VOICE_KEY)) {
            releaseCommunicateConfig(expiredKey);
        } else if (expiredKey.startsWith(Constants.APP_BANNED_VIDEO_KEY)) {
            releaseCommunicateConfig(expiredKey);
        } else if (expiredKey.startsWith(RedisKeyConsts.guild_quit_key)) {
            // 截取前缀是否是公会退会到期key
            quitGuild(expiredKey);
        } else if (expiredKey.startsWith(RedisKeyConsts.PRIVATE_LETTER_WEB_SOCKET_CLOSE_KEY)) {
            // 截取前缀是否是私信连接断开超时监听的缓存,超时后断掉正在进行的通话记录
            privateWs(expiredKey);
        } else if (expiredKey.startsWith(RedisKeyConsts.createMotorcadeKey)) {
            // 创建车队过期监听
            createCar(expiredKey);
        } else if (expiredKey.startsWith(RedisKeyConsts.CHAT_ROOM_WEB_SOCKET_CLOSE_KEY)) {
            // 截取前缀是否是聊天室连接断开超时监听的缓存
            roomWs(expiredKey);
        } else if (expiredKey.startsWith(RedisKeyConsts.XINXUAN_1V1_VOICE_KEEP_KEY)) {
            // 判断是否是强制结束通话的key
            if (expiredKey.contains("force_end:")) {
                // 强制结束通话处理
                forceEndCall(expiredKey);
            } else {
                // 截取前缀是否是1v1语音通话ws心跳监听的缓存,判断账户余额可够继续通话
                keepLive(expiredKey);
            }
        } else if (expiredKey.startsWith(CacheConstants.ONLINE_USER_CACHE_KEY)) {
            // 在线用户状态-缓存
            onlineUserStatus(expiredKey);
        } else if (expiredKey.startsWith(CacheConstants.ONLINE_USER_CACHE_KEY_ROOM)) {
            // 房间在线用户状态-缓存
            roomOnlineUserStatus(expiredKey);
        } else if (expiredKey.startsWith(RedisKeyConsts.CHAT_ROOM_FOLLOW_OWNER_ALTER_PREFIX + "timer:")) {
            // 关注房主弹窗提醒
            followOwnerAlert(expiredKey);
        }
    }

    /**
     * 在线用户状态-缓存失效时,将用户设置为离线状态
     *
     * @param expiredKey 过期关键
     */
    private void onlineUserStatus(String expiredKey) {
        String userId = expiredKey.split(CacheConstants.ONLINE_USER_CACHE_KEY)[1];
        AppUserEntity user = appUserMapper.selectAppUserById(Long.parseLong(userId));
        user.setIsOnline(0);
        user.setUpdatedTime(LocalDateTime.now());
        appUserMapper.updateAppUser(user);
    }

    /**
     * 房间在线用户状态-缓存失效时，将用户从房间中移除
     *
     * @param expiredKey 过期的key
     */
    private void roomOnlineUserStatus(String expiredKey) {
        try {
            // 从key中提取用户ID和房间ID
            // 格式为：online:user:room:{roomId}:{userId}
            String keyPart = expiredKey.substring(CacheConstants.ONLINE_USER_CACHE_KEY_ROOM.length());
            String[] parts = keyPart.split(":");
            if (parts.length != 2) {
                log.error("房间在线用户key格式错误: {}", expiredKey);
                return;
            }
            
            Long roomId = Long.parseLong(parts[0]);
            Long userId = Long.parseLong(parts[1]);
            
            log.info("房间在线用户心跳监听超时，准备将用户移出房间，roomId: {}, userId: {}", roomId, userId);
            
            // 清除关注房主弹窗倒计时
            chatRoomWebSocket.clearFollowOwnerAlertTimer(roomId, userId);

            AppChatRoom chatRoom = appChatRoomMapper.selectAppChatRoomById(roomId);
            if (chatRoom.getUserId().equals(userId)) {
                redisCache.setCacheObject(RedisKeyConsts.CHAT_ROOM_WEB_SOCKET_CLOSE_KEY + userId + "-" + roomId, userId, 5, TimeUnit.MINUTES);
            } else {
                redisCache.setCacheObject(RedisKeyConsts.CHAT_ROOM_WEB_SOCKET_CLOSE_KEY + userId + "-" + roomId, userId, 2, TimeUnit.MINUTES);
            }

        } catch (Exception e) {
            log.error("处理房间在线用户状态缓存失效异常", e);
        }
    }

    private void keepLive(String expiredKey) {
        log.info("1v1语音通话分钟级扣费监听触发->" + expiredKey);
        String[] split = expiredKey.split(RedisKeyConsts.XINXUAN_1V1_VOICE_KEEP_KEY);
        Long communicateTelephoneId = Long.valueOf(split[1].split(":")[0]);

        AppCommunicateTelephoneRecords appCommunicateTelephoneRecords = appCommunicateTelephoneRecordsMapper.selectAppCommunicateTelephoneRecordsById(communicateTelephoneId);
        if (appCommunicateTelephoneRecords.getStatus() == AppCommunicateTelephoneStatusTypeEnums.TYPE1.getId()) {
            // 执行分钟级实时扣费逻辑（每分钟第1秒执行）
            this.processMinutelyCharge(appCommunicateTelephoneRecords);
        }
    }

    /**
     * 处理分钟级实时扣费逻辑
     * @param communicateTelephoneRecords 通话记录
     */
    private void processMinutelyCharge(AppCommunicateTelephoneRecords communicateTelephoneRecords) {
        try {
            // 获取发起用户和接收用户信息
            AppUserEntity initiateUser = appUserMapper.selectAppUserById(communicateTelephoneRecords.getInitiateUserId());
            AppUserEntity receiveUser = appUserMapper.selectAppUserById(communicateTelephoneRecords.getReceiveUserId());
            
            // 确定付费用户和费率用户
            AppUserEntity payingUser;
            AppUserEntity rateUser;
            AppUserEntity incomeUser;
            
            if (initiateUser.getSex() != null && receiveUser.getSex() != null 
                && initiateUser.getSex() != receiveUser.getSex()) {
                // 异性通话：扣男性的钱，费率按女性的标准，收益给女性
                if (initiateUser.getSex() == AppUserSexTypeEnums.TYPE0.getId()) { // 发起者是男性
                    payingUser = initiateUser;
                    rateUser = receiveUser;
                    incomeUser = receiveUser;
                } else { // 发起者是女性
                    payingUser = receiveUser;
                    rateUser = initiateUser;
                    incomeUser = initiateUser;
                }
            } else {
                // 同性通话：扣发起用户的钱，按接收用户的费率，收益给接收用户
                payingUser = initiateUser;
                rateUser = receiveUser;
                incomeUser = receiveUser;
            }
            
            // 获取费率用户的通话配置
            AppUserCommunicateTelephoneConfig rateUserConfig = appUserCommunicateTelephoneConfigService.getUserCommunicateTelephoneConfigByUserId(rateUser.getId());
            
            // 计算下一分钟的费用
            BigDecimal nextMinuteFee = getMinutesGoldForCharge(communicateTelephoneRecords, rateUserConfig);
            
            // 重新获取付费用户的最新余额
            AppUserEntity currentPayingUser = appUserMapper.selectAppUserById(payingUser.getId());
            
            log.info("分钟级扣费检查 - 通话ID[{}] 付费用户ID[{}] 当前余额[{}] 下一分钟费用[{}]", 
                    communicateTelephoneRecords.getId(), payingUser.getId(), 
                    currentPayingUser.getGoldBalance(), nextMinuteFee);
            
            // 检查余额是否足够下一分钟的通话费用
            if (currentPayingUser.getGoldBalance().compareTo(nextMinuteFee) >= 0) {
                // 余额充足，扣除下一分钟费用
                appUserMapper.subUserGoldBalance(payingUser.getId(), nextMinuteFee);
                
                // 记录付费用户的金币账单
                recordGoldBill(payingUser.getId(), communicateTelephoneRecords, nextMinuteFee, incomeUser.getId());
                
                // 记录收益用户的钻石收益
                recordDiamondIncome(incomeUser.getId(), communicateTelephoneRecords, nextMinuteFee);
                
                log.info("分钟级扣费成功 - 通话ID[{}] 付费用户ID[{}] 扣除金币[{}] 继续通话", 
                        communicateTelephoneRecords.getId(), payingUser.getId(), nextMinuteFee);
                
                // 继续设置Redis监听，延长通话时间
                this.setPhoneRedis(communicateTelephoneRecords);
                
            } else {
                // 余额不足，进入余额不足处理流程
                log.warn("分钟级扣费 - 余额不足，通话ID[{}] 付费用户ID[{}] 余额[{}] 需要[{}]", 
                        communicateTelephoneRecords.getId(), payingUser.getId(), 
                        currentPayingUser.getGoldBalance(), nextMinuteFee);
                
                // 进入余额不足处理流程
                this.processInsufficientBalance(communicateTelephoneRecords, payingUser, incomeUser);
            }
            
        } catch (Exception e) {
            log.error("分钟级扣费处理异常 - 通话ID[{}]", communicateTelephoneRecords.getId(), e);
            // 异常情况下，强制结束通话
            this.endPhone2(communicateTelephoneRecords);
        }
    }

    /**
     * 处理余额不足的情况
     * @param communicateTelephoneRecords 通话记录
     * @param payingUser 付费用户
     * @param incomeUser 收益用户
     */
    private void processInsufficientBalance(AppCommunicateTelephoneRecords communicateTelephoneRecords, 
                                           AppUserEntity payingUser, AppUserEntity incomeUser) {
        try {
            // 重新获取付费用户的最新余额
            AppUserEntity currentPayingUser = appUserMapper.selectAppUserById(payingUser.getId());
            BigDecimal remainingGold = currentPayingUser.getGoldBalance();
            
            // 第1秒：扣除剩余的所有金币并给收益用户相应收益
            if (remainingGold.compareTo(BigDecimal.ZERO) > 0) {
                // 扣除剩余金币
                appUserMapper.subUserGoldBalance(payingUser.getId(), remainingGold);
                
                // 记录付费用户的金币账单
                recordGoldBill(payingUser.getId(), communicateTelephoneRecords, remainingGold, incomeUser.getId());
                
                // 记录收益用户的钻石收益
                recordDiamondIncome(incomeUser.getId(), communicateTelephoneRecords, remainingGold);
                
                log.info("余额不足处理 - 扣除剩余金币 - 通话ID[{}] 付费用户ID[{}] 剩余金币[{}]", 
                        communicateTelephoneRecords.getId(), payingUser.getId(), remainingGold);
            }
            
            // 通过WebSocket通知双方用户余额不足
            Map<String, Object> result = new HashMap<>();
            result.put("pushType", AppPrivateLetterMsgTypeEnums.TYPE10.getId());
            result.put("title", "余额不足");
            result.put("content", "金币余额不足，通话将在17秒后结束");
            result.put("communicateTelephoneId", communicateTelephoneRecords.getId());
            result.put("timeLeft", 17); // 剩余时间17秒
            result.put("consumedGold", remainingGold); // 本次扣除的金币
            
            // 发送给双方用户
            privateLetterWebSocket.sendCommonMsg(payingUser.getId().toString(), result);
            privateLetterWebSocket.sendCommonMsg(incomeUser.getId().toString(), result);
            
            log.info("余额不足通知已发送 - 通话ID[{}] 付费用户ID[{}] 收益用户ID[{}] 扣除金币[{}]", 
                    communicateTelephoneRecords.getId(), payingUser.getId(), incomeUser.getId(), remainingGold);
            
            // 设置17秒后强制挂断的Redis监听
            String forceEndKey = RedisKeyConsts.XINXUAN_1V1_VOICE_KEEP_KEY + "force_end:" + communicateTelephoneRecords.getId();
            redisCache.setCacheObject(forceEndKey, JSONObject.toJSONString(communicateTelephoneRecords), 17, TimeUnit.SECONDS);
            
        } catch (Exception e) {
            log.error("处理余额不足异常 - 通话ID[{}]", communicateTelephoneRecords.getId(), e);
            // 异常情况下直接结束通话
            this.endPhone2(communicateTelephoneRecords);
        }
    }

    /**
     * 记录金币账单
     */
    private void recordGoldBill(Long userId, AppCommunicateTelephoneRecords communicateTelephoneRecords, 
                               BigDecimal amount, Long toUserId) {
        AppUserGoldBill userGoldBill = new AppUserGoldBill();
        userGoldBill.setUserId(userId);
        if (communicateTelephoneRecords.getType() == AppCommunicateTelephoneTypeEnums.TYPE1.getId()
                || communicateTelephoneRecords.getType() == AppCommunicateTelephoneTypeEnums.TYPE3.getId()) {
            userGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE3.getId());// 语音通话
        } else {
            userGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE4.getId());// 视频通话
        }
        userGoldBill.setObjectId(communicateTelephoneRecords.getId());
        userGoldBill.setAmount(amount.negate());// 扣除金额为负数
        userGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
        userGoldBill.setToUserId(toUserId);
        userGoldBill.setCreateTime(LocalDateTime.now());
        appUserGoldBillMapper.insertAppUserGoldBill(userGoldBill);
    }

    /**
     * 记录钻石收益
     */
    private void recordDiamondIncome(Long userId, AppCommunicateTelephoneRecords communicateTelephoneRecords, 
                                    BigDecimal goldAmount) {
        // 计算钻石收益（这里简化处理，实际应该根据收益比例计算）
        AppConfig appConfig = appConfigMapper.getAppConfig();
        BigDecimal earningsPoints = goldAmount.multiply(new BigDecimal("0.5")).multiply(appConfig.getOneGoldEqRmb());
        
        // 添加收益用户钻石
        appUserMapper.addUserPointsBalance(userId, earningsPoints);
        
        // 记录收益用户的钻石账单
        AppUserPointsBill receiveUserPointsBill = new AppUserPointsBill();
        receiveUserPointsBill.setUserId(userId);
        receiveUserPointsBill.setCreateTime(new Date());
        if (communicateTelephoneRecords.getType() == AppCommunicateTelephoneTypeEnums.TYPE1.getId()
                || communicateTelephoneRecords.getType() == AppCommunicateTelephoneTypeEnums.TYPE3.getId()) {
            receiveUserPointsBill.setBillType((long) AppPointsBillTypeEnums.TYPE6.getId());// 语音通话收入
        } else {
            receiveUserPointsBill.setBillType((long) AppPointsBillTypeEnums.TYPE7.getId());// 视频通话收入
        }
        receiveUserPointsBill.setObjectId(communicateTelephoneRecords.getId());
        receiveUserPointsBill.setAmount(earningsPoints);
        receiveUserPointsBill.setTotalAmount(goldAmount);
        receiveUserPointsBill.setIsDel(WhetherTypeEnum.NO.getName());
        receiveUserPointsBill.setRemarksMsg("分钟级扣费收益");
        appUserPointsBillMapper.insertAppUserPointsBill(receiveUserPointsBill);
    }

    /**
     * 计算每分钟通话费用
     */
    private BigDecimal getMinutesGoldForCharge(AppCommunicateTelephoneRecords communicateTelephoneRecords, 
                                              AppUserCommunicateTelephoneConfig rateUserConfig) {
        BigDecimal minutesGold;
        switch (communicateTelephoneRecords.getType()) {
            case 1: // 语音通话
                minutesGold = rateUserConfig.getVoiceMinutesGold();
                break;
            case 2: // 视频通话
                minutesGold = rateUserConfig.getVideoMinutesGold();
                break;
            case 3: // 匹配语音通话
                minutesGold = new BigDecimal("10"); // 固定10金币每分钟
                break;
            default: // 匹配视频通话
                minutesGold = new BigDecimal("30"); // 固定30金币每分钟
                break;
        }
        return minutesGold;
    }

    /**
     * 强制结束通话处理（17秒后执行）
     * @param expiredKey 过期的key
     */
    private void forceEndCall(String expiredKey) {
        try {
            // 从key中解析通话ID
            String[] parts = expiredKey.split("force_end:");
            if (parts.length < 2) {
                log.error("强制结束通话key格式错误: {}", expiredKey);
                return;
            }
            
            Long communicateTelephoneId = Long.valueOf(parts[1]);
            AppCommunicateTelephoneRecords communicateTelephoneRecords = appCommunicateTelephoneRecordsMapper.selectAppCommunicateTelephoneRecordsById(communicateTelephoneId);
            
            if (communicateTelephoneRecords == null) {
                log.warn("通话记录不存在，无法强制结束通话: {}", communicateTelephoneId);
                return;
            }
            
            // 检查通话是否还在进行中
            if (communicateTelephoneRecords.getStatus() != AppCommunicateTelephoneStatusTypeEnums.TYPE1.getId()) {
                log.info("通话已结束，无需强制结束: {}", communicateTelephoneId);
                return;
            }
            
            log.info("执行强制结束通话 - 通话ID[{}]", communicateTelephoneId);
            
            // 获取发起用户和接收用户信息
            AppUserEntity initiateUser = appUserMapper.selectAppUserById(communicateTelephoneRecords.getInitiateUserId());
            AppUserEntity receiveUser = appUserMapper.selectAppUserById(communicateTelephoneRecords.getReceiveUserId());
            
            // 确定付费用户和收益用户
            AppUserEntity payingUser;
            AppUserEntity incomeUser;
            
            if (initiateUser.getSex() != null && receiveUser.getSex() != null 
                && initiateUser.getSex() != receiveUser.getSex()) {
                // 异性通话：扣男性的钱，收益给女性
                if (initiateUser.getSex() == AppUserSexTypeEnums.TYPE0.getId()) {
                    payingUser = initiateUser;
                    incomeUser = receiveUser;
                } else {
                    payingUser = receiveUser;
                    incomeUser = initiateUser;
                }
            } else {
                // 同性通话：扣发起用户的钱，收益给接收用户
                payingUser = initiateUser;
                incomeUser = receiveUser;
            }
            
            // 注意：用户的剩余金币在第1秒余额不足时已经被扣除了，这里不需要再次扣除
            log.info("强制结束通话 - 17秒延迟到期 - 通话ID[{}] 付费用户ID[{}] 收益用户ID[{}]", 
                    communicateTelephoneId, payingUser.getId(), incomeUser.getId());
            
            // 调用原有的结束通话方法
            TelResult telResult = appCommunicateTelephoneRecordsService.getTelResult(payingUser.getId(), communicateTelephoneRecords);
            
            if (telResult != null) {
                // 推送强制结束通话WebSocket给双方
                Map<String, Object> result = new HashMap<>();
                result.put("pushType", AppPrivateLetterMsgTypeEnums.TYPE26.getId());
                result.put("title", "强制结束通话");
                result.put("content", "余额不足强制结束通话");
                result.put("communicateTelephoneId", communicateTelephoneId);
                result.put("initiateUserId", communicateTelephoneRecords.getInitiateUserId());
                result.put("receiveUserId", communicateTelephoneRecords.getReceiveUserId());
                result.put("type", communicateTelephoneRecords.getType());
                result.put("status", AppCommunicateTelephoneStatusTypeEnums.TYPE3.getId());
                result.put("hangUpTime", telResult.communicateTelephoneRecordsUpd.getHangUpTime());
                result.put("hangUpUserId", telResult.communicateTelephoneRecordsUpd.getHangUpUserId());
                
                // 分别推送给付费用户和收益用户，注意金币已经在第1秒被扣除了
                Map<String, Object> payingUserResult = new HashMap<>(result);
                payingUserResult.put("consumptionGold", BigDecimal.ZERO); // 金币已经在第1秒被扣除
                payingUserResult.put("amountOfIncome", BigDecimal.ZERO);
                
                Map<String, Object> incomeUserResult = new HashMap<>(result);
                incomeUserResult.put("consumptionGold", BigDecimal.ZERO);
                incomeUserResult.put("amountOfIncome", BigDecimal.ZERO); // 收益已经在第1秒记录
                
                privateLetterWebSocket.sendCommonMsg(payingUser.getId().toString(), payingUserResult);
                privateLetterWebSocket.sendCommonMsg(incomeUser.getId().toString(), incomeUserResult);
                
                log.info("强制结束通话完成 - 通话ID[{}] 付费用户ID[{}] 收益用户ID[{}]", 
                        communicateTelephoneId, payingUser.getId(), incomeUser.getId());
            }
            
        } catch (Exception e) {
            log.error("强制结束通话异常: {}", expiredKey, e);
        }
    }

    public void endPhone2(AppCommunicateTelephoneRecords item) {
        try {
            // 判断是否使用视频卡结算
            log.warn("--------------------------------开始执行结算逻辑------------------");
            Integer useVideoCard = item.getUseVideoCard();
            boolean isUseVideoCard = useVideoCard != null && useVideoCard == WhetherTypeEnum.YES.getName();
            final CallTime callTime = getCallTime(item);

            // 余额是否够继续通话
            boolean isKeep;
            if (isUseVideoCard) {
                // 获取用户可用视频卡个数
                int userVideoCardCount = appUserVideoCardMapper.getUserVideoCardCount(item.getInitiateUserId());
                // 视频卡按张计费(1 张 = 60 秒)。只有当已消耗的总秒数达到所有视频卡可支撑的时长时才需要挂断
                isKeep = userVideoCardCount * 60 > callTime.getCallMinutes();
                log.info("视频卡剩余可用:{} 张, 已通话:{} 秒, 继续通话: {}", userVideoCardCount, callTime.getCallMinutes(), isKeep);
            } else {
                // 计算用户可用金币余额，允许运行到余额为 0 为止
                BigDecimal goldBalance = callTime.getUser().getGoldBalance().subtract(callTime.getCallsGold());
                isKeep = goldBalance.compareTo(BigDecimal.ZERO) >= 0;
                log.info("用户余额:{}, 已消耗:{}, 剩余:{}, 继续通话:{}", callTime.getUser().getGoldBalance(), callTime.getCallsGold(), goldBalance, isKeep);
            }

            log.warn("是否可以继续通话：isKeep: {}", isKeep);
            if (isKeep) {
                this.setPhoneRedis(item);
                return;
            }

            log.info("{}",callTime.getCallMinutes());
            log.info("发起用户:{}接听用户:{}", item.getInitiateUserId(), item.getReceiveUserId());
            log.info("强制结束通话,通话记录id:{},通话时长: {}秒, 通话分钟: {}分钟, 消费金额: {}金币", item.getId(), callTime.callMinutes, callTime.minute, callTime.callsGold);
            // 余额不足,判断当前是否为整分钟
            log.warn("余额不足当前通话秒数:{}", callTime.callMinutes % 60);
            if (callTime.getCallMinutes() % 60 > 30) {
                // 强制关闭通话
                log.warn("余额不足,强制关闭通话,通话记录id:{},通话时长: {}秒, 通话分钟: {}分钟, 消费金额: {}金币", item.getId(), callTime.callMinutes, callTime.minute, callTime.callsGold);
                TelResult telResult = appCommunicateTelephoneRecordsService.getTelResult(item.getInitiateUserId(), item);
                
                // 处理可能的null返回值（表示另一个线程正在处理该通话记录）
                if (telResult == null) {
                    log.warn("通话记录[{}]正在被其他线程处理，跳过本次处理", item.getId());
                    return;
                }

                // 推送强制结束通话WebSocket
                Map<String, Object> result = new HashMap<>();
                result.put("pushType", AppPrivateLetterMsgTypeEnums.TYPE26.getId());
                result.put("title", "强制结束通话");
                result.put("content", "余额不足强制结束通话");
                // 消费金额
                result.put("consumptionGold", callTime.getMinutesGold().negate());
                // 收益钻石
                result.put("amountOfIncome", telResult.getAmountOfIncome());
                result.put("initiateUserId", item.getInitiateUserId());// 发起用户id
                result.put("receiveUserId", item.getReceiveUserId());// 接收用户id
                result.put("type", item.getType());// 类型
                result.put("status", AppCommunicateTelephoneStatusTypeEnums.TYPE3.getId());// 状态
                result.put("communicateTelephoneId", item.getId());// 通话id
                result.put("hangUpTime", telResult.communicateTelephoneRecordsUpd.getHangUpTime());// 挂断时间
                result.put("hangUpUserId", telResult.communicateTelephoneRecordsUpd.getHangUpUserId());// 操作挂断的用户id

                // === 根据性别区分扣费方和收益方，分别推送不同的金额 ===
                AppUserEntity initiateUserEntity = appUserMapper.selectAppUserById(item.getInitiateUserId());
                AppUserEntity receiveUserEntity  = appUserMapper.selectAppUserById(item.getReceiveUserId());

                Long payingUserId  = item.getInitiateUserId();
                Long incomeUserId  = item.getReceiveUserId();
                if (initiateUserEntity.getSex() != null && receiveUserEntity.getSex() != null
                        && !initiateUserEntity.getSex().equals(receiveUserEntity.getSex())) {
                    if (initiateUserEntity.getSex() == 0) { // 男性发起
                        payingUserId = item.getInitiateUserId();
                        incomeUserId = item.getReceiveUserId();
                    } else { // 女性发起
                        payingUserId = item.getReceiveUserId();
                        incomeUserId = item.getInitiateUserId();
                    }
                }

                Map<String, Object> payingBody  = new HashMap<>(result);
                payingBody.put("amountOfIncome", BigDecimal.ZERO);

                Map<String, Object> incomeBody = new HashMap<>(result);
                incomeBody.put("consumptionGold", BigDecimal.ZERO);

                privateLetterWebSocket.sendCommonMsg(payingUserId.toString(), payingBody);
                privateLetterWebSocket.sendCommonMsg(incomeUserId.toString(), incomeBody);

            } else {
                log.warn("余额不足,继续通话,通话记录id:{},通话时长: {}秒, 通话分钟: {}分钟, 消费金额: {}金币", item.getId(), callTime.callMinutes, callTime.minute, callTime.callsGold);
                // 重新计算余额(按分钟计费)，防止放大 60 倍
                BigDecimal balance = callTime.getUser().getGoldBalance().subtract(callTime.getCallsGold());
                // 推送余额不足WebSocket
                Map<String, Object> result = new HashMap<>();
                // 推送类型为10，代表是余额不足消息
                result.put("pushType", AppPrivateLetterMsgTypeEnums.TYPE10.getId());
                String content = isUseVideoCard ? "视频卡余额不足" : "金币余额不足";
                result.put("title", content);
                result.put("content", content);
                result.put("useVideoCard", isUseVideoCard ? 1 : 0);
                Map<String, Object> details = new HashMap<>();
                details.put("communicateTelephoneId", item.getId());
                details.put("goldBalance", balance);
                details.put("minutesGold", callTime.getMinutesGold());
                result.put("details", details);
                privateLetterWebSocket.sendCommonMsg(item.getInitiateUserId().toString(), result);
                this.setPhoneRedis(item);
            }

        } catch (Exception error) {
            log.error("处理通话结束异常", error);
        }
    }

    /**
     * 计算当前通话时长
     *
     * @param item 通话记录
     * @return {@link CallTime }
     */
    private CallTime getCallTime(AppCommunicateTelephoneRecords item) {
        // 获取接收用户的通话配置,获取每分钟通话金额
        AppUserCommunicateTelephoneConfig receiveUserCommunicateTelephoneConfig = appUserCommunicateTelephoneConfigService.getUserCommunicateTelephoneConfigByUserId(item.getReceiveUserId());
        // 每分钟消费的金币
        BigDecimal minutesGold = appCommunicateTelephoneRecordsService.getMinutesGold(item, receiveUserCommunicateTelephoneConfig);

        AppUserEntity user = appUserMapper.selectAppUserById(item.getInitiateUserId());

        long callSeconds = (new Date().getTime() - item.getConnectTime().getTime()) / 1000; // 通话秒数
        // 已消费的整分钟数，整数除法向上取整 = (秒+59)/60
        long consumedMinutes = (callSeconds + 59) / 60;

        // 计算当前通话金额 = 已消费分钟 * 每分钟费用
        BigDecimal callsGold = minutesGold.multiply(BigDecimal.valueOf(consumedMinutes));
        return new CallTime(minutesGold, user, callSeconds, consumedMinutes, callsGold);
    }

    /**
     * 设置分钟级扣费监听（每分钟第1秒执行扣费）
     *
     * @param item 项
     */
    private void setPhoneRedis(AppCommunicateTelephoneRecords item) {
        String key = RedisKeyConsts.XINXUAN_1V1_VOICE_KEEP_KEY + item.getId() + ":" + item.getInitiateUserId();
        // 设置60秒监听，在每分钟的第1秒执行扣费逻辑
        redisCache.setCacheObject(key, JSONObject.toJSONString(item), 60, TimeUnit.SECONDS);
        log.info("设置分钟级扣费监听 - 通话ID[{}] 下次扣费时间：60秒后", item.getId());
    }

    private void roomWs(String expiredKey) {
        try {
            String dataKey = expiredKey.split(RedisKeyConsts.CHAT_ROOM_WEB_SOCKET_CLOSE_KEY)[1];
            if (!StringUtils.isBlank(dataKey)) {
                Long userId = Long.valueOf(dataKey.split("-")[0]);
                Long chatRoomId = Long.valueOf(dataKey.split("-")[1]);
                wsRoomService.quitRoom(chatRoomId, userId);
                // 给房间内容的用户发送离开房间信息消息
                chatRoomWebSocket.toSendEnterOrLeaveMsg(chatRoomId, userId,
                        commonService.getUserInfoByUserId(userId), AppChatRoomWebSocketPushMsgTypeEnums.TYPE2.getId()
                );
            }
        } catch (Exception e) {

        }
    }

    private void createCar(String expiredKey) {
        String dataKey = expiredKey.split(RedisKeyConsts.createMotorcadeKey)[1];
        if (!StringUtils.isBlank(dataKey)) {
            Long chatRoomId = Long.parseLong(dataKey);
            AppChatRoom chatRoom = appChatRoomMapper.selectAppChatRoomById(chatRoomId);
            // 如果还处于待发车就直接关闭
            if (null != chatRoom && chatRoom.getStatus()
                    .intValue() == AppChatRoomStatusTypeEnums.TYPE0.getId() && chatRoom.getType()
                    .intValue() == AppChatRoomTypeEnums.TYPE4.getId()) {
                Date time = new Date();
                AppChatRoom chatRoomUpd = new AppChatRoom();
                chatRoomUpd.setId(chatRoom.getId());
                chatRoomUpd.setLastStartTime(chatRoom.getStartTime());
                chatRoomUpd.setLastEndTime(time);
                chatRoomUpd.setStatus((long) AppChatRoomStatusTypeEnums.TYPE2.getId());// 状态改为已结束
                chatRoomUpd.setUpdateTime(time);
                chatRoomUpd.setUseMusicJson("{}");
                appChatRoomMapper.updateAppChatRoom(chatRoomUpd);
                // 关闭所有在线用户
                appChatRoomUserMapper.closeAllOnlineUserByChatRoomId(chatRoomId);
                // 清空所有用户收到礼物的金币总数
                appChatRoomUserMapper.clearAllUserGiftGoldSumByChatRoomId(chatRoomId);
                try {
                    // 推送关闭消息给聊天室内所有用户
                    AppChatRoomPushWebSocketMsgVo msgResult = new AppChatRoomPushWebSocketMsgVo();
                    msgResult.setChatRoomId(chatRoomId);
                    msgResult.setPushType(AppChatRoomWebSocketPushMsgTypeEnums.TYPE21.getId());
                    msgResult.setTitle("车队解散");
                    msgResult.setContent("车队解散");
                    Map<String, Object> details = new HashMap<>();
                    details.put("deliveryUserId", -1);// 送达用户id:-1代表所有
                    details.put("sendUserId", null);// 发送用户id
                    details.put("sendUserInfo", null);// 发送用户详情
                    msgResult.setDetails(details);
                    chatRoomWebSocket.sendMsgByChatRoomId(chatRoomId, msgResult);

                } catch (Exception e) {

                }

                try {
                    // 给大房间推送消息
                    AppChatRoomPushWebSocketMsgVo msgResult = new AppChatRoomPushWebSocketMsgVo();
                    msgResult.setChatRoomId(chatRoom.getFatherId());
                    msgResult.setPushType(AppChatRoomWebSocketPushMsgTypeEnums.TYPE33.getId());
                    msgResult.setTitle("有车队解散");
                    msgResult.setContent("有车队解散");
                    Map<String, Object> details = new HashMap<>();
                    details.put("motorcadeId", chatRoom.getId());// 车队id
                    msgResult.setDetails(details);
                    chatRoomWebSocket.sendMsgByChatRoomId(chatRoom.getFatherId(), msgResult);
                } catch (Exception e) {

                }

                try {
                    redisCache.delLike("cd-jr-jl-" + chatRoomId + "-*");
                } catch (Exception e) {

                }


            }
        }
    }

    private void privateWs(String expiredKey) {
        String userId = expiredKey.split(RedisKeyConsts.PRIVATE_LETTER_WEB_SOCKET_CLOSE_KEY)[1];
        if (!StringUtils.isBlank(userId)) {
            // 获取分布式锁防止并发处理
            RLock lock = redisson.getLock("websocket:privateWs:" + userId);
            if (lock.isLocked()) {
                log.warn("用户【{}】私信WebSocket心跳处理中，跳过本次处理", userId);
                return;
            }
            
            try {
                lock.lock(30, TimeUnit.SECONDS);
                log.error("用户【{}】私信WebSocket心跳监听超时,准备挂断电话", userId);
                log.warn("排查异常挂断电话业务线（过期key事件：ws连接中断，直接挂断当前正在通话电话）");
                endTelRecord(userId);
            } finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }

    /**
     * 结束该用户通话中的应用
     *
     * @param userId 用户id
     */
    private Long endTelRecord(String userId) {

        Long[] userIds = commonService.finishCommunicateTelephone(Long.parseLong(userId)).getUserIds();
        if (null != userIds) {
            // 推送强制结束通话WebSocket
            Map<String, Object> result = new HashMap<>();
            result.put("pushType", AppPrivateLetterMsgTypeEnums.TYPE26.getId());
            result.put("title", "强制结束通话");
            result.put("content", "redis心跳监听超时");
            result.put("communicateTelephoneId", userIds[2]);
            privateLetterWebSocket.sendCommonMsg(userIds[0].toString(), result);
            privateLetterWebSocket.sendCommonMsg(userIds[1].toString(), result);
            return userIds[0] == Long.parseLong(userId) ? userIds[1] : userIds[0];
        }
        return null;
    }

    private void quitGuild(String expiredKey) {
        String objectId = expiredKey.split(RedisKeyConsts.guild_quit_key)[1];
        if (StringUtil.isNotBlank(objectId)) {
            Long guildReturnRecordsId = Long.parseLong(objectId);
            AppGuildReturnRecords guildReturnRecords = appGuildReturnRecordsMapper.selectAppGuildReturnRecordsById(guildReturnRecordsId);
            if (null != guildReturnRecords) {
                guildReturnRecords.setStatus((long) WhetherTypeEnum.YES.getName());
                guildReturnRecords.setEndTime(new Date());
                appGuildReturnRecordsMapper.updateAppGuildReturnRecords(guildReturnRecords);
                appGuildMemberMapper.deleteGuildMemberByUserIdAndGuildId(guildReturnRecords.getUserId(), guildReturnRecords.getGuildId());
            }
        }
    }

    /**
     * 解封ip
     *
     * <AUTHOR>
     * @date 2025/4/10 上午10:03
     */
    private void releaseIp(String expiredKey) {
        String userId = expiredKey.split(Constants.APP_BANNED_IP_KEY)[1];
        if (StringUtil.isNotBlank(userId)) {
            AppUserEntity user = appUserMapper.selectAppUserById(Long.parseLong(userId));
            if (user == null) return;
            // 获取用户ip
            String ip = user.getLastLoginIp();
            if (StringUtil.isNotBlank(ip)) {
                ip = ip.split(":")[1];
                appIpBannedMapper.deleteAppIpBannedByIp(ip);
            }
        }
    }

    /**
     * 解封设备
     *
     * <AUTHOR>
     * @date 2025/4/10 上午10:03
     */
    private void releaseEquipment(String expiredKey) {
        String userId = expiredKey.split(Constants.APP_BANNED_EQUIPMENT_KEY)[1];
        if (StringUtil.isNotBlank(userId)) {
            AppUserEntity user = appUserMapper.selectAppUserById(Long.parseLong(userId));
            if (user != null) {
                appForbiddenEquipmentMapper.relieveForbidden(user.getPushId(), user.getEquipmentType());
            }
        }
    }

    /**
     * 解封账号
     *
     * <AUTHOR>
     * @date 2025/4/10 上午10:12
     */
    private void releaseUser(String expiredKey) {
        String userId = expiredKey.split(Constants.APP_USER_BANNED_KEY)[1];
        if (StringUtil.isNotBlank(userId)) {
            AppUserEntity user = appUserMapper.selectAppUserById(Long.parseLong(userId));
            if (null != user && user.getUserStatus() == AppUserStatusTypeEnums.TYPE4.getCode()) {// 如果该用户是封号状态，那就改为正常
                AppUserEntity userUpd = new AppUserEntity();
                userUpd.setId(user.getId());
                userUpd.setUserStatus(AppUserStatusTypeEnums.TYPE1.getCode());
                appUserMapper.updateAppUser(userUpd);
            }
        }
    }

    /**
     * 解封私信 语音/视频通话
     *
     * <AUTHOR>
     * @date 2025/4/10 上午10:12
     */
    private void releaseCommunicateConfig(String expiredKey) {
        String userId = StringUtils.substringAfterLast(expiredKey, "-");
        ;
        if (StringUtil.isNotBlank(userId)) {
            AppUserCommunicateTelephoneConfig config = appUserCommunicateTelephoneConfigMapper.getUserCommunicateTelephoneConfigByUserId(Long.parseLong(userId));
            if (null != config) {
                if (expiredKey.startsWith(Constants.APP_BANNED_CHAT_KEY)) {
                    config.setIsBannedChat(WhetherTypeEnum.NO.getName());
                } else if (expiredKey.startsWith(Constants.APP_BANNED_VOICE_KEY)) {
                    config.setIsBannedVoice(WhetherTypeEnum.NO.getName());
                } else {
                    config.setIsBannedVideo(WhetherTypeEnum.NO.getName());
                }
                appUserCommunicateTelephoneConfigMapper.updateAppUserCommunicateTelephoneConfig(config);
            }
        }
    }

    private void telephone(String expiredKey) {
        log.info("触发通话邀请超时->" + expiredKey);
        String communicateTelephoneId = expiredKey.split(RedisKeyConsts.INITIATE_COMMUNICATE_TELEPHONE_TIME_OUT_LISTENING_KEY)[1];
        AppCommunicateTelephoneRecords communicateTelephoneRecords = appCommunicateTelephoneRecordsMapper.selectAppCommunicateTelephoneRecordsById(Long.parseLong(communicateTelephoneId));
        if (null != communicateTelephoneRecords) {
            // 根据通话类型设置未接通的状态
            int callType = communicateTelephoneRecords.getType();
            int newStatus;
            if (callType == AppCommunicateTelephoneTypeEnums.TYPE2.getId() || callType == AppCommunicateTelephoneTypeEnums.TYPE4.getId()) {
                // 视频通话，设置为未接视频来电
                newStatus = AppCommunicateTelephoneStatusTypeEnums.TYPE4.getId();
            } else {
                // 语音通话，设置为未接语音来电
                newStatus = AppCommunicateTelephoneStatusTypeEnums.TYPE5.getId();
            }

            communicateTelephoneRecords.setStatus(newStatus);
            communicateTelephoneRecords.setHangUpTime(new Date());
            communicateTelephoneRecords.setHangUpUserId(communicateTelephoneRecords.getInitiateUserId());
            communicateTelephoneRecords.setUpdateTime(new Date());
            appCommunicateTelephoneRecordsMapper.updateAppCommunicateTelephoneRecords(communicateTelephoneRecords);

            // 获取状态描述
            String statusDesc = AppCommunicateTelephoneStatusTypeEnums.getEnum(newStatus).getDesc();

            // 推送强制结束通话WebSocket
            Map<String, Object> result = new HashMap<>();
            result.put("pushType", AppPrivateLetterMsgTypeEnums.TYPE32.getId());
            result.put("title", "通话结束");
            result.put("content", statusDesc);
            // 通话费用
            result.put("consumptionGold", 0);
            // 收益钻石
            result.put("amountOfIncome", 0);
            result.put("communicateTelephoneId", communicateTelephoneRecords.getId());
            result.put("initiateUserId", communicateTelephoneRecords.getInitiateUserId());// 发起用户id
            result.put("receiveUserId", communicateTelephoneRecords.getReceiveUserId());// 接收用户id
            result.put("hangUpTime", communicateTelephoneRecords.getHangUpTime());// 挂断时间
            result.put("hangUpUserId", communicateTelephoneRecords.getHangUpUserId());// 操作挂断的用户id
            result.put("type", communicateTelephoneRecords.getType());// 类型
            result.put("status", newStatus);// 状态

            try {
                privateLetterWebSocket.sendCommonMsg(communicateTelephoneRecords.getInitiateUserId().toString(), result);
                privateLetterWebSocket.sendCommonMsg(communicateTelephoneRecords.getReceiveUserId().toString(), result);

                // 创建一条通话记录消息
                AppChattingRecords appChattingRecords = new AppChattingRecords();
                appChattingRecords.setSendUserId(communicateTelephoneRecords.getInitiateUserId());
                appChattingRecords.setReceiveUserId(communicateTelephoneRecords.getReceiveUserId());
                appChattingRecords.setSendTime(new Date());
                appChattingRecords.setIsRead(0);

                // 根据通话类型设置消息类型
                if (callType == AppCommunicateTelephoneTypeEnums.TYPE2.getId() || callType == AppCommunicateTelephoneTypeEnums.TYPE4.getId()) {
                    // 视频通话
                    appChattingRecords.setMsgType((long) AppSendMsgTypeEnums.TYPE12.getId());
                } else {
                    // 语音通话
                    appChattingRecords.setMsgType((long) AppSendMsgTypeEnums.TYPE10.getId());
                }

                JSONObject content = new JSONObject();
                // 根据通话类型设置content内容
                if (callType == AppCommunicateTelephoneTypeEnums.TYPE2.getId() || callType == AppCommunicateTelephoneTypeEnums.TYPE4.getId()) {
                    content.put("content", AppSendMsgTypeEnums.TYPE12.getDesc());
                } else {
                    content.put("content", AppSendMsgTypeEnums.TYPE10.getDesc());
                }

                // 根据通话状态设置不同的flag
                content.put("flag", statusDesc);

                appChattingRecords.setContent(content.toJSONString());

                // 保存通话记录消息
                appChattingRecordsService.insertAppChattingRecords(appChattingRecords);
            } catch (Exception e) {
                log.error("推送通话结束WebSocket失败->" + e.getMessage());
            }
        }
    }

    private void skill(String expiredKey) {
        // 获取订单Id
        String orderId = expiredKey.split(RedisKeyConsts.SKILL_ORDER_PAY_TIME_OUT_LISTENING_KEY)[1];
        if (StringUtil.isNotBlank(orderId)) {
            AppSkillOrder order = appSkillOrderMapper.selectAppSkillOrderById(Long.parseLong(orderId));
            if (null != order && order.getOrderStatus().intValue() == AppSkillOrderStatusEnums.TYPE0.getId()) {

                try {
                    AppSkillOrder orderUpd = new AppSkillOrder();
                    orderUpd.setId(Long.parseLong(orderId));
                    orderUpd.setOrderStatus((long) AppSkillOrderStatusEnums.TYPE5.getId());
                    appSkillOrderMapper.updateAppSkillOrder(orderUpd);

                    appUserMapper.addUserGoldBalance(order.getUserId(), order.getOrderPrice());


                    AppUserGoldBill userGoldBill = new AppUserGoldBill();
                    userGoldBill.setUserId(order.getUserId());
                    userGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE28.getId());
                    userGoldBill.setObjectId(Long.parseLong(orderId));
                    userGoldBill.setAmount(order.getOrderPrice());
                    userGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
                    appUserGoldBillMapper.insertAppUserGoldBill(userGoldBill);

                } catch (Exception e) {
                    // 手动回滚数据
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                }

            }
        }
    }

    private void order(String expiredKey) {
        // 获取订单编号
        String orderNo = expiredKey.split(RedisKeyConsts.ORDER_PAY_TIME_OUT_LISTENING_KEY)[1];
        if (StringUtil.isNotBlank(orderNo)) {
            AppOrder order = appOrderMapper.selectAppOrderByOrderNo(orderNo);
            if (null != order && order.getOrderStatus().intValue() == AppOrderStatusEnums.TYPE0.getId()) {
                log.info("----------订单编号【{}】,支付超时处理开始----------", orderNo);
                try {
                    AppOrder orderUpd = new AppOrder();
                    orderUpd.setId(order.getId());
                    orderUpd.setPayEndTime(new Date());
                    orderUpd.setIsDel(WhetherTypeEnum.YES.getName());// 删除订单
                    appOrderMapper.updateAppOrder(orderUpd);
                } catch (Exception e) {
                    // 手动回滚数据
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                }
                log.info("----------订单编号【{}】,支付超时处理完成----------", orderNo);
            }
        }
    }

    /**
     * 处理关注房主弹窗提醒
     *
     * @param expiredKey 过期的key
     */
    private void followOwnerAlert(String expiredKey) {
        try {
            // 从key中解析房间ID和用户ID
            // 格式为：app:chatRoom:alter:timer:{roomId}:{userId}
            String timerPart = expiredKey.substring((RedisKeyConsts.CHAT_ROOM_FOLLOW_OWNER_ALTER_PREFIX + "timer:").length());
            String[] parts = timerPart.split(":");
            if (parts.length != 2) {
                log.error("关注房主弹窗key格式错误: {}", expiredKey);
                return;
            }

            Long chatRoomId = Long.parseLong(parts[0]);
            Long userId = Long.parseLong(parts[1]);

            // 检查用户是否已在房间
            ChatRoomWebSocket userWebSocket = ChatRoomWebSocket.getcurrentWenSocketByUuId(userId);
            if (userWebSocket == null || !userWebSocket.getChatRoomId().equals(chatRoomId)) {
                log.info("用户已离开房间，不发送关注房主弹窗消息: chatRoomId={}, userId={}", chatRoomId, userId);
                return;
            }

            // 发送关注房主弹窗消息
            chatRoomWebSocket.sendFollowOwnerAlertMessage(chatRoomId, userId);
            // 获取系统配置中的停留时间
            Integer stayTimeMinutes = 2; // 默认2分钟
            String stayTimeKey = CacheConstants.SYS_CONFIG_KEY + "room_alter_time";
            Object stayTimeObj = redisCache.getCacheObject(stayTimeKey);
            if (stayTimeObj != null) {
                try {
                    stayTimeMinutes = Integer.parseInt(stayTimeObj.toString());
                } catch (Exception e) {
                    log.error("解析停留时间配置失败", e);
                }
            }
            // 设置倒计时key
            redisCache.setCacheObject(expiredKey, userId, stayTimeMinutes, TimeUnit.MINUTES);

        } catch (Exception e) {
            log.error("处理关注房主弹窗提醒失败", e);
        }
    }

    @AllArgsConstructor
    @Getter
    private static class CallTime {
        public final BigDecimal minutesGold;
        public final AppUserEntity user;
        public final long callMinutes;
        public final long minute;
        public final BigDecimal callsGold;
    }

}
