<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppBxGlConfigMapper">

    <resultMap type="AppBxGlConfig" id="AppBxGlConfigResult">
        <result property="id" column="id"/>
        <result property="sumPutGl" column="sum_put_gl"/>
        <result property="sumOutGl" column="sum_out_gl"/>
        <result property="sumProfitGl" column="sum_profit_gl"/>
        <result property="extractedNumGl" column="extracted_num_gl"/>
        <result property="blGl" column="bl_gl"/>
        <result property="minKzl" column="min_kzl"/>
        <result property="maxKzl" column="max_kzl"/>
    </resultMap>

    <sql id="selectAppBxGlConfigVo">
        select id, sum_put_gl, sum_out_gl, sum_profit_gl, extracted_num_gl, bl_gl, min_kzl, max_kzl from
        app_bx_gl_config
    </sql>

    <select id="selectAppBxGlConfigList" parameterType="AppBxGlConfig" resultMap="AppBxGlConfigResult">
        <include refid="selectAppBxGlConfigVo"/>
        <where>
            <if test="sumPutGl != null ">and sum_put_gl = #{sumPutGl}</if>
            <if test="sumOutGl != null ">and sum_out_gl = #{sumOutGl}</if>
            <if test="sumProfitGl != null ">and sum_profit_gl = #{sumProfitGl}</if>
            <if test="extractedNumGl != null ">and extracted_num_gl = #{extractedNumGl}</if>
            <if test="blGl != null ">and bl_gl = #{blGl}</if>
            <if test="minKzl != null ">and min_kzl = #{minKzl}</if>
            <if test="maxKzl != null ">and max_kzl = #{maxKzl}</if>
        </where>
    </select>

    <select id="selectAppBxGlConfigById" parameterType="Long" resultMap="AppBxGlConfigResult">
        <include refid="selectAppBxGlConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppBxGlConfig" parameterType="AppBxGlConfig" useGeneratedKeys="true" keyProperty="id">
        insert into app_bx_gl_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sumPutGl != null">sum_put_gl,</if>
            <if test="sumOutGl != null">sum_out_gl,</if>
            <if test="sumProfitGl != null">sum_profit_gl,</if>
            <if test="extractedNumGl != null">extracted_num_gl,</if>
            <if test="blGl != null">bl_gl,</if>
            <if test="minKzl != null">min_kzl,</if>
            <if test="maxKzl != null">max_kzl,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sumPutGl != null">#{sumPutGl},</if>
            <if test="sumOutGl != null">#{sumOutGl},</if>
            <if test="sumProfitGl != null">#{sumProfitGl},</if>
            <if test="extractedNumGl != null">#{extractedNumGl},</if>
            <if test="blGl != null">#{blGl},</if>
            <if test="minKzl != null">#{minKzl},</if>
            <if test="maxKzl != null">#{maxKzl},</if>
        </trim>
    </insert>

    <update id="updateAppBxGlConfig" parameterType="AppBxGlConfig">
        update app_bx_gl_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="sumPutGl != null">sum_put_gl = #{sumPutGl},</if>
            <if test="sumOutGl != null">sum_out_gl = #{sumOutGl},</if>
            <if test="sumProfitGl != null">sum_profit_gl = #{sumProfitGl},</if>
            <if test="extractedNumGl != null">extracted_num_gl = #{extractedNumGl},</if>
            <if test="blGl != null">bl_gl = #{blGl},</if>
            <if test="minKzl != null">min_kzl = #{minKzl},</if>
            <if test="maxKzl != null">max_kzl = #{maxKzl},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppBxGlConfigById" parameterType="Long">
        delete from app_bx_gl_config where id = #{id}
    </delete>

    <delete id="deleteAppBxGlConfigByIds" parameterType="String">
        delete from app_bx_gl_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getUserSumNum" resultType="java.math.BigDecimal" parameterType="java.lang.Long">
        select sum(abs(amount)) from app_user_gold_bill
        where user_id=#{userId} and bill_type=16
    </select>

    <select id="getUserToDaySumNum" resultType="java.math.BigDecimal" parameterType="java.lang.Long">
        select sum(abs(amount)) from app_user_gold_bill
        where user_id=#{userId} and bill_type=16
        and create_time >= CURDATE() AND create_time &lt; CURDATE() + INTERVAL 1 DAY
    </select>
</mapper>