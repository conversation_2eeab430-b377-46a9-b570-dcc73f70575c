<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppGuildReturnRecordsMapper">

    <resultMap type="AppGuildReturnRecords" id="AppGuildReturnRecordsResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="guildId" column="guild_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="endTime" column="end_time"/>
    </resultMap>

    <sql id="selectAppGuildReturnRecordsVo">
        select id, user_id, guild_id, create_time, update_time, status, end_time from app_guild_return_records
    </sql>

    <select id="selectAppGuildReturnRecordsList" parameterType="AppGuildReturnRecords"
            resultMap="AppGuildReturnRecordsResult">
        <include refid="selectAppGuildReturnRecordsVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="guildId != null ">and guild_id = #{guildId}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="endTime != null ">and end_time = #{endTime}</if>
        </where>
    </select>

    <select id="selectAppGuildReturnRecordsById" parameterType="Long" resultMap="AppGuildReturnRecordsResult">
        <include refid="selectAppGuildReturnRecordsVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppGuildReturnRecords" parameterType="AppGuildReturnRecords" useGeneratedKeys="true"
            keyProperty="id">
        insert into app_guild_return_records
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="guildId != null">guild_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="endTime != null">end_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="guildId != null">#{guildId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="endTime != null">#{endTime},</if>
        </trim>
    </insert>

    <update id="updateAppGuildReturnRecords" parameterType="AppGuildReturnRecords">
        update app_guild_return_records
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="guildId != null">guild_id = #{guildId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppGuildReturnRecordsById" parameterType="Long">
        delete from app_guild_return_records where id = #{id}
    </delete>

    <delete id="deleteAppGuildReturnRecordsByIds" parameterType="String">
        delete from app_guild_return_records where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getGuildReturnRecordsList" parameterType="java.lang.Long"
            resultType="com.hzy.core.model.vo.app.AppGuildReturnRecordsVo">
        select
        re.id as recordsId,
        re.user_id as userId,
        re.guild_id as guildId,
        re.create_time as createTime,
        re.status as `status`,
        re.end_time as endTime,
        u.sex as userSex,
        u.recode_code as recodeCode,
        u.age as userAge,
        u.nick_name as userNickName,
        u.birthday as userBirthday,
        u.head_portrait as userHeadPortrait
        from
        app_guild_return_records re,
        app_user u
        where re.guild_id=#{guildId}
        and re.user_id=u.id
        order by re.id desc
    </select>

    <select id="getUserReturnRecords" parameterType="Long" resultMap="AppGuildReturnRecordsResult">
        <include refid="selectAppGuildReturnRecordsVo"/>
        where guild_id=#{guildId} and user_id=#{userId}
        order by id desc limit 0,1
    </select>
</mapper>