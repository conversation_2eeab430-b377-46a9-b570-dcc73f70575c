<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppEmoteMapper">

    <resultMap type="AppEmote" id="AppEmoteResult">
        <result property="id" column="id"/>
        <result property="url" column="url"/>
        <result property="orderNo" column="order_no"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedTime" column="updated_time"/>
        <result property="createdBy" column="created_by"/>
        <result property="updatedBy" column="updated_by"/>
    </resultMap>

    <sql id="selectAppEmoteVo">
        select id, url, order_no, created_time, updated_time, created_by, updated_by from app_emote
    </sql>

    <select id="selectAppEmoteList" resultMap="AppEmoteResult">
        <include refid="selectAppEmoteVo"/>
        order by order_no desc
    </select>

    <select id="selectAppEmoteById" parameterType="Long" resultMap="AppEmoteResult">
        <include refid="selectAppEmoteVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppEmote" parameterType="AppEmote" useGeneratedKeys="true" keyProperty="id">
        insert into app_emote
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="url != null">url,</if>
            <if test="orderNo != null">order_no,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updatedBy != null">updated_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="url != null">#{url},</if>
            <if test="orderNo != null">#{orderNo},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
        </trim>
    </insert>

    <update id="updateAppEmote" parameterType="AppEmote">
        update app_emote
        <trim prefix="SET" suffixOverrides=",">
            <if test="url != null">url = #{url},</if>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppEmoteById" parameterType="Long">
        delete from app_emote where id = #{id}
    </delete>

    <delete id="deleteAppEmoteByIds" parameterType="String">
        delete from app_emote where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getSysEmoteList" resultType="java.lang.String">
        select url from app_emote
        order by id desc
    </select>
</mapper>