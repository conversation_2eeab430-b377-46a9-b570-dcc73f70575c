<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppSkillMapper">

    <resultMap type="AppSkill" id="AppSkillResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="icoUrl" column="ico_url"/>
        <result property="instructions" column="instructions"/>
        <result property="aptitude" column="aptitude"/>
        <result property="isGame" column="is_game"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
        <result property="imgUrl" column="img_url"/>
        <result property="avatarUrl" column="avatar_url"/>
    </resultMap>

    <sql id="selectAppSkillVo">
        select id, name, ico_url, instructions, aptitude, is_game, create_time, update_time, create_by, update_by,
        is_del, img_url, avatar_url from app_skill
    </sql>

    <select id="selectAppSkillList" parameterType="AppSkill" resultMap="AppSkillResult">
        <include refid="selectAppSkillVo"/>
        where is_del=false
        <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
        <if test="isGame != null ">and is_game = #{isGame}</if>
        order by id desc
    </select>

    <select id="selectAppSkillById" parameterType="Long" resultMap="AppSkillResult">
        <include refid="selectAppSkillVo"/>
        where id = #{id} and is_del=false
    </select>

    <select id="getAppSkillById" parameterType="Long" resultMap="AppSkillResult">
        <include refid="selectAppSkillVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppSkill" parameterType="AppSkill" useGeneratedKeys="true" keyProperty="id">
        insert into app_skill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="icoUrl != null">ico_url,</if>
            <if test="instructions != null">instructions,</if>
            <if test="aptitude != null">aptitude,</if>
            <if test="isGame != null">is_game,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
            <if test="imgUrl != null">img_url,</if>
            <if test="avatarUrl != null">avatar_url,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="icoUrl != null">#{icoUrl},</if>
            <if test="instructions != null">#{instructions},</if>
            <if test="aptitude != null">#{aptitude},</if>
            <if test="isGame != null">#{isGame},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
        </trim>
    </insert>

    <update id="updateAppSkill" parameterType="AppSkill">
        update app_skill
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="icoUrl != null">ico_url = #{icoUrl},</if>
            <if test="instructions != null">instructions = #{instructions},</if>
            <if test="aptitude != null">aptitude = #{aptitude},</if>
            <if test="isGame != null">is_game = #{isGame},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
        </trim>
        where id = #{id}
    </update>


    <update id="deleteAppSkillById" parameterType="Long">
        update app_skill set is_del=1 where id = #{id}
    </update>

    <delete id="deleteAppSkillByIds" parameterType="String">
        delete from app_skill where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getSkillList" resultType="com.hzy.core.model.vo.app.AppSkillVo">
        select id as skillId, name, ico_url as icoUrl, instructions, aptitude, is_game as isGame,img_url as imgUrl,
        avatar_url as avatarUrl from app_skill
        where is_del=false
        order by id asc
    </select>
</mapper>