<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppTopUpGradeConfigMapper">

    <resultMap type="AppTopUpGradeConfig" id="AppTopUpGradeConfigResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="icoUrl" column="ico_url"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="gradeSize" column="grade_size"/>
        <result property="moneyValue" column="money_value"/>
    </resultMap>

    <sql id="selectAppTopUpGradeConfigVo">
        select id,
        `name`,
        ico_url,
        create_time,
        update_time,
        create_by,
        update_by,
        grade_size,
        money_value
        from app_top_up_grade_config
    </sql>

    <select id="selectAppTopUpGradeConfigList" parameterType="AppTopUpGradeConfig"
            resultMap="AppTopUpGradeConfigResult">
        <include refid="selectAppTopUpGradeConfigVo"/>
        <where>
            <if test="name != null  and name != ''">and `name` like concat('%', #{name}, '%')</if>
            <if test="icoUrl != null  and icoUrl != ''">and ico_url = #{icoUrl}</if>
            <if test="gradeSize != null ">and grade_size = #{gradeSize}</if>
            <if test="moneyValue != null ">and money_value = #{moneyValue}</if>
        </where>
        order by grade_size
    </select>


    <insert id="insertAppTopUpGradeConfig" parameterType="AppTopUpGradeConfig" useGeneratedKeys="true" keyProperty="id">
        insert into app_top_up_grade_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">`name`,</if>
            <if test="icoUrl != null">ico_url,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="gradeSize != null">grade_size,</if>
            <if test="moneyValue != null">money_value,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="icoUrl != null">#{icoUrl},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="gradeSize != null">#{gradeSize},</if>
            <if test="moneyValue != null">#{moneyValue},</if>
        </trim>
    </insert>

    <update id="updateAppTopUpGradeConfig" parameterType="AppTopUpGradeConfig">
        update app_top_up_grade_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">`name` = #{name},</if>
            <if test="icoUrl != null">ico_url = #{icoUrl},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="gradeSize != null">grade_size = #{gradeSize},</if>
            <if test="moneyValue != null">money_value = #{moneyValue},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppTopUpGradeConfigById" parameterType="Long">
        delete
        from app_top_up_grade_config
        where id = #{id}
    </delete>

    <delete id="deleteAppTopUpGradeConfigByIds" parameterType="String">
        delete from app_top_up_grade_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getTopUpGradeConfigList" resultType="com.hzy.core.model.vo.app.AppTitleNobilityGradeConfigVo">
        select id as gradeId,
        `name`,
        ico_url as icoUrl,
        grade_size as gradeSize,
        money_value as upgradesValue
        from app_top_up_grade_config
        order by grade_size asc, id desc
    </select>

    <select id="getMinTopUpGrade" resultMap="AppTopUpGradeConfigResult">
        select id,
        `name`,
        ico_url,
        create_time,
        update_time,
        create_by,
        update_by,
        grade_size,
        money_value
        from app_top_up_grade_config
        order by grade_size asc limit 0, 1
    </select>

    <select id="getNextTopUpGrade" parameterType="java.lang.Long" resultMap="AppTopUpGradeConfigResult">
        select id,
        `name`,
        ico_url,
        create_time,
        update_time,
        create_by,
        update_by,
        grade_size,
        money_value
        from app_top_up_grade_config
        where grade_size &gt; #{currentGradeSize}
        order by grade_size asc limit 0, 1
    </select>
    <select id="selectAppTopUpGradeConfigById" resultType="com.hzy.core.entity.AppTopUpGradeConfig">
        select id,
        `name`,
        ico_url,
        create_time,
        update_time,
        create_by,
        update_by,
        grade_size,
        money_value
        from app_top_up_grade_config
        where id = #{id}
    </select>
    <select id="getIconByUserId" resultType="java.lang.String">
        select ac.ico_url
        from app_top_up_grade_config ac
                 left join app_user_top_up_grade au on au.current_grade_id = ac.id
        where au.user_id = #{userId}
    </select>


    <select id="getTopUpGradeConfigByValue" resultMap="AppTopUpGradeConfigResult">
        select id,
        `name`,
        ico_url,
        create_time,
        update_time,
        create_by,
        update_by,
        grade_size,
        money_value
        from app_top_up_grade_config
        where money_value &lt;= #{value}
        order by money_value desc limit 0, 1
    </select>
</mapper>