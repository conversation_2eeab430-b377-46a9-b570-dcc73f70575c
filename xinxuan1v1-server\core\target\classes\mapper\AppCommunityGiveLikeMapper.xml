<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppCommunityGiveLikeMapper">

    <resultMap type="AppCommunityGiveLike" id="AppCommunityGiveLikeResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="communityId" column="community_id"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectAppCommunityGiveLikeVo">
        select id, user_id, community_id, create_time from app_community_give_like
    </sql>

    <select id="selectAppCommunityGiveLikeList" parameterType="AppCommunityGiveLike"
            resultMap="AppCommunityGiveLikeResult">
        <include refid="selectAppCommunityGiveLikeVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="communityId != null ">and community_id = #{communityId}</if>
        </where>
    </select>

    <select id="selectAppCommunityGiveLikeById" parameterType="Long" resultMap="AppCommunityGiveLikeResult">
        <include refid="selectAppCommunityGiveLikeVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppCommunityGiveLike" parameterType="AppCommunityGiveLike" useGeneratedKeys="true"
            keyProperty="id">
        insert into app_community_give_like
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="communityId != null">community_id,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="communityId != null">#{communityId},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateAppCommunityGiveLike" parameterType="AppCommunityGiveLike">
        update app_community_give_like
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="communityId != null">community_id = #{communityId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppCommunityGiveLikeById" parameterType="Long">
        delete from app_community_give_like where id = #{id}
    </delete>

    <delete id="deleteAppCommunityGiveLikeByIds" parameterType="String">
        delete from app_community_give_like where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="cancelGiveLike" parameterType="java.lang.Long">
        delete
        from app_community_give_like
        where community_id = #{postId}
        and user_id = #{userId}
    </delete>
</mapper>