package com.hzy.client.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.hzy.client.webSocket.ChatRoomWebSocket;
import com.hzy.client.webSocket.PrivateLetterWebSocket;
import com.hzy.core.entity.*;
import com.hzy.core.enums.*;
import com.hzy.core.exception.AppException;
import com.hzy.core.mapper.*;
import com.hzy.core.model.dto.admin.GiveGiftDTO;
import com.hzy.core.model.vo.AjaxResult;
import com.hzy.core.model.vo.R;
import com.hzy.core.model.vo.app.*;
import com.hzy.core.page.TableDataInfo;
import com.hzy.core.service.*;
import com.hzy.core.utils.DateUtils;
import com.hzy.core.utils.PageHelperUtils;
import com.hzy.core.utils.StringUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.hzy.core.constant.GameConstants.LUCK_BOX_NUM_POOL_KEY;
import static com.hzy.core.constant.GameConstants.LUCK_BOX_POOL_STATUS_KEY;
import static com.hzy.core.constant.GiftConstant.GAME_GIFT_CATEGORY_ID;
import static java.util.Collections.shuffle;

/**
 * 礼物服务
 */
@Slf4j
@Service
public class AppGiftService extends ServiceImpl<AppGiftMapper, AppGift> {

    private final String key = "room-heat";
    @Resource
    private AppUserPointsBillMapper appUserPointsBillMapper;
    @Resource
    private AppUserCharmGradeMapper appUserCharmGradeMapper;
    @Resource
    private AppCharmGradeConfigMapper appCharmGradeConfigMapper;
    @Resource
    private AppGiftMapper appGiftMapper;
    @Resource
    private Redisson redisson;
    @Resource
    private AppBxUserTjMapper appBxUserTjMapper;
    @Resource
    private AppUserMapper appUserMapper;
    @Resource
    private AppUserGoldBillMapper appUserGoldBillMapper;
    @Resource
    private AppConfigMapper appConfigMapper;
    @Resource
    private AppUserGiftBackpackMapper appUserGiftBackpackMapper;
    @Resource
    private AppDressUpChestConfigMapper appDressUpChestConfigMapper;
    @Resource
    private AppGiftCategoryMapper appGiftCategoryMapper;
    @Resource
    private AppDressUpChestMainConfigMapper appDressUpChestMainConfigMapper;
    @Resource
    private AppBlacklistMapper appBlacklistMapper;
    @Resource
    private AppChatRoomUserMapper appChatRoomUserMapper;
    @Resource
    private AppChatRoomMapper appChatRoomMapper;
    @Resource
    private AppCommonService appCommonService;
    @Resource
    private ChatRoomWebSocket chatRoomWebSocket;
    @Autowired
    private PrivateLetterWebSocket privateLetterWebSocket;
    @Resource
    private AppUserPersonalDressingMapper appUserPersonalDressingMapper;
    @Resource
    private AppChestGiftConfigMapper appChestGiftConfigMapper;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private AppChestUpgradeConfigMapper appChestUpgradeConfigMapper;
    @Resource
    private AppBxGlConfigMapper appBxGlConfigMapper;
    @Resource
    private UserGiveGiftMapper userGiveGiftMapper;
    @Resource
    private AppGuildMemberMapper appGuildMemberMapper;
    @Resource
    private AppUserGiftBackpackService appUserGiftBackpackService;
    @Resource
    private AppMessageService appMessageService;
    @Resource
    private ChannelCommissionRecordService channelCommissionRecordService;
    @Resource
    private AppUserGoldBillService appUserGoldBillService;
    @Resource
    private AppGiftIncomeConfigService appGiftIncomeConfigService;
    @Autowired
    private GamePoolService gamePoolService;
    @Autowired
    private AppGuildMemberService appGuildMemberService;
    @Autowired
    private AppGuildMapper appGuildMapper;
    @Resource
    private AppGiftService2 appGiftService2;
    @Resource
    private AppChattingRecordsService appChattingRecordsService;
    @Resource
    private AppPrivateLetterListService appPrivateLetterListService;
    @Resource
    private AppGiftCategoryService appGiftCategoryService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public static AppOpenDressUpVo toOpenDressUpChest(List<AppOpenDressUpVo> chestGiftConfigList, String errorMsg) {
        // 宝箱已配置的礼物列表按照比例从小到大排序
        chestGiftConfigList.sort(Comparator.comparing(AppOpenDressUpVo::getRatio));

        // 计算节点 节点的数量比奖品的数量多一个，即0
        List<BigDecimal> nodeList = new ArrayList<>();
        // 第一个节点为0
        nodeList.add(new BigDecimal("0"));
        for (AppOpenDressUpVo giftConfig : chestGiftConfigList) {
            // 每一个节点等于前一个节点+当前奖品的概率
            nodeList.add(nodeList.get(nodeList.size() - 1).add(giftConfig.getRatio()));
        }
        // 生成 0-结束节点 的随机数
        BigDecimal randomBigDecimal = RandomUtil.randomBigDecimal(new BigDecimal("0"),
                nodeList.get(nodeList.size() - 1));
        // 最终抽奖逻辑 此处需要从第二个节点开始遍历
        for (int i = 1; i < nodeList.size(); i++) {
            // 本次节点
            BigDecimal endNode = nodeList.get(i);
            // 前一个节点
            BigDecimal startNode = nodeList.get(i - 1);
            // 若随机数大于等于前一个节点并且小于本节点，在list中位于i-1位置的奖品为抽中奖品
            // Tip：比较大小时，左闭右开与左开右闭都可以，不影响整体概率
            if (randomBigDecimal.compareTo(startNode) >= 0 && randomBigDecimal.compareTo(endNode) < 0) {
                return chestGiftConfigList.get(i - 1);
            }

        }
        throw new AppException(errorMsg);
    }

    /**
     * 执行开宝箱
     *
     * @param chestGiftConfigList
     * @param errorMsg            返回错误信息
     * @return
     */
    public static Long toOpenChest(List<AppChestGiftConfig> chestGiftConfigList, String errorMsg) {
        // 宝箱已配置的礼物列表按照比例从小到大排序
        chestGiftConfigList.sort(Comparator.comparing(AppChestGiftConfig::getRatio));

        // 计算节点 节点的数量比奖品的数量多一个，即0
        List<BigDecimal> nodeList = new ArrayList<>();
        // 第一个节点为0
        nodeList.add(new BigDecimal("0"));
        for (AppChestGiftConfig giftConfig : chestGiftConfigList) {
            // 每一个节点等于前一个节点+当前奖品的概率
            nodeList.add(nodeList.get(nodeList.size() - 1).add(giftConfig.getRatio()));
        }
        // 生成 0-结束节点 的随机数
        BigDecimal randomBigDecimal = RandomUtil.randomBigDecimal(new BigDecimal("0"),
                nodeList.get(nodeList.size() - 1));
        // 最终抽奖逻辑 此处需要从第二个节点开始遍历
        for (int i = 1; i < nodeList.size(); i++) {
            // 本次节点
            BigDecimal endNode = nodeList.get(i);
            // 前一个节点
            BigDecimal startNode = nodeList.get(i - 1);
            // 若随机数大于等于前一个节点并且小于本节点，在list中位于i-1位置的奖品为抽中奖品
            // Tip：比较大小时，左闭右开与左开右闭都可以，不影响整体概率
            if (randomBigDecimal.compareTo(startNode) >= 0 && randomBigDecimal.compareTo(endNode) < 0) {
                return chestGiftConfigList.get(i - 1).getBindGiftId();
            }

        }
        throw new AppException(errorMsg);
    }

    public static void main(String[] args) {

        for (int i = 0; i < 10; i++) {
            BigDecimal randomBigDecimal = RandomUtil.randomBigDecimal(new BigDecimal("0"), new BigDecimal("0.01"));
            System.out.println(randomBigDecimal);
        }

    }

    private void replenishMagicEggPrizePool(AppChestUpgradeConfig upgradeConfig) {

        RLock lock = redisson.getLock("app:kbx:jc:cj:" + upgradeConfig.getId());
        if (!lock.isLocked()) {
            lock.lock(-1, TimeUnit.SECONDS);

            try {
                long size = redisTemplate.boundListOps("app:kbx:jc:" + upgradeConfig.getId()).size();
                if (size <= 0) {
                    // 获取该宝箱已配置的礼物列表
                    AppChestGiftConfig chestGiftConfigQuery = new AppChestGiftConfig();
                    chestGiftConfigQuery.setGittId(upgradeConfig.getId());
                    List<AppChestGiftConfig> prizePoolConfigList = appChestGiftConfigMapper
                            .selectAppChestGiftConfigList(chestGiftConfigQuery);
                    if (CollectionUtils.isEmpty(prizePoolConfigList)) {
                        return;
                    }
                    toCreatePrizePoolNew(prizePoolConfigList, upgradeConfig);
                }
            } catch (Exception e) {

            } finally {
                try {
                    lock.unlock();
                } catch (Exception error) {
                    try {
                        lock.delete();
                    } catch (Exception er) {

                    }

                }
            }

        }

    }

    private Boolean toCreatePrizePoolNew(final List<AppChestGiftConfig> prizePoolConfigList,
                                         final AppChestUpgradeConfig upgradeConfig) {
        redisTemplate.delete("app:kbx:jc:" + upgradeConfig.getId());

        List<AppGiftInfoVo> oneRoundPrizePool = getOneRound(prizePoolConfigList);
        if (!CollectionUtils.isEmpty(oneRoundPrizePool)) {
            // 将list打散
            shuffle(oneRoundPrizePool);
            redisTemplate.opsForList().rightPushAll("app:kbx:jc:" + upgradeConfig.getId(), oneRoundPrizePool);
        }

        return true;
    }

    private List<AppGiftInfoVo> getOneRound(List<AppChestGiftConfig> prizePoolConfigList) {
        List<AppGiftInfoVo> prizePoolConfigListNew = new ArrayList<>();
        for (AppChestGiftConfig config : prizePoolConfigList) {
            int residueNum = config.getRatio().intValue();
            for (int j = 0; j < residueNum; j++) {
                AppGiftInfoVo vo = new AppGiftInfoVo();
                vo.setGiftId(config.getBindGiftId());
                AppGift gift = appGiftMapper.getGiftById(config.getBindGiftId());
                vo.setGiftPrice(null == gift ? new BigDecimal("0") : gift.getMasonryPrice());
                prizePoolConfigListNew.add(vo);
            }
        }

        List<Object> list = redisTemplate.executePipelined(new SessionCallback<Object>() {
            @Override
            public <K, V> Object execute(RedisOperations<K, V> operations) throws DataAccessException {
                ListOperations<String, Object> listOperations = (ListOperations<String, Object>) operations
                        .opsForList();
                listOperations.range("bx-mf-kc", 0, -1);
                return null;
            }
        });

        List<Object> results = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (Object o : list) {
                List<Object> dataList = (List<Object>) o;
                results.addAll(dataList);
            }
        }

        if (!CollectionUtils.isEmpty(results)) {
            List<Object> objects = new ArrayList<>();
            for (Object o : results) {
                if (null != o) {
                    Long giftId = Long.parseLong(o.toString());
                    Iterator<AppGiftInfoVo> re = prizePoolConfigListNew.iterator();
                    while (re.hasNext()) {
                        AppGiftInfoVo item = re.next();
                        if (item.getGiftId().equals(giftId)) {
                            re.remove();
                            objects.add(o);
                        }

                    }
                }
            }

            if (!CollectionUtils.isEmpty(objects)) {
                List<Object> result = redisTemplate.executePipelined(new SessionCallback<Object>() {
                    @Override
                    public <K, V> Object execute(RedisOperations<K, V> operations) throws DataAccessException {
                        ListOperations<String, Object> listOperations = (ListOperations<String, Object>) operations
                                .opsForList();
                        for (Object re : objects) {
                            listOperations.remove("bx-mf-kc", 1, re);
                        }
                        return null;
                    }
                });
            }
        }

        return prizePoolConfigListNew;
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public List<AppOpenGiftVo> toPrizeDrawGift(Integer num, BigDecimal sumPrice, AppGift appGift, Long userId) {

        AppConfig appConfig = appConfigMapper.getAppConfig();

        List<AppOpenGiftVo> prizeDrawGiftList = new ArrayList<>();// 抽中的礼物列表

        if (appConfig.getBxJcType().intValue() == 1) {// 数量公池

            AppChestUpgradeConfig chestUpgradeConfigQuery = new AppChestUpgradeConfig();
            chestUpgradeConfigQuery.setChestGiftId(appGift.getId());
            List<AppChestUpgradeConfig> upgradeConfigList = appChestUpgradeConfigMapper
                    .selectAppChestUpgradeConfigList(chestUpgradeConfigQuery);
            if (CollectionUtils.isEmpty(upgradeConfigList)) {
                throw new AppException("平台未配置进阶池");
            }

            BigDecimal sumNum = appBxGlConfigMapper.getUserToDaySumNum(userId);
            if (null == sumNum) {
                sumNum = new BigDecimal("0");
            }

            AppChestUpgradeConfig upgradeConfig = null;

            for (AppChestUpgradeConfig item : upgradeConfigList) {
                if (sumNum.compareTo(item.getMinAmount()) >= 0) {
                    if (sumNum.compareTo(item.getMaxAmount()) <= 0 || item.getMaxAmount()
                            .compareTo(new BigDecimal("-1")) == 0) {
                        upgradeConfig = item;
                        break;
                    }
                }
            }

            if (null == upgradeConfig) {
                throw new AppException("未找到进阶池");
            }

            String key = "app:kbx:jc:" + upgradeConfig.getId();

            long size = redisTemplate.boundListOps(key).size();
            if (size <= 0) {
                replenishMagicEggPrizePool(upgradeConfig);
            }

            appChestUpgradeConfigMapper.addSumPut(upgradeConfig.getId(), sumPrice);

            appChestUpgradeConfigMapper.addExtractedNum(upgradeConfig.getId(), (long) num);

            BigDecimal max = sumPrice.multiply(upgradeConfig.getSlcBl());
            BigDecimal dq = new BigDecimal("0");

            if (max.compareTo(new BigDecimal("0")) <= 0) {
                throw new AppException("什么都没开到哦～");
            }

            max = max.setScale(0, RoundingMode.DOWN);

            max = new BigDecimal(RandomUtil.randomLong(1, max.longValue()));

            max = max.setScale(0, RoundingMode.DOWN);

            int i = 100;

            List<Object> list = redisTemplate.executePipelined(new SessionCallback<Object>() {
                @Override
                public <K, V> Object execute(RedisOperations<K, V> operations) throws DataAccessException {
                    ListOperations<String, Object> listOperations = (ListOperations<String, Object>) operations
                            .opsForList();
                    listOperations.range(key, 0, -1);
                    return null;
                }
            });

            List<Object> results = new ArrayList<>();
            if (!CollectionUtils.isEmpty(list)) {
                for (Object o : list) {
                    List<Object> dataList = (List<Object>) o;
                    results.addAll(dataList);
                }
            }

            List<Object> objects = new ArrayList<>();
            if (!CollectionUtils.isEmpty(results)) {
                while (dq.compareTo(max) < 0 && i > 0) {
                    if (!CollectionUtils.isEmpty(results)) {
                        shuffle(results);
                        Iterator<Object> iterator = results.iterator();
                        while (iterator.hasNext()) {
                            Object re = iterator.next();
                            AppGiftInfoVo vo = (AppGiftInfoVo) re;
                            BigDecimal dq2 = dq.add(vo.getGiftPrice());
                            if (dq2.compareTo(max) <= 0) {
                                dq = dq.add(vo.getGiftPrice());
                                objects.add(re);
                                iterator.remove();
                            }
                        }
                    }

                    i = i - 1;
                }
            }

            if (!CollectionUtils.isEmpty(objects)) {
                List<Object> result = redisTemplate.executePipelined(new SessionCallback<Object>() {
                    @Override
                    public <K, V> Object execute(RedisOperations<K, V> operations) throws DataAccessException {
                        ListOperations<String, Object> listOperations = (ListOperations<String, Object>) operations
                                .opsForList();
                        for (Object re : objects) {
                            listOperations.remove(key, 1, re);
                        }
                        return null;
                    }
                });
            }

            if (!CollectionUtils.isEmpty(objects)) {
                for (Object result : objects) {
                    if (result != null) {
                        AppGiftInfoVo infoVo = (AppGiftInfoVo) result;
                        if (!infoVo.getGiftId().equals(0L)) {
                            AppOpenGiftVo vo = new AppOpenGiftVo();
                            vo.setGiftId(infoVo.getGiftId());
                            prizeDrawGiftList.add(vo);
                        }

                    }
                }
            }

            if (CollectionUtils.isEmpty(prizeDrawGiftList)) {
                throw new AppException("什么都没开到哦～");
            }

            BigDecimal zj = new BigDecimal("0");

            List<AppOpenGiftVo> openGiftNameList = appGiftMapper.getGiftNameByGiftIdList(prizeDrawGiftList);
            for (AppOpenGiftVo vo : openGiftNameList) {
                zj = zj.add(vo.getGiftPrice());
            }

            List<AppOpenGiftVo> prizeDrawGiftListNew = new ArrayList<>();

            Map<Long, List<AppOpenGiftVo>> openGiftListGroup = prizeDrawGiftList.stream()
                    .collect(Collectors.groupingBy(AppOpenGiftVo::getGiftId));
            for (Map.Entry<Long, List<AppOpenGiftVo>> entry : openGiftListGroup.entrySet()) {
                AppOpenGiftVo giftVo = new AppOpenGiftVo();
                giftVo.setGiftId(entry.getKey());
                giftVo.setNum(entry.getValue().size());

                for (AppOpenGiftVo item : openGiftNameList) {
                    if (item.getGiftId().equals(giftVo.getGiftId())) {
                        giftVo.setGiftName(item.getGiftName());
                        giftVo.setGiftPrice(item.getGiftPrice());
                        giftVo.setGiftUrl(item.getGiftUrl());
                        giftVo.setGiftPrice(item.getGiftPrice());
                        break;
                    }
                }
                prizeDrawGiftListNew.add(giftVo);
            }

            if (appChestUpgradeConfigMapper.addSumProfit(upgradeConfig.getId()) <= 0) {
                throw new AppException("什么都没开到哦～");
            }

            appChestUpgradeConfigMapper.addSumOut(upgradeConfig.getId(), zj);

            AppBxUserTj appBxUserTj = appBxUserTjMapper.getUserAppBxUserTj(userId, appGift.getId());
            if (null == appBxUserTj) {
                appBxUserTj = new AppBxUserTj();
                appBxUserTj.setCreateTime(new Date());
                appBxUserTj.setUserId(userId);
                appBxUserTj.setSrSum(new BigDecimal("0"));
                appBxUserTj.setZcSum(new BigDecimal("0"));
                appBxUserTj.setBxId(appGift.getId());
                appBxUserTjMapper.insertAppBxUserTj(appBxUserTj);
            }
            appBxUserTj.setSrSum(appBxUserTj.getSrSum().add(dq));
            appBxUserTj.setZcSum(appBxUserTj.getZcSum().add(sumPrice));
            appBxUserTj.setUpdateTime(new Date());
            appBxUserTjMapper.updateAppBxUserTj(appBxUserTj);

            return prizeDrawGiftListNew;

        } else {// 概率池

            AppChestGiftConfig chestGiftConfigQuery = new AppChestGiftConfig();
            chestGiftConfigQuery.setGittId(appGift.getId());
            List<AppChestGiftConfig> prizePoolConfigList = appChestGiftConfigMapper
                    .selectAppChestGiftConfigList(chestGiftConfigQuery);
            if (CollectionUtils.isEmpty(prizePoolConfigList)) {
                throw new AppException("该宝箱未配置礼物数据");
            }
            // 获取该宝箱已配置的礼物列表
            prizePoolConfigList.forEach(item -> {
                AppGift gift = appGiftMapper.getGiftById(item.getBindGiftId());
                item.setPrice(null == gift ? new BigDecimal("0") : gift.getMasonryPrice());
            });

            appChestUpgradeConfigMapper.addSumPutBl(appGift.getId(), sumPrice);

            appChestUpgradeConfigMapper.addExtractedNumBl(appGift.getId(), (long) num);

            AppBxGlConfig bxGlConfig = appBxGlConfigMapper.selectAppBxGlConfigById(appGift.getId());

            final BigDecimal jcNum = bxGlConfig.getSumPutGl().subtract(bxGlConfig.getSumOutGl());

            final BigDecimal max = sumPrice.multiply(bxGlConfig.getBlGl());

            BigDecimal sumNum = appBxGlConfigMapper.getUserSumNum(userId);
            if (null == sumNum) {
                sumNum = new BigDecimal("0");
            }

            BigDecimal minPrice = new BigDecimal("0");

            BigDecimal maxPrice = max;

            if (bxGlConfig.getMinKzl().compareTo(new BigDecimal("0")) > 0) {
                minPrice = sumNum.multiply(bxGlConfig.getMinKzl());
                if (minPrice.compareTo(jcNum) > 0) {
                    minPrice = new BigDecimal("0");
                }
            }

            if (bxGlConfig.getMaxKzl().compareTo(new BigDecimal("0")) > 0) {
                maxPrice = sumNum.multiply(bxGlConfig.getMaxKzl());
                if (maxPrice.compareTo(jcNum) > 0) {
                    maxPrice = max;
                    if (maxPrice.compareTo(jcNum) > 0) {
                        maxPrice = jcNum;
                    }
                }
            } else {
                if (maxPrice.compareTo(jcNum) > 0) {
                    maxPrice = jcNum;
                }
            }

            minPrice = minPrice.setScale(0, RoundingMode.DOWN);

            maxPrice = maxPrice.setScale(0, RoundingMode.DOWN);

            maxPrice = new BigDecimal(RandomUtil.randomLong(1, maxPrice.longValue()));

            maxPrice = maxPrice.setScale(0, RoundingMode.DOWN);

            BigDecimal dq = new BigDecimal("0");

            while (num > 0 || dq.compareTo(new BigDecimal("0")) <= 0) {
                num = num - 1;

                // 宝箱已配置的礼物列表按照比例从小到大排序
                prizePoolConfigList.sort(Comparator.comparing(AppChestGiftConfig::getGl));
                // 计算节点 节点的数量比奖品的数量多一个，即0
                List<BigDecimal> nodeList = new ArrayList<>();
                // 第一个节点为0
                nodeList.add(new BigDecimal("0"));
                for (AppChestGiftConfig giftConfig : prizePoolConfigList) {
                    // 每一个节点等于前一个节点+当前奖品的概率
                    nodeList.add(nodeList.get(nodeList.size() - 1).add(giftConfig.getGl()));
                }
                // 生成 0-结束节点 的随机数
                BigDecimal randomBigDecimal = RandomUtil.randomBigDecimal(new BigDecimal("0"),
                        nodeList.get(nodeList.size() - 1));
                // 最终抽奖逻辑 此处需要从第二个节点开始遍历
                for (int i = 1; i < nodeList.size(); i++) {
                    BigDecimal endNode = nodeList.get(i);
                    BigDecimal startNode = nodeList.get(i - 1);
                    if (randomBigDecimal.compareTo(startNode) >= 0 && randomBigDecimal.compareTo(endNode) < 0) {
                        AppChestGiftConfig config = prizePoolConfigList.get(i - 1);
                        BigDecimal price = dq.add(config.getPrice());
                        if (price.compareTo(maxPrice) <= 0) {
                            AppOpenGiftVo vo = new AppOpenGiftVo();
                            vo.setGiftId(config.getBindGiftId());
                            prizeDrawGiftList.add(vo);
                            dq = dq.add(config.getPrice());
                            break;
                        }
                    }

                }

            }

            while (dq.compareTo(minPrice) < 0) {
                // 宝箱已配置的礼物列表按照比例从小到大排序
                prizePoolConfigList.sort(Comparator.comparing(AppChestGiftConfig::getGl));
                // 计算节点 节点的数量比奖品的数量多一个，即0
                List<BigDecimal> nodeList = new ArrayList<>();
                // 第一个节点为0
                nodeList.add(new BigDecimal("0"));
                for (AppChestGiftConfig giftConfig : prizePoolConfigList) {
                    // 每一个节点等于前一个节点+当前奖品的概率
                    nodeList.add(nodeList.get(nodeList.size() - 1).add(giftConfig.getGl()));
                }
                // 生成 0-结束节点 的随机数
                BigDecimal randomBigDecimal = RandomUtil.randomBigDecimal(new BigDecimal("0"),
                        nodeList.get(nodeList.size() - 1));
                // 最终抽奖逻辑 此处需要从第二个节点开始遍历
                for (int i = 1; i < nodeList.size(); i++) {
                    BigDecimal endNode = nodeList.get(i);
                    BigDecimal startNode = nodeList.get(i - 1);
                    if (randomBigDecimal.compareTo(startNode) >= 0 && randomBigDecimal.compareTo(endNode) < 0) {
                        AppChestGiftConfig config = prizePoolConfigList.get(i - 1);
                        BigDecimal price = dq.add(config.getPrice());
                        if (price.compareTo(maxPrice) <= 0) {
                            AppOpenGiftVo vo = new AppOpenGiftVo();
                            vo.setGiftId(config.getBindGiftId());
                            prizeDrawGiftList.add(vo);
                            dq = dq.add(config.getPrice());
                            break;
                        }
                    }

                }

            }

            if (CollectionUtils.isEmpty(prizeDrawGiftList)) {
                throw new AppException("什么都没开到哦～");
            }

            appChestUpgradeConfigMapper.addSumOutBl(appGift.getId(), dq);
            if (appChestUpgradeConfigMapper.addSumProfitBl(appGift.getId()) <= 0) {
                throw new AppException("什么都没开到哦～");
            }

            List<AppOpenGiftVo> openGiftNameList = appGiftMapper.getGiftNameByGiftIdList(prizeDrawGiftList);

            List<AppOpenGiftVo> prizeDrawGiftListNew = new ArrayList<>();

            Map<Long, List<AppOpenGiftVo>> openGiftListGroup = prizeDrawGiftList.stream()
                    .collect(Collectors.groupingBy(AppOpenGiftVo::getGiftId));
            for (Map.Entry<Long, List<AppOpenGiftVo>> entry : openGiftListGroup.entrySet()) {
                AppOpenGiftVo giftVo = new AppOpenGiftVo();
                giftVo.setGiftId(entry.getKey());
                giftVo.setNum(entry.getValue().size());

                for (AppOpenGiftVo item : openGiftNameList) {
                    if (item.getGiftId().equals(giftVo.getGiftId())) {
                        giftVo.setGiftName(item.getGiftName());
                        giftVo.setGiftPrice(item.getGiftPrice());
                        giftVo.setGiftUrl(item.getGiftUrl());
                        giftVo.setGiftPrice(item.getGiftPrice());
                        break;
                    }
                }
                prizeDrawGiftListNew.add(giftVo);
            }

            AppBxUserTj appBxUserTj = appBxUserTjMapper.getUserAppBxUserTj(userId, appGift.getId());
            if (null == appBxUserTj) {
                appBxUserTj = new AppBxUserTj();
                appBxUserTj.setCreateTime(new Date());
                appBxUserTj.setUserId(userId);
                appBxUserTj.setSrSum(new BigDecimal("0"));
                appBxUserTj.setZcSum(new BigDecimal("0"));
                appBxUserTj.setBxId(appGift.getId());
                appBxUserTjMapper.insertAppBxUserTj(appBxUserTj);
            }
            appBxUserTj.setSrSum(appBxUserTj.getSrSum().add(dq));
            appBxUserTj.setZcSum(appBxUserTj.getZcSum().add(sumPrice));
            appBxUserTj.setUpdateTime(new Date());
            appBxUserTjMapper.updateAppBxUserTj(appBxUserTj);

            return prizeDrawGiftListNew;

        }

    }

    /**
     * 一键赠送
     *
     * @param
     * @param
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public R oneClickGift(AppUserEntity user, Long chatRoomId, Long toUserId) {
        Long userId = user.getId();
        GiveGiftDTO giveGiftDTO = new GiveGiftDTO();
        giveGiftDTO.setUserId(userId);
        AppConfig appConfig1 = appConfigMapper.getAppConfig();
        List<BackPackVo> backPackList = appUserGiftBackpackMapper.getBackPackList(giveGiftDTO.getUserId());
        if (toUserId == null) {
            return R.fail("参数toUserId不能为null");
        }
        RLock lock = redisson.getLock("app:user:giveGiftsByBackpack:" + userId);
        if (lock.isLocked()) {
            return R.fail("操作频繁，请稍后再试");
        }
        lock.lock(120, TimeUnit.SECONDS);
        BigDecimal totalPrice = BigDecimal.ZERO;
        AppChatRoom chatRoom = null;
        String chatRoomName = "";
        if (chatRoomId != null && chatRoomId != 0) {
            chatRoom = appChatRoomMapper.selectAppChatRoomById(chatRoomId);
            if (ObjectUtils.isEmpty(chatRoom)) {
                lock.unlock();
                return R.fail("当前聊天室不存在");
            }

            if (chatRoom.getStatus().intValue() == AppChatRoomStatusTypeEnums.TYPE0.getId()) {
                lock.unlock();
                return R.fail("当前聊天室初始化中");
            }
            if (chatRoom.getStatus().intValue() == AppChatRoomStatusTypeEnums.TYPE2.getId()) {
                lock.unlock();
                return R.fail("当前聊天室已结束");
            }

            AppChatRoomUser chatRoomUser = appChatRoomUserMapper.selectAppChatRoomUserByChatRoomIdAndUserId(chatRoomId,
                    userId);
            if (null == chatRoomUser || chatRoomUser.getIsJoin().intValue() == WhetherTypeEnum.NO.getName()) {
                lock.unlock();
                return R.fail("赠送失败,您未进入聊天室");
            }

            AppUserEntity receiveUser = appUserMapper.selectAppUserById(toUserId);
            if (ObjectUtils.isEmpty(receiveUser)) {
                lock.unlock();
                return R.fail("赠送失败,对方账号不存在");
            }

            chatRoomName = chatRoom.getName();
            // 获取赠送礼物的礼物列表
            // List<BackPackVo> backPackList =
            // appUserGiftBackpackMapper.getBackPackList(userId);
            if (ObjectUtils.isEmpty(backPackList)) {
                lock.unlock();
                return R.fail("礼物数量不足");
            } else {
                // 计算当前用户背包礼物的金额
                totalPrice = backPackList.stream()
                        .map(item -> item.getMasonryPrice().multiply(BigDecimal.valueOf(item.getGiftNum())))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 去掉赠送的礼物
                backPackList.stream().forEach(s -> {
                    appUserGiftBackpackService.deduceUserBackpackGiftNum(s.getGiftId(), s.getGiftNum(), userId);
                });
                // 计算钻石
                AppConfig appConfig = appCommonService.getAppConfig();// 获取app配置信息
                // 计算赠送礼物金币等于多少人民币
                BigDecimal consumptionRmb = totalPrice.multiply(appConfig.getOneGoldEqRmb());
                // 计算人民币等于多少钻石
                BigDecimal rmbEqPoints = consumptionRmb.multiply(appConfig.getOneRmbEqPoints());
                // 接收者收入钻石金额
                BigDecimal rmbEqPointsIncome = rmbEqPoints.multiply(chatRoom.getGiftIncomeScale());
                if (rmbEqPoints.compareTo(BigDecimal.ZERO) > 0) {
                    // 累加接收人钻石余额
                    appUserMapper.addUserPointsBalance(toUserId, rmbEqPointsIncome);
                }

                BigDecimal rmbEqPointsHallOwner = BigDecimal.ZERO;
                // 房主
                if (!ObjectUtils.isEmpty(chatRoom.getHallOwnerUserId())) {
                    // 房主收益
                    rmbEqPointsHallOwner = rmbEqPoints.multiply(chatRoom.getHallOwnerCommissionScale());
                    appUserMapper.addUserPointsBalance(chatRoom.getHallOwnerUserId(), rmbEqPointsHallOwner);
                }
                Date time = new Date();
                // 赠送记录
                // 添加礼物赠送记录
                UserGiveGiftEntity userGiveGiftEntity;
                List<UserGiveGiftEntity> list = new ArrayList<>();
                AppChatRoom chatRoom1 = appChatRoomMapper.selectAppChatRoomById(chatRoomId);

                for (BackPackVo s : backPackList) {
                    userGiveGiftEntity = new UserGiveGiftEntity();
                    BigDecimal price = calculatePoints(s.getMasonryPrice());
                    // BigDecimal price = goldCalculatePoints(s.getMasonryPrice());
                    BigDecimal priceTotal = price.multiply(BigDecimal.valueOf(s.getGiftNum()));
                    // 接收者钻石金额
                    BigDecimal inComePrice = priceTotal.multiply(chatRoom1.getGiftIncomeScale());
                    // 厅主钻石余额
                    BigDecimal hallOwnerPrice = BigDecimal.ZERO;
                    if (!ObjectUtils.isEmpty(chatRoom1.getHallOwnerCommissionScale())) {
                        hallOwnerPrice = priceTotal.multiply(chatRoom1.getHallOwnerCommissionScale());
                    }

                    userGiveGiftEntity.setUserId(userId);
                    userGiveGiftEntity.setToUserId(toUserId);
                    userGiveGiftEntity.setGiftId(s.getGiftId());
                    userGiveGiftEntity.setGiftName(s.getGiftName());
                    userGiveGiftEntity.setGiftNum(s.getGiftNum());
                    userGiveGiftEntity.setGiftGoldUnitAmount(s.getMasonryPrice());
                    userGiveGiftEntity.setGiftGoldTotalAmount(s.getMasonryPrice()
                            .multiply(BigDecimal.valueOf(s.getGiftNum())));
                    userGiveGiftEntity.setReceivePointTotalAmount(inComePrice);
                    userGiveGiftEntity.setHallPointTotalAmount(hallOwnerPrice);
                    userGiveGiftEntity.setProfitAmount(price.multiply(BigDecimal.valueOf(s.getGiftNum()))
                            .subtract(inComePrice.add(hallOwnerPrice)));
                    userGiveGiftEntity.setChatRoomId(chatRoomId);
                    if (chatRoomId != null) {
                        userGiveGiftEntity.setHallOwnerUserId(chatRoom.getHallOwnerUserId());
                    }
                    userGiveGiftEntity.setCreatedTime(LocalDateTime.now());
                    userGiveGiftEntity.setDeleted(false);
                    list.add(userGiveGiftEntity);
                    // userGiveGiftMapper.insert(userGiveGiftEntity);
                }
                userGiveGiftMapper.saveGiveGift(list);
                for (UserGiveGiftEntity giveGiftEntity : list) {
                    for (BackPackVo backPackVo : backPackList) {
                        if (giveGiftEntity.getGiftId().equals(backPackVo.getGiftId())) {
                            backPackVo.setGiveId(giveGiftEntity.getId());
                        }
                    }
                }

                // 记录发送用户账单
                AppUserGoldBill userPointsBill = new AppUserGoldBill();
                // AppUserPointsBill userPointsBill = new AppUserPointsBill();
                userPointsBill.setUserId(userId);
                userPointsBill.setChatRoomId(chatRoomId);
                userPointsBill.setBillType((long) AppPointsBillTypeEnums.TYPE2.getId());// 账单类型为收到礼物
                userPointsBill.setObjectId(backPackList.get(0).getGiveId());// 产生账单的相关id为该礼物发送者用户的id
                userPointsBill.setAmount(totalPrice);// 金额为发送者的金币金额
                userPointsBill.setIsDel(WhetherTypeEnum.NO.getName());
                // appUserPointsBillMapper.insertAppUserPointsBill(userPointsBill);
                appUserGoldBillMapper.insertAppUserGoldBill(userPointsBill);

                // 记录接收用户账单
                AppUserPointsBill toUserPointsBill = new AppUserPointsBill();
                toUserPointsBill.setUserId(toUserId);
                toUserPointsBill.setCreateTime(time);
                toUserPointsBill.setBillType((long) AppPointsBillTypeEnums.TYPE13.getId());// 账单类型为收到礼物
                toUserPointsBill.setObjectId(backPackList.get(0).getGiveId());// 产生账单的相关id为该礼物发送者用户的id
                toUserPointsBill.setAmount(rmbEqPointsIncome);// 金额为本次获得的收益
                toUserPointsBill.setIsDel(WhetherTypeEnum.NO.getName());
                toUserPointsBill.setGuildId(appGuildMemberMapper.getGuildIdByUserId(toUserId));
                appUserPointsBillMapper.insertAppUserPointsBill(toUserPointsBill);

                // 存在厅主就记录厅主的账单
                if (!ObjectUtils.isEmpty(chatRoom.getHallOwnerUserId())) {
                    // 记录接收用户账单
                    AppUserPointsBill HallOwnerUserPointsBill = new AppUserPointsBill();
                    HallOwnerUserPointsBill.setUserId(chatRoom.getHallOwnerUserId());
                    HallOwnerUserPointsBill.setCreateTime(time);
                    HallOwnerUserPointsBill.setBillType((long) AppPointsBillTypeEnums.TYPE19.getId());// 账单类型为厅主收益
                    HallOwnerUserPointsBill.setObjectId(backPackList.get(0).getGiveId());// 产生账单的相关id为该礼物发送者用户的id
                    HallOwnerUserPointsBill.setAmount(rmbEqPointsHallOwner);// 金额为本次厅主获得的收益
                    HallOwnerUserPointsBill.setIsDel(WhetherTypeEnum.NO.getName());
                    HallOwnerUserPointsBill.setGuildId(appGuildMemberMapper.getGuildIdByUserId(chatRoom.getHallOwnerUserId()));
                    appUserPointsBillMapper.insertAppUserPointsBill(HallOwnerUserPointsBill);
                }
                /*
                 * //赠送记录
                 * //添加礼物赠送记录
                 * UserGiveGiftEntity userGiveGiftEntity;
                 * List<UserGiveGiftEntity> list = new ArrayList<>();
                 * AppChatRoom chatRoom1 = appChatRoomMapper.selectAppChatRoomById(chatRoomId);
                 *
                 * for (BackPackVo s : backPackList) {
                 * userGiveGiftEntity = new UserGiveGiftEntity();
                 * BigDecimal price = calculatePoints(s.getMasonryPrice());
                 * BigDecimal priceTotal = price.multiply(BigDecimal.valueOf(s.getGiftNum()));
                 * //接收者钻石金额
                 * BigDecimal inComePrice = priceTotal.multiply(chatRoom1.getGiftIncomeScale());
                 * //厅主钻石余额
                 * BigDecimal hallOwnerPrice = BigDecimal.ZERO;
                 * if (!ObjectUtils.isEmpty(chatRoom1.getHallOwnerCommissionScale())) {
                 * hallOwnerPrice =
                 * priceTotal.multiply(chatRoom1.getHallOwnerCommissionScale());
                 * }
                 *
                 * userGiveGiftEntity.setUserId(userId);
                 * userGiveGiftEntity.setToUserId(toUserId);
                 * userGiveGiftEntity.setGiftId(s.getGiftId());
                 * userGiveGiftEntity.setGiftName(s.getGiftName());
                 * userGiveGiftEntity.setGiftNum(s.getGiftNum());
                 * userGiveGiftEntity.setGiftGoldUnitAmount(s.getMasonryPrice());
                 * userGiveGiftEntity.setGiftGoldTotalAmount(s.getMasonryPrice().multiply(
                 * BigDecimal.valueOf(s.getGiftNum())));
                 * userGiveGiftEntity.setReceivePointTotalAmount(inComePrice);
                 * userGiveGiftEntity.setHallPointTotalAmount(hallOwnerPrice);
                 * userGiveGiftEntity.setProfitAmount(price.multiply(BigDecimal.valueOf(s.
                 * getGiftNum())).subtract(inComePrice.add(hallOwnerPrice)));
                 * userGiveGiftEntity.setChatRoomId(chatRoomId);
                 * userGiveGiftEntity.setCreatedTime(LocalDateTime.now());
                 * userGiveGiftEntity.setDeleted(false);
                 * list.add(userGiveGiftEntity);
                 * //userGiveGiftMapper.insert(userGiveGiftEntity);
                 * }
                 * userGiveGiftMapper.saveGiveGift(list);
                 */

                // 添加魅力值
                BigDecimal userContributionValue = BigDecimal.ZERO;
                BigDecimal toUserCharmValue = BigDecimal.ZERO;
                userContributionValue = totalPrice;
                toUserCharmValue = totalPrice;
                userPointsBill.setUserContributionValue(userContributionValue);
                userPointsBill.setToUserCharmValue(toUserCharmValue);
                appUserGoldBillMapper.updateAppUserGoldBill(userPointsBill);

                appChatRoomUserMapper.addUserGiftGold(chatRoomId, toUserId, totalPrice);
                lock.unlock();
            }

        } else {
            AppUserEntity receiveUser = appUserMapper.selectAppUserById(toUserId);
            if (ObjectUtils.isEmpty(receiveUser)) {
                lock.unlock();
                return R.fail("赠送失败,对方账号不存在");
            }

            // 计算当前用户背包礼物的金额
            totalPrice = backPackList.stream()
                    .map(item -> item.getMasonryPrice().multiply(BigDecimal.valueOf(item.getGiftNum())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 去掉赠送的礼物
            backPackList.stream().forEach(s -> {
                appUserGiftBackpackService.deduceUserBackpackGiftNum(s.getGiftId(), s.getGiftNum(), userId);
            });

            BigDecimal price = goldCalculatePoints(totalPrice, toUserId, appChatRoomMapper.selectAppChatRoomById(chatRoomId));
            if (price.compareTo(BigDecimal.ZERO) > 0) {
                // 累加接收人钻石余额
                appUserMapper.addUserPointsBalance(toUserId, price);
            }
            // 添加礼物赠送记录
            UserGiveGiftEntity userGiveGiftEntity;
            List<UserGiveGiftEntity> list = new ArrayList<>();
            for (BackPackVo s : backPackList) {
                userGiveGiftEntity = new UserGiveGiftEntity();
                BigDecimal price1 = calculatePoints(s.getMasonryPrice());
                BigDecimal priceTotal = price1.multiply(BigDecimal.valueOf(s.getGiftNum()));
                // 接收者钻石金额
                BigDecimal inComePrice = priceTotal.multiply(appConfig1.getGiftIncomeScale());
                // 厅主钻石余额
                BigDecimal hallOwnerPrice = BigDecimal.ZERO;

                userGiveGiftEntity.setUserId(userId);
                userGiveGiftEntity.setToUserId(toUserId);
                userGiveGiftEntity.setGiftId(s.getGiftId());
                userGiveGiftEntity.setGiftName(s.getGiftName());
                userGiveGiftEntity.setGiftNum(s.getGiftNum());
                userGiveGiftEntity.setGiftGoldUnitAmount(s.getMasonryPrice());
                userGiveGiftEntity.setGiftGoldTotalAmount(s.getMasonryPrice()
                        .multiply(BigDecimal.valueOf(s.getGiftNum())));
                userGiveGiftEntity.setReceivePointTotalAmount(inComePrice);
                userGiveGiftEntity.setHallPointTotalAmount(hallOwnerPrice);
                userGiveGiftEntity.setProfitAmount(price1.multiply(BigDecimal.valueOf(s.getGiftNum()))
                        .subtract(inComePrice.add(hallOwnerPrice)));
                userGiveGiftEntity.setCreatedTime(LocalDateTime.now());
                userGiveGiftEntity.setDeleted(false);
                list.add(userGiveGiftEntity);
                // userGiveGiftMapper.insert(userGiveGiftEntity);
            }
            userGiveGiftMapper.saveGiveGift(list);
            for (UserGiveGiftEntity giveGiftEntity : list) {
                for (BackPackVo backPackVo : backPackList) {
                    if (giveGiftEntity.getGiftId().equals(backPackVo.getGiftId())) {
                        backPackVo.setGiveId(giveGiftEntity.getId());
                    }
                }
            }

            // 记录发送用户账单
            AppUserGoldBill userPointsBill = new AppUserGoldBill();
            // AppUserPointsBill userPointsBill = new AppUserPointsBill();
            userPointsBill.setUserId(userId);
            userPointsBill.setChatRoomId(chatRoomId);
            userPointsBill.setBillType((long) AppPointsBillTypeEnums.TYPE2.getId());// 账单类型为收到礼物
            userPointsBill.setObjectId(backPackList.get(0).getGiveId());// 产生账单的相关id为该礼物发送者用户的id
            userPointsBill.setAmount(totalPrice);// 金额为发送者的金币金额
            userPointsBill.setIsDel(WhetherTypeEnum.NO.getName());
            // appUserPointsBillMapper.insertAppUserPointsBill(userPointsBill);
            appUserGoldBillMapper.insertAppUserGoldBill(userPointsBill);

            // 记录接收用户账单
            AppUserPointsBill toUserPointsBill = new AppUserPointsBill();
            toUserPointsBill.setUserId(toUserId);
            toUserPointsBill.setCreateTime(new Date());
            toUserPointsBill.setBillType((long) AppPointsBillTypeEnums.TYPE13.getId());// 账单类型为收到礼物
            toUserPointsBill.setObjectId(backPackList.get(0).getGiveId());// 产生账单的相关id为该礼物发送者用户的id
            toUserPointsBill.setAmount(price);// 金额为本次获得的收益
            toUserPointsBill.setIsDel(WhetherTypeEnum.NO.getName());
            toUserPointsBill.setGuildId(appGuildMemberMapper.getGuildIdByUserId(toUserId));
            appUserPointsBillMapper.insertAppUserPointsBill(toUserPointsBill);
        }

        BackPackVo gift = backPackList.stream().max(Comparator.comparing(BackPackVo::getMasonryPrice)).get();
        // 礼物的部分字段不推送

        // 获取礼物接收人的用户信息
        AppViewUserInfoVo toUserInfoVo = null;
        String toUserNickName = null;
        // 获取当前用户信息
        AppViewUserInfoVo userInfoVo = null;
        String nickName = null;
        if (chatRoomId != null) {
            setRoomHeatRedis(chatRoomId, totalPrice);

            // appChatRoomMapper.addHeat(chatRoomId, totalPrice.multiply(new
            // BigDecimal("0.1")).longValue());
            privateLetterWebSocket.sendChatRoomRefreshNumberMsg();

            // 添加接收用户收到礼物的金币总数
            // appChatRoomUserMapper.addUserGiftGold(chatRoomId, toUserId, totalPrice);

            // 获取礼物接收人的用户信息
            toUserInfoVo = appCommonService.getUserInfoByUserId(toUserId);
            toUserNickName = StringUtils.isBlank(toUserInfoVo.getNickName()) ? toUserInfoVo.getRecodeCode()
                    : toUserInfoVo.getNickName();

            // 获取当前用户信息
            userInfoVo = appCommonService.getUserInfoByUserId(userId);
            nickName = StringUtils.isBlank(userInfoVo.getNickName()) ? userInfoVo.getRecodeCode()
                    : userInfoVo.getNickName();

            // 推送送礼消息
            AppChatRoomPushWebSocketMsgVo msgResult = new AppChatRoomPushWebSocketMsgVo();
            msgResult.setChatRoomId(chatRoomId);
            msgResult.setTitle(nickName + "送礼给" + toUserNickName);
            msgResult.setContent(nickName + "送礼给" + toUserNickName);
            Map<String, Object> details = new HashMap<>();

            details.put("sendUserId", userId);// 发送用户id
            details.put("sendUserInfo", userInfoVo);// 发送用户详情
            details.put("deliveryUserId", -1);// 送达用户id:-1代表所有
            details.put("toUserId", toUserId);// 接收用户id
            details.put("toUserInfo", toUserInfoVo);// 接收用户详情

            msgResult.setPushType(AppChatRoomWebSocketPushMsgTypeEnums.TYPE167.getId());
            msgResult.setDetails(details);

            details.put("giftInfos", backPackList);
            try {
                log.info("一键送礼物,展示文字:{}", JSONObject.toJSONString(msgResult));
                chatRoomWebSocket.sendMsgByChatRoomId(chatRoomId, msgResult);
            } catch (Exception e) {
            }
            AppSendMsgVo appSendMsgVo = new AppSendMsgVo();
            appSendMsgVo.setMsgType((long) AppSendMsgTypeEnums.TYPE5.getId());
            appSendMsgVo.setReceiveUserId(toUserId);
            msgResult.setPushType(AppChatRoomWebSocketPushMsgTypeEnums.TYPE166.getId());
            // 房间中发送的消息
            for (BackPackVo backPackVo : backPackList) {

                details.put("giftInfo", backPackVo);
                details.put("num", backPackVo.getGiftNum());// 礼物数量

                try {
                    log.info("一键送礼物,展示动效:{}", JSONObject.toJSONString(msgResult));
                    chatRoomWebSocket.sendMsgByChatRoomId(chatRoomId, msgResult);
                } catch (Exception e) {
                }
            }
            // 拼接json
            JSONObject obj = new JSONObject();
            // 创建giftInfo
            AppSendMsg appSendMsg = new AppSendMsg();
            List<AppGifMsgVo> appGifMsgVolList = new ArrayList<>();
            // 构建giftInfoList中的元素
            // 找出金额最高的gift
            BigDecimal maxPrice = BigDecimal.ZERO;
            for (BackPackVo backPackVo : backPackList) {

                AppGifMsgVo appGifMsgVo = new AppGifMsgVo();
                appGifMsgVo.setGiftId(backPackVo.getGiftId());
                appGifMsgVo.setGiftNum(backPackVo.getGiftNum());
                appGifMsgVo.setName(backPackVo.getGiftName());
                appGifMsgVo.setGiftSellPrice(backPackVo.getMasonryPrice());
                appGifMsgVolList.add(appGifMsgVo);
                if (backPackVo.getMasonryPrice().compareTo(maxPrice) > 0) {
                    maxPrice = backPackVo.getMasonryPrice();
                    appSendMsg.setGiftSellPrice(maxPrice);
                    appSendMsg.setName(backPackVo.getGiftName());
                    appSendMsg.setUrl(backPackVo.getImgUrl());
                    appSendMsg.setGiftBili(0.7);
                    appSendMsg.setGiftId(backPackVo.getGiftId());
                    appSendMsg.setSumCount(backPackVo.getGiftNum());
                    appSendMsg.setIsSelectBase(true);
                    appSendMsg.setMsgType((long) AppSendMsgTypeEnums.TYPE9.getId());

                }
            }
            appSendMsg.setGiftInfoList(appGifMsgVolList);
            obj.put("content", "[礼物信息]");
            obj.put("giftInfo", appSendMsg);
            appSendMsgVo.setContent(String.valueOf(obj));
            appSendMsgVo.setMsgType((long) AppSendMsgTypeEnums.TYPE5.getId());
            appSendMsgVo.setReceiveUserId(toUserId);
            appMessageService.sendMsg(user, appSendMsgVo);

        } else {
            toUserInfoVo = appCommonService.getUserInfoByUserId(toUserId);
            toUserNickName = StringUtils.isBlank(toUserInfoVo.getNickName()) ? toUserInfoVo.getRecodeCode()
                    : toUserInfoVo.getNickName();

            // 获取当前用户信息
            userInfoVo = appCommonService.getUserInfoByUserId(userId);
            nickName = StringUtils.isBlank(userInfoVo.getNickName()) ? userInfoVo.getRecodeCode()
                    : userInfoVo.getNickName();

            // 推送送礼消息
            AppChatRoomPushWebSocketMsgVo msgResult = new AppChatRoomPushWebSocketMsgVo();
            msgResult.setChatRoomId(chatRoomId);
            msgResult.setTitle(nickName + "送礼给" + toUserNickName);
            msgResult.setContent(nickName + "送礼给" + toUserNickName);
            Map<String, Object> details = new HashMap<>();

            details.put("sendUserId", userId);// 发送用户id
            details.put("sendUserInfo", userInfoVo);// 发送用户详情
            details.put("deliveryUserId", -1);// 送达用户id:-1代表所有
            details.put("toUserId", toUserId);// 接收用户id
            details.put("toUserInfo", toUserInfoVo);// 接收用户详情

            // details.put("giftInfo", gift);
            // details.put("num", gift.getGiftNum());//礼物数量
            msgResult.setPushType(AppChatRoomWebSocketPushMsgTypeEnums.TYPE167.getId());
            msgResult.setDetails(details);
            AppSendMsgVo appSendMsgVo = new AppSendMsgVo();
            appSendMsgVo.setMsgType((long) AppSendMsgTypeEnums.TYPE5.getId());
            appSendMsgVo.setReceiveUserId(toUserId);
            msgResult.setPushType(AppChatRoomWebSocketPushMsgTypeEnums.TYPE166.getId());
            // 拼接json
            JSONObject obj = new JSONObject();
            // 创建giftInfo
            AppSendMsg appSendMsg = new AppSendMsg();
            List<AppGifMsgVo> appGifMsgVolList = new ArrayList<>();
            // 构建giftInfoList中的元素
            // 找出金额最高的gift
            BigDecimal maxPrice = BigDecimal.ZERO;
            for (BackPackVo backPackVo : backPackList) {

                AppGifMsgVo appGifMsgVo = new AppGifMsgVo();
                appGifMsgVo.setGiftId(backPackVo.getGiftId());
                appGifMsgVo.setGiftNum(backPackVo.getGiftNum());
                appGifMsgVo.setName(backPackVo.getGiftName());
                appGifMsgVo.setGiftSellPrice(backPackVo.getMasonryPrice());
                appGifMsgVolList.add(appGifMsgVo);
                if (backPackVo.getMasonryPrice().compareTo(maxPrice) > 0) {
                    maxPrice = backPackVo.getMasonryPrice();
                    appSendMsg.setGiftSellPrice(maxPrice);
                    appSendMsg.setName(backPackVo.getGiftName());
                    appSendMsg.setUrl(backPackVo.getImgUrl());
                    appSendMsg.setGiftBili(0.7);
                    appSendMsg.setGiftId(backPackVo.getGiftId());
                    appSendMsg.setSumCount(backPackVo.getGiftNum());
                    appSendMsg.setIsSelectBase(true);
                    appSendMsg.setMsgType((long) AppSendMsgTypeEnums.TYPE9.getId());

                }
            }
            appSendMsg.setGiftInfoList(appGifMsgVolList);
            obj.put("content", "[礼物信息]");
            obj.put("giftInfo", appSendMsg);
            appSendMsgVo.setContent(String.valueOf(obj));
            appSendMsgVo.setMsgType((long) AppSendMsgTypeEnums.TYPE5.getId());
            appSendMsgVo.setReceiveUserId(toUserId);
            appMessageService.sendMsg(user, appSendMsgVo);
        }
        // 获取 app 配置
        AppConfig appConfig = appCommonService.getAppConfig();
        if (gift.getMasonryPrice().compareTo(appConfig.getFullServerFloatingScreenPrice()) >= 0) {// 推全服
            if (null == toUserInfoVo) {
                toUserInfoVo = appCommonService.getUserInfoByUserId(toUserId);
                toUserNickName = StringUtils.isBlank(toUserInfoVo.getNickName()) ? toUserInfoVo.getRecodeCode()
                        : toUserInfoVo.getNickName();
            }

            if (null == userInfoVo) {
                // 获取当前用户信息
                userInfoVo = appCommonService.getUserInfoByUserId(userId);
                nickName = StringUtils.isBlank(userInfoVo.getNickName()) ? userInfoVo.getRecodeCode()
                        : userInfoVo.getNickName();
            }

            // 推送送礼消息
            Map<String, Object> pushResult = new HashMap<>();
            pushResult.put("pushType", AppPrivateLetterMsgTypeEnums.TYPE14.getId());
            pushResult.put("title", nickName + "送礼给" + toUserNickName);
            pushResult.put("content", nickName + "送礼给" + toUserNickName);
            pushResult.put("chatRoomId", chatRoomId);
            pushResult.put("chatRoomName", chatRoomName);
            Map<String, Object> details = new HashMap<>();
            details.put("sendUserId", userId);// 发送用户id
            details.put("sendUserInfo", userInfoVo);// 发送用户详情
            details.put("deliveryUserId", -1);// 送达用户id:-1代表所有
            details.put("toUserId", toUserId);// 接收用户id
            // details.put("openGift", openGift);//对方开到的礼物信息,不为空就代表一定是礼物
            details.put("toUserInfo", toUserInfoVo);// 接收用户详情
            details.put("giftInfo", gift);
            details.put("num", gift.getGiftNum());// 礼物数量
            pushResult.put("details", details);

            // 推送私聊消息
            for (BackPackVo backPackVo : backPackList) {
                details.put("giftInfo", backPackVo);
                details.put("num", backPackVo.getGiftNum());// 礼物数量
                try {
                    privateLetterWebSocket.toSendPrivateLetterByAllUser(pushResult);
                } catch (Exception e) {
                }
            }
        }

        Date time = new Date();
        // 升级贡献等级
        appGiftService2.levelUp(userId, totalPrice, time);
        // 升级魅力等级
        appGiftService2.charmlevelUp(toUserId, totalPrice, time);

        return R.ok(backPackList, "一键赠送成功");
    }

    /**
     * 增加房间热度并设置到redis,礼物的10%计入热度
     *
     * @param chatRoomId 聊天室id
     * @param totalPrice 总价格
     */
    public void setRoomHeatRedis(Long chatRoomId, BigDecimal totalPrice) {
        Long roomHeat = totalPrice.multiply(new BigDecimal("10")).longValue();
        HashOperations<String, String, Long> hashOperations = redisTemplate.opsForHash();

        // 获取当前热度值
        Long currentHeat = hashOperations.get(key, key + ":" + chatRoomId);
        if (currentHeat == null) {
            // 如果当前热度不存在，则设为0
            currentHeat = 0L;
        }
        // 计算新的热度值
        Long newHeat = currentHeat + roomHeat;
        hashOperations.put(key, key + ":" + chatRoomId, newHeat);
    }

    /**
     * 金币计算钻石
     *
     * @param price
     * @return
     */
    public BigDecimal calculatePoints(BigDecimal price) {
        // 计算钻石
        AppConfig appConfig = appCommonService.getAppConfig();// 获取app配置信息
        // 计算赠送礼物金币等于多少人民币
        BigDecimal consumptionRmb = price.multiply(appConfig.getOneGoldEqRmb());
        // 计算人民币等于多少钻石
        return consumptionRmb.multiply(appConfig.getOneRmbEqPoints());
    }

    /**
     * 计算金币可以兑换多少钻石,需要根据接收人身份是计算
     *
     * @param price 价格
     * @return {@link BigDecimal }
     */
    public BigDecimal goldCalculatePoints(BigDecimal price, Long toUserId, AppChatRoom chatRoom) {

        // 计算钻石
        AppConfig appConfig = appConfigMapper.getAppConfig();
        BigDecimal giftIncomeScale = appConfig.getGiftIncomeScale();

        // 未实名认证
        AppUserEntity user = appUserMapper.selectAppUserById(toUserId);
        if (user.getIsRealNameAuth() == WhetherTypeEnum.NO.getName()) {
            return price.multiply(giftIncomeScale).divide(appConfig.getOnePointsEqGold());
        }

        // 本月收入金额
        // BigDecimal billSumAmountOfMonth = appUserGoldBillService.getBillSumAmonutOfMonth(toUserId, DateUtil.now());
        // giftIncomeScale = appGiftIncomeConfigService.getGiftIncomeRate(billSumAmountOfMonth, price,
        //         AppIntegralScenarioTypeEnums.TYPE0, toUserId);


        // TODO:接收礼物人的收益比例,需要根据接收人的目前身份进行判断执行不同的收益比例
//        Optional<AppGuildMember> appGuildMember = appGuildMemberService.lambdaQuery()
//                .eq(AppGuildMember::getUserId, toUserId).oneOpt();
//
//
//        if (appGuildMember.isPresent()) {
//            // 加公会,执行公会主播的收益比例
//            giftIncomeScale = appGuildMember.get().getGiftIncomeScale();
//        } else {
//            // 未加入公会,未在房间中,则执行每月礼物收入区间进行判断当前的收益比例,
//            if (ObjectUtil.isNull(chatRoom) || ObjectUtil.isEmpty(chatRoom.getId())) {
//                // 私聊
//                // 本月收入金额
//                BigDecimal billSumAmonutOfMonth = appUserGoldBillService.getBillSumAmonutOfMonth(toUserId, DateUtil.now());
//                // 在哪个比例区间 金币计算钻石
//
//            }
//            // 在房间
//
//        }
        // 计算赠送礼物金币等于多少人民币
        return price.multiply(giftIncomeScale).divide(appConfig.getOnePointsEqGold());
    }

    /**
     * 背包一键赠送
     *
     * @param request
     * @param toUserId
     * @param chatRoomId
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult oneKeyGiveGiftsByBackpack(HttpServletRequest request, Long toUserId, Long chatRoomId) {

        /*
         * if (null == toUserId || toUserId.intValue() < 1) {
         * return AjaxResult.error("参数【toUserId】为空");
         * }
         * AppUserEntity user = getUser(request);
         * Long userId = user.getId();
         *
         * RLock lock = redisson.getLock("app:user:giveGiftsByBackpack:" + userId);
         * if (lock.isLocked()) {
         * return AjaxResult.frequent();
         * }
         * lock.lock(120, TimeUnit.SECONDS);
         *
         *
         * String chatRoomName = "";
         * Long fatherId = null;
         * AppChatRoom chatRoom = null;
         * if (null != chatRoomId) {//聊天室id不为空
         * chatRoom = appChatRoomMapper.selectAppChatRoomById(chatRoomId);
         * if (chatRoom == null) {
         * lock.unlock();
         * return AjaxResult.error("当前聊天室不存在");
         * }
         * if (chatRoom.getStatus().intValue() ==
         * AppChatRoomStatusTypeEnums.TYPE0.getId()) {
         * lock.unlock();
         * return AjaxResult.error("当前聊天室初始化中");
         * }
         * if (chatRoom.getStatus().intValue() ==
         * AppChatRoomStatusTypeEnums.TYPE2.getId()) {
         * lock.unlock();
         * return AjaxResult.error("当前聊天室已结束");
         * }
         * if (!userId.equals(chatRoom.getUserId())) {//当前用户非房主，判断是否进入聊天室
         * AppChatRoomUser chatRoomUser =
         * appChatRoomUserMapper.selectAppChatRoomUserByChatRoomIdAndUserId(chatRoomId,
         * userId);
         * if (null == chatRoomUser || chatRoomUser.getIsJoin().intValue() ==
         * WhetherTypeEnum.NO.getName()) {
         * lock.unlock();
         * return AjaxResult.error("赠送失败,您未进入聊天室");
         * }
         * }
         * chatRoomName = chatRoom.getName();
         * if (chatRoom.getType().intValue() == AppChatRoomTypeEnums.TYPE4.getId()) {
         * fatherId = chatRoom.getFatherId();
         * } else {
         * fatherId = chatRoom.getId();
         * }
         * }
         *
         *
         * AppUserEntity receiveUser = appUserMapper.selectAppUserById(toUserId);
         * if (null == receiveUser) {
         * lock.unlock();
         * return AjaxResult.error("赠送失败,对方账号不存在");
         * }
         *
         * //根据礼物id获取用户背包这个分类的背包列表,不查宝箱分类和背包分类的礼物，只查礼物分类
         * List<Long> notCategoryId = new ArrayList<>();
         * notCategoryId.add(AppGiftCategoryEnums.PARCEL.getId());
         * notCategoryId.add(AppGiftCategoryEnums.BACKPACK.getId());
         *
         * List<AppUserGiftBackpack> giftBackpackList =
         * appUserGiftBackpackMapper.getUserBackPackCategoryGiftBackpackListByGiftId(
         * userId, notCategoryId, null);
         * if (CollectionUtils.isEmpty(giftBackpackList)) {
         * lock.unlock();
         * return AjaxResult.error("礼物数量不足");
         * }
         *
         * BigDecimal price = new BigDecimal("0");
         *
         * Date time = new Date();
         *
         * List<AppGift> gifts = new ArrayList<>();
         * List<Long> giftIds = new ArrayList<>();
         *
         * List<AppOpenGiftVo> giftList = new ArrayList<>();
         *
         * for (AppUserGiftBackpack item : giftBackpackList) {
         * if (!giftIds.contains(item.getGiftId())) {
         * AppGift gift = appGiftMapper.getGiftById(item.getGiftId());
         *
         * AppOpenGiftVo giftVo = new AppOpenGiftVo();
         * giftVo.setGiftId(gift.getId());
         * giftVo.setGiftName(gift.getGiftName());
         * giftVo.setGiftUrl(gift.getImgUrl());
         * giftVo.setNum(item.getSumNum().intValue());
         * giftVo.setGiftPrice(gift.getMasonryPrice());
         * giftList.add(giftVo);
         *
         * gifts.add(gift);
         *
         * giftIds.add(item.getGiftId());
         * price = price.add((gift.getMasonryPrice().multiply(new
         * BigDecimal(item.getSumNum()))));
         * } else {
         * for (AppGift g : gifts) {
         * if (item.getGiftId().equals(g.getId())) {
         * price = price.add((g.getMasonryPrice().multiply(new
         * BigDecimal(item.getSumNum()))));
         *
         * AppOpenGiftVo giftVo = new AppOpenGiftVo();
         * giftVo.setGiftId(g.getId());
         * giftVo.setGiftName(g.getGiftName());
         * giftVo.setGiftUrl(g.getImgUrl());
         * giftVo.setNum(item.getSumNum().intValue());
         * giftVo.setGiftPrice(g.getMasonryPrice());
         * giftList.add(giftVo);
         *
         * gifts.add(g);
         *
         * break;
         * }
         * }
         * }
         * }
         * for (AppUserGiftBackpack item : giftBackpackList) {
         * if (!toUserId.equals(userId)) {
         * //将该礼物记录到接收者礼物背包
         * AppUserGiftBackpack toUserGiftBackpack = new AppUserGiftBackpack();
         * toUserGiftBackpack.setIsDel(WhetherTypeEnum.NO.getName());
         * toUserGiftBackpack.setCreateTime(time);
         * toUserGiftBackpack.setUserId(toUserId);
         * toUserGiftBackpack.setGiftId(item.getGiftId());
         * toUserGiftBackpack.setSumNum(item.getSumNum());
         * toUserGiftBackpack.setSendUserId(userId);
         * toUserGiftBackpack.setSendUserGoldBillId(0L);
         * toUserGiftBackpack.setOldSumNum(item.getSumNum());
         * toUserGiftBackpack.setIsReissue(WhetherTypeEnum.NO.getName());
         * appUserGiftBackpackMapper.insertAppUserGiftBackpack(toUserGiftBackpack);
         * }
         *
         *
         * item.setSumNum(0L);
         * appUserGiftBackpackMapper.updateAppUserGiftBackpack(item);
         * }
         *
         *
         * List<AppOpenGiftVo> giftListNew = new ArrayList<>();
         *
         * Map<Long, List<AppOpenGiftVo>> openGiftListGroup =
         * giftList.stream().collect(Collectors.groupingBy(AppOpenGiftVo::getGiftId));
         * for (Map.Entry<Long, List<AppOpenGiftVo>> entry :
         * openGiftListGroup.entrySet()) {
         * AppOpenGiftVo giftVo = new AppOpenGiftVo();
         * giftVo.setGiftId(entry.getKey());
         * giftVo.setNum(0);
         *
         * for (AppOpenGiftVo item : giftList) {
         * if (item.getGiftId().equals(giftVo.getGiftId())) {
         * giftVo.setGiftName(item.getGiftName());
         * giftVo.setGiftPrice(item.getGiftPrice());
         * giftVo.setGiftUrl(item.getGiftUrl());
         * giftVo.setGiftPrice(item.getGiftPrice());
         * break;
         * }
         * }
         * for (AppOpenGiftVo item : giftList) {
         * if (item.getGiftId().equals(giftVo.getGiftId())) {
         * giftVo.setNum(giftVo.getNum() + (item.getNum()));
         * }
         * }
         * giftListNew.add(giftVo);
         * }
         *
         *
         * AppConfig appConfig = appCommonService.getAppConfig();//获取app配置信息
         *
         * //计算本次消耗的金币等于多少人民币
         * BigDecimal consumptionRmb = price.multiply(appConfig.getOneGoldEqRmb());
         * //计算本次收入的人民币
         * BigDecimal earningsRmb =
         * consumptionRmb.multiply(appConfig.getGiftIncomeScale());
         * //计算本次收入的钻石
         * BigDecimal earningsPoints =
         * earningsRmb.multiply(appConfig.getOneRmbEqPoints());
         * if (earningsPoints.compareTo(new BigDecimal("0")) > 0) {
         * //添加接收人钻石余额
         * appUserMapper.addUserPointsBalance(toUserId, earningsPoints);
         * //记录接收用户账单
         * AppUserPointsBill toUserPointsBill = new AppUserPointsBill();
         * toUserPointsBill.setUserId(toUserId);
         * toUserPointsBill.setCreateTime(time);
         * toUserPointsBill.setBillType((long)
         * AppPointsBillTypeEnums.TYPE5.getId());//账单类型为收到礼物
         * toUserPointsBill.setObjectId(userId);//产生账单的相关id为该礼物发送者用户的id
         * toUserPointsBill.setAmount(earningsPoints);//金额为本次获得的收益
         * toUserPointsBill.setIsDel(WhetherTypeEnum.NO.getName());
         * appUserPointsBillMapper.insertAppUserPointsBill(toUserPointsBill);
         *
         * AppUserEntity receiveRecodeUser = null;
         * if (null != receiveUser.getRecodeUserId() &&
         * !receiveUser.getRecodeUserId().equals(0L)) {//判断接收人有没有推荐人
         * receiveRecodeUser =
         * appUserMapper.selectAppUserById(receiveUser.getRecodeUserId());
         * }
         * if (null != receiveRecodeUser) {//如果礼物接收人有推荐人，那就给推荐人分佣
         * //推荐人的收益在接收用户得到的钻石的基础上去抽
         * BigDecimal recodeUserEarningsPoints =
         * earningsPoints.multiply(appConfig.getInviteGiveGiftScale());
         * if (recodeUserEarningsPoints.compareTo(new BigDecimal("0")) > 0) {
         * appUserMapper.addUserPointsBalance(receiveRecodeUser.getId(),
         * recodeUserEarningsPoints);
         * AppUserPointsBill recodeUserPointsBill = new AppUserPointsBill();
         * recodeUserPointsBill.setUserId(receiveRecodeUser.getId());
         * recodeUserPointsBill.setCreateTime(time);
         * recodeUserPointsBill.setBillType((long)
         * AppPointsBillTypeEnums.TYPE13.getId());
         * recodeUserPointsBill.setObjectId(toUserId);
         * recodeUserPointsBill.setAmount(recodeUserEarningsPoints);
         * recodeUserPointsBill.setIsDel(WhetherTypeEnum.NO.getName());
         * appUserPointsBillMapper.insertAppUserPointsBill(recodeUserPointsBill);
         * }
         * }
         * }
         *
         * if (null != chatRoom) {
         *
         * if (chatRoom.getType().intValue() == AppChatRoomTypeEnums.TYPE4.getId())
         * {//车队房间就查他的父厅
         *
         * AppChatRoom chatRoomFather =
         * appChatRoomMapper.selectAppChatRoomById(chatRoom.getFatherId());
         *
         * if (null != chatRoomFather && null != chatRoomFather.getHallOwnerUserId() &&
         * !chatRoomFather.getHallOwnerUserId().equals(0L)) {//如果房间绑定了厅主
         * //计算本次厅主收入的钻石
         * BigDecimal hallOwnerEarningsPoints = new BigDecimal("0");
         * try {
         * hallOwnerEarningsPoints =
         * consumptionRmb.multiply(appConfig.getOneRmbEqPoints()).multiply(appConfig.
         * getHallOwnerCommissionScale()).setScale(2, BigDecimal.ROUND_DOWN);
         * } catch (Exception e) {
         *
         * }
         * if (hallOwnerEarningsPoints.compareTo(new BigDecimal("0")) > 0)
         * {//如果本次公会收入钻石大于0
         * //增加厅主收益记录
         * AppHallOwnerEarningsRecords appHallOwnerEarningsRecords = new
         * AppHallOwnerEarningsRecords();
         * appHallOwnerEarningsRecords.setUserId(chatRoomFather.getHallOwnerUserId());
         * appHallOwnerEarningsRecords.setAmount(hallOwnerEarningsPoints);
         * appHallOwnerEarningsRecords.setCreateTime(time);
         * appHallOwnerEarningsRecords.setTriggerObecjtId(userId);
         * appHallOwnerEarningsRecords.setReceiveObjectId(toUserId);
         * appHallOwnerEarningsRecordsMapper.insertAppHallOwnerEarningsRecords(
         * appHallOwnerEarningsRecords);
         * //添加厅主待结算金额
         * //appUserMapper.addToBeSettled(chatRoomFather.getHallOwnerUserId(),
         * hallOwnerEarningsPoints);
         * }
         *
         * }
         *
         * if (null != chatRoomFather.getGuildId() &&
         * !chatRoomFather.getGuildId().equals(0L)) {//如果房间绑定了公会
         * //获取公会信息
         * AppGuild guild =
         * appGuildMapper.getGuildInfoById(chatRoomFather.getGuildId());
         * if (null != guild) {//如果公会存在
         * //判断礼物接收人是否已加入了该公会
         * if (guild.getUserId().equals(toUserId) || null !=
         * appGuildMemberMapper.getGuildMemberByUserIdAndGuildId(guild.getId(),
         * toUserId)) {
         * //计算本次公会收入的钻石
         * BigDecimal guildEarningsPoints = new BigDecimal("0");
         * try {
         * guildEarningsPoints =
         * consumptionRmb.multiply(appConfig.getOneRmbEqPoints()).multiply(appConfig.
         * getGuildCommissionScale()).setScale(2, BigDecimal.ROUND_DOWN);
         * } catch (Exception e) {
         *
         * }
         * if (guildEarningsPoints.compareTo(new BigDecimal("0")) > 0) {//如果本次公会收入钻石大于0
         * //增加公会收益记录
         * AppGuildEarningsRecords appGuildEarningsRecords = new
         * AppGuildEarningsRecords();
         * appGuildEarningsRecords.setGuildId(guild.getId());
         * appGuildEarningsRecords.setAmount(guildEarningsPoints);
         * appGuildEarningsRecords.setCreateTime(time);
         * appGuildEarningsRecords.setTriggerObecjtId(userId);
         * appGuildEarningsRecords.setReceiveObjectId(toUserId);
         * appGuildEarningsRecordsMapper.insertAppGuildEarningsRecords(
         * appGuildEarningsRecords);
         * //添加公会待结算金额
         * appGuildMapper.addToBeSettled(guild.getId(), guildEarningsPoints);
         *
         * }
         *
         * }
         *
         *
         * }
         *
         * }
         *
         *
         * } else {//非车队房间
         *
         *
         * if (null != chatRoom.getHallOwnerUserId() &&
         * !chatRoom.getHallOwnerUserId().equals(0L)) {//如果房间绑定了厅主
         * //计算本次厅主收入的钻石
         * BigDecimal hallOwnerEarningsPoints = new BigDecimal("0");
         * try {
         * hallOwnerEarningsPoints =
         * consumptionRmb.multiply(appConfig.getOneRmbEqPoints()).multiply(appConfig.
         * getHallOwnerCommissionScale()).setScale(2, BigDecimal.ROUND_DOWN);
         * } catch (Exception e) {
         *
         * }
         * if (hallOwnerEarningsPoints.compareTo(new BigDecimal("0")) > 0)
         * {//如果本次公会收入钻石大于0
         * //增加厅主收益记录
         * AppHallOwnerEarningsRecords appHallOwnerEarningsRecords = new
         * AppHallOwnerEarningsRecords();
         * appHallOwnerEarningsRecords.setUserId(chatRoom.getHallOwnerUserId());
         * appHallOwnerEarningsRecords.setAmount(hallOwnerEarningsPoints);
         * appHallOwnerEarningsRecords.setCreateTime(time);
         * appHallOwnerEarningsRecords.setTriggerObecjtId(userId);
         * appHallOwnerEarningsRecords.setReceiveObjectId(toUserId);
         * appHallOwnerEarningsRecordsMapper.insertAppHallOwnerEarningsRecords(
         * appHallOwnerEarningsRecords);
         * //添加厅主待结算金额
         * //appUserMapper.addToBeSettled(chatRoom.getHallOwnerUserId(),
         * hallOwnerEarningsPoints);
         * }
         *
         * }
         *
         * if (null != chatRoom.getGuildId() && !chatRoom.getGuildId().equals(0L))
         * {//如果房间绑定了公会
         * //获取公会信息
         * AppGuild guild = appGuildMapper.getGuildInfoById(chatRoom.getGuildId());
         * if (null != guild) {//如果公会存在
         * //判断礼物接收人是否已加入了该公会
         * if (guild.getUserId().equals(toUserId) || null !=
         * appGuildMemberMapper.getGuildMemberByUserIdAndGuildId(guild.getId(),
         * toUserId)) {
         * //计算本次公会收入的钻石
         * BigDecimal guildEarningsPoints = new BigDecimal("0");
         * try {
         * guildEarningsPoints =
         * consumptionRmb.multiply(appConfig.getOneRmbEqPoints()).multiply(appConfig.
         * getGuildCommissionScale()).setScale(2, BigDecimal.ROUND_DOWN);
         * } catch (Exception e) {
         *
         * }
         * if (guildEarningsPoints.compareTo(new BigDecimal("0")) > 0) {//如果本次公会收入钻石大于0
         * //增加公会收益记录
         * AppGuildEarningsRecords appGuildEarningsRecords = new
         * AppGuildEarningsRecords();
         * appGuildEarningsRecords.setGuildId(guild.getId());
         * appGuildEarningsRecords.setAmount(guildEarningsPoints);
         * appGuildEarningsRecords.setCreateTime(time);
         * appGuildEarningsRecords.setTriggerObecjtId(userId);
         * appGuildEarningsRecords.setReceiveObjectId(toUserId);
         * appGuildEarningsRecordsMapper.insertAppGuildEarningsRecords(
         * appGuildEarningsRecords);
         * //添加公会待结算金额
         * appGuildMapper.addToBeSettled(guild.getId(), guildEarningsPoints);
         *
         * }
         *
         * }
         *
         *
         * }
         *
         * }
         *
         *
         * }
         * }
         *
         *
         * //获取接收者的魅力等级
         * AppUserCharmGrade userCharmGrade =
         * appUserCharmGradeMapper.selectAppUserCharmGradeByUserId(toUserId);
         * if (null == userCharmGrade) {//不存在就新增
         * //获取最小魅力等级配置
         * AppCharmGradeConfig charmGradeConfig =
         * appCharmGradeConfigMapper.getMinCharmGradeConfig();
         * if (null != charmGradeConfig) {
         * userCharmGrade = new AppUserCharmGrade();
         * userCharmGrade.setUserId(toUserId);
         * userCharmGrade.setCurrentGradeId(charmGradeConfig.getId());
         * userCharmGrade.setSumGoldValue(new BigDecimal("0"));
         * userCharmGrade.setCreateTime(time);
         * appUserCharmGradeMapper.insertAppUserCharmGrade(userCharmGrade);
         * }
         * }
         * if (null != userCharmGrade) {
         * AppCharmGradeConfig charmGradeConfig =
         * appCharmGradeConfigMapper.selectAppCharmGradeConfigById(userCharmGrade.
         * getCurrentGradeId());
         * if (null != charmGradeConfig) {
         * AppCharmGradeConfig nextCharmGradeConfig =
         * appCharmGradeConfigMapper.getNextCharmGradeConfig(charmGradeConfig.
         * getGradeSize());
         * AppUserCharmGrade userCharmGradeUpd = new AppUserCharmGrade();
         * userCharmGradeUpd.setId(userCharmGrade.getId());
         * BigDecimal goldValue = price.divide(new BigDecimal("10"), 2,
         * RoundingMode.HALF_UP);
         * userCharmGradeUpd.setSumGoldValue(userCharmGrade.getSumGoldValue().add(
         * goldValue));
         * userCharmGradeUpd.setUpdateTime(time);
         * if (null != nextCharmGradeConfig &&
         * userCharmGradeUpd.getSumGoldValue().compareTo(nextCharmGradeConfig.
         * getGoldValue()) >= 0) {//如果当前等级总值大于等于下一级所需要的，那就升级
         * userCharmGradeUpd.setCurrentGradeId(nextCharmGradeConfig.getId());
         * }
         * appUserCharmGradeMapper.updateAppUserCharmGrade(userCharmGradeUpd);
         * }
         * }
         *
         *
         * AppUserGoldBill userGoldBill = new AppUserGoldBill();
         * userGoldBill.setUserId(userId);
         * userGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE21.getId());
         * userGoldBill.setObjectId(toUserId);
         * userGoldBill.setAmount(new BigDecimal("0"));
         * userGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
         * if (null == fatherId || fatherId.intValue() < 1) {
         * userGoldBill.setChatRoomId(0L);
         * } else {
         * userGoldBill.setChatRoomId(fatherId);
         * }
         * userGoldBill.setUserContributionValue(price);
         * userGoldBill.setToUserCharmValue(price);
         * appUserGoldBillMapper.insertAppUserGoldBill(userGoldBill);
         *
         *
         * //获取礼物接收人的用户信息
         * AppViewUserInfoVo toUserInfoVo = null;
         * String toUserNickName = null;
         * //获取当前用户信息
         * AppViewUserInfoVo userInfoVo = null;
         * String nickName = null;
         *
         *
         * if (null != chatRoomId) {//聊天室id不为空就去推送消息
         *
         * appChatRoomMapper.addHeat(chatRoomId, price.multiply(new
         * BigDecimal("0.1")).longValue());
         * privateLetterWebSocket.sendChatRoomRefreshNumberMsg();
         *
         * //添加接收用户收到礼物的金币总数
         * appChatRoomUserMapper.addUserGiftGold(chatRoomId, toUserId, price);
         *
         * toUserInfoVo = appCommonService.getUserInfoByUserId(toUserId);
         * toUserNickName = StringUtils.isBlank(toUserInfoVo.getNickName()) ?
         * toUserInfoVo.getRecodeCode() : toUserInfoVo.getNickName();
         * //获取当前用户信息
         * userInfoVo = appCommonService.getUserInfoByUserId(userId);
         * nickName = StringUtils.isBlank(userInfoVo.getNickName()) ?
         * userInfoVo.getRecodeCode() : userInfoVo.getNickName();
         *
         *
         * //推送送礼消息
         * AppChatRoomPushWebSocketMsgVo msgResult = new
         * AppChatRoomPushWebSocketMsgVo();
         * msgResult.setChatRoomId(chatRoomId);
         * msgResult.setPushType(AppChatRoomWebSocketPushMsgTypeEnums.TYPE166.getId());
         * msgResult.setTitle(nickName + "送礼给" + toUserNickName);
         * msgResult.setContent(nickName + "送礼给" + toUserNickName);
         * Map<String, Object> details = new HashMap<>();
         *
         *
         * details.put("sendUserId", userId);//发送用户id
         * details.put("sendUserInfo", userInfoVo);//发送用户详情
         * details.put("deliveryUserId", -1);//送达用户id:-1代表所有
         * details.put("toUserId", toUserId);//接收用户id
         * details.put("toUserInfo", toUserInfoVo);//接收用户详情
         *
         * details.put("giftInfoList", giftListNew);
         * msgResult.setDetails(details);
         * try {
         * chatRoomWebSocket.sendMsgByChatRoomId(chatRoomId, msgResult);
         * } catch (Exception e) {
         * }
         * }
         *
         *
         * //将入参返回给前端
         * Map<String, Object> result = new HashMap<>();
         * result.put("toUserId", toUserId);
         * result.put("giftInfoList", giftListNew);
         *
         * lock.unlock();
         * return AjaxResult.success("赠送成功", result);
         */
        return null;
    }

    /**
     * 送礼给用户(仅背包分类)
     *
     * @param request
     * @param toUserId
     * @param giftId
     * @param num
     * @param chatRoomId
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult giveGiftsByBackpack(HttpServletRequest request, Long toUserId, Long giftId, Integer num,
                                          Long chatRoomId) {
        /*
         * if (null == toUserId || toUserId.intValue() < 1) {
         * return AjaxResult.error("参数【toUserId】为空");
         * }
         * AppUserEntity user = getUser(request);
         * Long userId = user.getId();
         *
         * //操作加锁
         * RLock lock = redisson.getLock("app:user:giveGiftsByBackpack:" + userId);
         * if (lock.isLocked()) {
         * return AjaxResult.frequent();
         * }
         * lock.lock(120, TimeUnit.SECONDS);
         * if (null == num || num.intValue() < 1) {
         * num = 1;
         * }
         *
         *
         * String chatRoomName = "";
         * Long fatherId = null;
         * AppChatRoom chatRoom = null;
         * if (null != chatRoomId) {//聊天室id不为空
         * chatRoom = appChatRoomMapper.selectAppChatRoomById(chatRoomId);
         * if (chatRoom == null) {
         * lock.unlock();
         * return AjaxResult.error("当前聊天室不存在");
         * }
         * if (chatRoom.getStatus().intValue() ==
         * AppChatRoomStatusTypeEnums.TYPE0.getId()) {
         * lock.unlock();
         * return AjaxResult.error("当前聊天室初始化中");
         * }
         * if (chatRoom.getStatus().intValue() ==
         * AppChatRoomStatusTypeEnums.TYPE2.getId()) {
         * lock.unlock();
         * return AjaxResult.error("当前聊天室已结束");
         * }
         * if (!userId.equals(chatRoom.getUserId())) {//当前用户非房主，判断是否进入聊天室
         * AppChatRoomUser chatRoomUser =
         * appChatRoomUserMapper.selectAppChatRoomUserByChatRoomIdAndUserId(chatRoomId,
         * userId);
         * if (null == chatRoomUser || chatRoomUser.getIsJoin().intValue() ==
         * WhetherTypeEnum.NO.getName()) {
         * lock.unlock();
         * return AjaxResult.error("赠送失败,您未进入聊天室");
         * }
         * }
         * chatRoomName = chatRoom.getName();
         * if (chatRoom.getType().intValue() == AppChatRoomTypeEnums.TYPE4.getId()) {
         * fatherId = chatRoom.getFatherId();
         * } else {
         * fatherId = chatRoom.getId();
         * }
         * }
         *
         *
         * AppUserEntity receiveUser = appUserMapper.selectAppUserById(toUserId);
         * if (null == receiveUser) {
         * lock.unlock();
         * return AjaxResult.error("赠送失败,对方账号不存在");
         * }
         *
         *
         * //根据礼物id获取用户背包这个分类的背包列表,不查宝箱分类和背包分类的礼物，只查礼物分类
         * List<Long> notCategoryId = new ArrayList<>();
         * notCategoryId.add(AppGiftCategoryEnums.PARCEL.getId());
         * notCategoryId.add(AppGiftCategoryEnums.BACKPACK.getId());
         *
         * List<AppUserGiftBackpack> giftBackpackList =
         * appUserGiftBackpackMapper.getUserBackPackCategoryGiftBackpackListByGiftId(
         * userId, notCategoryId, giftId);
         * if (CollectionUtils.isEmpty(giftBackpackList)) {
         * lock.unlock();
         * return AjaxResult.error("礼物数量不足");
         * }
         * Integer sumNum = 0;
         * for (AppUserGiftBackpack item : giftBackpackList) {
         * sumNum = sumNum + item.getSumNum().intValue();
         * }
         * if (sumNum.intValue() < num.intValue()) {
         * lock.unlock();
         * return AjaxResult.error("礼物数量不足");
         * }
         *
         * AppGift gift = appGiftMapper.getGiftById(giftId);
         * if (null == gift) {
         * lock.unlock();
         * return AjaxResult.error("礼物配置数据不存在");
         * }
         *
         * BigDecimal singleGiftPrice = gift.getMasonryPrice();//单个礼物价格
         *
         * Date time = new Date();
         *
         * BigDecimal price = gift.getMasonryPrice().multiply(new
         * BigDecimal(num));//本次价格等于：礼物价格乘以数量
         *
         * //扣除用户该礼物背包的数量
         * Integer sumSbtract = num;//总的要扣除数量
         * for (AppUserGiftBackpack item : giftBackpackList) {
         * if (sumSbtract > 0) {
         * if (item.getSumNum().intValue() >= sumSbtract) {
         * item.setSumNum(item.getSumNum() - sumSbtract);
         * sumSbtract = 0;
         * } else {
         * sumSbtract = sumSbtract - item.getSumNum().intValue();
         * item.setSumNum(0L);
         * }
         * }
         * }
         * if (sumSbtract > 0) {
         * lock.unlock();
         * throw new AppException("礼物数量不足");
         * }
         *
         * for (AppUserGiftBackpack item : giftBackpackList) {
         * appUserGiftBackpackMapper.updateAppUserGiftBackpack(item);
         * }
         *
         *
         * AppConfig appConfig = appCommonService.getAppConfig();//获取app配置信息
         *
         * //计算本次消耗的金币等于多少人民币
         * BigDecimal consumptionRmb = price.multiply(appConfig.getOneGoldEqRmb());
         * //计算本次收入的人民币
         * BigDecimal earningsRmb =
         * consumptionRmb.multiply(appConfig.getGiftIncomeScale());
         * //计算本次收入的钻石
         * BigDecimal earningsPoints =
         * earningsRmb.multiply(appConfig.getOneRmbEqPoints());
         * if (earningsPoints.compareTo(new BigDecimal("0")) > 0) {
         * //添加接收人钻石余额
         * appUserMapper.addUserPointsBalance(toUserId, earningsPoints);
         * //记录接收用户账单
         * AppUserPointsBill toUserPointsBill = new AppUserPointsBill();
         * toUserPointsBill.setUserId(toUserId);
         * toUserPointsBill.setCreateTime(time);
         * toUserPointsBill.setBillType((long)
         * AppPointsBillTypeEnums.TYPE5.getId());//账单类型为收到礼物
         * toUserPointsBill.setObjectId(userId);//产生账单的相关id为该礼物发送者用户的id
         * toUserPointsBill.setAmount(earningsPoints);//金额为本次获得的收益
         * toUserPointsBill.setIsDel(WhetherTypeEnum.NO.getName());
         * appUserPointsBillMapper.insertAppUserPointsBill(toUserPointsBill);
         *
         * AppUserEntity receiveRecodeUser = null;
         * if (null != receiveUser.getRecodeUserId() &&
         * !receiveUser.getRecodeUserId().equals(0L)) {//判断接收人有没有推荐人
         * receiveRecodeUser =
         * appUserMapper.selectAppUserById(receiveUser.getRecodeUserId());
         * }
         * if (null != receiveRecodeUser) {//如果礼物接收人有推荐人，那就给推荐人分佣
         * //推荐人的收益在接收用户得到的钻石的基础上去抽
         * BigDecimal recodeUserEarningsPoints =
         * earningsPoints.multiply(appConfig.getInviteGiveGiftScale());
         * if (recodeUserEarningsPoints.compareTo(new BigDecimal("0")) > 0) {
         * appUserMapper.addUserPointsBalance(receiveRecodeUser.getId(),
         * recodeUserEarningsPoints);
         * AppUserPointsBill recodeUserPointsBill = new AppUserPointsBill();
         * recodeUserPointsBill.setUserId(receiveRecodeUser.getId());
         * recodeUserPointsBill.setCreateTime(time);
         * recodeUserPointsBill.setBillType((long)
         * AppPointsBillTypeEnums.TYPE13.getId());
         * recodeUserPointsBill.setObjectId(toUserId);
         * recodeUserPointsBill.setAmount(recodeUserEarningsPoints);
         * recodeUserPointsBill.setIsDel(WhetherTypeEnum.NO.getName());
         * appUserPointsBillMapper.insertAppUserPointsBill(recodeUserPointsBill);
         * }
         * }
         * }
         *
         * if (null != chatRoom) {
         *
         * if (chatRoom.getType().intValue() == AppChatRoomTypeEnums.TYPE4.getId())
         * {//车队房间就查他的父厅
         *
         * AppChatRoom chatRoomFather =
         * appChatRoomMapper.selectAppChatRoomById(chatRoom.getFatherId());
         *
         * if (null != chatRoomFather && null != chatRoomFather.getHallOwnerUserId() &&
         * !chatRoomFather.getHallOwnerUserId().equals(0L)) {//如果房间绑定了厅主
         * //计算本次厅主收入的钻石
         * BigDecimal hallOwnerEarningsPoints = new BigDecimal("0");
         * try {
         * hallOwnerEarningsPoints =
         * consumptionRmb.multiply(appConfig.getOneRmbEqPoints()).multiply(appConfig.
         * getHallOwnerCommissionScale()).setScale(2, BigDecimal.ROUND_DOWN);
         * } catch (Exception e) {
         *
         * }
         * if (hallOwnerEarningsPoints.compareTo(new BigDecimal("0")) > 0)
         * {//如果本次公会收入钻石大于0
         * //增加厅主收益记录
         * AppHallOwnerEarningsRecords appHallOwnerEarningsRecords = new
         * AppHallOwnerEarningsRecords();
         * appHallOwnerEarningsRecords.setUserId(chatRoomFather.getHallOwnerUserId());
         * appHallOwnerEarningsRecords.setAmount(hallOwnerEarningsPoints);
         * appHallOwnerEarningsRecords.setCreateTime(time);
         * appHallOwnerEarningsRecords.setTriggerObecjtId(userId);
         * appHallOwnerEarningsRecords.setReceiveObjectId(toUserId);
         * appHallOwnerEarningsRecordsMapper.insertAppHallOwnerEarningsRecords(
         * appHallOwnerEarningsRecords);
         * //添加厅主待结算金额
         * //appUserMapper.addToBeSettled(chatRoomFather.getHallOwnerUserId(),
         * hallOwnerEarningsPoints);
         * }
         *
         * }
         *
         * if (null != chatRoomFather.getGuildId() &&
         * !chatRoomFather.getGuildId().equals(0L)) {//如果房间绑定了公会
         * //获取公会信息
         * AppGuild guild =
         * appGuildMapper.getGuildInfoById(chatRoomFather.getGuildId());
         * if (null != guild) {//如果公会存在
         * //判断礼物接收人是否已加入了该公会
         * if (guild.getUserId().equals(toUserId) || null !=
         * appGuildMemberMapper.getGuildMemberByUserIdAndGuildId(guild.getId(),
         * toUserId)) {
         * //计算本次公会收入的钻石
         * BigDecimal guildEarningsPoints = new BigDecimal("0");
         * try {
         * guildEarningsPoints =
         * consumptionRmb.multiply(appConfig.getOneRmbEqPoints()).multiply(appConfig.
         * getGuildCommissionScale()).setScale(2, BigDecimal.ROUND_DOWN);
         * } catch (Exception e) {
         *
         * }
         * if (guildEarningsPoints.compareTo(new BigDecimal("0")) > 0) {//如果本次公会收入钻石大于0
         * //增加公会收益记录
         * AppGuildEarningsRecords appGuildEarningsRecords = new
         * AppGuildEarningsRecords();
         * appGuildEarningsRecords.setGuildId(guild.getId());
         * appGuildEarningsRecords.setAmount(guildEarningsPoints);
         * appGuildEarningsRecords.setCreateTime(time);
         * appGuildEarningsRecords.setTriggerObecjtId(userId);
         * appGuildEarningsRecords.setReceiveObjectId(toUserId);
         * appGuildEarningsRecordsMapper.insertAppGuildEarningsRecords(
         * appGuildEarningsRecords);
         * //添加公会待结算金额
         * appGuildMapper.addToBeSettled(guild.getId(), guildEarningsPoints);
         *
         * }
         *
         * }
         *
         *
         * }
         *
         * }
         *
         *
         * } else {//非车队房间
         *
         *
         * if (null != chatRoom.getHallOwnerUserId() &&
         * !chatRoom.getHallOwnerUserId().equals(0L)) {//如果房间绑定了厅主
         * //计算本次厅主收入的钻石
         * BigDecimal hallOwnerEarningsPoints = new BigDecimal("0");
         * try {
         * hallOwnerEarningsPoints =
         * consumptionRmb.multiply(appConfig.getOneRmbEqPoints()).multiply(appConfig.
         * getHallOwnerCommissionScale()).setScale(2, BigDecimal.ROUND_DOWN);
         * } catch (Exception e) {
         *
         * }
         * if (hallOwnerEarningsPoints.compareTo(new BigDecimal("0")) > 0)
         * {//如果本次公会收入钻石大于0
         * //增加厅主收益记录
         * AppHallOwnerEarningsRecords appHallOwnerEarningsRecords = new
         * AppHallOwnerEarningsRecords();
         * appHallOwnerEarningsRecords.setUserId(chatRoom.getHallOwnerUserId());
         * appHallOwnerEarningsRecords.setAmount(hallOwnerEarningsPoints);
         * appHallOwnerEarningsRecords.setCreateTime(time);
         * appHallOwnerEarningsRecords.setTriggerObecjtId(userId);
         * appHallOwnerEarningsRecords.setReceiveObjectId(toUserId);
         * appHallOwnerEarningsRecordsMapper.insertAppHallOwnerEarningsRecords(
         * appHallOwnerEarningsRecords);
         * //添加厅主待结算金额
         * //appUserMapper.addToBeSettled(chatRoom.getHallOwnerUserId(),
         * hallOwnerEarningsPoints);
         * }
         *
         * }
         *
         * if (null != chatRoom.getGuildId() && !chatRoom.getGuildId().equals(0L))
         * {//如果房间绑定了公会
         * //获取公会信息
         * AppGuild guild = appGuildMapper.getGuildInfoById(chatRoom.getGuildId());
         * if (null != guild) {//如果公会存在
         * //判断礼物接收人是否已加入了该公会
         * if (guild.getUserId().equals(toUserId) || null !=
         * appGuildMemberMapper.getGuildMemberByUserIdAndGuildId(guild.getId(),
         * toUserId)) {
         * //计算本次公会收入的钻石
         * BigDecimal guildEarningsPoints = new BigDecimal("0");
         * try {
         * guildEarningsPoints =
         * consumptionRmb.multiply(appConfig.getOneRmbEqPoints()).multiply(appConfig.
         * getGuildCommissionScale()).setScale(2, BigDecimal.ROUND_DOWN);
         * } catch (Exception e) {
         *
         * }
         * if (guildEarningsPoints.compareTo(new BigDecimal("0")) > 0) {//如果本次公会收入钻石大于0
         * //增加公会收益记录
         * AppGuildEarningsRecords appGuildEarningsRecords = new
         * AppGuildEarningsRecords();
         * appGuildEarningsRecords.setGuildId(guild.getId());
         * appGuildEarningsRecords.setAmount(guildEarningsPoints);
         * appGuildEarningsRecords.setCreateTime(time);
         * appGuildEarningsRecords.setTriggerObecjtId(userId);
         * appGuildEarningsRecords.setReceiveObjectId(toUserId);
         * appGuildEarningsRecordsMapper.insertAppGuildEarningsRecords(
         * appGuildEarningsRecords);
         * //添加公会待结算金额
         * appGuildMapper.addToBeSettled(guild.getId(), guildEarningsPoints);
         *
         * }
         *
         * }
         *
         *
         * }
         *
         * }
         *
         *
         * }
         * }
         *
         *
         * if (!userId.equals(toUserId)) {
         * //将该礼物记录到接收者礼物背包
         * AppUserGiftBackpack toUserGiftBackpack = new AppUserGiftBackpack();
         * toUserGiftBackpack.setIsDel(WhetherTypeEnum.NO.getName());
         * toUserGiftBackpack.setCreateTime(time);
         * toUserGiftBackpack.setUserId(toUserId);
         * toUserGiftBackpack.setGiftId(giftId);
         * toUserGiftBackpack.setSumNum((long) num);
         * toUserGiftBackpack.setSendUserId(userId);
         * toUserGiftBackpack.setSendUserGoldBillId(0L);
         * toUserGiftBackpack.setOldSumNum((long) num);
         * toUserGiftBackpack.setIsReissue(WhetherTypeEnum.NO.getName());
         * appUserGiftBackpackMapper.insertAppUserGiftBackpack(toUserGiftBackpack);
         * }
         *
         *
         * //获取接收者的魅力等级
         * AppUserCharmGrade userCharmGrade =
         * appUserCharmGradeMapper.selectAppUserCharmGradeByUserId(toUserId);
         * if (null == userCharmGrade) {//不存在就新增
         * //获取最小魅力等级配置
         * AppCharmGradeConfig charmGradeConfig =
         * appCharmGradeConfigMapper.getMinCharmGradeConfig();
         * if (null != charmGradeConfig) {
         * userCharmGrade = new AppUserCharmGrade();
         * userCharmGrade.setUserId(toUserId);
         * userCharmGrade.setCurrentGradeId(charmGradeConfig.getId());
         * userCharmGrade.setSumGoldValue(new BigDecimal("0"));
         * userCharmGrade.setCreateTime(time);
         * appUserCharmGradeMapper.insertAppUserCharmGrade(userCharmGrade);
         * }
         * }
         * if (null != userCharmGrade) {
         * AppCharmGradeConfig charmGradeConfig =
         * appCharmGradeConfigMapper.selectAppCharmGradeConfigById(userCharmGrade.
         * getCurrentGradeId());
         * if (null != charmGradeConfig) {
         * AppCharmGradeConfig nextCharmGradeConfig =
         * appCharmGradeConfigMapper.getNextCharmGradeConfig(charmGradeConfig.
         * getGradeSize());
         * AppUserCharmGrade userCharmGradeUpd = new AppUserCharmGrade();
         * userCharmGradeUpd.setId(userCharmGrade.getId());
         * BigDecimal goldValue = price.divide(new BigDecimal("10"), 2,
         * RoundingMode.HALF_UP);
         * userCharmGradeUpd.setSumGoldValue(userCharmGrade.getSumGoldValue().add(
         * goldValue));
         * userCharmGradeUpd.setUpdateTime(time);
         * if (null != nextCharmGradeConfig &&
         * userCharmGradeUpd.getSumGoldValue().compareTo(nextCharmGradeConfig.
         * getGoldValue()) >= 0) {//如果当前等级总值大于等于下一级所需要的，那就升级
         * userCharmGradeUpd.setCurrentGradeId(nextCharmGradeConfig.getId());
         * }
         * appUserCharmGradeMapper.updateAppUserCharmGrade(userCharmGradeUpd);
         * }
         * }
         *
         * //礼物的部分字段不推送
         * gift.setCreateTime(null);
         * gift.setUpdateTime(null);
         * gift.setCreateBy(null);
         * gift.setUpdateBy(null);
         * gift.setSales(null);
         *
         * AppUserGoldBill userGoldBill = new AppUserGoldBill();
         * userGoldBill.setUserId(userId);
         * userGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE21.getId());
         * userGoldBill.setObjectId(toUserId);
         * userGoldBill.setAmount(new BigDecimal("0"));
         * userGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
         * if (null == fatherId || fatherId.intValue() < 1) {
         * userGoldBill.setChatRoomId(0L);
         * } else {
         * userGoldBill.setChatRoomId(fatherId);
         * }
         * userGoldBill.setUserContributionValue(price);
         * userGoldBill.setToUserCharmValue(price);
         * appUserGoldBillMapper.insertAppUserGoldBill(userGoldBill);
         *
         *
         * //获取礼物接收人的用户信息
         * AppViewUserInfoVo toUserInfoVo = null;
         * String toUserNickName = null;
         * //获取当前用户信息
         * AppViewUserInfoVo userInfoVo = null;
         * String nickName = null;
         *
         *
         * if (null != chatRoomId) {//聊天室id不为空就去推送消息
         *
         * appChatRoomMapper.addHeat(chatRoomId, price.multiply(new
         * BigDecimal("0.1")).longValue());
         * privateLetterWebSocket.sendChatRoomRefreshNumberMsg();
         *
         * //添加接收用户收到礼物的金币总数
         * appChatRoomUserMapper.addUserGiftGold(chatRoomId, toUserId, price);
         *
         * toUserInfoVo = appCommonService.getUserInfoByUserId(toUserId);
         * toUserNickName = StringUtils.isBlank(toUserInfoVo.getNickName()) ?
         * toUserInfoVo.getRecodeCode() : toUserInfoVo.getNickName();
         * //获取当前用户信息
         * userInfoVo = appCommonService.getUserInfoByUserId(userId);
         * nickName = StringUtils.isBlank(userInfoVo.getNickName()) ?
         * userInfoVo.getRecodeCode() : userInfoVo.getNickName();
         *
         *
         * //推送送礼消息
         * AppChatRoomPushWebSocketMsgVo msgResult = new
         * AppChatRoomPushWebSocketMsgVo();
         * msgResult.setChatRoomId(chatRoomId);
         * msgResult.setPushType(AppChatRoomWebSocketPushMsgTypeEnums.TYPE16.getId());
         * msgResult.setTitle(nickName + "送礼给" + toUserNickName);
         * msgResult.setContent(nickName + "送礼给" + toUserNickName);
         * Map<String, Object> details = new HashMap<>();
         *
         *
         * details.put("sendUserId", userId);//发送用户id
         * details.put("sendUserInfo", userInfoVo);//发送用户详情
         * details.put("deliveryUserId", -1);//送达用户id:-1代表所有
         * details.put("toUserId", toUserId);//接收用户id
         * details.put("toUserInfo", toUserInfoVo);//接收用户详情
         *
         *
         * details.put("giftInfo", gift);
         * details.put("num", num);//礼物数量
         * msgResult.setDetails(details);
         * try {
         * chatRoomWebSocket.sendMsgByChatRoomId(chatRoomId, msgResult);
         * } catch (Exception e) {
         * }
         * }
         *
         * if (singleGiftPrice.compareTo(new BigDecimal("13140")) >= 0) {//推全服
         * if (null == toUserInfoVo) {
         * toUserInfoVo = appCommonService.getUserInfoByUserId(toUserId);
         * toUserNickName = StringUtils.isBlank(toUserInfoVo.getNickName()) ?
         * toUserInfoVo.getRecodeCode() : toUserInfoVo.getNickName();
         * }
         *
         * if (null == userInfoVo) {
         * //获取当前用户信息
         * userInfoVo = appCommonService.getUserInfoByUserId(userId);
         * nickName = StringUtils.isBlank(userInfoVo.getNickName()) ?
         * userInfoVo.getRecodeCode() : userInfoVo.getNickName();
         *
         * }
         *
         * //推送送礼消息
         * Map<String, Object> pushResult = new HashMap<>();
         * pushResult.put("pushType", AppPrivateLetterMsgTypeEnums.TYPE14.getId());
         * pushResult.put("title", nickName + "送礼给" + toUserNickName);
         * pushResult.put("content", nickName + "送礼给" + toUserNickName);
         * pushResult.put("chatRoomId", chatRoomId);
         * pushResult.put("chatRoomName", chatRoomName);
         * Map<String, Object> details = new HashMap<>();
         * details.put("sendUserId", userId);//发送用户id
         * details.put("sendUserInfo", userInfoVo);//发送用户详情
         * details.put("deliveryUserId", -1);//送达用户id:-1代表所有
         * details.put("toUserId", toUserId);//接收用户id
         * details.put("toUserInfo", toUserInfoVo);//接收用户详情
         * details.put("giftInfo", gift);
         * details.put("num", num);//礼物数量
         * pushResult.put("details", details);
         *
         * try {
         * privateLetterWebSocket.toSendPrivateLetterByAllUser(pushResult);
         * } catch (Exception e) {
         *
         * }
         *
         * }
         *
         * //将入参返回给前端
         * Map<String, Object> result = new HashMap<>();
         * result.put("toUserId", toUserId);
         * result.put("giftId", giftId);
         * result.put("num", num);
         *
         * lock.unlock();
         * return AjaxResult.success("赠送成功", result);
         */
        return null;
    }

    /**
     * 开个性化箱子(支持连续)
     *
     * @param giftId
     * @param num
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult openDressUpChest(AppUserEntity user, Long giftId, Integer num) {
        if (null == giftId || giftId.intValue() < 1) {
            return AjaxResult.error("请选择宝箱");
        }
        if (null == num) {
            return AjaxResult.error("请选择数量");
        }
        if (num.intValue() < 1) {
            return AjaxResult.error("数量不能小于1");
        }

        Long userId = user.getId();

        // 操作加锁
        RLock lock = redisson.getLock("app:openDressUpChest:" + userId);
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(120, TimeUnit.SECONDS);

        AppDressUpChestMainConfig dressUpChestMainConfig = appDressUpChestMainConfigMapper
                .selectAppDressUpChestMainConfigById(giftId);
        if (null == dressUpChestMainConfig) {
            lock.unlock();
            return AjaxResult.error("宝箱不存在或已下架");
        }
        AppConfig appConfig = appCommonService.getAppConfig();
        if (appConfig.getIsChest().intValue() == WhetherTypeEnum.NO.getName()) {
            lock.unlock();
            return AjaxResult.error("功能未开启");
        }

        List<AppOpenDressUpVo> dressUpChestConfigList = appDressUpChestConfigMapper.getListByChestId(giftId);

        if (CollectionUtils.isEmpty(dressUpChestConfigList)) {
            lock.unlock();
            throw new AppException("该宝箱未配置数据");
        }
        BigDecimal price = dressUpChestMainConfig.getPrice().multiply(new BigDecimal(num));// 本次价格

        // 判断金币余额
        if (user.getGoldBalance().compareTo(price) < 0) {
            lock.unlock();
            return AjaxResult.error("金币余额不足");
        }

        // 扣除用户金币余额
        if (appUserMapper.subtractUserGoldBalance(userId, price) <= 0) {
            lock.unlock();
            throw new AppException("金币余额不足");
        }
        Date time = new Date();
        // 记录当前用户账单
        AppUserGoldBill userGoldBill = new AppUserGoldBill();
        userGoldBill.setUserId(userId);
        userGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE16.getId());
        userGoldBill.setObjectId(-1L);
        userGoldBill.setAmount(price.negate());
        userGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
        appUserGoldBillMapper.insertAppUserGoldBill(userGoldBill);

        // 执行开宝箱
        List<AppOpenDressUpVo> openGiftList = new ArrayList<>();

        for (int i = 0; i < num; i++) {
            try {
                AppOpenDressUpVo openDressUpVo = toOpenDressUpChest(dressUpChestConfigList,
                        "The random number generated is not within any prize range");
                if (null != openDressUpVo) {
                    openGiftList.add(openDressUpVo);
                }
            } catch (Exception e) {
                lock.unlock();
                throw new AppException(e.getMessage());
            }
        }

        if (CollectionUtils.isEmpty(openGiftList)) {
            lock.unlock();
            throw new AppException("openGiftList isEmpty");
        }

        Map<Long, List<AppOpenDressUpVo>> openGiftListGroup = openGiftList.stream()
                .collect(Collectors.groupingBy(AppOpenDressUpVo::getDressUpId));

        List<AppOpenDressUpVo> openDressUpList = new ArrayList<>();

        for (Map.Entry<Long, List<AppOpenDressUpVo>> entry : openGiftListGroup.entrySet()) {
            AppOpenDressUpVo giftVo = new AppOpenDressUpVo();
            giftVo.setDressUpId(entry.getKey());
            giftVo.setNum(entry.getValue().size());
            giftVo.setRatio(null);
            for (AppOpenDressUpVo item : openGiftList) {
                if (item.getDressUpId().equals(giftVo.getDressUpId())) {
                    giftVo.setName(item.getName());
                    giftVo.setImgUrl(item.getImgUrl());
                    giftVo.setGifUrl(item.getGifUrl());
                    giftVo.setColorValue(item.getColorValue());
                    giftVo.setCategoryId(item.getCategoryId());
                    giftVo.setValidDays(item.getValidDays());
                    giftVo.setGoldPrice(item.getGoldPrice());
                    break;
                }
            }
            giftVo.setAddDays(giftVo.getValidDays() * giftVo.getNum());
            openDressUpList.add(giftVo);
        }

        for (AppOpenDressUpVo item : openDressUpList) {

            AppUserPersonalDressing personalDressing = appUserPersonalDressingMapper.getUserIdAndId(item.getDressUpId(),
                    userId);
            if (null == personalDressing) {
                AppUserPersonalDressing userPersonalDressing = new AppUserPersonalDressing();
                userPersonalDressing.setUserId(userId);
                userPersonalDressing.setPersonalDressingId(item.getDressUpId());
                userPersonalDressing.setIsDel(WhetherTypeEnum.NO.getName());
                userPersonalDressing.setIsUse(WhetherTypeEnum.NO.getName());
                userPersonalDressing.setCreateTime(time);
                userPersonalDressing.setExpiredTime(DateUtils.addDays(time, item.getAddDays().intValue()));
                appUserPersonalDressingMapper.insertAppUserPersonalDressing(userPersonalDressing);
            } else {
                AppUserPersonalDressing userPersonalDressing = new AppUserPersonalDressing();
                userPersonalDressing.setId(personalDressing.getId());
                if (time.after(personalDressing.getExpiredTime())) {
                    userPersonalDressing.setExpiredTime(DateUtils.addDays(time, item.getAddDays().intValue()));
                } else {
                    userPersonalDressing
                            .setExpiredTime(DateUtils.addDays(personalDressing.getExpiredTime(), item.getAddDays()
                                    .intValue()));
                }
                appUserPersonalDressingMapper.updateAppUserPersonalDressing(userPersonalDressing);

            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("openDressUpList", openDressUpList);

        lock.unlock();

        return AjaxResult.success("操作成功", result);
    }

    /**
     * 连续开箱子
     *
     * @param request
     * @param giftId
     * @param chatRoomId
     * @param num
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult openChestByContinuous(HttpServletRequest request, Long giftId, Long chatRoomId, Integer num) {
        /*
         * if (null == giftId || giftId.intValue() < 1) {
         * return AjaxResult.error("请选择宝箱");
         * }
         * if (null == num) {
         * return AjaxResult.error("请选择数量");
         * }
         * if (num.intValue() < 1) {
         * return AjaxResult.error("数量不能小于1");
         * }
         * if (null == chatRoomId) {
         * return AjaxResult.error("功能已关闭");
         * }
         * AppUserEntity user = getUser(request);
         * Long userId = user.getId();
         *
         * //操作加锁
         * RLock lock = redisson.getLock("app:continuousOpenChest:" + userId);
         * if (lock.isLocked()) {
         * return AjaxResult.frequent();
         * }
         * lock.lock(120, TimeUnit.SECONDS);
         *
         * AppGift gift = appGiftMapper.selectAppGiftById(giftId);
         *
         * if (null == gift) {
         * lock.unlock();
         * return AjaxResult.error("宝箱不存在或已下架");
         * }
         *
         * if (!gift.getCategoryId().equals(AppGiftCategoryEnums.PARCEL.getId()))
         * {//调用此接口必须是包裹分类的礼物
         * lock.unlock();
         * return AjaxResult.error("Gift category mismatch");
         * }
         * if (!AppChestGiftEnums.getChestGiftIdList().contains(gift.getId()))
         * {//仅支持枚举类中的宝箱
         * lock.unlock();
         * return AjaxResult.error("无效的宝箱数据");
         * }
         * BigDecimal price = gift.getMasonryPrice().multiply(new
         * BigDecimal(num));//本次价格
         *
         * AppConfig appConfig = appCommonService.getAppConfig();
         * if (appConfig.getIsChest().intValue() == WhetherTypeEnum.NO.getName()) {
         * lock.unlock();
         * return AjaxResult.error("功能未开启");
         * }
         *
         * Date time = new Date();
         *
         * List<AppBxBfjl> bfjls = appBxBfjlMapper.getUserBfjlList(userId);
         *
         * List<Long> giftIdList = new ArrayList<>();
         *
         * if (!CollectionUtils.isEmpty(bfjls)) {
         * AppConfig config = appConfigMapper.getAppConfig();
         *
         * BigDecimal bfPrice = new BigDecimal("0");
         * for (AppBxBfjl bfjl : bfjls) {
         * for (int i = 0; i < bfjl.getSubNum(); i++) {
         * giftIdList.add(bfjl.getGiftId());
         * }
         * bfPrice = bfPrice.add(new
         * BigDecimal(bfjl.getSubNum()).multiply(bfjl.getGiftPrice()));
         *
         *
         * AppUserGiftBackpack toUserGiftBackpack = new AppUserGiftBackpack();
         * toUserGiftBackpack.setIsDel(WhetherTypeEnum.NO.getName());
         * toUserGiftBackpack.setCreateTime(time);
         * toUserGiftBackpack.setUserId(userId);
         * toUserGiftBackpack.setGiftId(bfjl.getGiftId());
         * toUserGiftBackpack.setSumNum(bfjl.getSubNum());
         * toUserGiftBackpack.setSendUserId(userId);
         * toUserGiftBackpack.setSendUserGoldBillId(-100L);
         * toUserGiftBackpack.setOldSumNum(bfjl.getSubNum());
         * toUserGiftBackpack.setIsReissue(WhetherTypeEnum.YES.getName());
         * toUserGiftBackpack.setObjectId(-1L);
         * appUserGiftBackpackMapper.insertAppUserGiftBackpack(toUserGiftBackpack);
         *
         * bfjl.setIsSubKc(WhetherTypeEnum.YES.getName());
         * bfjl.setSubNum(0L);
         * bfjl.setSubTime(time);
         * appBxBfjlMapper.updateAppBxBfjl(bfjl);
         * }
         *
         * if (config.getMzlsJcType().intValue() == 1) {
         * if (!CollectionUtils.isEmpty(giftIdList)) {
         * redisTemplate.opsForList().rightPushAll("bx-mf-kc", giftIdList);
         * }
         * } else {
         * if (bfPrice.compareTo(new BigDecimal("0")) > 0) {
         * appChestUpgradeConfigMapper.subSumProfitBl(giftId, bfPrice);
         * }
         * }
         * }
         *
         *
         * //判断金币余额
         * if (user.getGoldBalance().compareTo(price) < 0) {
         * lock.unlock();
         * return AjaxResult.error("金币余额不足");
         * }
         * // BigDecimal userSumTopUpOrderPrice =
         * appOrderMapper.getUserSumTopUpOrderPrice(userId);
         * // if (null == userSumTopUpOrderPrice || userSumTopUpOrderPrice.compareTo(new
         * BigDecimal("38")) < 0) {
         * // lock.unlock();
         * // throw new AppException("无法开启该宝箱");
         * // }
         *
         *
         * //扣除用户金币余额
         * if (appUserMapper.subtractUserGoldBalance(userId, price) <= 0) {
         * lock.unlock();
         * throw new AppException("金币余额不足");
         * }
         *
         * //记录当前用户账单
         * AppUserGoldBill userGoldBill = new AppUserGoldBill();
         * userGoldBill.setUserId(userId);
         * userGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE16.getId());
         * userGoldBill.setObjectId(0L);
         * userGoldBill.setAmount(price.negate());
         * userGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
         * appUserGoldBillMapper.insertAppUserGoldBill(userGoldBill);
         *
         *
         * //执行开宝箱
         * List<AppOpenGiftVo> openGiftList = null;
         * try {
         * openGiftList = toPrizeDrawGift(num, price, gift, userId);
         * } catch (Exception e) {
         * lock.unlock();
         * throw new AppException(e.getMessage());
         * }
         *
         *
         * if (CollectionUtils.isEmpty(openGiftList)) {
         * lock.unlock();
         * throw new AppException("什么都没开到哦～");
         * }
         *
         *
         * boolean isAllPush = false;//是否全服推送
         * for (AppOpenGiftVo item : openGiftList) {
         * if (item.getGiftPrice().compareTo(new BigDecimal("520")) >= 0) {//推全服
         * isAllPush = true;
         * break;
         * }
         * }
         *
         *
         * //将礼物添加到用户背包
         * for (AppOpenGiftVo item : openGiftList) {
         * AppUserGiftBackpack toUserGiftBackpack = new AppUserGiftBackpack();
         * toUserGiftBackpack.setIsDel(WhetherTypeEnum.NO.getName());
         * toUserGiftBackpack.setCreateTime(time);
         * toUserGiftBackpack.setUserId(userId);
         * toUserGiftBackpack.setGiftId(item.getGiftId());
         * toUserGiftBackpack.setSumNum((long) item.getNum());
         * toUserGiftBackpack.setSendUserId(userId);
         * toUserGiftBackpack.setSendUserGoldBillId(userGoldBill.getId());
         * toUserGiftBackpack.setOldSumNum((long) item.getNum());
         * toUserGiftBackpack.setIsReissue(WhetherTypeEnum.NO.getName());
         * toUserGiftBackpack.setObjectId(-1L);
         * appUserGiftBackpackMapper.insertAppUserGiftBackpack(toUserGiftBackpack);
         * }
         *
         *
         * //添加礼物销量
         * appGiftMapper.addSales(giftId, (long) num);
         *
         * gift.setCreateTime(null);
         * gift.setUpdateTime(null);
         * gift.setCreateBy(null);
         * gift.setUpdateBy(null);
         * gift.setSales(null);
         *
         * //获取当前用户信息
         * AppViewUserInfoVo userInfoVo = appCommonService.getUserInfoByUserId(userId);
         *
         * if (null != chatRoomId) {//聊天室id不为空就去推送消息
         *
         * //推送送礼消息
         * AppChatRoomPushWebSocketMsgVo msgResult = new
         * AppChatRoomPushWebSocketMsgVo();
         * msgResult.setChatRoomId(chatRoomId);
         * msgResult.setPushType(AppChatRoomWebSocketPushMsgTypeEnums.TYPE29.getId());
         * msgResult.setTitle("用户开多个宝箱消息");
         * msgResult.setContent("用户开多个宝箱消息");
         * Map<String, Object> details = new HashMap<>();
         * details.put("sendUserId", userId);//发送用户id
         * details.put("sendUserInfo", userInfoVo);//发送用户详情
         * details.put("deliveryUserId", -1);//送达用户id:-1代表所有
         * details.put("toUserId", null);
         * details.put("toUserInfo", null);
         * details.put("giftInfo", gift);
         * details.put("openGiftList", openGiftList);//开到的礼物信息,不为空就代表一定是礼物
         * details.put("num", num);//开的总数量
         * msgResult.setDetails(details);
         * try {
         * chatRoomWebSocket.sendMsgByChatRoomId(chatRoomId, msgResult);
         * } catch (Exception e) {
         * }
         * }
         *
         * if (isAllPush) {
         * AppChatRoom chatRoom = appChatRoomMapper.selectAppChatRoomById(chatRoomId);
         * String chatRoomName = null != chatRoom ? chatRoom.getName() : "";
         * //推送送礼消息
         * Map<String, Object> pushResult = new HashMap<>();
         * pushResult.put("pushType", AppPrivateLetterMsgTypeEnums.TYPE15.getId());
         * pushResult.put("title", "宝箱全服消息");
         * pushResult.put("content", "宝箱全服消息");
         * pushResult.put("chatRoomId", chatRoomId);
         * pushResult.put("chatRoomName", chatRoomName);
         * Map<String, Object> details = new HashMap<>();
         * details.put("sendUserId", userId);//发送用户id
         * details.put("sendUserInfo", userInfoVo);//发送用户详情
         * details.put("deliveryUserId", -1);//送达用户id:-1代表所有
         * details.put("toUserId", null);
         * details.put("toUserInfo", null);
         * details.put("giftInfo", gift);
         * details.put("openGiftList", openGiftList);//开到的礼物信息,不为空就代表一定是礼物
         * details.put("num", num);//礼物数量
         * pushResult.put("details", details);
         * try {
         * privateLetterWebSocket.toSendPrivateLetterByAllUser(pushResult);
         * } catch (Exception e) {
         *
         * }
         * }
         *
         *
         * Map<String, Object> result = new HashMap<>();
         *
         * result.put("openGiftList", openGiftList);
         *
         * lock.unlock();
         *
         * return AjaxResult.success("操作成功", result);
         */
        return null;
    }

    /**
     * 获取是否显示全服飘屏
     *
     * @return
     */
    public AjaxResult getShowAllFloatingScreen() {

        AppConfig appConfig = appCommonService.getAppConfig();

        return AjaxResult.success("查询成功", appConfig.getIsAllFloatingScreen());

    }

    /**
     * 获取是否显示飘屏
     *
     * @return
     */
    public AjaxResult getSHowFloatingScreen() {

        AppConfig appConfig = appCommonService.getAppConfig();

        return AjaxResult.success("查询成功", appConfig.getIsFloatingScreen());

    }

    /**
     * 获取个性化奖池列表
     *
     * @param request
     * @param giftId
     * @return
     */
    public AjaxResult getDressUpPrizePoolList(HttpServletRequest request, Long giftId) {
        if (null == giftId) {
            return AjaxResult.error("参数【giftId】为空");
        }
        List<AppOpenDressUpVo> list = appDressUpChestConfigMapper.getPrizePoolList(giftId);

        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }

        return AjaxResult.success(list);

    }

    /**
     * 获取奖池列表
     *
     * @param request
     * @return
     */
    public AjaxResult getPrizePoolList(HttpServletRequest request, Long giftId) {
        if (null == giftId) {
            return AjaxResult.error("参数【giftId】为空");
        }
        List<AppGiftVo> list = appChestGiftConfigMapper.getPrizePoolList(giftId);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }

        return AjaxResult.success(list);

    }

    /**
     * 礼物消息推送全服
     *
     * @param toUserId        到用户id
     * @param num             全国矿工工会
     * @param chatRoomId      聊天室id
     * @param singleGiftPrice 单件礼品价格
     * @param appConfig       应用程序配置
     * @param nickName        尼克名字
     * @param toUserNickName  致用户昵称
     * @param chatRoomName    聊天室名称
     * @param userId          用户id
     * @param userInfoVo
     * @param toUserInfoVo
     * @param gift
     */
    private void pushWsAll(Long toUserId, Integer num, Long chatRoomId, BigDecimal singleGiftPrice, AppConfig appConfig,
                           String nickName, String toUserNickName, String chatRoomName, Long userId, AppViewUserInfoVo userInfoVo,
                           AppViewUserInfoVo toUserInfoVo, AppGift gift) {
        if (singleGiftPrice.compareTo(appConfig.getFullServerFloatingScreenPrice()) >= 0) {
            // 推送送礼消息
            Map<String, Object> pushResult = new HashMap<>();
            pushResult.put("pushType", AppPrivateLetterMsgTypeEnums.TYPE14.getId());
            pushResult.put("title", nickName + "送礼给" + toUserNickName);
            pushResult.put("content", nickName + "送礼给" + toUserNickName);
            pushResult.put("chatRoomId", chatRoomId);
            pushResult.put("chatRoomName", chatRoomName);
            Map<String, Object> details = new HashMap<>();
            details.put("sendUserId", userId);// 发送用户id
            details.put("sendUserInfo", userInfoVo);// 发送用户详情
            details.put("deliveryUserId", -1);// 送达用户id:-1代表所有
            details.put("toUserId", toUserId);// 接收用户id
            // details.put("openGift", openGift);//对方开到的礼物信息,不为空就代表一定是礼物
            details.put("toUserInfo", toUserInfoVo);// 接收用户详情
            details.put("giftInfo", gift);
            details.put("num", num);// 礼物数量
            pushResult.put("details", details);

            try {
                privateLetterWebSocket.toSendPrivateLetterByAllUser(pushResult);
            } catch (Exception e) {
            }
        }
    }

    /**
     * 刷新礼物头条
     *
     * @param singleGiftPrice 单件礼品价格
     * @param appConfig       应用程序配置
     * @param nickName        名字
     * @param toUserNickName  用户昵称
     */
    private void pushWsGiftTop(BigDecimal singleGiftPrice, AppConfig appConfig, String nickName,
                               String toUserNickName) {
        if (singleGiftPrice.compareTo(appConfig.getHomeGiftTopLine()) >= 0) {
            // 推送送礼消息
            Map<String, Object> pushResult = new HashMap<>();
            pushResult.put("pushType", AppPrivateLetterMsgTypeEnums.TYPE16.getId());
            pushResult.put("title", nickName + "送礼给" + toUserNickName);
            pushResult.put("details",
                    userGiveGiftMapper.homeGiftTop(appConfigMapper.getAppConfig().getHomeGiftTopLine())
                            .get(0));

            try {
                privateLetterWebSocket.toSendPrivateLetterByAllUser(pushResult);
            } catch (Exception e) {
            }
        }
    }

    /**
     * 赠送礼物业务线
     *
     * @param user     用户
     * @param toUserId 到用户id
     * @param giftId   礼物id
     * @param num      数量
     * @param chatRoom 聊天室
     * @return {@link AjaxResult }
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult giveGifts2(AppUserEntity user, Long toUserId, Long giftId, Integer num, AppChatRoom chatRoom, Long backType) {
        try {
            // 操作加锁
            Long userId = user.getId();
            RLock lock = redisson.getLock("app:user:giveGifts:" + userId);
            if (lock.isLocked()) {
                return AjaxResult.frequent();
            }
            lock.lock(120, TimeUnit.SECONDS);
            
            // 1. 基础验证
            if (user.getId().equals(toUserId)) {
                return AjaxResult.error("不能给自己送礼物");
            }

            // 2. 黑名单检查
            AppBlacklist blacklistByUser = appBlacklistMapper.selectAppBlacklistByUserIdAndToUserId(user.getId(), toUserId);
            if (null != blacklistByUser) {
                return AjaxResult.error("赠送失败,您已将对方加入黑名单");
            }

            // 3. 接收用户检查
            AppUserEntity receiveUser = appUserMapper.selectAppUserById(toUserId);
            if (null == receiveUser) {
                return AjaxResult.error("赠送失败,对方账号不存在");
            }

            // 4. 礼物信息检查
            AppGift gift = appGiftMapper.selectAppGiftById(giftId);
            if (null == gift) {
                return AjaxResult.error("礼物不存在或已下架");
            }
            // 背包礼物,扣除背包库存
            if (ObjectUtil.isNotNull(backType) && backType == 1) {
                // 扣除背包库存
                boolean deductBackpackStock = appUserGiftBackpackMapper.deductBackpackStock(user.getId(), giftId, num);
                if (!deductBackpackStock) {
                    return AjaxResult.error("背包礼物库存不足");
                }
            }
            boolean isChatRoomIsNull = ObjectUtil.isNull(chatRoom);

            // 新增聊天关系,不存在,就新增
            appPrivateLetterListService.addChatRelation(user.getId(), toUserId, isChatRoomIsNull);

            // 5. 如果是盲盒礼物,走盲盒逻辑
            if (gift.getLuckFlag().equals(1)) {
                return this.giveGifts3(user, toUserId, giftId, num, chatRoom);
            }

            // 6. 计算礼物总价
            BigDecimal giftTotalPrice = gift.getMasonryPrice().multiply(new BigDecimal(num));

            // 7. 检查用户余额
            if (user.getGoldBalance().compareTo(giftTotalPrice) < 0) {
                return AjaxResult.error("金币余额不足");
            }

            // 8. 扣除用户金币
            if (appUserMapper.subtractUserGoldBalance(user.getId(), giftTotalPrice) <= 0) {
                throw new AppException("金币余额不足");
            }

            // 9. 处理礼物赠送核心逻辑
            GiftGivingResult giftResult = processGiftGiving(user, toUserId, gift, num, chatRoom, giftTotalPrice, false, null);

            // 10. 添加礼物销量
            appGiftMapper.addSales(giftId, (long) num);

            // 11. 非背包礼物的渠道返佣 - 仅对女性接收者进行固定10%积分分佣
            if (!GAME_GIFT_CATEGORY_ID.equals(gift.getCategoryId())) {
                channelCommissionRecordService.saveChannelGiftRecordsForFemale(receiveUser, gift, giftResult.getGiftRecord());
            }

            // 12. 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("toUserId", toUserId);
            result.put("giftId", giftId);
            result.put("num", num);
            return AjaxResult.success("赠送成功", result);

        } catch (AppException e) {
            log.error("给用户{}赠送礼物{}失败", toUserId, giftId, e);
            return AjaxResult.error("赠送失败:" + e.getMessage());
        } finally {
            // 释放锁
            RLock lock = redisson.getLock("app:user:giveGifts:" + user.getId());
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 盲盒礼物业务线
     *
     * @param user     用户
     * @param toUserId 到用户id
     * @param giftId   礼物id
     * @param num      数量
     * @param chatRoom 聊天室
     * @return {@link AjaxResult }
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult giveGifts3(AppUserEntity user, Long toUserId, Long giftId, Integer num, AppChatRoom chatRoom) {
        try {
            // 1. 基础验证
            AppBlacklist blacklistByUser = appBlacklistMapper.selectAppBlacklistByUserIdAndToUserId(user.getId(), toUserId);
            if (null != blacklistByUser) {
                return AjaxResult.error("赠送失败,您已将对方加入黑名单");
            }

            AppUserEntity receiveUser = appUserMapper.selectAppUserById(toUserId);
            if (null == receiveUser) {
                return AjaxResult.error("赠送失败,对方账号不存在");
            }

            // 2. 获取盲盒礼物信息
            AppGift gift = appGiftMapper.selectAppGiftById(giftId);
            if (null == gift) {
                return AjaxResult.error("礼物不存在或已下架");
            }

            // 3. 处理盲盒奖池
            BigDecimal putLuckAmount = gamePoolService.putLuckAmount(giftId);
            BigDecimal getLuckAmount = gamePoolService.getLuckAmount(giftId);

            // 4. 抽取盲盒礼物
            List<AppGift> newGift = new ArrayList<>();
            List<AppGift> luckGiftList = this.lambdaQuery()
                    .eq(AppGift::getLuckId, giftId)
                    .eq(AppGift::getIsDel, false)
                    .list();

            // 判断盲盒类型：1-数量池，2-概率池
            Integer luckPoolType = gift.getLuckPoolType() != null ? gift.getLuckPoolType() : 2;

            // 根据不同类型进行抽奖处理
            if (luckPoolType == 1) {
                // 数量池抽奖逻辑
                newGift = handleNumPoolDrawing(giftId, num, luckGiftList, gift, putLuckAmount);
            } else {
                // 原有的概率池抽奖逻辑
                for (int i = 0; i < num; i++) {
                    putLuckAmount = putLuckAmount.add(gift.getMasonryPrice());
                    // 奖池剩余的金额控爆率
                    BigDecimal money = putLuckAmount.multiply(gift.getRate() == null ? BigDecimal.ONE : gift.getRate())
                            .subtract(getLuckAmount);

                    List<AppGift> availableGifts = new ArrayList<>(luckGiftList);
                    availableGifts.removeIf(item -> item.getMasonryPrice().compareTo(money) > 0);

                    AppGift drawnGift = drawPrize(availableGifts);
                    getLuckAmount = getLuckAmount.add(drawnGift.getMasonryPrice());
                    newGift.add(drawnGift);
                }
            }

            // 5. 更新奖池
            gamePoolService.addPutLuckAmount(putLuckAmount, giftId);
            gamePoolService.addGetLuckAmount(getLuckAmount, giftId);

            // 6. 合并相同礼物
            List<AppGift> stackedGifts = this.foldGift(newGift);
            List<AppChattingRecords> chattingRecords = new ArrayList<>();

            // 7. 处理每个抽中的礼物
            for (AppGift drawnGift : stackedGifts) {
                // 计算礼物总价
                BigDecimal giftTotalPrice = drawnGift.getMasonryPrice().multiply(drawnGift.getNumber());

                // 检查用户余额
                if (user.getGoldBalance().compareTo(gift.getMasonryPrice().multiply(drawnGift.getNumber())) < 0) {
                    throw new AppException("金币余额不足");
                }

                // 扣除用户金币
                if (appUserMapper.subtractUserGoldBalance(user.getId(), gift.getMasonryPrice()
                        .multiply(drawnGift.getNumber())) <= 0) {
                    throw new AppException("金币余额不足");
                }

                // 处理礼物赠送核心逻辑
                GiftGivingResult giftResult = processGiftGiving(user, toUserId, drawnGift, drawnGift.getNumber().intValue(),
                        chatRoom, giftTotalPrice, true, gift);

                // 添加礼物销量
                appGiftMapper.addSales(gift.getId(), drawnGift.getNumber().longValue());

                // 非背包礼物的渠道返佣 - 仅对女性接收者进行固定10%积分分佣
                if (!GAME_GIFT_CATEGORY_ID.equals(gift.getCategoryId())) {
                    channelCommissionRecordService.saveChannelGiftRecordsForFemale(receiveUser, gift, giftResult.getGiftRecord());
                }
                // 盲盒礼物插入聊天消息
                AppChattingRecords chattingRecord = new AppChattingRecords();
                chattingRecord.setSendUserId(user.getId());
                chattingRecord.setReceiveUserId(toUserId);
                chattingRecord.setSendTime(new Date());
                chattingRecord.setMsgType((long) AppSendMsgTypeEnums.TYPE5.getId());

                MsgGiftContent msgGiftContent = new MsgGiftContent();
                msgGiftContent.getGiftInfo()
                        .setGiftId(drawnGift.getId())
                        .setName(drawnGift.getGiftName())
                        .setGiftSellPrice(drawnGift.getMasonryPrice())
                        .setNum(drawnGift.getNumber().intValue())
                        .setUrl(drawnGift.getImgUrl());
                chattingRecord.setContent(JSONObject.toJSONString(msgGiftContent));
                chattingRecords.add(chattingRecord);

                AppChattingRecordsVo vo = new AppChattingRecordsVo();
                BeanUtils.copyProperties(chattingRecord, vo);
                vo.setSendUserNickname(user.getNickName());

                // 给接收者推送消息
                privateLetterWebSocket.sendPrivateLetter(toUserId.toString(), vo);

            }
            // 在房间不插入
            if (ObjectUtil.isNull(chatRoom.getId())) {
                appChattingRecordsService.saveOrUpdateBatch(chattingRecords);
            }

            // 8. 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("toUserId", toUserId);
            result.put("giftId", stackedGifts.size() == 1 ? stackedGifts.get(0).getId() : giftId);
            result.put("num", num);
            result.put("gift", stackedGifts);

            boolean isChatRoomIsNull = ObjectUtil.isNull(chatRoom);
            // 新增聊天关系,不存在,就新增
            appPrivateLetterListService.addChatRelation(user.getId(), toUserId, isChatRoomIsNull);

            return AjaxResult.success("赠送成功", result);

        } catch (AppException e) {
            log.error("给用户{}赠送礼物{}失败", toUserId, giftId, e);
            return AjaxResult.error("赠送失败:" + e.getMessage());
        }
    }

    /**
     * 概率抽奖
     *
     * @param prizes 奖
     * @return {@link AppGift }
     */
    private AppGift drawPrize(List<AppGift> prizes) {

        log.info("参与抽奖数:{},礼物:{}", prizes.size(), JSONObject.toJSONString(prizes));
        // 计算总权重
        int totalWeight = prizes.stream()
                .filter(item -> !item.getLuckWeight().equals(0))
                .mapToInt(prize -> prize.getLuckWeight())
                .sum();
        if (totalWeight == 0) {
            throw new AppException("概率池总权重为0,无法抽奖");
        }

        // 生成一个随机数 [0, totalWeight)
        int randomNum = new Random().nextInt(totalWeight);
        log.info("随机数：{},随机数区间:{}", randomNum, totalWeight);

        // 根据随机数选择奖品
        int currentWeight = 0;
        for (AppGift prize : prizes) {
            currentWeight += prize.getLuckWeight();
            log.info("当前权重：{},{}", currentWeight, prize.getGiftName());
            if (randomNum < currentWeight) {
                log.info("中奖：{},{}", prize.getGiftName(), prize.getMasonryPrice());
                return prize;
            }
        }

        return null; // 不会发生，但为了语法完整性
    }

    /**
     * 堆叠同类型的礼物
     *
     * @param range 奖品礼物list
     * @return {@link List }<{@link AppTurntableGiftVo }>
     */
    private List<AppGift> foldGift(Collection<AppGift> range) {
        List<AppGift> allRange = new ArrayList<>();
        if (CollectionUtils.isEmpty(range)) {
            return allRange;
        }

        for (AppGift appTurntableGiftVo : range) {
            AppGift one = this.findOne(appTurntableGiftVo.getId(), allRange);
            if (one != null) {
                BigDecimal num = one.getNumber();
                if (num == null) {
                    one.setNumber(BigDecimal.valueOf(1));
                } else {
                    one.setNumber(num.add(BigDecimal.ONE));
                }
            } else {
                appTurntableGiftVo.setNumber(BigDecimal.ONE);
                allRange.add(appTurntableGiftVo);
            }
            // 根据礼物价格排序
            allRange.sort(Comparator.comparing(AppGift::getMasonryPrice).reversed());
        }

        return allRange;
    }

    AppGift findOne(Long id, List<AppGift> list) {
        if (!CollectionUtils.isEmpty(list)) {
            for (AppGift appTurntableGiftVo : list) {
                if (appTurntableGiftVo.getId().equals(id)) {
                    return appTurntableGiftVo;
                }
            }
        }
        return null;
    }

    /**
     * 获取礼物和分类列表
     *
     * @return
     */
    public List<AppGiftCategoryVo> getGiftAndCategoryListNew(Long userId) {
        List<AppGiftCategoryVo> categoryGiftList = this.getDirectlySendGifts();

        List<AppGiftVo> giftList = appUserGiftBackpackMapper.getUserBackPackCategoryGifts(userId);
        AppGiftCategoryVo appGiftCategoryVo = new AppGiftCategoryVo();
        appGiftCategoryVo.setCategoryId(0L);
        appGiftCategoryVo.setGiftList(giftList);
        appGiftCategoryVo.setCategoryName("背包");
        categoryGiftList.add(appGiftCategoryVo);
        return categoryGiftList;
    }

    public AjaxResult getGiftAndCategoryList(Long userId, Long chatRoomId) {
        if (null == chatRoomId || chatRoomId.intValue() < 1) {
            chatRoomId = null;
        }

        // 是否查询宝箱礼物
        boolean isQueryChestGift = false;
        AppConfig appConfig = appCommonService.getAppConfig();
        if (appConfig.getIsChest().intValue() == WhetherTypeEnum.NO.getName()) {
            isQueryChestGift = false;
        } else {
            if (null != chatRoomId) {
                // BigDecimal userSumTopUpOrderPrice =
                // appOrderMapper.getUserSumTopUpOrderPrice(userId);
                // if (null != userSumTopUpOrderPrice && userSumTopUpOrderPrice.compareTo(new
                // BigDecimal("38")) >= 0) {
                //
                // }
                isQueryChestGift = true;
            }
        }

        boolean isHaveChest = false;

        List<AppGiftCategoryVo> list = appGiftMapper.getAppGiftAndCategoryList(isQueryChestGift);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        } else {
            // 不查宝箱分类和背包分类的礼物，只查礼物分类
            List<Long> notCategoryId = new ArrayList<>();
            notCategoryId.add(AppGiftCategoryEnums.PARCEL.getId());
            notCategoryId.add(AppGiftCategoryEnums.BACKPACK.getId());
            list.forEach(item -> {
                if (item.getCategoryId().equals(AppGiftCategoryEnums.BACKPACK.getId())) {// 如果是背包分类，那就获取用户的背包分类下的礼物
                    List<AppGiftVo> giftList = appUserGiftBackpackMapper.getUserBackPackCategoryGiftList(userId,
                            notCategoryId);
                    if (CollectionUtils.isEmpty(giftList)) {
                        giftList = new ArrayList<>();
                    }
                    item.setGiftList(giftList);
                }
            });

            for (AppGiftCategoryVo item : list) {
                if (item.getCategoryId().equals(AppGiftCategoryEnums.PARCEL.getId())) {
                    isHaveChest = true;
                    break;
                }
            }

        }

        if (appConfig.getIsChest().intValue() == WhetherTypeEnum.YES.getName() && null != chatRoomId && !isHaveChest) {
            AppGiftCategoryVo chestVo = new AppGiftCategoryVo();
            chestVo.setCategoryId(AppGiftCategoryEnums.PARCEL.getId());
            chestVo.setCategoryName(appGiftCategoryMapper.getName(chestVo.getCategoryId()));
            list.add(chestVo);
            isHaveChest = true;
        }
        if (isHaveChest) {
            for (AppGiftCategoryVo item : list) {
                if (item.getCategoryId().equals(AppGiftCategoryEnums.PARCEL.getId())) {
                    List<AppGiftVo> giftList = item.getGiftList();
                    if (CollectionUtils.isEmpty(giftList)) {
                        giftList = new ArrayList<>();
                    }
                    List<AppGiftVo> dressUpChestList = appDressUpChestMainConfigMapper.getDressUpChestMainConfigList();
                    if (!CollectionUtils.isEmpty(dressUpChestList)) {
                        giftList.addAll(dressUpChestList);
                    }
                    item.setGiftList(giftList);
                }
            }
        }
        return AjaxResult.success(list);
    }

    /**
     * 获取用户收到的礼物列表
     *
     * @param page
     * @param size
     * @param viewUserId 被查看的用户id,不传就是查当前用户
     * @param
     * @return
     */
    public TableDataInfo getUserReceiveGiftList(int page, int size, Long viewUserId, Long userId) {
        if (null == viewUserId || viewUserId.intValue() < 1) {
            viewUserId = userId;
        }
        // 根据礼物id去重
        List<AppUserReceiveGiftVo> list = appUserGiftBackpackMapper.getUserReceiveGiftList2(viewUserId);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        }
        return AjaxResult.getDataTable(list, list.size());
    }

    public List<AppGiftCategoryVo> getDirectlySendGifts() {
        final List<AppGiftDetailVo> categoryGiftList = this.appGiftMapper.getCategoryGiftList();

        if (CollectionUtils.isEmpty(categoryGiftList)) {
            return new ArrayList<>();
        }

        List<AppGiftCategory> categoryList = appGiftCategoryMapper.selectAppGiftCategoryList(new AppGiftCategory());

        // 按categoryId分组并按照categoryList的顺序排序
        Map<Long, List<AppGiftDetailVo>> groupedGifts = categoryGiftList.stream()
                .collect(Collectors.groupingBy(AppGiftDetailVo::getCategoryId,
                        LinkedHashMap::new, // 使用LinkedHashMap保持顺序
                        Collectors.toList()));

        // 按照categoryList的顺序重新排序groupedGifts
        Map<Long, List<AppGiftDetailVo>> sortedGroupedGifts = new LinkedHashMap<>();
        Map<Long, List<AppGiftDetailVo>> finalGroupedGifts = groupedGifts;
        categoryList.forEach(category -> {
            if (finalGroupedGifts.containsKey(category.getId())) {
                sortedGroupedGifts.put(category.getId(), finalGroupedGifts.get(category.getId()));
            }
        });
        groupedGifts = sortedGroupedGifts;

        // 转换为AppGiftCategoryVo列表
        return groupedGifts.entrySet().stream().map(entry -> {
            AppGiftCategoryVo categoryVo = new AppGiftCategoryVo();
            categoryVo.setCategoryId(entry.getKey());

            // 从第一个礼物中获取分类信息
            AppGiftDetailVo firstGift = entry.getValue().get(0);
            categoryVo.setCategoryName(firstGift.getCategoryName());
            categoryVo.setIcoUrl(firstGift.getIcoUrl());

            // 转换礼物列表
            List<AppGiftVo> giftList = entry.getValue().stream().map(detail -> {
                AppGiftVo giftVo = new AppGiftVo();
                giftVo.setGiftId(detail.getGiftId());
                giftVo.setGiftName(detail.getGiftName());
                giftVo.setMasonryPrice(detail.getMasonryPrice());
                giftVo.setImgUrl(detail.getImgUrl());
                giftVo.setGiveGifImgUrl(detail.getGiveGifImgUrl());
                giftVo.setEffectPictureUrl(detail.getEffectPictureUrl());
                giftVo.setLuckFlag(detail.getLuckFlag());
                giftVo.setLuckImg(detail.getLuckImg());
                return giftVo;
            }).collect(Collectors.toList());

            categoryVo.setGiftList(giftList);
            return categoryVo;
        }).collect(Collectors.toList());
    }

    public List<AppGiftVo> getBackpackGifts(Long userId) {
        return appUserGiftBackpackMapper.getUserBackPackCategoryGifts(userId);
    }

    /**
     * 送礼给用户(仅礼物分类)多选
     *
     * @param user       用户
     * @param toUserIds  到用户id
     * @param giftId     礼物id
     * @param num        数量
     * @param chatRoomId 聊天室id
     * @return {@link AjaxResult }
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult giveGiftsUsers(AppUserEntity user, List<Integer> toUserIds, Long giftId, Integer num,
                                     Long chatRoomId, Long backType) {
        if (toUserIds.isEmpty()) {
            return AjaxResult.error("请选择要赠送的用户");
        }
        Long userId = user.getId();

        if (toUserIds.contains(userId.intValue())) {
            return AjaxResult.error("房间内不能给自己送礼物");
        }

        if (null == giftId || giftId.intValue() < 1) {
            return AjaxResult.error("请选择礼物");
        }

        if (null == num || num.intValue() < 1) {
            num = 1;
        }
        // 将入参返回给前端
        AppChatRoom chatRoom = null;

        if (null != chatRoomId) {// 聊天室id不为空
            chatRoom = appChatRoomMapper.selectAppChatRoomById(chatRoomId);
            if (chatRoom == null) {
                return AjaxResult.error("当前聊天室不存在");
            }
            if (chatRoom.getStatus().intValue() == AppChatRoomStatusTypeEnums.TYPE0.getId()) {
                return AjaxResult.error("当前聊天室初始化中");
            }
            if (chatRoom.getStatus().intValue() == AppChatRoomStatusTypeEnums.TYPE2.getId()) {
                return AjaxResult.error("当前聊天室已结束");
            }
        }
        // 将 List<Integer> 转换为 List<Long>
        List<Long> toUserIdsLong = toUserIds.stream().map(Integer::longValue).collect(Collectors.toList());
        AjaxResult ajaxResult = new AjaxResult();
        for (Long toUserId : toUserIdsLong) {
            ajaxResult = giveGifts2(user, toUserId, giftId, num, chatRoom, backType);
        }
        return ajaxResult;
    }

    /**
     * 获取所有礼物(APP)
     *
     * @return
     */
    public List<AppGift> getUserActiveGiftWall() {
        return appGiftMapper.selectAppGiftList(new AppGift());
    }

    public TableDataInfo getUserSendGiftList(Integer page, Integer size, Long viewUserId, Long type) {
        Page p = PageHelperUtils.startPage(page, size, true);
        List<AppUserReceiveGiftVo> list = new ArrayList<>();
        if (null != type && type.intValue() == 1) {
            list = appUserGiftBackpackMapper.getUserReceiveGiftList3(viewUserId);
            if (CollectionUtils.isEmpty(list)) {
                list = new ArrayList<>();
                return AjaxResult.getDataTable(list, p.getTotal(), null);
            }
        } else {
            list = appUserGiftBackpackMapper.getUserSendGiftList(viewUserId);
            if (CollectionUtils.isEmpty(list)) {
                list = new ArrayList<>();
            }
            return AjaxResult.getDataTable(list, p.getTotal(), null);
        }
        return AjaxResult.getDataTable(list, p.getTotal(), null);
    }

    /**
     * 礼物赠送的核心处理逻辑
     */
    private GiftGivingResult processGiftGiving(AppUserEntity user, Long toUserId, AppGift gift, Integer num,
                                               AppChatRoom chatRoom, BigDecimal giftTotalPrice, boolean isLuckGift, AppGift luckGift) {
        GiftGivingResult result = new GiftGivingResult();

        AppConfig appConfig = appCommonService.getAppConfig();
        BigDecimal commissionScale = BigDecimal.ZERO;

        if (ObjectUtil.isNotNull(chatRoom) && !ObjectUtils.isEmpty(chatRoom.getId())) {
            AppChatRoom appChatRoom = appChatRoomMapper.selectAppChatRoomById(chatRoom.getId());
            commissionScale = appChatRoom.getHallOwnerCommissionScale();

        }

        // 计算钻石分配
        BigDecimal pointBalance = goldCalculatePoints(giftTotalPrice, toUserId, chatRoom);
        // 计算钻石分配结果
        PointsDistribution pointsDistribution = calculatePointsDistribution(pointBalance, giftTotalPrice,
                commissionScale, appConfig, chatRoom, toUserId);

        // 记录礼物赠送
        UserGiveGiftEntity giftRecord = createGiftRecord(user.getId(), toUserId, gift, num, giftTotalPrice, pointsDistribution,
                chatRoom, isLuckGift);
        userGiveGiftMapper.insert(giftRecord);

        // 记录账单
        recordBillsAndUpdateBalances(user, toUserId, giftRecord, pointsDistribution, chatRoom, luckGift);

        // 更新等级
        updateLevels(user.getId(), toUserId, giftTotalPrice);

        // 推送消息
        if (chatRoom != null && chatRoom.getId() != null) {
            sendNotifications(user, toUserId, gift, num, chatRoom, giftTotalPrice, appConfig);
        }

        result.setGiftRecord(giftRecord);
        result.setPointsDistribution(pointsDistribution);
        return result;
    }

    /**
     * 计算钻石分配结果
     *
     * @param totalPoints     收益的总钻石
     * @param giftTotalPrice  礼品总价
     * @param commissionScale 厅主抽成比例
     * @param appConfig       应用程序配置
     * @param chatRoom        聊天室
     * @param toUserId        接收礼物用户id
     * @return {@link PointsDistribution }
     */
    private PointsDistribution calculatePointsDistribution(BigDecimal totalPoints, BigDecimal giftTotalPrice,
                                                           BigDecimal commissionScale, AppConfig appConfig, AppChatRoom chatRoom, Long toUserId) {
        PointsDistribution distribution = new PointsDistribution();

        // 接收人的钻石收益
        distribution.setReceiverPoints(totalPoints);
        // 厅主的收益
        if (ObjectUtil.isNotNull(chatRoom) && ObjectUtil.isNotEmpty(chatRoom.getId())) {
            // 使用 BigDecimal 进行精确计算
            BigDecimal hallOwnerPoints = giftTotalPrice.multiply(commissionScale)
                    .divide(appConfig.getOnePointsEqGold(), 2, RoundingMode.HALF_UP);
            distribution.setHallOwnerPoints(hallOwnerPoints);
        }
        // 公会长的收益
        BigDecimal guildLeadScale = appGuildMapper.getGuildLeadScale(toUserId);
        if (guildLeadScale != null && guildLeadScale.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal divide = giftTotalPrice.multiply(guildLeadScale).multiply(new BigDecimal("0.5")).multiply(new BigDecimal("0.1"))
                    .divide(appConfig.getOnePointsEqGold(), 2, RoundingMode.HALF_UP);
            distribution.setGuildLeadPoints(divide);
        }
        return distribution;
    }

    /**
     * 创建礼物赠送记录
     */
    private UserGiveGiftEntity createGiftRecord(Long userId, Long toUserId, AppGift gift, Integer num,
                                                BigDecimal giftTotalPrice, PointsDistribution pointsDistribution, AppChatRoom chatRoom,
                                                boolean isLuckGift) {
        UserGiveGiftEntity record = new UserGiveGiftEntity();
        record.setUserId(userId);
        record.setToUserId(toUserId);
        record.setGiftId(gift.getId());
        record.setGiftName(gift.getGiftName());
        record.setGiftNum(num);
        record.setGiftType(1);
        record.setGiftGoldUnitAmount(gift.getMasonryPrice());
        record.setGiftGoldTotalAmount(giftTotalPrice);
        record.setReceivePointTotalAmount(pointsDistribution.getReceiverPoints());
        record.setHallPointTotalAmount(pointsDistribution.getHallOwnerPoints());

        if (isLuckGift) {
            record.setLuckGiftId(gift.getId());
            record.setLuckGiftName(gift.getGiftName());
        }

        if (chatRoom != null && chatRoom.getId() != null) {
            record.setChatRoomId(chatRoom.getId());
            record.setHallOwnerUserId(chatRoom.getHallOwnerUserId());
        }

        record.setCreatedTime(LocalDateTime.now());
        record.setDeleted(false);
        record.setGuildId(appGuildMemberMapper.getGuildIdByUserId(toUserId));

        return record;
    }

    /**
     * 记录账单并更新余额
     */
    private void recordBillsAndUpdateBalances(AppUserEntity user, Long toUserId, UserGiveGiftEntity giftRecord,
                                              PointsDistribution pointsDistribution, AppChatRoom chatRoom, AppGift luckGift) {
        Date time = new Date();

        // 赠送者账单
        AppUserGoldBill senderBill = new AppUserGoldBill();
        senderBill.setUserId(user.getId());
        senderBill.setBillType((long) AppGoldBillTypeEnums.TYPE2.getId());
        senderBill.setObjectId(giftRecord.getId());
        if (luckGift != null) {
            senderBill.setAmount(luckGift.getMasonryPrice().negate());
        } else {
            senderBill.setAmount(giftRecord.getGiftGoldTotalAmount().negate());
        }
        senderBill.setIsDel(WhetherTypeEnum.NO.getName());
        senderBill.setToUserId(toUserId);
        if (chatRoom != null) {
            senderBill.setChatRoomId(chatRoom.getId());
        }
        appUserGoldBillMapper.insertAppUserGoldBill(senderBill);

        // 接收者账单
        AppUserPointsBill receiverBill = new AppUserPointsBill();
        receiverBill.setUserId(toUserId);
        receiverBill.setCreateTime(time);

        // 根据是否在房间设置不同的账单类型
        if (chatRoom != null && chatRoom.getId() != null) {
            // 在房间内收到礼物
            receiverBill.setBillType((long) AppPointsBillTypeEnums.TYPE5.getId());
            receiverBill.setChatRoomId(chatRoom.getId());
        } else {
            // 私聊收到礼物
            receiverBill.setBillType((long) AppPointsBillTypeEnums.TYPE8.getId());
        }

        // 工会成员记录此时的提现比例
        AppGuildMember appGuildMember = appGuildMemberMapper.getGuildMemberByUserId(toUserId);
        if (appGuildMember != null) {
            receiverBill.setGiftIncomeScale(appGuildMember.getGiftIncomeScale());
        }

        receiverBill.setObjectId(giftRecord.getId());
        if (appGuildMember == null || appGuildMember.getGiftIncomeScale().compareTo(new BigDecimal("0.6")) == 0) {
            appUserMapper.addUserPointsBalance(toUserId, pointsDistribution.getReceiverPoints());
            receiverBill.setAmount(pointsDistribution.getReceiverPoints());
        }else {
            receiverBill.setAmount(new BigDecimal("0"));
        }
        receiverBill.setTotalAmount(giftRecord.getGiftGoldTotalAmount());
        receiverBill.setIsDel(WhetherTypeEnum.NO.getName());
        receiverBill.setRemarksMsg(StringUtils.format(AppPointsBillTypeEnums.TYPE13.getDesc(), user.getNickName()));
        receiverBill.setGuildId(appGuildMemberMapper.getGuildIdByUserId(toUserId));
        appUserPointsBillMapper.insertAppUserPointsBill(receiverBill);

        AppUserEntity toUser = appUserMapper.selectAppUserById(toUserId);
        // 厅主账单
        if (chatRoom != null && chatRoom.getHallOwnerUserId() != null
                && pointsDistribution.getHallOwnerPoints().compareTo(BigDecimal.ZERO) > 0) {
            AppUserPointsBill hallOwnerBill = new AppUserPointsBill();
            hallOwnerBill.setUserId(chatRoom.getHallOwnerUserId());
            hallOwnerBill.setCreateTime(time);
            hallOwnerBill.setBillType((long) AppPointsBillTypeEnums.TYPE19.getId());
            hallOwnerBill.setObjectId(giftRecord.getId());
            hallOwnerBill.setAmount(pointsDistribution.getHallOwnerPoints());
            hallOwnerBill.setTotalAmount(giftRecord.getGiftGoldTotalAmount());
            hallOwnerBill.setIsDel(WhetherTypeEnum.NO.getName());
            hallOwnerBill.setRemarksMsg(StringUtils.format(AppPointsBillTypeEnums.TYPE19.getDesc(), toUser.getNickName()));
            receiverBill.setGuildId(appGuildMemberMapper.getGuildIdByUserId(chatRoom.getHallOwnerUserId()));
            appUserPointsBillMapper.insertAppUserPointsBill(hallOwnerBill);

            appUserMapper.addUserPointsBalance(chatRoom.getHallOwnerUserId(), pointsDistribution.getHallOwnerPoints());
        }
        // 公会长账单,只记录在管理后台可查看,查账和提现用
        if (pointsDistribution.getGuildLeadPoints() != null && pointsDistribution.getGuildLeadPoints()
                .compareTo(BigDecimal.ZERO) > 0) {
            Long guildLeadId = appGuildMapper.getGuildLeadIdByUserId(toUserId);
            AppUserPointsBill guildLead = new AppUserPointsBill();
            guildLead.setUserId(guildLeadId);
            guildLead.setCreateTime(time);
            guildLead.setBillType((long) AppPointsBillTypeEnums.TYPE23.getId());
            guildLead.setObjectId(giftRecord.getId());
            guildLead.setAmount(pointsDistribution.getGuildLeadPoints());
            guildLead.setTotalAmount(giftRecord.getGiftGoldTotalAmount());
            guildLead.setIsDel(WhetherTypeEnum.NO.getName());
            guildLead.setRemarksMsg(StringUtils.format(AppPointsBillTypeEnums.TYPE23.getDesc(), toUser.getNickName()));
            receiverBill.setGuildId(appGuildMemberMapper.getGuildIdByUserId(guildLeadId));
            appUserPointsBillMapper.insertAppUserPointsBill(guildLead);

            // 添加公会长钻石
            appUserMapper.addUserPointsBalance(guildLeadId, pointsDistribution.getGuildLeadPoints());
        }

    }

    /**
     * 更新等级信息
     */
    private void updateLevels(Long userId, Long toUserId, BigDecimal giftTotalPrice) {
        Date time = new Date();
        // 升级贡献等级
        appGiftService2.levelUp(userId, giftTotalPrice, time);
        // 升级魅力等级
        appGiftService2.charmlevelUp(toUserId, giftTotalPrice, time);
    }

    /**
     * 发送通知消息
     */
    private void sendNotifications(AppUserEntity user, Long toUserId, AppGift gift, Integer num,
                                   AppChatRoom chatRoom, BigDecimal giftTotalPrice, AppConfig appConfig) {
        // 获取用户信息
        AppViewUserInfoVo toUserInfoVo = appCommonService.getUserInfoByUserId(toUserId);
        String toUserNickName = StringUtils.isBlank(toUserInfoVo.getNickName()) ? toUserInfoVo.getRecodeCode()
                : toUserInfoVo.getNickName();
        AppViewUserInfoVo userInfoVo = appCommonService.getUserInfoByUserId(user.getId());
        String nickName = StringUtils.isBlank(userInfoVo.getNickName()) ? userInfoVo.getRecodeCode()
                : userInfoVo.getNickName();

        // 增加房间热度
        setRoomHeatRedis(chatRoom.getId(), giftTotalPrice);

        privateLetterWebSocket.sendChatRoomRefreshNumberMsg();

        // 更新房间用户礼物金币
        appChatRoomUserMapper.addUserGiftGold(chatRoom.getId(), toUserId, giftTotalPrice);

        // 发送房间消息
        sendChatRoomMessage(chatRoom, user.getId(), toUserId, gift, num, userInfoVo, toUserInfoVo, nickName, toUserNickName);

        // 发送全服消息
        if (gift.getMasonryPrice().compareTo(appConfig.getFullServerFloatingScreenPrice()) >= 0) {
            sendServerWideMessage(chatRoom, user.getId(), toUserId, gift, num, userInfoVo, toUserInfoVo, nickName, toUserNickName);
        }
    }

    /**
     * 发送房间消息
     */
    private void sendChatRoomMessage(AppChatRoom chatRoom, Long userId, Long toUserId, AppGift gift, Integer num,
                                     AppViewUserInfoVo userInfoVo, AppViewUserInfoVo toUserInfoVo, String nickName, String toUserNickName) {
        AppChatRoomPushWebSocketMsgVo msgResult = new AppChatRoomPushWebSocketMsgVo();
        msgResult.setChatRoomId(chatRoom.getId());
        msgResult.setPushType(AppChatRoomWebSocketPushMsgTypeEnums.TYPE16.getId());
        msgResult.setTitle(nickName + "送礼给" + toUserNickName);
        msgResult.setContent(nickName + "送礼给" + toUserNickName);

        Map<String, Object> details = new HashMap<>();
        details.put("sendUserId", userId);
        details.put("sendUserInfo", userInfoVo);
        details.put("deliveryUserId", -1);
        details.put("toUserId", toUserId);
        details.put("toUserInfo", toUserInfoVo);
        details.put("giftInfo", gift);
        details.put("num", num);
        msgResult.setDetails(details);

        try {
            chatRoomWebSocket.sendMsgByChatRoomId(chatRoom.getId(), msgResult);
        } catch (Exception e) {
            log.error("发送房间消息失败", e);
        }
    }

    /**
     * 发送全服消息
     */
    private void sendServerWideMessage(AppChatRoom chatRoom, Long userId, Long toUserId, AppGift gift, Integer num,
                                       AppViewUserInfoVo userInfoVo, AppViewUserInfoVo toUserInfoVo, String nickName, String toUserNickName) {
        Map<String, Object> pushResult = new HashMap<>();
        pushResult.put("pushType", AppPrivateLetterMsgTypeEnums.TYPE14.getId());
        pushResult.put("title", nickName + "送礼给" + toUserNickName);
        pushResult.put("content", nickName + "送礼给" + toUserNickName);
        pushResult.put("chatRoomId", chatRoom.getId());
        pushResult.put("chatRoomName", chatRoom.getName());

        Map<String, Object> details = new HashMap<>();
        details.put("sendUserId", userId);
        details.put("sendUserInfo", userInfoVo);
        details.put("deliveryUserId", -1);
        details.put("toUserId", toUserId);
        details.put("toUserInfo", toUserInfoVo);
        details.put("giftInfo", gift);
        details.put("num", num);
        pushResult.put("details", details);

        try {
            privateLetterWebSocket.toSendPrivateLetterByAllUser(pushResult);
        } catch (Exception e) {
            log.error("发送全服消息失败", e);
        }
    }

    @Data
    static class MsgGiftContent {
        private String content = "[礼物消息]";
        private GiftInfo giftInfo = new GiftInfo();

        @Accessors(chain = true)
        @Data
        static class GiftInfo {
            private Long giftId;
            private String name;
            private String url;
            private Integer num;
            private BigDecimal giftSellPrice;
        }
    }

    /**
     * 用于存储钻石分配的结果
     */
    @Data
    private static class PointsDistribution {
        /**
         * 接收者钻石
         */
        private BigDecimal receiverPoints = BigDecimal.ZERO;
        /**
         * 厅主钻石
         */
        private BigDecimal hallOwnerPoints = BigDecimal.ZERO;
        /**
         * 公会长的礼物收益比例
         */
        private BigDecimal guildLeadPoints = BigDecimal.ZERO;
    }

    /**
     * 用于存储礼物赠送的处理结果
     */
    @Data
    private static class GiftGivingResult {
        /**
         * 礼物记录
         */
        private UserGiveGiftEntity giftRecord;
        /**
         * 钻石分配
         */
        private PointsDistribution pointsDistribution;
    }

    /**
     * 处理数量池抽奖，支持抽奖次数大于奖池数量的情况
     * 
     * @param giftId 礼物ID
     * @param num 抽奖次数
     * @param luckGiftList 盲盒礼物列表
     * @param gift 当前盲盒礼物
     * @param putLuckAmount 投入金额累计
     * @return 抽中的礼物列表
     */
    private List<AppGift> handleNumPoolDrawing(Long giftId, int num, List<AppGift> luckGiftList, AppGift gift, BigDecimal putLuckAmount) {
        String redisKey = LUCK_BOX_NUM_POOL_KEY + giftId;
        String statusKey = LUCK_BOX_POOL_STATUS_KEY + giftId;
        List<AppGift> newGift = new ArrayList<>();
        BigDecimal getLuckAmount = BigDecimal.ZERO;
        
        // 一次性从Redis获取所有需要的数据
        List<AppTurntableGiftVo> allGifts = redisTemplate.opsForList().range(redisKey, 0, -1);

        // 如果数量池为空，自动重置
        if (allGifts == null || allGifts.isEmpty()) {
            appGiftService2.initLuckBoxNumPool(giftId);
            allGifts = redisTemplate.opsForList().range(redisKey, 0, -1);
            if (allGifts == null || allGifts.isEmpty()) {
                throw new AppException("数量池抽奖失败，请联系管理员");
            }
        }

        // 计算当前奖池总数量
        int totalAvailableCount = 0;
        for (AppTurntableGiftVo giftVo : allGifts) {
            totalAvailableCount += giftVo.getNum();
        }

        // 判断抽奖次数是否大于奖池数量
        if (num > totalAvailableCount) {
            // 剩余奖池不够了，先全部抽出
            int remainingDraws = num - totalAvailableCount;
            
            // 复制一份当前奖池中所有礼物，准备抽完
            List<AppTurntableGiftVo> currentPoolGifts = new ArrayList<>(allGifts);
            
            // 抽取当前奖池中所有礼物
            List<AppGift> drawnGifts = drawAllFromCurrentPool(giftId, currentPoolGifts, luckGiftList, gift, putLuckAmount, totalAvailableCount);
            newGift.addAll(drawnGifts);
            
            // 记录已抽出礼物的总金额
            for (AppGift drawnGift : drawnGifts) {
                getLuckAmount = getLuckAmount.add(drawnGift.getMasonryPrice().multiply(drawnGift.getNumber()));
            }
            
            // 重置奖池
            appGiftService2.resetLuckBoxNumPool(giftId);
            
            // 获取重置后的奖池
            List<AppTurntableGiftVo> newPoolGifts = redisTemplate.opsForList().range(redisKey, 0, -1);
            if (newPoolGifts == null || newPoolGifts.isEmpty()) {
                throw new AppException("重置奖池后，奖池为空，请联系管理员");
            }
            
            // 从新奖池中抽取剩余所需数量
            List<AppGift> remainingGifts = drawRemainingFromPool(giftId, newPoolGifts, luckGiftList, gift, putLuckAmount, remainingDraws);
            newGift.addAll(remainingGifts);
            
            // 累加剩余抽取的礼物金额
            for (AppGift drawnGift : remainingGifts) {
                getLuckAmount = getLuckAmount.add(drawnGift.getMasonryPrice().multiply(drawnGift.getNumber()));
            }
        } else {
            // 奖池数量足够，直接抽取指定数量
            // 复制一份原始数据用于后续批量更新
            List<AppTurntableGiftVo> updatedGifts = new ArrayList<>(allGifts);
            
            // 获取并解析Redis中的状态数据
            Object currentAmount = stringRedisTemplate.opsForHash().get(statusKey, "getAmount");
            BigDecimal getAmount = BigDecimal.ZERO;
            if (currentAmount != null) {
                getAmount = new BigDecimal(currentAmount.toString());
            }
            
            Object extractedNum = stringRedisTemplate.opsForHash().get(statusKey, "extractedNum");
            int extractNum = 0;
            if (extractedNum != null) {
                extractNum = Integer.parseInt(extractedNum.toString());
            }
            
            // 统计需要批量更新的礼物数量
            Map<Long, Integer> giftUpdateCountMap = new HashMap<>();
            
            // 在内存中处理所有抽奖逻辑
            for (int i = 0; i < num; i++) {
                // 投入金额累计
                putLuckAmount = putLuckAmount.add(gift.getMasonryPrice());
                
                // 在内存中展开礼物列表
                int totalAvailablePrizes = 0;
                List<AppTurntableGiftVo> expandedGifts = new ArrayList<>();
                
                for (AppTurntableGiftVo giftVo : updatedGifts) {
                    int giftNum = giftVo.getNum();
                    for (int j = 0; j < giftNum; j++) {
                        expandedGifts.add(giftVo);
                        totalAvailablePrizes++;
                    }
                }
                
                if (totalAvailablePrizes == 0) {
                    throw new AppException("数量池礼物数量异常，请联系管理员");
                }
                
                // 随机选择一个礼物
                int randomIndex = new Random().nextInt(totalAvailablePrizes);
                AppTurntableGiftVo selectedGift = expandedGifts.get(randomIndex);
                
                // 在内存中更新礼物数量
                for (int j = 0; j < updatedGifts.size(); j++) {
                    AppTurntableGiftVo giftVo = updatedGifts.get(j);
                    if (giftVo.getGiftId().equals(selectedGift.getGiftId())) {
                        if (giftVo.getNum() > 1) {
                            giftVo.setNum(giftVo.getNum() - 1);
                        } else {
                            updatedGifts.remove(j);
                        }
                        break;
                    }
                }
                
                // 获取抽中的礼物信息
                AppGift drawnGift = luckGiftList.stream()
                        .filter(item -> item.getId().equals(selectedGift.getGiftId()))
                        .findFirst()
                        .orElse(null);
                if (drawnGift == null) {
                    throw new AppException("抽中礼物不存在");
                }
                
                // 创建一个新的礼物对象，避免修改原始对象
                AppGift newDrawnGift = new AppGift();
                BeanUtils.copyProperties(drawnGift, newDrawnGift);
                newDrawnGift.setNumber(BigDecimal.ONE);
                
                // 累计批量更新数据
                giftUpdateCountMap.merge(drawnGift.getId(), 1, Integer::sum);
                
                // 累计产出金额
                getLuckAmount = getLuckAmount.add(drawnGift.getMasonryPrice());
                
                // 更新状态计数
                getAmount = getAmount.add(drawnGift.getMasonryPrice());
                extractNum++;
                
                // 添加到抽中礼物列表
                newGift.add(newDrawnGift);
            }
            
            // 批量更新礼物累计抽出数量
            for (Map.Entry<Long, Integer> entry : giftUpdateCountMap.entrySet()) {
                appGiftMapper.updateLuckTotalNum(entry.getKey(), entry.getValue());
            }
            
            // 统一更新Redis
            // 1. 更新数量池
            redisTemplate.delete(redisKey);
            for (AppTurntableGiftVo giftVo : updatedGifts) {
                redisTemplate.opsForList().rightPush(redisKey, giftVo);
            }
            
            // 2. 更新状态数据
            stringRedisTemplate.opsForHash().put(statusKey, "putAmount", putLuckAmount.toString());
            stringRedisTemplate.opsForHash().put(statusKey, "getAmount", getAmount.toString());
            stringRedisTemplate.opsForHash().put(statusKey, "extractedNum", String.valueOf(extractNum));
        }
        
        // 更新奖池
        gamePoolService.addPutLuckAmount(putLuckAmount, giftId);
        gamePoolService.addGetLuckAmount(getLuckAmount, giftId);
        
        return newGift;
    }
    
    /**
     * 抽取当前奖池中所有剩余的礼物
     *
     * @param giftId 礼物ID
     * @param allGifts 当前奖池所有礼物
     * @param luckGiftList 盲盒礼物列表
     * @param gift 当前盲盒礼物
     * @param putLuckAmount 投入金额累计
     * @param totalAvailableCount 总可用数量
     * @return 抽中的礼物列表
     */
    private List<AppGift> drawAllFromCurrentPool(Long giftId, List<AppTurntableGiftVo> allGifts, List<AppGift> luckGiftList, 
                                                AppGift gift, BigDecimal putLuckAmount, int totalAvailableCount) {
        List<AppGift> drawnGifts = new ArrayList<>();
        String statusKey = LUCK_BOX_POOL_STATUS_KEY + giftId;
        BigDecimal getAmount = BigDecimal.ZERO;
        int extractNum = 0;
        
        // 获取并解析Redis中的状态数据
        Object currentAmount = stringRedisTemplate.opsForHash().get(statusKey, "getAmount");
        if (currentAmount != null) {
            getAmount = new BigDecimal(currentAmount.toString());
        }
        
        Object extractedNum = stringRedisTemplate.opsForHash().get(statusKey, "extractedNum");
        if (extractedNum != null) {
            extractNum = Integer.parseInt(extractedNum.toString());
        }
        
        // 统计需要批量更新的礼物数量
        Map<Long, Integer> giftUpdateCountMap = new HashMap<>();
        BigDecimal totalGetAmount = BigDecimal.ZERO;
        
        // 展开所有礼物，以便随机抽取
        List<AppTurntableGiftVo> expandedGifts = new ArrayList<>();
        for (AppTurntableGiftVo giftVo : allGifts) {
            for (int i = 0; i < giftVo.getNum(); i++) {
                expandedGifts.add(giftVo);
            }
        }
        
        // 随机打乱礼物列表
        Collections.shuffle(expandedGifts);
        
        // 抽取所有礼物
        for (AppTurntableGiftVo giftVo : expandedGifts) {
            // 获取抽中的礼物信息
            AppGift drawnGift = luckGiftList.stream()
                    .filter(item -> item.getId().equals(giftVo.getGiftId()))
                    .findFirst()
                    .orElse(null);
            
            if (drawnGift == null) {
                log.error("抽中礼物不存在，礼物ID: {}", giftVo.getGiftId());
                continue;
            }
            
            // 创建一个新的礼物对象，避免修改原始对象
            AppGift newDrawnGift = new AppGift();
            BeanUtils.copyProperties(drawnGift, newDrawnGift);
            newDrawnGift.setNumber(BigDecimal.ONE);
            
            // 累计批量更新数据
            giftUpdateCountMap.merge(drawnGift.getId(), 1, Integer::sum);
            
            // 累计产出金额
            totalGetAmount = totalGetAmount.add(drawnGift.getMasonryPrice());
            
            // 添加到抽中礼物列表
            drawnGifts.add(newDrawnGift);
            
            // 投入金额累计
            putLuckAmount = putLuckAmount.add(gift.getMasonryPrice());
        }
        
        // 批量更新礼物累计抽出数量
        for (Map.Entry<Long, Integer> entry : giftUpdateCountMap.entrySet()) {
            appGiftMapper.updateLuckTotalNum(entry.getKey(), entry.getValue());
        }
        
        // 更新状态数据
        getAmount = getAmount.add(totalGetAmount);
        extractNum += expandedGifts.size();
        
        // 更新Redis状态数据
        stringRedisTemplate.opsForHash().put(statusKey, "putAmount", putLuckAmount.toString());
        stringRedisTemplate.opsForHash().put(statusKey, "getAmount", getAmount.toString());
        stringRedisTemplate.opsForHash().put(statusKey, "extractedNum", String.valueOf(extractNum));
        
        return drawnGifts;
    }
    
    /**
     * 从重置后的奖池中抽取剩余所需数量
     *
     * @param giftId 礼物ID
     * @param newPoolGifts 新奖池礼物列表
     * @param luckGiftList 盲盒礼物列表
     * @param gift 当前盲盒礼物
     * @param putLuckAmount 投入金额累计
     * @param remainingDraws 剩余需要抽取的次数
     * @return 抽中的礼物列表
     */
    private List<AppGift> drawRemainingFromPool(Long giftId, List<AppTurntableGiftVo> newPoolGifts, List<AppGift> luckGiftList, 
                                               AppGift gift, BigDecimal putLuckAmount, int remainingDraws) {
        List<AppGift> drawnGifts = new ArrayList<>();
        String redisKey = LUCK_BOX_NUM_POOL_KEY + giftId;
        String statusKey = LUCK_BOX_POOL_STATUS_KEY + giftId;
        
        // 复制一份原始数据用于后续批量更新
        List<AppTurntableGiftVo> updatedGifts = new ArrayList<>(newPoolGifts);
        
        // 获取并解析Redis中的状态数据
        Object currentAmount = stringRedisTemplate.opsForHash().get(statusKey, "getAmount");
        BigDecimal getAmount = BigDecimal.ZERO;
        if (currentAmount != null) {
            getAmount = new BigDecimal(currentAmount.toString());
        }
        
        Object extractedNum = stringRedisTemplate.opsForHash().get(statusKey, "extractedNum");
        int extractNum = 0;
        if (extractedNum != null) {
            extractNum = Integer.parseInt(extractedNum.toString());
        }
        
        // 统计需要批量更新的礼物数量
        Map<Long, Integer> giftUpdateCountMap = new HashMap<>();
        
        // 在内存中处理所有抽奖逻辑
        for (int i = 0; i < remainingDraws; i++) {
            // 投入金额累计
            putLuckAmount = putLuckAmount.add(gift.getMasonryPrice());
            
            // 在内存中展开礼物列表
            int totalAvailablePrizes = 0;
            List<AppTurntableGiftVo> expandedGifts = new ArrayList<>();
            
            for (AppTurntableGiftVo giftVo : updatedGifts) {
                int giftNum = giftVo.getNum();
                for (int j = 0; j < giftNum; j++) {
                    expandedGifts.add(giftVo);
                    totalAvailablePrizes++;
                }
            }
            
            if (totalAvailablePrizes == 0) {
                throw new AppException("新奖池礼物数量异常，请联系管理员");
            }
            
            // 随机选择一个礼物
            int randomIndex = new Random().nextInt(totalAvailablePrizes);
            AppTurntableGiftVo selectedGift = expandedGifts.get(randomIndex);
            
            // 在内存中更新礼物数量
            for (int j = 0; j < updatedGifts.size(); j++) {
                AppTurntableGiftVo giftVo = updatedGifts.get(j);
                if (giftVo.getGiftId().equals(selectedGift.getGiftId())) {
                    if (giftVo.getNum() > 1) {
                        giftVo.setNum(giftVo.getNum() - 1);
                    } else {
                        updatedGifts.remove(j);
                    }
                    break;
                }
            }
            
            // 获取抽中的礼物信息
            AppGift drawnGift = luckGiftList.stream()
                    .filter(item -> item.getId().equals(selectedGift.getGiftId()))
                    .findFirst()
                    .orElse(null);
            if (drawnGift == null) {
                throw new AppException("抽中礼物不存在");
            }
            
            // 创建一个新的礼物对象，避免修改原始对象
            AppGift newDrawnGift = new AppGift();
            BeanUtils.copyProperties(drawnGift, newDrawnGift);
            newDrawnGift.setNumber(BigDecimal.ONE);
            
            // 累计批量更新数据
            giftUpdateCountMap.merge(drawnGift.getId(), 1, Integer::sum);
            
            // 累计产出金额
            getAmount = getAmount.add(drawnGift.getMasonryPrice());
            extractNum++;
            
            // 添加到抽中礼物列表
            drawnGifts.add(newDrawnGift);
        }
        
        // 批量更新礼物累计抽出数量
        for (Map.Entry<Long, Integer> entry : giftUpdateCountMap.entrySet()) {
            appGiftMapper.updateLuckTotalNum(entry.getKey(), entry.getValue());
        }
        
        // 统一更新Redis
        // 1. 更新数量池
        redisTemplate.delete(redisKey);
        for (AppTurntableGiftVo giftVo : updatedGifts) {
            redisTemplate.opsForList().rightPush(redisKey, giftVo);
        }
        
        // 2. 更新状态数据
        stringRedisTemplate.opsForHash().put(statusKey, "putAmount", putLuckAmount.toString());
        stringRedisTemplate.opsForHash().put(statusKey, "getAmount", getAmount.toString());
        stringRedisTemplate.opsForHash().put(statusKey, "extractedNum", String.valueOf(extractNum));
        
        return drawnGifts;
    }
}
