<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppUserCharmGradeMapper">

    <resultMap type="AppUserCharmGrade" id="AppUserCharmGradeResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="currentGradeId" column="current_grade_id"/>
        <result property="sumGoldValue" column="sum_gold_value"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAppUserCharmGradeVo">
        select id, user_id, current_grade_id, sum_gold_value, create_time, update_time
        from app_user_charm_grade
    </sql>

    <select id="selectAppUserCharmGradeList" parameterType="AppUserCharmGrade" resultMap="AppUserCharmGradeResult">
        <include refid="selectAppUserCharmGradeVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="currentGradeId != null ">and current_grade_id = #{currentGradeId}</if>
            <if test="sumGoldValue != null ">and sum_gold_value = #{sumGoldValue}</if>
        </where>
    </select>

    <select id="selectAppUserCharmGradeById" parameterType="Long" resultMap="AppUserCharmGradeResult">
        <include refid="selectAppUserCharmGradeVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppUserCharmGrade" parameterType="AppUserCharmGrade" useGeneratedKeys="true" keyProperty="id">
        insert into app_user_charm_grade
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="currentGradeId != null">current_grade_id,</if>
            <if test="sumGoldValue != null">sum_gold_value,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="currentGradeId != null">#{currentGradeId},</if>
            <if test="sumGoldValue != null">#{sumGoldValue},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateAppUserCharmGrade" parameterType="AppUserCharmGrade">
        update app_user_charm_grade
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="currentGradeId != null">current_grade_id = #{currentGradeId},</if>
            <if test="sumGoldValue != null">sum_gold_value = #{sumGoldValue},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppUserCharmGradeById" parameterType="Long">
        delete
        from app_user_charm_grade
        where id = #{id}
    </delete>

    <delete id="deleteAppUserCharmGradeByIds" parameterType="String">
        delete from app_user_charm_grade where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getUserCharmGrade" parameterType="java.lang.Long"
            resultType="com.hzy.core.model.vo.app.AppUserTitleNobilityVo">
        select
        cg.id as currentGradeId,
        cg.name as currentName,
        cg.ico_url as currentIcoUrl,
        cg.grade_size as currentGradeSize,
        ug.sum_gold_value as currentGradeValue
        from app_user_charm_grade ug,
        app_charm_grade_config cg
        where cg.id = ug.current_grade_id and ug.user_id = #{userId}
        order by ug.id desc limit 0, 1
    </select>


    <select id="selectAppUserCharmGradeByUserId" parameterType="Long" resultMap="AppUserCharmGradeResult">
        <include refid="selectAppUserCharmGradeVo"/>
        where user_id = #{userId}
    </select>
</mapper>