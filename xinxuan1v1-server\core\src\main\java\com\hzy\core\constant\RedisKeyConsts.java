package com.hzy.core.constant;

/**
 * redis key 常量
 *
 * <AUTHOR>
 */
public interface RedisKeyConsts {


    /**
     * 订单支付15分钟有效
     */
    int orderPayValidMinute = 15;

    /**
     * 订单支付超时监听的key
     */
    String ORDER_PAY_TIME_OUT_LISTENING_KEY = "app:order:pay:timeout";


    /**
     * 聊天室连接断开超时监听的key
     */
    String CHAT_ROOM_WEB_SOCKET_CLOSE_KEY = "app:chatRoom:webSocket:timeout";
    
    /**
     * 关注房主弹窗提醒前缀
     */
    String CHAT_ROOM_FOLLOW_OWNER_ALTER_PREFIX = "app:chatRoom:alter:";

    /**
     * 关注房主停留时间计时key，格式为：app:chatRoom:alter:timer:{roomId}:{userId}
     */
    String CHAT_ROOM_FOLLOW_OWNER_TIMER_KEY = "app:chatRoom:alter:timer:%s:%s";
    
    /**
     * 关注房主已提醒标记key，格式为：app:chatRoom:alter:notified:{roomId}:{userId}
     */
    String CHAT_ROOM_FOLLOW_OWNER_NOTIFIED_KEY = "app:chatRoom:alter:notified:%s:%s";
    
    /**
     * 1v1语音通话ws心跳监听的key
     */
    String XINXUAN_1V1_VOICE_KEEP_KEY = "voice:1v1:keep:";
    /**
     * 监听心跳时间:秒
     */
    // 心跳监听间隔，适当增大缓冲避免网络抖动导致误触
    int XINXUAN_1V1_VOICE_KEEP_TIME = 15;


    String createMotorcadeKey = "app:createMotorcadeKey:timeout";


    /**
     * 私信连接断开超时监听的key
     */
    String PRIVATE_LETTER_WEB_SOCKET_CLOSE_KEY = "app:privateLetter:webSocket:timeout";

    /**
     * 通话邀请超时时间-秒
     */
    int initTelTimeout = 45;

    /**
     * 通话邀请超时监听的key
     */
    String INITIATE_COMMUNICATE_TELEPHONE_TIME_OUT_LISTENING_KEY = "app:initiateCommunicateTelephone:timeout";


    /**
     * app配置信息key
     */
    String appConfigKey = "AppConfigInfoKey-0730";


    /**
     * 敏感词列表key
     */
    String app_sensitive_lexicon_list_key = "app:sensitive:lexicon:list";


    /**
     * 禁止创建房间key
     */
    String DISABLE_CREATE_CHAT_ROOM_KEY = "app:disable:create:chatRoom";


    String guild_quit_key = "app:guild:quit:key:";


    /**
     * 技能订单支付超时监听的key
     */
    String SKILL_ORDER_PAY_TIME_OUT_LISTENING_KEY = "app:skill:order:pay:timeout";

    /**
     * 通话违规挂断
     */
    String APP_HANG_UP = "app:hang:up-";


}
