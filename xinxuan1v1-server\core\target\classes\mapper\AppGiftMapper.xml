<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppGiftMapper">

    <resultMap type="AppGift" id="AppGiftResult">
        <result property="id" column="id"/>
        <result property="giftName" column="gift_name"/>
        <result property="categoryId" column="category_id"/>
        <result property="isDel" column="is_del"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="masonryPrice" column="masonry_price"/>
        <result property="imgUrl" column="img_url"/>
        <result property="giveGifImgUrl" column="give_gif_img_url"/>
        <result property="sales" column="sales"/>
        <result property="effectPictureUrl" column="effect_picture_url"/>
        <result property="sort" column="sort"/>
        <result property="bindPersonalDressingIds" column="bind_personal_dressing_ids"/>
    </resultMap>

    <sql id="selectAppGiftVo">
        select id,
        gift_name,
        category_id,
        is_del,
        create_time,
        update_time,
        create_by,
        update_by,
        masonry_price,
        img_url,
        give_gif_img_url,
        sales,
        effect_picture_url,
        sort,
        bind_personal_dressing_ids,
        luck_flag,
        luck_id,
        luck_weight,
        luck_img,
        luck_pool_type,
        luck_num_init,
        luck_total_num,
        rate
        from app_gift
    </sql>

    <select id="selectAppGiftList" parameterType="AppGift" resultMap="AppGiftResult">
        select gi.id,
        gi.gift_name,
        gi.category_id,
        gi.is_del,
        gi.create_time,
        gi.update_time,
        gi.create_by,
        gi.update_by,
        gi.masonry_price,
        gi.img_url,
        gi.give_gif_img_url,
        gi.sales,
        gi.effect_picture_url,
        gi.sort,
        gi.bind_personal_dressing_ids,
        ca.category_name as categoryName
        from app_gift gi,
        app_gift_category ca
        where gi.is_del = false and ca.id = gi.category_id
        and ca.deleted = false
        <if test="giftName != null  and giftName != ''">and gi.gift_name like concat('%', #{giftName}, '%')</if>
        <if test="categoryId != null ">and gi.category_id = #{categoryId}</if>
        <if test="queryIdList!=null and queryIdList.size()>0">
            and gi.id in
            <foreach item="queryId" collection="queryIdList" open="(" separator="," close=")">
                #{queryId}
            </foreach>
        </if>
        <if test="notIdList!=null and notIdList.size()>0">
            and gi.id not in
            <foreach item="notId" collection="notIdList" open="(" separator="," close=")">
                #{notId}
            </foreach>
        </if>
        <if test="sortType==0">
            order by gi.masonry_price desc
        </if>
        <if test="sortType==1">
            order by gi.masonry_price asc
        </if>
        <if test="sortType==null">
            order by gi.masonry_price desc, ca.created_time desc, gi.sort asc, gi.create_time desc
        </if>
    </select>

    <select id="selectAppGiftById" parameterType="Long" resultMap="AppGiftResult">
        <include refid="selectAppGiftVo"/>
        where id = #{id} and is_del=false
    </select>

    <select id="getGiftById" parameterType="Long" resultMap="AppGiftResult">
        <include refid="selectAppGiftVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppGift" parameterType="AppGift" useGeneratedKeys="true" keyProperty="id">
        insert into app_gift
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="giftName != null">gift_name,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="masonryPrice != null">masonry_price,</if>
            <if test="imgUrl != null">img_url,</if>
            <if test="giveGifImgUrl != null">give_gif_img_url,</if>
            <if test="sales != null">sales,</if>
            <if test="effectPictureUrl != null">effect_picture_url,</if>
            <if test="sort != null">sort,</if>
            <if test="bindPersonalDressingIds != null">bind_personal_dressing_ids,</if>
            <if test="luckFlag != null">luck_flag,</if>
            <if test="luckId != null">luck_id,</if>
            <if test="luckWeight != null">luck_weight,</if>
            <if test="luckImg != null">luck_img,</if>
            <if test="luckPoolType != null">luck_pool_type,</if>
            <if test="luckNumInit != null">luck_num_init,</if>
            <if test="luckTotalNum != null">luck_total_num,</if>
            <if test="rate != null">rate,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="giftName != null">#{giftName},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="masonryPrice != null">#{masonryPrice},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="giveGifImgUrl != null">#{giveGifImgUrl},</if>
            <if test="sales != null">#{sales},</if>
            <if test="effectPictureUrl != null">#{effectPictureUrl},</if>
            <if test="sort != null">#{sort},</if>
            <if test="bindPersonalDressingIds != null">#{bindPersonalDressingIds},</if>
            <if test="luckFlag != null">#{luckFlag},</if>
            <if test="luckId != null">#{luckId},</if>
            <if test="luckWeight != null">#{luckWeight},</if>
            <if test="luckImg != null">#{luckImg},</if>
            <if test="luckPoolType != null">#{luckPoolType},</if>
            <if test="luckNumInit != null">#{luckNumInit},</if>
            <if test="luckTotalNum != null">#{luckTotalNum},</if>
            <if test="rate != null">#{rate},</if>
        </trim>
    </insert>


    <update id="updateAppGift" parameterType="AppGift">
        update app_gift
        <trim prefix="SET" suffixOverrides=",">
            <if test="giftName != null">gift_name = #{giftName},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="masonryPrice != null">masonry_price = #{masonryPrice},</if>
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
            <if test="giveGifImgUrl != null">give_gif_img_url = #{giveGifImgUrl},</if>
            <if test="sales != null">sales = #{sales},</if>
            <if test="effectPictureUrl != null">effect_picture_url = #{effectPictureUrl},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="bindPersonalDressingIds != null">bind_personal_dressing_ids = #{bindPersonalDressingIds},</if>
            <if test="luckFlag != null">luck_flag = #{luckFlag},</if>
            <if test="luckId != null">luck_id = #{luckId},</if>
            <if test="luckWeight != null">luck_weight = #{luckWeight},</if>
            <if test="luckImg != null">luck_img = #{luckImg},</if>
            <if test="luckPoolType != null">luck_pool_type = #{luckPoolType},</if>
            <if test="luckNumInit != null">luck_num_init = #{luckNumInit},</if>
            <if test="luckTotalNum != null">luck_total_num = #{luckTotalNum},</if>
            <if test="rate != null">rate = #{rate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppGiftByIds" parameterType="String">
        delete from app_gift where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <resultMap type="com.hzy.core.model.vo.app.AppGiftCategoryVo" id="AppGiftCategoryVoResult">
        <result property="categoryId" column="categoryId"/>
        <result property="categoryName" column="categoryName"/>
        <result property="icoUrl" column="icoUrl"/>
        <collection property="giftList" javaType="java.util.List" resultMap="AppGiftVoResult"/>
    </resultMap>

    <resultMap type="com.hzy.core.model.vo.app.AppGiftVo" id="AppGiftVoResult">
        <result property="giftId" column="giftId"/>
        <result property="giftName" column="giftName"/>
        <result property="masonryPrice" column="masonryPrice"/>
        <result property="imgUrl" column="imgUrl"/>
        <result property="giveGifImgUrl" column="giveGifImgUrl"/>
        <result property="effectPictureUrl" column="effectPictureUrl"/>
        <result property="luckFlag" column="luckFlag"/>
        <result property="luckImg" column="luckImg"/>
    </resultMap>


    <select id="getAppGiftAndCategoryList" resultMap="AppGiftCategoryVoResult" parameterType="java.lang.Boolean">
        select ca.id as categoryId,
        ca.category_name as categoryName,
        ca.ico_url as icoUrl,
        gi.id as giftId,
        gi.gift_name giftName,
        gi.masonry_price as masonryPrice,
        gi.img_url as imgUrl,
        gi.give_gif_img_url as giveGifImgUrl,
        gi.effect_picture_url as effectPictureUrl
        from app_gift_category ca
        left join app_gift gi on (gi.is_del = false and ca.id = gi.category_id
        <if test="!isQueryChestGift">
            and gi.id not in (121,120,119,81,80,79)
        </if>
        )
        where ca.deleted = false
        <if test="!isQueryChestGift">
            and ca.id!=12
        </if>
        order by ca.sort asc, ca.id desc, gi.masonry_price asc, gi.id desc
    </select>

    <update id="addSales">
        update app_gift
        set sales=sales + #{num}
        where id = #{giftId}
    </update>

    <select id="getGiftNameByGiftIdList" resultType="com.hzy.core.model.vo.app.AppOpenGiftVo">
        select
        g.id as giftId,
        g.gift_name as giftName,
        g.masonry_price as giftPrice,
        g.img_url as giftUrl
        from app_gift g
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.giftId}
        </foreach>
    </select>


    <select id="getGiftNameByGiftIdList2" resultType="com.hzy.core.model.vo.app.AppTurntableGiftVo">
        select
        g.id as giftId,
        g.gift_name as giftName,
        g.masonry_price as giftPrice,
        g.img_url as giftImgUrl,
        g.masonry_price as giftPrice
        from app_gift g
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.giftId}
        </foreach>
    </select>


    <select id="getGiftName" resultType="java.lang.String" parameterType="java.lang.Long">
        select gift_name
        from app_gift
        where id = #{id}
    </select>

    <select id="getCategoryGiftList" resultType="com.hzy.core.model.vo.app.AppGiftDetailVo">
        select ca.id as categoryId,
        ca.category_name as categoryName,
        ca.ico_url as icoUrl,
        gi.id as giftId,
        gi.gift_name giftName,
        gi.masonry_price as masonryPrice,
        gi.img_url as imgUrl,
        gi.give_gif_img_url as giveGifImgUrl,
        gi.effect_picture_url as effectPictureUrl,
        gi.luck_flag as luckFlag,
        gi.luck_img as luckImg
        from app_gift_category ca
        left join app_gift gi on (gi.is_del = false and ca.id = gi.category_id)
        where ca.deleted = false
        and ca.id > 0
        order by ca.sort asc, gi.luck_flag desc, ca.id desc, gi.masonry_price asc, gi.id desc
    </select>
    <select id="selectAppGiftByName" resultType="com.hzy.core.entity.AppGift" parameterType="java.lang.String">
        select id,
        gift_name as giftName,
        category_id as categoryId,
        is_del,
        create_time as createTime,
        update_time as updateTime,
        create_by as createBy,
        update_by as updateBy,
        masonry_price as masonryPrice,
        img_url as imgUrl,
        give_gif_img_url as giveGifImgUrl,
        sales,
        effect_picture_url as effectPictureUrl,
        sort,
        bind_personal_dressing_ids as bindPersonalDressingIds
        from app_gift
        where gift_name = #{giftName}
        and is_del = 0
    </select>

    <select id="getGiftByName" resultType="com.hzy.core.entity.AppGift">
        select id,
        gift_name,
        category_id,
        is_del,
        create_time,
        update_time,
        create_by,
        update_by,
        masonry_price,
        img_url,
        give_gif_img_url,
        sales,
        effect_picture_url,
        sort,
        bind_personal_dressing_ids
        from app_gift
        where gift_name = #{giftName}
        LIMIT 1
    </select>
    <select id="queryAppGiftByName" resultType="com.hzy.core.entity.AppGift"
            parameterType="com.hzy.core.entity.AppGift">
        select id,
        gift_name as giftName,
        category_id as categoryId,
        masonry_price as masonryPrice,
        sales,
        sort
        from app_gift
        where gift_name = #{giftName}
        and is_del = 0
        and pool_id = #{jjcId}
    </select>
    <select id="getAppGiftAndCategoryList2" resultType="com.hzy.core.model.vo.app.AppGiftVo">
        select gpgr.id,
        ag.id as giftId,
        gpgr.bind_gift_id,
        ag.gift_name as giftName,
        ag.masonry_price as masonryPrice,
        ag.img_url as giftImgUrl,
        ag.give_gif_img_url as giftGiveGifImgUrl,
        ag.effect_picture_url as giftEffectPictureUrl,
        gpgr.pool_id,
        gup.name as name,
        gc.name AS game_name,
        gc.id AS game_id,
        gup.id as jjcId,
        ag.img_url as imgUrl
        from app_gift ag
        left join game_pool_gift_relation gpgr on ag.id = gpgr.bind_gift_id and gpgr.deleted = false
        left join game_upgrade_pool gup on gpgr.pool_id = gup.id and gup.deleted = false
        left join game_config gc on gc.id = gup.game_id and gc.deleted = false
        where ag.category_id = -1
        and ag.is_del = false
        <if test="giftName != null and giftName != ''">
            AND ag.gift_name LIKE CONCAT('%', #{giftName}, '%')
        </if>
        <if test="gameId != null ">
            AND gc.id=#{gameId}
        </if>
        <if test="jjcId != null">
            AND gup.id = #{jjcId}
        </if>
        order by ag.masonry_price desc
    </select>

    <update id="updateLuckTotalNum">
        update app_gift
        set luck_total_num = IFNULL(luck_total_num, 0) + #{count}
        where id = #{giftId}
    </update>

</mapper>