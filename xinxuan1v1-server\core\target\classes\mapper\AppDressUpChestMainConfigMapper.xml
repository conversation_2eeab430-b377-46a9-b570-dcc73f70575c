<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppDressUpChestMainConfigMapper">

    <resultMap type="AppDressUpChestMainConfig" id="AppDressUpChestMainConfigResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
        <result property="chestName" column="chest_name"/>
        <result property="price" column="price"/>
        <result property="giveGifImgUrl" column="give_gif_img_url"/>
        <result property="imgUrl" column="img_url"/>
    </resultMap>

    <sql id="selectAppDressUpChestMainConfigVo">
        select id, create_time, update_time, create_by, update_by, is_del, chest_name, price, give_gif_img_url, img_url
        from app_dress_up_chest_main_config
    </sql>

    <select id="selectAppDressUpChestMainConfigList" parameterType="AppDressUpChestMainConfig"
            resultMap="AppDressUpChestMainConfigResult">
        select
        c.id,
        c.create_time,
        c.update_time,
        c.create_by,
        c.update_by,
        c.is_del,
        c.chest_name,
        c.price,
        c.give_gif_img_url,
        c.img_url,
        (select count(*) from app_dress_up_chest_config cc where cc.is_del=false and cc.chest_id=c.id) as sumCount
        from app_dress_up_chest_main_config c
        where c.is_del=false
        <if test="chestName != null ">and c.chest_name like concat('%', #{chestName}, '%')</if>
        order by c.id desc
    </select>

    <select id="selectAppDressUpChestMainConfigById" parameterType="Long" resultMap="AppDressUpChestMainConfigResult">
        <include refid="selectAppDressUpChestMainConfigVo"/>
        where id = #{id} and is_del=false
    </select>

    <insert id="insertAppDressUpChestMainConfig" parameterType="AppDressUpChestMainConfig" useGeneratedKeys="true"
            keyProperty="id">
        insert into app_dress_up_chest_main_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
            <if test="chestName != null">chest_name,</if>
            <if test="price != null">price,</if>
            <if test="giveGifImgUrl != null">give_gif_img_url,</if>
            <if test="imgUrl != null">img_url,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="chestName != null">#{chestName},</if>
            <if test="price != null">#{price},</if>
            <if test="giveGifImgUrl != null">#{giveGifImgUrl},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
        </trim>
    </insert>

    <update id="updateAppDressUpChestMainConfig" parameterType="AppDressUpChestMainConfig">
        update app_dress_up_chest_main_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="chestName != null">chest_name = #{chestName},</if>
            <if test="price != null">price = #{price},</if>
            <if test="giveGifImgUrl != null">give_gif_img_url = #{giveGifImgUrl},</if>
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteAppDressUpChestMainConfigById" parameterType="Long">
        update app_dress_up_chest_main_config set is_del=true where id = #{id}
    </update>

    <delete id="deleteAppDressUpChestMainConfigByIds" parameterType="String">
        delete from app_dress_up_chest_main_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="getDressUpChestMainConfigList" resultType="com.hzy.core.model.vo.app.AppGiftVo">
        select
        c.id as giftId,
        c.chest_name as giftName,
        c.price as masonryPrice,
        c.img_url as imgUrl,
        c.give_gif_img_url as giveGifImgUrl,
        c.give_gif_img_url as effectPictureUrl,
        1 as isDressUp
        from app_dress_up_chest_main_config c
        where c.is_del=false
        order by c.price asc,c.id desc
    </select>
</mapper>