<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppUpgradeMapper">

    <resultMap type="AppUpgrade" id="AppUpgradeResult">
        <result property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="buildVersion" column="build_version"/>
        <result property="isMandatory" column="is_mandatory"/>
        <result property="url" column="url"/>
        <result property="type" column="type"/>
        <result property="content" column="content"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAppUpgradeVo">
        select id, version, build_version, is_mandatory, url, `type`, content, create_time, update_time from app_upgrade
    </sql>



    <select id="selectAppUpgradeById" parameterType="Long" resultMap="AppUpgradeResult">
        <include refid="selectAppUpgradeVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppUpgrade" parameterType="AppUpgrade" useGeneratedKeys="true" keyProperty="id">
        insert into app_upgrade
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="version != null">version,</if>
            <if test="buildVersion != null">build_version,</if>
            <if test="isMandatory != null">is_mandatory,</if>
            <if test="url != null">url,</if>
            <if test="type != null">`type`,</if>
            <if test="content != null">content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="version != null">#{version},</if>
            <if test="buildVersion != null">#{buildVersion},</if>
            <if test="isMandatory != null">#{isMandatory},</if>
            <if test="url != null">#{url},</if>
            <if test="type != null">#{type},</if>
            <if test="content != null">#{content},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateAppUpgrade" parameterType="AppUpgrade">
        update app_upgrade
        <trim prefix="SET" suffixOverrides=",">
            <if test="version != null">version = #{version},</if>
            <if test="buildVersion != null">build_version = #{buildVersion},</if>
            <if test="isMandatory != null">is_mandatory = #{isMandatory},</if>
            <if test="url != null">url = #{url},</if>
            <if test="type != null">`type` = #{type},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppUpgradeById" parameterType="Long">
        delete from app_upgrade where id = #{id}
    </delete>

    <delete id="deleteAppUpgradeByIds" parameterType="String">
        delete from app_upgrade where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getAppVersion" resultMap="AppUpgradeResult">
        select id, version, build_version, is_mandatory, url, `type`, content, create_time, update_time,
        build_version as SoftType,
        url as downloadUrl,
        content as description
        from app_upgrade
        where `type` = #{type}
        order by build_version desc
        limit 0,1
    </select>
    <select id="selectAppUpgradeList" resultType="com.hzy.core.entity.AppUpgrade">
        select id, version, build_version, is_mandatory, url, `type`, content, create_time, update_time
        from app_upgrade
        order by id desc

    </select>
</mapper>