<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.SysUserWhitelistMapper">
    <insert id="insertSysUserWhitelist" parameterType="com.hzy.core.entity.SysUserWhitelistEntity">
        insert into sys_user_whitelist(user_ip, expiration_time,created_time,updated_time)
        values ( #{userIp}, #{expirationTime},#{createdTime},#{updatedTime})
    </insert>

    <select id="isInWhiteList" resultType="java.lang.Integer" parameterType="java.lang.String">
        select count(1) from sys_user_whitelist where user_ip = #{ip} and deleted = 0
    </select>
    <select id="selectLoginUser" resultType="com.hzy.core.entity.SysUserWhitelistEntity"
            parameterType="java.lang.String">
        select * from sys_user_whitelist where user_ip = #{ip} LIMIT 0, 1
    </select>
</mapper>