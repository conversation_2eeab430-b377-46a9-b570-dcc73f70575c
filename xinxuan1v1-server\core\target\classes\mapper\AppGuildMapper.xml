<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppGuildMapper">

    <resultMap type="AppGuild" id="AppGuildResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDel" column="is_del"/>
        <result property="userId" column="user_id"/>
        <result property="avatar" column="avatar"/>
        <result property="guildName" column="guild_name"/>
        <result property="intro" column="intro"/>
        <result property="guildNumber" column="guild_number"/>
        <result property="toBeSettled" column="to_be_settled"/>
        <result property="isTop" column="is_top"/>
        <result property="sysUserId" column="sys_user_id"/>
    </resultMap>

    <sql id="selectAppGuildVo">
        select id, create_time, update_time, create_by, update_by, is_del, user_id, avatar, guild_name, intro,
        guild_number,to_be_settled,is_top,guild_commission_scale
        from app_guild
    </sql>

    <select id="selectAppGuildList" parameterType="AppGuild" resultMap="AppGuildResult">
        select g.id, g.create_time, g.update_time, g.create_by, g.update_by, g.is_del, g.user_id, g.avatar,
        g.guild_name, g.intro,
        g.guild_number,
        u.id as userId,
        u.recode_code as guildLeaderUserRecodeCode,
        u.nick_name as guildLeaderUserNickName,
        u.head_portrait as guildLeaderUserAvatar,
        u.phone as guildLeaderUserPhone,
        g.to_be_settled,
        g.is_top
        from app_guild g,
        app_user u
        where g.is_del=false
        and u.id=g.user_id
        <if test="userId != null ">and g.user_id = #{userId}</if>
        <if test="guildName != null  and guildName != ''">and g.guild_name like concat('%', #{guildName}, '%')</if>
        <if test="guildNumber != null  and guildNumber != ''">and g.guild_number = #{guildNumber}</if>
        <if test="guildLeaderUserPhone != null  and guildLeaderUserPhone != ''">and u.phone = #{guildLeaderUserPhone}
        </if>
        <if test="guildLeaderUserRecodeCode != null  and guildLeaderUserRecodeCode != ''">and u.id =
            #{guildLeaderUserRecodeCode}
        </if>
        <if test="guildLeaderUserNickName != null  and guildLeaderUserNickName != ''">and u.nick_name like concat('%',
            #{guildLeaderUserNickName}, '%')
        </if>
        order by g.id desc
    </select>


    <select id="selectAppGuildList1" parameterType="AppGuild" resultMap="AppGuildResult">
        SELECT
        g.id,
        g.create_time,
        g.update_time,
        g.create_by,
        g.update_by,
        g.is_del,
        g.user_id,
        g.avatar,
        g.guild_name,
        g.intro,
        g.guild_number,
        u.id AS userId,
        u.recode_code AS guildLeaderUserRecodeCode,
        u.nick_name AS guildLeaderUserNickName,
        u.head_portrait AS guildLeaderUserAvatar,
        u.phone AS guildLeaderUserPhone,
        g.to_be_settled,
        g.is_top,
        g.sys_user_id,
        su.nick_name AS sysUserNickName,
        SUM(CASE WHEN b.bill_type IN (5,6,7,8) AND b.user_id IS NOT NULL THEN b.total_amount ELSE 0 END) AS guildFlowAmount,
        SUM(CASE WHEN b.bill_type IN (8) AND b.user_id IS NOT NULL THEN b.total_amount ELSE 0 END) AS chatFlowAmount,
        SUM(CASE WHEN b.bill_type IN (6, 7) AND b.user_id IS NOT NULL THEN b.total_amount ELSE 0 END) AS voiceFlowAmount
        FROM
        app_guild g
        LEFT JOIN
        app_guild_member gm ON g.id = gm.guild_id
        LEFT JOIN
        app_user_points_bill b ON gm.user_id = b.user_id
        AND b.created_time >= g.create_time
        LEFT JOIN
        app_user u ON g.user_id = u.id
        left join
        sys_user su ON g.sys_user_id = su.user_id
        WHERE
        g.is_del = FALSE
        <if test="queryBeginTime != null and queryBeginTime != ''"><!-- 开始时间检索 -->
            AND date_format(b.created_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''"><!-- 结束时间检索 -->
            AND date_format(b.created_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        <if test="guildIds != null">
            <choose>
                <when test="guildIds.size() != 0">
                    AND g.id in
                    <foreach collection="guildIds" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    and 1=0
                </otherwise>
            </choose>
        </if>
        <if test="giftType != null">
            AND ugg.gift_type = #{giftType}
        </if>
        <if test="sysUserId !=null and sysUserId !=1">
            AND g.sys_user_id = #{sysUserId}
        </if>
        <if test="userId != null ">
            and g.user_id = #{userId}
        </if>
        <if test="numberUserId != null">
            AND gm.user_id = #{numberUserId}
        </if>
        <if test="guildName != null  and guildName != ''">
            and g.guild_name like concat('%', #{guildName}, '%')
        </if>
        <if test="guildNumber != null  and guildNumber != ''">
            and g.guild_number = #{guildNumber}
        </if>
        <if test="guildLeaderUserPhone != null  and guildLeaderUserPhone != ''">
            and u.phone = #{guildLeaderUserPhone}
        </if>
        <if test="guildLeaderUserRecodeCode != null  and guildLeaderUserRecodeCode != ''">
            and u.recode_code =#{guildLeaderUserRecodeCode}
        </if>
        <if test="guildLeaderUserNickName != null  and guildLeaderUserNickName != ''">
            and u.nick_name like concat('%',#{guildLeaderUserNickName}, '%')
        </if>
        GROUP BY
        g.id
        order by g.id desc
    </select>


    <select id="selectAppGuildList2" parameterType="AppGuild" resultMap="AppGuildResult">
        SELECT
        g.id
        FROM
        app_guild g
        LEFT JOIN
        app_guild_member gm ON g.id = gm.guild_id
        LEFT JOIN
        app_chat_room cr ON gm.user_id = cr.hall_owner_user_id
        LEFT JOIN
        user_give_gift ugg ON cr.id = ugg.chat_room_id AND ugg.created_time &gt;= gm.create_time AND ugg.created_time
        &lt;= CURRENT_TIMESTAMP
        <if test="queryBeginTime != null and queryBeginTime != ''"><!-- 开始时间检索 -->
            AND date_format(ugg.created_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''"><!-- 结束时间检索 -->
            AND date_format(ugg.created_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        <if test="guildIds != null">
            AND g.id in
            <foreach collection="guildIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="giftType != null">
            AND ugg.gift_type = #{giftType}
        </if>
        LEFT JOIN
        app_user u ON g.user_id = u.id
        WHERE
        g.is_del = FALSE
        <if test="sysUserId !=null">
            AND g.sys_user_id = #{sysUserId}
        </if>
        <if test="userId != null ">
            and g.user_id = #{userId}
        </if>
        <if test="guildName != null  and guildName != ''">
            and g.guild_name like concat('%', #{guildName}, '%')
        </if>
        <if test="guildNumber != null  and guildNumber != ''">
            and g.guild_number = #{guildNumber}
        </if>
        <if test="guildLeaderUserPhone != null  and guildLeaderUserPhone != ''">
            and u.phone = #{guildLeaderUserPhone}
        </if>
        <if test="guildLeaderUserRecodeCode != null  and guildLeaderUserRecodeCode != ''">
            and u.recode_code =#{guildLeaderUserRecodeCode}
        </if>
        <if test="guildLeaderUserNickName != null  and guildLeaderUserNickName != ''">
            and u.nick_name like concat('%',#{guildLeaderUserNickName}, '%')
        </if>
        GROUP BY
        g.id,
        u.id,
        u.recode_code,
        u.nick_name,
        u.head_portrait,
        u.phone
        order by g.id desc
    </select>

    <select id="getTotalByGuildId" parameterType="AppGuild" resultType="java.math.BigDecimal">
        SELECT DISTINCT
        COALESCE(SUM(ugg.gift_gold_total_amount), 0) AS total
        FROM
        app_guild ag
        left JOIN app_guild_member agm ON agm.guild_id = ag.id
        left JOIN app_chat_room_user acru ON acru.user_id = agm.user_id
        left join app_chat_room cr on acru.chat_room_id=cr.id
        left JOIN user_give_gift ugg ON ugg.chat_room_id = acru.chat_room_id and ugg.to_user_id=agm.user_id
        WHERE
        ugg.created_time &gt;= ag.create_time
        -- AND bill.created_time &lt;= CURRENT_TIMESTAMP
        <if test="queryBeginTime != null and queryBeginTime != ''"><!-- 开始时间检索 -->
            AND date_format(ugg.created_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''"><!-- 结束时间检索 -->
            AND date_format(ugg.created_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        AND ag.id = #{id}
        <if test="type == 1">
            AND cr.type IN (1,2,3)
        </if>
        <if test="type == 2">
            AND cr.type IN (5)
        </if>
    </select>

    <select id="selectAppGuildTotal1" parameterType="AppGuild" resultMap="AppGuildResult">
        SELECT
        COALESCE(SUM(ugg.gift_gold_total_amount), 0) AS guildFlowAmount
        FROM
        app_guild g
        LEFT JOIN
        app_guild_member gm ON g.id = gm.guild_id
        LEFT JOIN
        app_chat_room cr ON gm.user_id = cr.hall_owner_user_id
        LEFT JOIN
        user_give_gift ugg ON cr.id = ugg.chat_room_id AND ugg.created_time &gt;= gm.create_time AND ugg.created_time
        &lt;= CURRENT_TIMESTAMP
        <if test="queryBeginTime != null and queryBeginTime != ''"><!-- 开始时间检索 -->
            AND date_format(ugg.created_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''"><!-- 结束时间检索 -->
            AND date_format(ugg.created_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        <if test="giftType != null">
            AND ugg.gift_type = #{giftType}
        </if>
        LEFT JOIN
        app_user u ON g.user_id = u.id
        WHERE
        g.is_del = FALSE
        <if test="numberUserId != null">
            AND gm.user_id = #{numberUserId}
        </if>

        <if test="userId != null ">and g.user_id = #{userId}</if>
        <if test="guildName != null  and guildName != ''">and g.guild_name like concat('%', #{guildName}, '%')</if>
        <if test="guildNumber != null  and guildNumber != ''">and g.guild_number = #{guildNumber}</if>
        <if test="guildLeaderUserPhone != null  and guildLeaderUserPhone != ''">and u.phone = #{guildLeaderUserPhone}
        </if>
        <if test="guildLeaderUserRecodeCode != null  and guildLeaderUserRecodeCode != ''">and u.recode_code =
            #{guildLeaderUserRecodeCode}
        </if>
        <if test="guildLeaderUserNickName != null  and guildLeaderUserNickName != ''">and u.nick_name like concat('%',
            #{guildLeaderUserNickName}, '%')
        </if>
        GROUP BY
        g.id,
        u.id,
        u.recode_code,
        u.nick_name,
        u.head_portrait,
        u.phone
    </select>

    <select id="selectAppGuildById" parameterType="Long" resultMap="AppGuildResult">
        select g.id, g.create_time, g.update_time, g.create_by, g.update_by, g.is_del, g.user_id, g.avatar,
        g.guild_name, g.intro,
        g.guild_number,
        u.recode_code as guildLeaderUserRecodeCode,
        u.nick_name as guildLeaderUserNickName,
        u.head_portrait as guildLeaderUserAvatar,
        u.phone as guildLeaderUserPhone,
        g.to_be_settled,
        g.is_top
        from app_guild g,
        app_user u
        where g.is_del=false
        and u.id=g.user_id
        and g.id = #{id}
    </select>

    <select id="getGuildInfoById" parameterType="Long" resultMap="AppGuildResult">
        <include refid="selectAppGuildVo"/>
        where id = #{id} and is_del=false
    </select>

    <insert id="insertAppGuild" parameterType="AppGuild" useGeneratedKeys="true" keyProperty="id">
        insert into app_guild
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isDel != null">is_del,</if>
            <if test="userId != null">user_id,</if>
            <if test="avatar != null">avatar,</if>
            <if test="guildName != null">guild_name,</if>
            <if test="intro != null">intro,</if>
            <if test="guildNumber != null">guild_number,</if>
            <if test="toBeSettled != null">to_be_settled,</if>
            <if test="isTop != null">is_top,</if>
            <if test="sysUserId != null" >sys_user_id</if>
            <if test="reviewStatus != null" >review_status</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="userId != null">#{userId},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="guildName != null">#{guildName},</if>
            <if test="intro != null">#{intro},</if>
            <if test="guildNumber != null">#{guildNumber},</if>
            <if test="toBeSettled != null">#{toBeSettled},</if>
            <if test="isTop != null">#{isTop},</if>
            <if test="sysUserId != null" >#{sysUserId}</if>
            <if test="reviewStatus != null" >#{reviewStatus}</if>

        </trim>
    </insert>

    <update id="updateAppGuild" parameterType="AppGuild">
        update app_guild
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="guildName != null">guild_name = #{guildName},</if>
            <if test="intro != null">intro = #{intro},</if>
            <if test="guildNumber != null">guild_number = #{guildNumber},</if>
            <if test="toBeSettled != null">to_be_settled = #{toBeSettled},</if>
            <if test="isTop != null">is_top = #{isTop},</if>
            <if test="sysUserId != null">sys_user_id = #{sysUserId},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteAppGuildById" parameterType="Long">
        update app_guild set is_del=true where id = #{id}
    </update>

    <delete id="deleteAppGuildByIds" parameterType="String">
        delete from app_guild where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="userIsGuild" parameterType="java.lang.Long" resultType="java.lang.Boolean">
        select if(count(*)>0,1,0) from app_guild where is_del=false and user_id=#{userId}
    </select>

    <select id="selectAppGuildByGuildNumber" parameterType="java.lang.String" resultMap="AppGuildResult">
        select id, create_time, update_time, create_by, update_by, is_del, user_id, avatar, guild_name, intro,
        guild_number from app_guild
        where is_del=false and guild_number = #{guildNumber}
        order by id desc limit 0,1
    </select>

    <select id="getAppGuild" parameterType="Long" resultType="com.hzy.core.model.vo.app.AppGuildVo">
        select
        g.id as guildId,
        g.create_time as createTime,
        g.user_id as userId,
        g.avatar,
        g.guild_name as guildName,
        g.intro,
        g.guild_number as guildNumber
        from app_guild g
        where g.id = #{guildId} and g.is_del=false
    </select>

    <select id="userIsJoinGuild" parameterType="java.lang.Long" resultType="java.lang.Boolean">
        select if(count(*) > 0, true, false)
        from app_guild_member
        where guild_id = #{guildId}
        and user_id = #{userId}
    </select>

    <select id="getUserJoinGuildId" parameterType="java.lang.Long" resultType="java.lang.Long">
        select g.id from app_guild g
        LEFT JOIN app_guild_member me on(me.guild_id=g.id and me.user_id=#{userId})
        where g.is_del=false
        and (g.user_id=#{userId} or me.id is not null)
        order by g.id desc limit 0,1
    </select>

    <select id="getGuildLeadIdByUserId" parameterType="java.lang.Long" resultType="java.lang.Long">
        select g.user_id from app_guild g
        LEFT JOIN app_guild_member me on(me.guild_id=g.id and me.user_id=#{userId})
        where g.is_del=false
        and (g.user_id=#{userId} or me.id is not null)
        order by g.id desc limit 0,1
    </select>


    <select id="getGoodsGuildList" resultType="com.hzy.core.model.vo.app.AppGuildListVo">
        select
        g.id as guildId,
        g.user_id as userId,
        g.avatar,
        g.guild_name as guildName,
        g.intro,
        g.guild_number as guildNumber
        from app_guild g
        where g.is_del=0
        order by g.id desc
        limit 0,10
    </select>


    <select id="getChatRoomLastWeekCharmLeaderboard" resultType="com.hzy.core.model.vo.app.AppLeaderboardResultVo"
            parameterType="java.lang.Long">
        select u.id as userId,
        u.recode_code as recodeCode,
        u.is_real_person_auth as isRealPersonAuth,
        u.is_real_name_auth as isRealNameAuth,
        if(u.phone!=''and u.phone is not null,1,0) as isPhoneAuth,
        u.last_operating_time as lastOperatingTime,
        u.photo_album as photoAlbumStr,
        u.nick_name as nickName,
        u.is_online as isOnline,
        u.phone as userPhone,
        u.birthday,
        u.age,
        u.sex,
        u.personal_signature as personalSignature,
        u.head_portrait as headPortrait,
        u.education_background as educationBackground,
        u.constellation,
        u.height,
        u.weight,
        u.location,
        u.annual_income as annualIncome,
        u.purchase_situation as purchaseSituation,
        u.car_purchase_situation as carPurchaseSituation,
        u.self_introduction as selfIntroduction,
        u.label,
        u.voice_signature as voiceSignature,
        u.longitude,
        u.latitude,
        u.province,
        u.city,
        ifnull(sum(bill.to_user_charm_value),0) as charmValue,
        u.area
        from
        app_user_gold_bill bill
        LEFT JOIN app_user u on(bill.object_id=u.id)
        where
        bill.chat_room_id=#{chatRoomId}
        and 1=if(DATE(bill.create_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 7 DAY)
        and DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 1 DAY),1,0)
        and 1=if(bill.bill_type=2 or bill.bill_type=21,1,0)
        and 1=if(bill.to_user_charm_value>0,1,0)
        group by u.id
        order by charmValue desc
        limit 0,100
    </select>


    <select id="getChatRoomLastWeekCharmLeaderboard1" resultType="com.hzy.core.model.vo.app.AppLeaderboardResultVo"
            parameterType="java.lang.Long">
        select u.id as userId,
        u.recode_code as recodeCode,
        u.is_real_person_auth as isRealPersonAuth,
        u.is_real_name_auth as isRealNameAuth,
        if(u.phone!=''and u.phone is not null,1,0) as isPhoneAuth,
        u.last_operating_time as lastOperatingTime,
        u.photo_album as photoAlbumStr,
        u.nick_name as nickName,
        u.is_online as isOnline,
        u.phone as userPhone,
        u.birthday,
        u.age,
        u.sex,
        u.personal_signature as personalSignature,
        u.head_portrait as headPortrait,
        u.education_background as educationBackground,
        u.constellation,
        u.height,
        u.weight,
        u.location,
        u.annual_income as annualIncome,
        u.purchase_situation as purchaseSituation,
        u.car_purchase_situation as carPurchaseSituation,
        u.self_introduction as selfIntroduction,
        u.label,
        u.voice_signature as voiceSignature,
        u.longitude,
        u.latitude,
        u.province,
        u.city,
        ifnull(sum(bill.gift_gold_total_amount),0) as charmValue,
        u.area
        from
        -- app_user_gold_bill bill
        user_give_gift bill
        LEFT JOIN app_user u on(bill.to_user_id=u.id)
        where
        bill.chat_room_id=#{chatRoomId}
        and 1=if(DATE(bill.created_time) BETWEEN DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 7 DAY)
        and DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) + 1 DAY),1,0)
        -- and 1=if(bill.bill_type=2 or bill.bill_type=21,1,0)
        and 1=if(bill.gift_gold_total_amount>0,1,0)
        group by u.id
        order by charmValue desc
        limit 0,100
    </select>


    <select id="getGuildList" resultType="com.hzy.core.model.vo.app.AppGuildListVo" parameterType="java.lang.String">
        select
        g.id,
        g.id as guildId,
        g.user_id as userId,
        g.avatar,
        g.guild_name as guildName,
        g.intro,
        g.guild_number as guildNumber,
        u.nick_name as guildUserName
        from app_guild g
        left join app_user u on u.id=g.user_id
        where g.is_del=false
        <if test="keyWord!=null and keyWord!=''">
            and (g.guild_name like concat('%',#{keyWord}, '%') or g.guild_number like concat('%',#{keyWord}, '%'))
        </if>
        order by g.id desc
    </select>


    <update id="addToBeSettled">
        update app_guild set to_be_settled=to_be_settled+#{val}
        where id=#{id}
    </update>

    <update id="subToBeSettled">
        update app_guild set to_be_settled=to_be_settled-#{val}
        where id=#{id}
    </update>
    <update id="updReviewStatus">
        update app_guild set review_status=#{status}
        where id=#{id}
    </update>

    <select id="getAllToBeSettledGuildList" resultMap="AppGuildResult">
        <include refid="selectAppGuildVo"/>
        where to_be_settled>0
        order by id desc
    </select>
    <select id="getGuildUser" resultType="com.hzy.core.entity.AppGuildEntity">
        SELECT DISTINCT
        ugg.user_id as senderId, -- 送礼人的用户ID
        au.nick_name as senderName, -- 送礼人的昵称
        ugg.chat_room_id as chatRoomId,
        ugg.gift_name as giftName,
        ugg.gift_num as giftNum,
        ugg.gift_gold_total_amount as giftGoldTotalAmount,
        ugg.to_user_id as receiverId, -- 接收人的用户ID
        au2.nick_name as receiverName, -- 接收人的昵称
        ugg.created_time as createdTime
        FROM
        app_guild ag
        JOIN app_guild_member agm ON agm.guild_id = ag.id
        JOIN app_chat_room_user acru ON acru.user_id = agm.user_id
        JOIN user_give_gift ugg ON ugg.chat_room_id = acru.chat_room_id and ugg.to_user_id=agm.user_id
        JOIN app_user au ON au.id = ugg.user_id -- 连接送礼人的昵称
        JOIN app_user au2 ON au2.id = ugg.to_user_id -- 连接接收人的昵称
        <where>
            <if test="guildId !=null ">
                ag.id=#{guildId}
            </if>
            <if test="userId !=null ">
                and ugg.to_user_id=#{userId}
            </if>
            <if test="queryBeginTime != null and queryBeginTime != ''">
                and date_format(ugg.created_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
            </if>

            <if test="queryEndTime != null and queryEndTime != ''">
                and date_format(ugg.created_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
            </if>
        </where>
        order by ugg.created_time desc
    </select>
    <select id="getGuildUserAndMsg" resultType="com.hzy.core.entity.AppGuildEntity">
        SELECT DISTINCT
        ugg.user_id as senderId, -- 送礼人的用户ID
        au.nick_name as senderName, -- 送礼人的昵称
        ugg.chat_room_id as chatRoomId,
        ugg.gift_name as giftName,
        ugg.gift_num as giftNum,
        ugg.gift_gold_total_amount as giftGoldTotalAmount,
        ugg.to_user_id as receiverId, -- 接收人的用户ID
        au2.nick_name as receiverName, -- 接收人的昵称
        ugg.created_time as createdTime
        FROM app_guild_member agm
        JOIN user_give_gift ugg ON  ugg.to_user_id=agm.user_id
        JOIN app_user au ON au.id = ugg.user_id -- 连接送礼人的昵称
        JOIN app_user au2 ON au2.id = ugg.to_user_id -- 连接接收人的昵称
        <where>
            <if test="guildId !=null ">
                agm.guild_id=#{guildId}
            </if>
            <if test="userId !=null ">
                and ugg.to_user_id=#{userId}
            </if>
            <if test="queryBeginTime != null and queryBeginTime != ''">
                and date_format(ugg.created_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
            </if>

            <if test="queryEndTime != null and queryEndTime != ''">
                and date_format(ugg.created_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
            </if>
        </where>
        order by ugg.created_time desc
    </select>

    <select id="getGuildUserTelRecords" resultType="com.hzy.core.entity.AppGuildEntity">
        SELECT DISTINCT
        actr.receive_user_id as senderId,
        au.nick_name as senderName,
        '语音通话' as giftName,
        TIME_TO_SEC(TIMEDIFF(actr.hang_up_time, actr.connect_time)) as giftNum,
        actr.consumption_gold as giftGoldTotalAmount,
        actr.receive_user_id as receiverId,
        au2.nick_name as receiverName,
        actr.create_time as createdTime
        FROM
        app_guild_member agm
        JOIN app_communicate_telephone_records actr ON
        actr.receive_user_id = agm.user_id
        JOIN app_user au ON
        au.id = actr.initiate_user_id
        JOIN app_user au2 ON
        au2.id = actr.receive_user_id
        RIGHT join app_user_points_bill aupb on
        aupb.object_id = actr.id
        <where>
            <if test="guildId !=null ">
                agm.guild_id=#{guildId}
            </if>
            <if test="userId !=null ">
                and actr.receive_user_id=#{userId}
            </if>
            <if test="queryBeginTime != null and queryBeginTime != ''">
                and date_format(actr.create_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
            </if>

            <if test="queryEndTime != null and queryEndTime != ''">
                and date_format(actr.create_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
            </if>
        </where>
        order by actr.create_time desc
    </select>

    <select id="getGuildUserRecords" resultType="com.hzy.core.entity.AppGuildEntity">
    SELECT * FROM (
        SELECT DISTINCT
            au.recode_code as senderId,
            au.nick_name as senderName,
            ugg.chat_room_id as chatRoomId,
            ugg.gift_name as giftName,
            ugg.gift_num as giftNum,
            ugg.gift_gold_total_amount as giftGoldTotalAmount,
            au2.recode_code as receiverId,
            au2.nick_name as receiverName,
            ugg.created_time as createdTime,
            ugg.id
        FROM app_guild_member agm
        JOIN user_give_gift ugg ON ugg.to_user_id=agm.user_id
        JOIN app_user au ON au.id = ugg.user_id
        JOIN app_user au2 ON au2.id = ugg.to_user_id
        WHERE 1=1
          and ugg.created_time &gt;= (SELECT create_time FROM app_guild WHERE id = agm.guild_id)
            <if test="guildId !=null">AND agm.guild_id=#{guildId}</if>
            <if test="userId !=null">AND au2.recode_code=#{userId}</if>
            <if test="queryBeginTime != null and queryBeginTime != ''">
                AND date_format(ugg.created_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
            </if>
            <if test="queryEndTime != null and queryEndTime != ''">
                AND date_format(ugg.created_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
            </if>

        UNION ALL

        SELECT DISTINCT
            au.recode_code as senderId,
            au.nick_name as senderName,
            null as chatRoomId,
            '语音通话' as giftName,
            TIME_TO_SEC(TIMEDIFF(actr.hang_up_time, actr.connect_time)) as giftNum,
            actr.consumption_gold as giftGoldTotalAmount,
            au2.recode_code as receiverId,
            au2.nick_name as receiverName,
            actr.create_time as createdTime,
            actr.id
        FROM app_guild_member agm
        JOIN app_communicate_telephone_records actr ON actr.receive_user_id = agm.user_id
        JOIN app_user au ON au.id = actr.initiate_user_id
        JOIN app_user au2 ON au2.id = actr.receive_user_id
        RIGHT JOIN app_user_points_bill aupb ON aupb.object_id = actr.id
        WHERE 1=1
           and aupb.created_time &gt;= (SELECT create_time FROM app_guild WHERE id = agm.guild_id)
            <if test="guildId !=null">AND agm.guild_id=#{guildId}</if>
            <if test="userId !=null">AND au2.recode_code=#{userId}</if>
            <if test="queryBeginTime != null and queryBeginTime != ''">
                AND date_format(actr.create_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
            </if>
            <if test="queryEndTime != null and queryEndTime != ''">
                AND date_format(actr.create_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
            </if>
    ) t
    ORDER BY createdTime DESC
</select>

    <select id="getGuildApplyList" resultType="com.hzy.core.model.vo.app.AppGuildVo">
        select
            g.id as guildId,
            g.user_id as userId,
            g.avatar,
            g.guild_name as guildName,
            g.intro,
            g.guild_number as guildNumber,
            g.review_status as reviewStatus,
            u.nick_name as guildUserName
        from app_guild g
                 left join app_user u on u.id=g.user_id
        where g.is_del=false
        <if test="guildId !=null ">
            and g.id=#{guildId}
        </if>
        <if test="userId !=null ">
            and g.user_id=#{userId}
        </if>
        <if test="nickName !=null and nickName != ''">
            and (u.nick_name like concat('%',#{nickName}, '%') or u.nick_name like concat('%',#{nickName}, '%'))
        </if>
        <if test="recodeCode !=null ">
            and u.recode_code=#{recodeCode}
        </if>
    </select>
    <select id="getGuildLeadScale" resultType="java.math.BigDecimal">
        select (ag.guild_commission_scale - agm.gift_income_scale) as guildLeadScale
        from app_guild ag
                 join app_guild_member agm on agm.guild_id = ag.id
        where agm.user_id = #{toUserId}
    </select>
    <select id="getGuildBySysUserId" resultType="com.hzy.core.entity.AppGuild">
        select * from app_guild where sys_user_id = #{sysUserId}
    </select>

    <!-- getGuildNameByUserId --> 

    <select id="getGuildNameByUserId" resultType="java.lang.String">
        select ag.guild_name as guildName
        from app_guild ag
        join app_guild_member agm on agm.guild_id = ag.id
        where agm.user_id = #{userId}
    </select>
</mapper>