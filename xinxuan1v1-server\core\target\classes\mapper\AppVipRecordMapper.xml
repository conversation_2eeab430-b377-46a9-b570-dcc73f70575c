<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppVipRecordMapper">


    <select id="getVipRecords" resultType="com.hzy.core.entity.AppVipRecord">
        select avr.*, au.nick_name as nickname, au.recode_code as recodeCode
        from app_vip_record avr
        left join app_user au on avr.user_id = au.id
        <where>
            <if test="nickname != null and nickname != ''">
                and au.nick_name = #{nickname}
            </if>
            <if test="recodeCode != null and recodeCode != ''">
                and au.recode_code = #{recodeCode}
            </if>
            <if test="type != null">
                and avr.type = #{type}
            </if>
            <if test="isIos != null">
                and avr.is_ios = #{isIos}
            </if>
            <if test="startTime != null and startTime != ''">
                and date_format(avr.create_time, '%y%m%d') &gt;= date_format(#{startTime}, '%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''">
                and date_format(avr.create_time, '%y%m%d') &lt;= date_format(#{endTime}, '%y%m%d')
            </if>
        </where>
        order by avr.id desc
    </select>
</mapper>