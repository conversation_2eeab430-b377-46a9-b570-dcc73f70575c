<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.SysStatisticsDataMapper">


    <select id="getPayStatisticsData" resultType="java.math.BigDecimal" parameterType="java.lang.Integer">
        select
        ifnull(sum(order_price),0)
        from
        app_order o
        where
        o.is_del=false
        and o.order_status=1
        and o.order_type=1
        <if test="queryType==0">
            and to_days(o.create_time) = to_days(DATE_SUB(CURDATE(), INTERVAL 1 DAY))
        </if>
        <if test="queryType==1">
            and to_days(o.create_time) = to_days(now())
        </if>
    </select>

    <select id="getUserCzStatisticsData" resultType="java.math.BigDecimal">
        select
        ifnull(sum(order_price),0)
        from
        app_order o
        where o.is_del=false
        and o.order_status=1
        and o.order_type=1
        and o.user_id=#{userId}
        <if test="queryType==1">
            and to_days(o.create_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and o.create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and o.create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(o.create_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
    </select>


    <select id="getUserSlStatisticsData" resultType="java.math.BigDecimal">
        select
        ifnull(sum(abs(bill.amount)),0) as charmValue
        from
        app_user_gold_bill bill
        where bill.is_del=false
        and (bill.bill_type=2 or bill.bill_type=21)
        and bill.object_id=#{userId}
        <if test="queryType==1">
            and to_days(bill.create_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and bill.create_time >= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY
            and bill.create_time &lt;= CURDATE() - INTERVAL WEEKDAY(CURDATE()) DAY + INTERVAL 7 DAY
        </if>
        <if test="queryType==3">
            and DATE_FORMAT(bill.create_time,'%Y%m')=DATE_FORMAT(NOW(),'%Y%m')
        </if>
    </select>

    <select id="getPayOrderCountStatisticsData" resultType="java.math.BigDecimal" parameterType="java.lang.Integer">
        select
        count(*)
        from
        app_order o
        where
        o.is_del=false
        and o.order_status=1
        and o.order_type=1
        <if test="queryType==0">
            and to_days(o.create_time) = to_days(DATE_SUB(CURDATE(), INTERVAL 1 DAY))
        </if>
        <if test="queryType==1">
            and to_days(o.create_time) = to_days(now())
        </if>
    </select>

    <select id="getPayPeopleCountStatisticsData" resultType="java.math.BigDecimal" parameterType="java.lang.Integer">
        select
        count(DISTINCT o.user_id)
        from
        app_order o
        where
        o.is_del=false
        and o.order_status=1
        and o.order_type=1
        <if test="queryType==0">
            and to_days(o.create_time) = to_days(DATE_SUB(CURDATE(), INTERVAL 1 DAY))
        </if>
        <if test="queryType==1">
            and to_days(o.create_time) = to_days(now())
        </if>
    </select>

    <select id="getRegisterPeopleCountStatisticsData" resultType="java.lang.Integer"
            parameterType="java.lang.Integer">
        select
        count(*)
        from
        app_user u
        where 1=1
        <if test="queryType==0">
            and to_days(u.created_time) = to_days(DATE_SUB(CURDATE(), INTERVAL 1 DAY))
        </if>
        <if test="queryType==1">
            and to_days(u.created_time) = to_days(now())
        </if>
    </select>

    <select id="getInviteRegisterPeopleCountStatisticsData" resultType="java.lang.Integer"
            parameterType="java.lang.Integer">
        select
        count(*)
        from
        app_user u
        where recode_user_id>0
        <if test="queryType==0">
            and to_days(u.created_time) = to_days(DATE_SUB(CURDATE(), INTERVAL 1 DAY))
        </if>
        <if test="queryType==1">
            and to_days(u.created_time) = to_days(now())
        </if>
    </select>

    <select id="getRegisterAndPayPeopleCountStatisticsData" resultType="java.lang.Integer"
            parameterType="java.lang.Integer">
        select
        count(DISTINCT u.id)
        from
        app_user u
        inner join app_order o on u.id = o.user_id
        where 
        o.is_del = false
        and o.order_status = 1
        and o.order_type = 1
        <if test="queryType==0">
            and to_days(u.created_time) = to_days(DATE_SUB(CURDATE(), INTERVAL 1 DAY))
            and to_days(o.create_time) = to_days(DATE_SUB(CURDATE(), INTERVAL 1 DAY))
        </if>
        <if test="queryType==1">
            and to_days(u.created_time) = to_days(now())
            and to_days(o.create_time) = to_days(now())
        </if>
        <if test="queryType==2">
            and to_days(u.created_time) = to_days(o.create_time)
        </if>
    </select>

    <select id="getConsumptionStatisticsData" resultType="java.math.BigDecimal" parameterType="java.lang.Integer">
        select
        ifnull(sum(abs(amount)),0)
        from app_user_gold_bill
        where amount &lt;0
        and 1=(if(bill_type=21,0,1))
        <if test="queryType==0">
            and to_days(create_time) = to_days(DATE_SUB(CURDATE(), INTERVAL 1 DAY))
        </if>
        <if test="queryType==1">
            and to_days(create_time) = to_days(now())
        </if>
    </select>


    <select id="getWithdrawStatisticsData" resultType="java.math.BigDecimal" parameterType="java.lang.Integer">
        select
        ifnull(sum(r.actual_amount),0)
        from
        app_withdraw_records r
        where r.status=1
        <if test="queryType==0">
            and DATE(r.create_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        </if>
        <if test="queryType==1">
            and DATE(r.create_time) = CURDATE()
        </if>
    </select>

    <select id="getMalePeopleCount" resultType="java.lang.Integer">
        select
        count(*)
        from
        app_user u
        where sex=0
    </select>

    <select id="getGirlPeopleCount" resultType="java.lang.Integer">
        select
        count(*)
        from
        app_user u
        where sex=1
    </select>

    <select id="getNoSexPeopleCount" resultType="java.lang.Integer">
        select
        count(*)
        from
        app_user u
        where sex=-1
    </select>


    <select id="getRegisterChartList" resultType="java.util.Map">
        select
        date_format(u.created_time,'%Y-%m-%d' ) as dateStr,
        count(*) as quantity
        from
        app_user u
        where YEARWEEK(date_format(u.created_time,'%Y-%m-%d' )) =YEARWEEK(now())
        group by date_format(u.created_time,'%Y-%m-%d' )
    </select>

    <select id="getTopUpChartList" resultType="java.util.Map">
        select
        date_format(o.create_time,'%Y-%m-%d' ) as dateStr,
        ifnull(sum(o.order_price),0) as quantity
        from
        app_order o
        where o.order_status=1
        and o.order_type=1
        and o.is_del=false
        and YEARWEEK(date_format(o.create_time,'%Y-%m-%d' )) =YEARWEEK(now())
        group by date_format(o.create_time,'%Y-%m-%d' )
    </select>

    <select id="getWithdrawChartList" resultType="java.util.Map">
        select
        date_format(r.create_time,'%Y-%m-%d' ) as dateStr,
        ifnull(sum(r.actual_amount),0) as quantity
        from
        app_withdraw_records r
        where r.status=1
        and YEARWEEK(date_format(r.create_time,'%Y-%m-%d' )) =YEARWEEK(now())
        group by date_format(r.create_time,'%Y-%m-%d' )
    </select>


    <select id="getAllUserSumPoints" resultType="java.math.BigDecimal">
        select
        ifnull(sum(u.points_balance),0)
        from
        app_user u
        where u.points_balance &gt; 0
        and u.is_virtual=0
    </select>

    <select id="getPayOrderDetails" resultType="com.hzy.core.model.vo.admin.PayOrderDetailVO" parameterType="java.lang.Integer">
        select
        o.id as orderId,
        o.order_no as orderNo,
        o.order_price as orderPrice,
        o.create_time as createTime,
        o.pay_time as payTime,
        u.recode_code as recodeCode,
        u.nick_name as nickName
        from
        app_order o
        left join app_user u on o.user_id = u.id
        where
        o.is_del=false
        and o.order_status=1
        and o.order_type=1
        <if test="recodeCode != null and recodeCode != ''">
            and u.recode_code = #{recodeCode}
        </if>
        <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
            and o.create_time &gt;= #{startTime}
            and o.create_time &lt;= #{endTime}
        </if>
        order by o.id desc
    </select>

    <select id="getPayUserDetails" resultType="com.hzy.core.model.vo.admin.PayUserDetailVO" parameterType="java.lang.Integer">
        select
        u.id as userId,
        u.recode_code as recodeCode,
        u.nick_name as nickName,
        sum(o.order_price) as totalAmount,
        min(o.create_time) as createTime,
        max(o.pay_time) as payTime
        from
        app_order o
        left join app_user u on o.user_id = u.id
        where
        o.is_del=false
        and o.order_status=1
        and o.order_type=1
        <if test="recodeCode != null and recodeCode != ''">
            and u.recode_code = #{recodeCode}
        </if>
        <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
            and o.create_time &gt;= #{startTime}
            and o.create_time &lt;= #{endTime}
        </if>
        group by u.id, u.recode_code, u.nick_name
        order by totalAmount desc
    </select>

    <select id="getWithdrawDetails" resultType="com.hzy.core.model.vo.admin.WithdrawDetailVO">
        select
        r.id as id,
        r.user_id as userId,
        r.withdraw_amount as withdrawAmount,
        r.create_time as createTime,
        r.status as status,
        u.recode_code as recodeCode,
        u.nick_name as nickName
        from
        app_withdraw_records r
        left join app_user u on r.user_id = u.id
        where 1=1
        <if test="recodeCode != null and recodeCode != ''">
            and u.recode_code = #{recodeCode}
        </if>
        <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
            and r.create_time &gt;= #{startTime}
            and r.create_time &lt;= #{endTime}
        </if>
        order by r.id desc
    </select>

</mapper>