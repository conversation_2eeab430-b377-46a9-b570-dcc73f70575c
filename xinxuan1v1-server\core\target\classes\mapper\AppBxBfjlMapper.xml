<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppBxBfjlMapper">

    <resultMap type="AppBxBfjl" id="AppBxBfjlResult">
        <result property="id" column="id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="jjcId" column="jjc_id"/>
        <result property="giftId" column="gift_id"/>
        <result property="giftNum" column="gift_num"/>
        <result property="giftPrice" column="gift_price"/>
        <result property="sumGiftPrice" column="sum_gift_price"/>
        <result property="userId" column="user_id"/>
        <result property="isSubKc" column="is_sub_kc"/>
        <result property="subNum" column="sub_num"/>
        <result property="subTime" column="sub_time"/>
    </resultMap>

    <sql id="selectAppBxBfjlVo">
        select id, create_by, create_time, jjc_id, gift_id, gift_num, gift_price, sum_gift_price, user_id, is_sub_kc,
        sub_num, sub_time from app_bx_bfjl
    </sql>

    <select id="getUserBfjlList" parameterType="Long" resultMap="AppBxBfjlResult">
        select id, create_by, create_time, jjc_id, gift_id, gift_num, gift_price, sum_gift_price, user_id, is_sub_kc,
        sub_num, sub_time from app_bx_bfjl
        where user_id=#{userId}
        and sub_num>0
    </select>

    <select id="selectAppBxBfjlList" parameterType="AppBxBfjl" resultMap="AppBxBfjlResult">
        select b.id, b.create_by, b.create_time, b.jjc_id, b.gift_id, b.gift_num, b.gift_price, b.sum_gift_price,
        b.user_id, b.is_sub_kc,
        b.sub_num, b.sub_time,
        u.nick_name as userNickName,
        u.recode_code as userRecode,
        u.phone as userPhone,
        u.head_portrait as userAvatar,
        g.gift_name as giftName,
        g.img_url as giftImgUrl
        from app_bx_bfjl b
        LEFT JOIN app_user u on(u.id=b.user_id)
        LEFT JOIN app_gift g on(g.id=b.gift_id)
        where 1=1
        <if test="isSubKc != null ">and b.is_sub_kc = #{isSubKc}</if>
        <if test="userRecode!=null and userRecode!=''">
            and u.recode_code like concat('%', #{userRecode}, '%')
        </if>
        <if test="userPhone!=null and userPhone!=''">
            and u.phone like concat('%', #{userPhone}, '%')
        </if>
        <if test="userNickName!=null and userNickName!=''">
            and u.nick_name like concat('%', #{userNickName}, '%')
        </if>
        <if test="giftName!=null and giftName!=''">
            and g.gift_name like concat('%', #{giftName}, '%')
        </if>

        order by b.id desc
    </select>

    <select id="selectAppBxBfjlById" parameterType="Long" resultMap="AppBxBfjlResult">
        <include refid="selectAppBxBfjlVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppBxBfjl" parameterType="AppBxBfjl" useGeneratedKeys="true" keyProperty="id">
        insert into app_bx_bfjl
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="jjcId != null">jjc_id,</if>
            <if test="giftId != null">gift_id,</if>
            <if test="giftNum != null">gift_num,</if>
            <if test="giftPrice != null">gift_price,</if>
            <if test="sumGiftPrice != null">sum_gift_price,</if>
            <if test="userId != null">user_id,</if>
            <if test="isSubKc != null">is_sub_kc,</if>
            <if test="subNum != null">sub_num,</if>
            <if test="subTime != null">sub_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="jjcId != null">#{jjcId},</if>
            <if test="giftId != null">#{giftId},</if>
            <if test="giftNum != null">#{giftNum},</if>
            <if test="giftPrice != null">#{giftPrice},</if>
            <if test="sumGiftPrice != null">#{sumGiftPrice},</if>
            <if test="userId != null">#{userId},</if>
            <if test="isSubKc != null">#{isSubKc},</if>
            <if test="subNum != null">#{subNum},</if>
            <if test="subTime != null">#{subTime},</if>
        </trim>
    </insert>

    <update id="updateAppBxBfjl" parameterType="AppBxBfjl">
        update app_bx_bfjl
        <trim prefix="SET" suffixOverrides=",">
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="jjcId != null">jjc_id = #{jjcId},</if>
            <if test="giftId != null">gift_id = #{giftId},</if>
            <if test="giftNum != null">gift_num = #{giftNum},</if>
            <if test="giftPrice != null">gift_price = #{giftPrice},</if>
            <if test="sumGiftPrice != null">sum_gift_price = #{sumGiftPrice},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="isSubKc != null">is_sub_kc = #{isSubKc},</if>
            <if test="subNum != null">sub_num = #{subNum},</if>
            <if test="subTime != null">sub_time = #{subTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppBxBfjlById" parameterType="Long">
        delete from app_bx_bfjl where id = #{id}
    </delete>

    <delete id="deleteAppBxBfjlByIds" parameterType="String">
        delete from app_bx_bfjl where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>