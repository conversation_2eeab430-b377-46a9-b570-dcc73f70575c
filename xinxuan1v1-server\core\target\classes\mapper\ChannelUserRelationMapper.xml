<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.ChannelUserRelationMapper">

    <resultMap type="ChannelUserRelation" id="ChannelUserRelationResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="channelId" column="channel_id"/>
        <result property="createdTime" column="created_time"/>
        <result property="updatedTime" column="updated_time"/>
    </resultMap>

    <sql id="selectChannelUserRelationVo">
        select id, user_id, channel_id, created_time, updated_time
        from channel_user_relation
    </sql>
    <insert id="insertChannelUserRelation">
        insert into channel_user_relation
        (user_id,channel_id,created_time,updated_time,deleted)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.userId}, #{item.channelId}, #{item.createdTime}, #{item.updatedTime}, #{item.deleted})
        </foreach>
    </insert>


    <select id="selectChannelUserRelationById" resultType="com.hzy.core.entity.ChannelUserRelation">
        select id, user_id, channel_id, created_time, updated_time
        from channel_user_relation
        where id = #{id}
    </select>


    <select id="selectChannelUserRelationByUserId" resultType="com.hzy.core.entity.ChannelUserRelation">
        select id, user_id, channel_id, created_time, updated_time
        from channel_user_relation
        where user_id = #{userId}
        and channel_id = #{channelId}
        and deleted = 0
    </select>
    <select id="selectChannelUserRelationList" resultType="java.lang.Long"
            parameterType="java.util.List">
        SELECT
        user_id
        FROM
        channel_user_relation
        WHERE
        channel_id IN
        <foreach item="channelId" collection="channelIds" open="(" separator="," close=")">
            #{channelId}
        </foreach>
    </select>
    <select id="queryChannelUserRelationList" resultType="com.hzy.core.entity.AppUserEntity"
            parameterType="java.lang.String">
        SELECT
        au.id,
        au.recode_code,
        au.nick_name,
        au.phone,
        au.sex,
        au.user_status,
        au.head_portrait,
        au.created_time
        FROM app_user AS au
        WHERE au.id NOT IN (
        SELECT cur.user_id
        FROM channel_user_relation AS cur
        WHERE cur.deleted = 0
        )
        <if test="nickName != null and nickName != ''">
            and au.nick_name like concat('%', #{nickName}, '%')
        </if>
        <if test="recodeCode != null and recodeCode != ''">
            and au.recode_code = #{recodeCode}
        </if>
    </select>


    <update id="updateChannelUserRelation" parameterType="ChannelUserRelation">
        update channel_user_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="channelId != null">channel_id = #{channelId},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <update id="deleteChannelUserRelationById" parameterType="Long">
        update
        channel_user_relation
        set deleted=1
        where user_id = #{userId}
    </update>

    <delete id="deleteChannelUserRelationByIds" parameterType="String">
        delete from channel_user_relation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="recoverChannelUserRelation">
        update channel_user_relation
        set deleted = 0,
            updated_time = now()
        where user_id = #{userId}
          and channel_id = #{channelId}
    </update>
</mapper>