<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.candy.CandyExchangeRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hzy.core.entity.candy.CandyExchangeRecord">
        <id column="exchange_id" property="exchangeId" />
        <result column="user_id" property="userId" />
        <result column="item_id" property="itemId" />
        <result column="gold_cost" property="goldCost" />
        <result column="candy_cost" property="candyCost" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        exchange_id, user_id, item_id, gold_cost, candy_cost, create_time
    </sql>
    
    <!-- 查询糖果商品兑换记录列表 -->
    <select id="selectExchangeRecordList" resultType="com.hzy.core.model.dto.app.CandyExchangeRecordDto">
        SELECT
            cer.exchange_id AS exchangeId,
            cer.user_id AS userId,
            au.recode_code AS recodeCode,
            au.nick_name AS nickName,
            cer.item_id AS itemId,
            csi.item_name AS itemName,
            csi.image_url AS imageUrl,
            cer.gold_cost AS goldCost,
            cer.candy_cost AS candyCost,
            cer.create_time AS createTime
        FROM
            candy_exchange_record cer
        LEFT JOIN app_user au ON cer.user_id = au.id
        LEFT JOIN candy_shop_item csi ON cer.item_id = csi.item_id
        WHERE
            1 = 1
        <if test="userId != null">
            AND cer.user_id = #{userId}
        </if>
        <if test="itemId != null">
            AND cer.item_id = #{itemId}
        </if>
        <if test="candyCost != null">
            AND cer.candy_cost = #{candyCost}
        </if>
        <if test="goldCost != null">
            AND cer.gold_cost = #{goldCost}
        </if>
        <if test="recodeCode != null and recodeCode != ''">
            AND au.recode_code = #{recodeCode}
        </if>
        <if test="nickName != null and nickName != ''">
            AND au.nick_name LIKE CONCAT('%', #{nickName}, '%')
        </if>
        <if test="startTime != null">
            AND cer.create_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND cer.create_time &lt;= #{endTime}
        </if>
        ORDER BY
            cer.exchange_id DESC
    </select>

</mapper>
