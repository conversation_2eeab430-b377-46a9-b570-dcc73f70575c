<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppUserPersonalDressingMapper">

    <resultMap type="AppUserPersonalDressing" id="AppUserPersonalDressingResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="personalDressingId" column="personal_dressing_id"/>
        <result property="isDel" column="is_del"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="expiredTime" column="expired_time"/>
        <result property="isUse" column="is_use"/>
    </resultMap>

    <sql id="selectAppUserPersonalDressingVo">
        select id,
        user_id,
        personal_dressing_id,
        is_del,
        create_time,
        update_time,
        expired_time,
        is_use
        from app_user_personal_dressing
    </sql>

    <select id="selectAppUserPersonalDressingList" parameterType="AppUserPersonalDressing"
            resultMap="AppUserPersonalDressingResult">
        <include refid="selectAppUserPersonalDressingVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="personalDressingId != null ">and personal_dressing_id = #{personalDressingId}</if>
            <if test="isDel != null ">and is_del = #{isDel}</if>
            <if test="expiredTime != null ">and expired_time = #{expiredTime}</if>
            <if test="isUse != null ">and is_use = #{isUse}</if>
        </where>
    </select>

    <select id="selectAppUserPersonalDressingById" parameterType="Long" resultMap="AppUserPersonalDressingResult">
        <include refid="selectAppUserPersonalDressingVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppUserPersonalDressing" parameterType="AppUserPersonalDressing" useGeneratedKeys="true"
            keyProperty="id">
        insert into app_user_personal_dressing
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="personalDressingId != null">personal_dressing_id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="expiredTime != null">expired_time,</if>
            <if test="isUse != null">is_use,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="personalDressingId != null">#{personalDressingId},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="expiredTime != null">#{expiredTime},</if>
            <if test="isUse != null">#{isUse},</if>
        </trim>
    </insert>

    <update id="updateAppUserPersonalDressing" parameterType="AppUserPersonalDressing">
        update app_user_personal_dressing
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="personalDressingId != null">personal_dressing_id = #{personalDressingId},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="expiredTime != null">expired_time = #{expiredTime},</if>
            <if test="isUse != null">is_use = #{isUse},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppUserPersonalDressingById" parameterType="Long">
        delete
        from app_user_personal_dressing
        where id = #{id}
    </delete>

    <delete id="deleteAppUserPersonalDressingByIds" parameterType="String">
        delete from app_user_personal_dressing where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getUserPersonalDressingList" parameterType="java.lang.Long"
            resultType="com.hzy.core.model.vo.app.AppUserPersonalDressingVo">
        select upd.id as userPersonalDressingId,
        pd.id as personalDressingId,
        pd.name,
        pd.img_url as imgUrl,
        pd.gif_url as gifUrl,
        pd.color_value as colorValue,
        pd.category_id as categoryId,
        upd.expired_time as expiredTime,
        upd.is_use as isUse
        from app_user_personal_dressing upd,
        app_personal_dressing pd
        where pd.id = upd.personal_dressing_id
        and upd.is_del = false
        and upd.user_id = #{userId}
        and pd.category_id = #{categoryId}
        and (now() &lt;= upd.expired_time or upd.expired_time is null)
        order by pd.sort asc, upd.id desc
    </select>

    <update id="setNoUserByUserIdAndCategoryId" parameterType="java.lang.Long">
        update app_user_personal_dressing upd,app_personal_dressing pd
        set upd.is_use= false
        where upd.user_id=#{userId}
        and pd.category_id=#{categoryId}
        and upd.personal_dressing_id=pd.id
    </update>

    <select id="selectUserPersonalDressingByUserIdAndPersonalDressingId" parameterType="Long"
            resultMap="AppUserPersonalDressingResult">
        <include refid="selectAppUserPersonalDressingVo"/>
        where user_id = #{userId} and personal_dressing_id=#{personalDressingId} and is_del=false
    </select>


    <select id="getUserUseInPersonalDressingInfo" parameterType="java.lang.Long"
            resultType="com.hzy.core.model.vo.app.AppUseInPersonalDressingVo">
        select upd.id as userPersonalDressingId,
        pd.id as personalDressingId,
        pd.name,
        pd.img_url as imgUrl,
        pd.gif_url as gifUrl,
        pd.color_value as colorValue,
        pd.category_id as categoryId
        from app_user_personal_dressing upd,
        app_personal_dressing pd
        where pd.id = upd.personal_dressing_id
        and upd.is_del = false
        and upd.is_use=true
        and upd.user_id = #{userId}
        and pd.category_id = #{categoryId}
        and now() &lt;= upd.expired_time
        order by upd.id asc limit 0,1
    </select>


    <select id="getInfoById" parameterType="java.lang.Long"
            resultType="com.hzy.core.model.vo.app.AppUseInPersonalDressingVo">
        select
        pd.id as personalDressingId,
        pd.name,
        pd.img_url as imgUrl,
        pd.gif_url as gifUrl,
        pd.color_value as colorValue,
        pd.category_id as categoryId,
        pd.gold_price,
        pd.valid_days
        from
        app_personal_dressing pd
        where pd.id = #{id}
    </select>

    <select id="getUserIdAndId" parameterType="Long" resultMap="AppUserPersonalDressingResult">
        <include refid="selectAppUserPersonalDressingVo"/>
        where is_del=false and user_id=#{userId} and personal_dressing_id = #{id}
        order by id desc limit 0,1
    </select>
</mapper>