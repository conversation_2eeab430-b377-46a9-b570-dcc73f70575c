<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppBxUserTjMapper">

    <resultMap type="AppBxUserTj" id="AppBxUserTjResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="bxId" column="bx_id"/>
        <result property="jjcId" column="jjc_id"/>
        <result property="srSum" column="sr_sum"/>
        <result property="zcSum" column="zc_sum"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAppBxUserTjVo">
        select id, user_id, bx_id, jjc_id, sr_sum, zc_sum, create_time, update_time from app_bx_user_tj
    </sql>

    <select id="selectAppBxUserTjList" parameterType="AppBxUserTj" resultMap="AppBxUserTjResult">
        select t.id, t.user_id,t.bx_id, t.jjc_id, t.sr_sum, t.zc_sum, t.create_time,t.update_time,
        u.recode_code as recodeCode,
        u.nick_name nickName,
        u.phone as phone,
        u.head_portrait as headPortrait,
        bx.gift_name bxName
        from app_bx_user_tj t
        LEFT JOIN app_user u on(u.id=t.user_id)

        LEFT JOIN app_gift bx on(bx.id=t.bx_id)
        where 1=1
        <if test="recodeCode!=null and recodeCode!=''">
            and u.recode_code like concat('%', #{recodeCode}, '%')
        </if>
        <if test="nickName!=null and nickName!=''">
            and u.nick_name like concat('%', #{nickName}, '%')
        </if>
        <if test="phone!=null and phone!=''">
            and u.phone like concat('%', #{phone}, '%')
        </if>
        <if test="bxId!=null">
            and bx.id=#{bxId}
        </if>

        <if test="queryBeginTime != null and queryBeginTime != ''">
            and date_format(t.create_time,'%y%m%d') &gt;= date_format(#{queryBeginTime},'%y%m%d')
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            and date_format(t.create_time,'%y%m%d') &lt;= date_format(#{queryEndTime},'%y%m%d')
        </if>
        order by t.id desc
    </select>

    <select id="selectAppBxUserTjById" parameterType="Long" resultMap="AppBxUserTjResult">
        <include refid="selectAppBxUserTjVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppBxUserTj" parameterType="AppBxUserTj" useGeneratedKeys="true" keyProperty="id">
        insert into app_bx_user_tj
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="bxId != null">bx_id,</if>
            <if test="jjcId != null">jjc_id,</if>
            <if test="srSum != null">sr_sum,</if>
            <if test="zcSum != null">zc_sum,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="bxId != null">#{bxId},</if>
            <if test="jjcId != null">#{jjcId},</if>
            <if test="srSum != null">#{srSum},</if>
            <if test="zcSum != null">#{zcSum},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateAppBxUserTj" parameterType="AppBxUserTj">
        update app_bx_user_tj
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="bxId != null">bx_id = #{bxId},</if>
            <if test="jjcId != null">jjc_id = #{jjcId},</if>
            <if test="srSum != null">sr_sum = #{srSum},</if>
            <if test="zcSum != null">zc_sum = #{zcSum},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppBxUserTjById" parameterType="Long">
        delete from app_bx_user_tj where id = #{id}
    </delete>

    <delete id="deleteAppBxUserTjByIds" parameterType="String">
        delete from app_bx_user_tj where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getUserAppBxUserTj" parameterType="Long" resultMap="AppBxUserTjResult">
        <include refid="selectAppBxUserTjVo"/>
        where user_id = #{userId} and bx_id=#{bxId}
        order by id desc limit 0,1
    </select>
</mapper>