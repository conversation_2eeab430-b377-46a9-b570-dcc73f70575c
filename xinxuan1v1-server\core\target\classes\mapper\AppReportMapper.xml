<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hzy.core.entity.AppReport">
        <id column="id" property="id" />
        <result column="remark" property="remark" />
        <result column="type" property="type" />
        <result column="user_id" property="userId" />
        <result column="to_user_id" property="toUserId" />
        <result column="photos" property="photos" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, remark, type, user_id, to_user_id, photos, created_time, updated_time, deleted
    </sql>
    <select id="selectAppReportInfoList" resultType="com.hzy.core.model.vo.admin.AppReportVo">
        SELECT
            r.*,
            u.nick_name AS user_name,
            u.recode_code AS user_recode,
            tu.nick_name AS to_user_name,
            tu.recode_code AS to_user_recode
        FROM
            app_report r
        LEFT JOIN app_user u ON r.user_id = u.id
        LEFT JOIN app_user tu ON r.to_user_id = tu.id
        WHERE
            r.deleted = 0
            <if test="userRecode != null and userRecode != ''">
                AND u.recode_code = #{userRecode}
            </if>
            <if test="toUserRecode != null and toUserRecode != ''">
                AND tu.recode_code = #{toUserRecode}
            </if>
            ORDER BY
                r.id DESC
    </select>


</mapper>
