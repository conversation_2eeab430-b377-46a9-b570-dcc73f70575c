<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppGoldTopupConfigMapper">

    <resultMap type="AppGoldTopupConfig" id="AppGoldTopupConfigResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="isIos" column="is_ios"/>
        <result property="iosId" column="ios_id"/>
        <result property="isDel" column="is_del"/>
        <result property="price" column="price"/>
        <result property="goldNum" column="gold_num"/>
        <result property="giveNum" column="give_num"/>
    </resultMap>

    <sql id="selectAppGoldTopupConfigVo">
        select id,
        create_time,
        update_time,
        create_by,
        update_by,
        is_ios,
        ios_id,
        is_del,
        price,
        gold_num,
        give_num,
        video_card_amount
        from app_gold_topup_config
    </sql>

    <select id="selectAppGoldTopupConfigList" parameterType="AppGoldTopupConfig" resultMap="AppGoldTopupConfigResult">
        <include refid="selectAppGoldTopupConfigVo"/>
        where is_del=false
        <if test="isIos != null ">and is_ios = #{isIos}</if>
        <if test="iosId != null ">and ios_id = #{iosId}</if>
        <if test="price != null ">and price = #{price}</if>
        <if test="goldNum != null ">and gold_num = #{goldNum}</if>
        <if test="giveNum != null ">and give_num = #{giveNum}</if>
        order by price asc, create_time desc
    </select>

    <select id="selectAppGoldTopupConfigById" parameterType="Long" resultMap="AppGoldTopupConfigResult">
        <include refid="selectAppGoldTopupConfigVo"/>
        where id = #{id} and is_del=false
    </select>

    <insert id="insertAppGoldTopupConfig" parameterType="AppGoldTopupConfig" useGeneratedKeys="true" keyProperty="id">
        insert into app_gold_topup_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="isIos != null">is_ios,</if>
            <if test="iosId != null">ios_id,</if>
            <if test="isDel != null">is_del,</if>
            <if test="price != null">price,</if>
            <if test="goldNum != null">gold_num,</if>
            <if test="giveNum != null">give_num,</if>
            <if test="videoCardAmount!= null">video_card_amount,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="isIos != null">#{isIos},</if>
            <if test="iosId != null">#{iosId},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="price != null">#{price},</if>
            <if test="goldNum != null">#{goldNum},</if>
            <if test="giveNum != null">#{giveNum},</if>
            <if test="videoCardAmount!= null">#{videoCardAmount},</if>
        </trim>
    </insert>

    <update id="updateAppGoldTopupConfig" parameterType="AppGoldTopupConfig">
        update app_gold_topup_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="isIos != null">is_ios = #{isIos},</if>
            <if test="iosId != null">ios_id = #{iosId},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="price != null">price = #{price},</if>
            <if test="goldNum != null">gold_num = #{goldNum},</if>
            <if test="giveNum != null">give_num = #{giveNum},</if>
            <if test="videoCardAmount != null">video_card_amount = #{videoCardAmount},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppGoldTopupConfigById" parameterType="Long">
        delete
        from app_gold_topup_config
        where id = #{id}
    </delete>

    <delete id="deleteAppGoldTopupConfigByIds" parameterType="String">
        delete from app_gold_topup_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getGoldTopupConfigList" resultType="com.hzy.core.model.vo.app.AppGoldTopupConfigVo"
            parameterType="java.lang.Integer">
        select id as goodsId,
        if(ios_id = 0, null, ios_id) as iosId,
        price as price,
        gold_num as goldNum,
        give_num as giveNum,
        video_card_amount as videoCardAmount
        from app_gold_topup_config
        where is_del = false
        and is_ios = #{isIos}
        order by price asc, create_time desc
    </select>


    <select id="checkIosIdExist" resultType="java.lang.Boolean" parameterType="java.lang.Long">
        select if(count(*) > 0, 1, 0)
        from app_gold_topup_config
        where is_ios = true
        and ios_id = #{iosId}
        and is_del = false
        <if test="null!=id">
            and id !=#{id}
        </if>
    </select>
</mapper>