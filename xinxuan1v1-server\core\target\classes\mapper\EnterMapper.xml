<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.EnterMapper">


    <select id="getEnterList" resultType="com.hzy.core.entity.EnterEntity"
            parameterType="com.hzy.core.entity.EnterEntity">
        SELECT
        aga.id,
        aga.initiate_user_id AS userId,
        au.nick_name,
        au.recode_code AS recodeCode,
        acr.hall_owner_commission_scale AS anchorSelfLifting,
        aga.`status`,
        agm.type as anchorType ,
        su.nick_name as brokerName,
        aga.update_time as updateTime,
        aga.create_time as createTime,
        aga.guild_id as guildId,
        aga.remarks
        FROM
        app_guild_apply aga
        LEFT JOIN app_guild_member agm ON agm.user_id = aga.initiate_user_id
        LEFT JOIN app_user au ON au.id = aga.initiate_user_id
        LEFT JOIN app_chat_room acr ON acr.hall_owner_user_id = aga.initiate_user_id
        LEFT JOIN sys_user su on su.user_id =aga.sys_user_id
        where aga.guild_id=#{guildId}
        <if test="userId!=null">
            aga.initiate_user_id = #{userId}
        </if>
        <if test="anchorType!=null">
            and acr.type = #{anchorType}
        </if>
        <if test="status!=null">
            and aga.`status` = #{status}
        </if>
        <if test="sysUserIds != null ">
            AND aga.sys_user_id in
            <foreach collection="sysUserIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="startTime != null and startTime != ''">
            and date_format(aga.create_time,'%y%m%d') &gt;= date_format(#{startTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''">
            and date_format(aga.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
    </select>
</mapper>