<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppConfigMapper">

    <resultMap type="AppConfig" id="AppConfigResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="serviceUserId" column="service_user_id"/>
        <result property="familyMaxNum" column="family_max_num"/>
        <result property="voiceMinutesGold" column="voice_minutes_gold"/>
        <result property="videoMinutesGold" column="video_minutes_gold"/>
        <result property="isEnableSendVirtualMsg" column="is_enable_send_virtual_msg"/>
        <result property="communicateTelephoneIncomeScale" column="communicate_telephone_income_scale"/>
        <result property="temporarilyGoldPrice" column="temporarily_gold_price"/>
        <result property="sendMsgGoldPrice" column="send_msg_gold_price"/>
        <result property="temporarilyIncomeScale" column="temporarily_income_scale"/>
        <result property="sendMsgIncomeScale" column="send_msg_income_scale"/>
        <result property="giftIncomeScale" column="gift_income_scale"/>
        <result property="oneGoldEqRmb" column="one_gold_eq_rmb"/>
        <result property="oneRmbEqPoints" column="one_rmb_eq_points"/>
        <result property="onePointsEqRmb" column="one_points_eq_rmb"/>
        <result property="minWithdrawPoints" column="min_withdraw_points"/>
        <result property="onePointsEqGold" column="one_points_eq_gold"/>
        <result property="inviteGold"    column="invite_gold"    />
        <result property="inviteTopupScale"    column="invite_topup_scale"    />
        <result property="inviteGiveGiftScale"    column="invite_give_gift_scale"    />
        <result property="shareUrl"    column="share_url"    />
        <result property="newUserGoldCount"    column="new_user_gold_count"    />
        <result property="withdrawServiceChargeScale"    column="withdraw_service_charge_scale"    />
        <result property="rechargeLimitAmount"    column="recharge_limit_amount"    />
        <result property="guildCommissionScale"    column="guild_commission_scale"    />
        <result property="hallOwnerCommissionScale"    column="hall_owner_commission_scale"    />
        <result property="isFloatingScreen"    column="is_floating_screen"    />
        <result property="isChest"    column="is_chest"    />
        <result property="turntable1Price"    column="turntable_1_price"    />
        <result property="turntable10Price"    column="turntable_10_price"    />
        <result property="turntable100Price"    column="turntable_100_price"    />
        <result property="turntableStatus"    column="turntable_status"    />
        <result property="isAllFloatingScreen"    column="is_all_floating_screen"    />
        <result property="isOpenGfWxPay"    column="is_open_gf_wx_pay"    />
        <result property="isOpenGfZfbPay"    column="is_open_gf_zfb_pay"    />
        <result property="isOpenSdWxPay"    column="is_open_sd_wx_pay"    />
        <result property="isOpenSdZfbPay"    column="is_open_sd_zfb_pay"    />
        <result property="zfbTxConfigId"    column="zfb_tx_config_id"    />
        <result property="mzlsJcType"    column="mzls_jc_type"    />
        <result property="bxMinKzl"    column="bx_min_kzl"    />
        <result property="bxMaxKzl"    column="bx_max_kzl"    />
        <result property="mzlsMinKzl"    column="mzls_min_kzl"    />
        <result property="mzlsMaxKzl"    column="mzls_max_kzl"    />
        <result property="bxSlcBl"    column="bx_slc_bl"    />
        <result property="mzlsSlcBl"    column="mzls_slc_bl"    />
        <result property="skillPriceScale"    column="skill_price_scale"    />
        <result property="bxJcType"    column="bx_jc_type"    />
        <result property="sumPutGl"    column="sum_put_gl"    />
        <result property="sumOutGl"    column="sum_out_gl"    />
        <result property="sumProfitGl"    column="sum_profit_gl"    />
        <result property="extractedNumGl"    column="extracted_num_gl"    />
        <result property="mzlsBlGl"    column="mzls_bl_gl"    />
        <result property="minKzl"    column="min_kzl"    />
        <result property="maxKzl"    column="max_kzl"    />
        <result property="chatRoomGiftPrice" column="chat_room_gift_price"/>
        <result property="fullServerFloatingScreenPrice" column="full_server_floating_screen_price"/>
    </resultMap>

    <sql id="selectAppConfigVo">
        select
        id,
        create_time,
        update_time,
        create_by,
        update_by,
        service_user_id,
        family_max_num,
        voice_minutes_gold,
        video_minutes_gold,
        is_enable_send_virtual_msg,
        communicate_telephone_income_scale,
        temporarily_gold_price,
        send_msg_gold_price,
        temporarily_income_scale,
        send_msg_income_scale,
        gift_income_scale,
        one_gold_eq_rmb,
        one_rmb_eq_points,
        one_points_eq_rmb,
        one_points_eq_gold,
        min_withdraw_points,
        invite_gold,
        invite_topup_scale,
        invite_give_gift_scale,
        share_url,
        new_user_gold_count,
        withdraw_service_charge_scale,
        recharge_limit_amount,
        guild_commission_scale,
        hall_owner_commission_scale,
        is_floating_screen,
        is_chest,
        turntable_1_price,
        turntable_10_price,
        turntable_100_price,
        turntable_status,
        is_all_floating_screen,
        is_open_gf_wx_pay,
        is_open_gf_zfb_pay,
        is_open_sd_wx_pay,
        is_open_sd_zfb_pay,
        zfb_tx_config_id,
        mzls_jc_type,
        bx_min_kzl,
        bx_max_kzl,
        mzls_min_kzl,
        mzls_max_kzl,
        bx_slc_bl,
        mzls_slc_bl,
        skill_price_scale,
        bx_jc_type,
        sum_put_gl,
        sum_out_gl,
        sum_profit_gl,
        extracted_num_gl,
        mzls_bl_gl,
        min_kzl,
        max_kzl,
        game_show_amount from app_config
    </sql>

    <select id="selectAppConfigList" parameterType="AppConfig" resultMap="AppConfigResult">
        <include refid="selectAppConfigVo"/>
        <where>
            <if test="serviceUserId != null ">and service_user_id = #{serviceUserId}</if>
            <if test="familyMaxNum != null ">and family_max_num = #{familyMaxNum}</if>
            <if test="voiceMinutesGold != null ">and voice_minutes_gold = #{voiceMinutesGold}</if>
            <if test="videoMinutesGold != null ">and video_minutes_gold = #{videoMinutesGold}</if>
            <if test="isEnableSendVirtualMsg != null ">and is_enable_send_virtual_msg = #{isEnableSendVirtualMsg}</if>
            <if test="communicateTelephoneIncomeScale != null ">and communicate_telephone_income_scale =
                #{communicateTelephoneIncomeScale}
            </if>
            <if test="temporarilyGoldPrice != null ">and temporarily_gold_price = #{temporarilyGoldPrice}</if>
            <if test="sendMsgGoldPrice != null ">and send_msg_gold_price = #{sendMsgGoldPrice}</if>
            <if test="temporarilyIncomeScale != null ">and temporarily_income_scale = #{temporarilyIncomeScale}</if>
            <if test="sendMsgIncomeScale != null ">and send_msg_income_scale = #{sendMsgIncomeScale}</if>
            <if test="giftIncomeScale != null ">and gift_income_scale = #{giftIncomeScale}</if>
            <if test="oneGoldEqRmb != null ">and one_gold_eq_rmb = #{oneGoldEqRmb}</if>
            <if test="oneRmbEqPoints != null ">and one_rmb_eq_points = #{oneRmbEqPoints}</if>
            <if test="onePointsEqRmb != null ">and one_points_eq_rmb = #{onePointsEqRmb}</if>
            <if test="minWithdrawPoints != null ">and min_withdraw_points = #{minWithdrawPoints}</if>
            <if test="inviteGold != null ">and invite_gold = #{inviteGold}</if>
            <if test="inviteTopupScale != null ">and invite_topup_scale = #{inviteTopupScale}</if>
            <if test="inviteGiveGiftScale != null ">and invite_give_gift_scale = #{inviteGiveGiftScale}</if>
            <if test="shareUrl != null  and shareUrl != ''">and share_url = #{shareUrl}</if>
            <if test="newUserGoldCount != null ">and new_user_gold_count = #{newUserGoldCount}</if>
            <if test="withdrawServiceChargeScale != null ">and withdraw_service_charge_scale =
                #{withdrawServiceChargeScale}
            </if>
            <if test="rechargeLimitAmount != null ">and recharge_limit_amount = #{rechargeLimitAmount}</if>
            <if test="guildCommissionScale != null ">and guild_commission_scale = #{guildCommissionScale}</if>
            <if test="hallOwnerCommissionScale != null ">and hall_owner_commission_scale = #{hallOwnerCommissionScale}
            </if>
            <if test="isFloatingScreen != null ">and is_floating_screen = #{isFloatingScreen}</if>
            <if test="isChest != null ">and is_chest = #{isChest}</if>
            <if test="turntable1Price != null ">and turntable_1_price = #{turntable1Price}</if>
            <if test="turntable10Price != null ">and turntable_10_price = #{turntable10Price}</if>
            <if test="turntable100Price != null ">and turntable_100_price = #{turntable100Price}</if>
            <if test="turntableStatus != null ">and turntable_status = #{turntableStatus}</if>
            <if test="isAllFloatingScreen != null ">and is_all_floating_screen = #{isAllFloatingScreen}</if>
            <if test="isOpenGfWxPay != null ">and is_open_gf_wx_pay = #{isOpenGfWxPay}</if>
            <if test="isOpenGfZfbPay != null ">and is_open_gf_zfb_pay = #{isOpenGfZfbPay}</if>
            <if test="isOpenSdWxPay != null ">and is_open_sd_wx_pay = #{isOpenSdWxPay}</if>
            <if test="isOpenSdZfbPay != null ">and is_open_sd_zfb_pay = #{isOpenSdZfbPay}</if>
            <if test="zfbTxConfigId != null ">and zfb_tx_config_id = #{zfbTxConfigId}</if>
            <if test="mzlsJcType != null ">and mzls_jc_type = #{mzlsJcType}</if>
            <if test="bxMinKzl != null ">and bx_min_kzl = #{bxMinKzl}</if>
            <if test="bxMaxKzl != null ">and bx_max_kzl = #{bxMaxKzl}</if>
            <if test="mzlsMinKzl != null ">and mzls_min_kzl = #{mzlsMinKzl}</if>
            <if test="mzlsMaxKzl != null ">and mzls_max_kzl = #{mzlsMaxKzl}</if>
            <if test="bxSlcBl != null ">and bx_slc_bl = #{bxSlcBl}</if>
            <if test="mzlsSlcBl != null ">and mzls_slc_bl = #{mzlsSlcBl}</if>
            <if test="skillPriceScale != null ">and skill_price_scale = #{skillPriceScale}</if>
            <if test="bxJcType != null ">and bx_jc_type = #{bxJcType}</if>
            <if test="sumPutGl != null ">and sum_put_gl = #{sumPutGl}</if>
            <if test="sumOutGl != null ">and sum_out_gl = #{sumOutGl}</if>
            <if test="sumProfitGl != null ">and sum_profit_gl = #{sumProfitGl}</if>
            <if test="extractedNumGl != null ">and extracted_num_gl = #{extractedNumGl}</if>
            <if test="mzlsBlGl != null ">and mzls_bl_gl = #{mzlsBlGl}</if>
            <if test="minKzl != null ">and min_kzl = #{minKzl}</if>
            <if test="maxKzl != null ">and max_kzl = #{maxKzl}</if>
        </where>
    </select>

    <select id="selectAppConfigById" parameterType="Long" resultMap="AppConfigResult">
        <include refid="selectAppConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppConfig" parameterType="AppConfig" useGeneratedKeys="true" keyProperty="id">
        insert into app_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="serviceUserId != null">service_user_id,</if>
            <if test="familyMaxNum != null">family_max_num,</if>
            <if test="voiceMinutesGold != null">voice_minutes_gold,</if>
            <if test="videoMinutesGold != null">video_minutes_gold,</if>
            <if test="isEnableSendVirtualMsg != null">is_enable_send_virtual_msg,</if>
            <if test="communicateTelephoneIncomeScale != null">communicate_telephone_income_scale,</if>
            <if test="temporarilyGoldPrice != null">temporarily_gold_price,</if>
            <if test="sendMsgGoldPrice != null">send_msg_gold_price,</if>
            <if test="temporarilyIncomeScale != null">temporarily_income_scale,</if>
            <if test="sendMsgIncomeScale != null">send_msg_income_scale,</if>
            <if test="giftIncomeScale != null">gift_income_scale,</if>
            <if test="oneGoldEqRmb != null">one_gold_eq_rmb,</if>
            <if test="oneRmbEqPoints != null">one_rmb_eq_points,</if>
            <if test="onePointsEqRmb != null">one_points_eq_rmb,</if>
            <if test="minWithdrawPoints != null">min_withdraw_points,</if>
            <if test="inviteGold != null">invite_gold,</if>
            <if test="inviteTopupScale != null">invite_topup_scale,</if>
            <if test="inviteGiveGiftScale != null">invite_give_gift_scale,</if>
            <if test="shareUrl != null">share_url,</if>
            <if test="newUserGoldCount != null">new_user_gold_count,</if>
            <if test="withdrawServiceChargeScale != null">withdraw_service_charge_scale,</if>
            <if test="rechargeLimitAmount != null">recharge_limit_amount,</if>
            <if test="guildCommissionScale != null">guild_commission_scale,</if>
            <if test="hallOwnerCommissionScale != null">hall_owner_commission_scale,</if>
            <if test="isFloatingScreen != null">is_floating_screen,</if>
            <if test="isChest != null">is_chest,</if>
            <if test="turntable1Price != null">turntable_1_price,</if>
            <if test="turntable10Price != null">turntable_10_price,</if>
            <if test="turntable100Price != null">turntable_100_price,</if>
            <if test="turntableStatus != null">turntable_status,</if>
            <if test="isAllFloatingScreen != null">is_all_floating_screen,</if>
            <if test="isOpenGfWxPay != null">is_open_gf_wx_pay,</if>
            <if test="isOpenGfZfbPay != null">is_open_gf_zfb_pay,</if>
            <if test="isOpenSdWxPay != null">is_open_sd_wx_pay,</if>
            <if test="isOpenSdZfbPay != null">is_open_sd_zfb_pay,</if>
            <if test="zfbTxConfigId != null">zfb_tx_config_id,</if>
            <if test="mzlsJcType != null">mzls_jc_type,</if>
            <if test="bxMinKzl != null">bx_min_kzl,</if>
            <if test="bxMaxKzl != null">bx_max_kzl,</if>
            <if test="mzlsMinKzl != null">mzls_min_kzl,</if>
            <if test="mzlsMaxKzl != null">mzls_max_kzl,</if>
            <if test="bxSlcBl != null">bx_slc_bl,</if>
            <if test="mzlsSlcBl != null">mzls_slc_bl,</if>
            <if test="skillPriceScale != null">skill_price_scale,</if>
            <if test="bxJcType != null">bx_jc_type,</if>
            <if test="sumPutGl != null">sum_put_gl,</if>
            <if test="sumOutGl != null">sum_out_gl,</if>
            <if test="sumProfitGl != null">sum_profit_gl,</if>
            <if test="extractedNumGl != null">extracted_num_gl,</if>
            <if test="mzlsBlGl != null">mzls_bl_gl,</if>
            <if test="minKzl != null">min_kzl,</if>
            <if test="maxKzl != null">max_kzl,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="serviceUserId != null">#{serviceUserId},</if>
            <if test="familyMaxNum != null">#{familyMaxNum},</if>
            <if test="voiceMinutesGold != null">#{voiceMinutesGold},</if>
            <if test="videoMinutesGold != null">#{videoMinutesGold},</if>
            <if test="isEnableSendVirtualMsg != null">#{isEnableSendVirtualMsg},</if>
            <if test="communicateTelephoneIncomeScale != null">#{communicateTelephoneIncomeScale},</if>
            <if test="temporarilyGoldPrice != null">#{temporarilyGoldPrice},</if>
            <if test="sendMsgGoldPrice != null">#{sendMsgGoldPrice},</if>
            <if test="temporarilyIncomeScale != null">#{temporarilyIncomeScale},</if>
            <if test="sendMsgIncomeScale != null">#{sendMsgIncomeScale},</if>
            <if test="giftIncomeScale != null">#{giftIncomeScale},</if>
            <if test="oneGoldEqRmb != null">#{oneGoldEqRmb},</if>
            <if test="oneRmbEqPoints != null">#{oneRmbEqPoints},</if>
            <if test="onePointsEqRmb != null">#{onePointsEqRmb},</if>
            <if test="minWithdrawPoints != null">#{minWithdrawPoints},</if>
            <if test="inviteGold != null">#{inviteGold},</if>
            <if test="inviteTopupScale != null">#{inviteTopupScale},</if>
            <if test="inviteGiveGiftScale != null">#{inviteGiveGiftScale},</if>
            <if test="shareUrl != null">#{shareUrl},</if>
            <if test="newUserGoldCount != null">#{newUserGoldCount},</if>
            <if test="withdrawServiceChargeScale != null">#{withdrawServiceChargeScale},</if>
            <if test="rechargeLimitAmount != null">#{rechargeLimitAmount},</if>
            <if test="guildCommissionScale != null">#{guildCommissionScale},</if>
            <if test="hallOwnerCommissionScale != null">#{hallOwnerCommissionScale},</if>
            <if test="isFloatingScreen != null">#{isFloatingScreen},</if>
            <if test="isChest != null">#{isChest},</if>
            <if test="turntable1Price != null">#{turntable1Price},</if>
            <if test="turntable10Price != null">#{turntable10Price},</if>
            <if test="turntable100Price != null">#{turntable100Price},</if>
            <if test="turntableStatus != null">#{turntableStatus},</if>
            <if test="isAllFloatingScreen != null">#{isAllFloatingScreen},</if>
            <if test="isOpenGfWxPay != null">#{isOpenGfWxPay},</if>
            <if test="isOpenGfZfbPay != null">#{isOpenGfZfbPay},</if>
            <if test="isOpenSdWxPay != null">#{isOpenSdWxPay},</if>
            <if test="isOpenSdZfbPay != null">#{isOpenSdZfbPay},</if>
            <if test="zfbTxConfigId != null">#{zfbTxConfigId},</if>
            <if test="mzlsJcType != null">#{mzlsJcType},</if>
            <if test="bxMinKzl != null">#{bxMinKzl},</if>
            <if test="bxMaxKzl != null">#{bxMaxKzl},</if>
            <if test="mzlsMinKzl != null">#{mzlsMinKzl},</if>
            <if test="mzlsMaxKzl != null">#{mzlsMaxKzl},</if>
            <if test="bxSlcBl != null">#{bxSlcBl},</if>
            <if test="mzlsSlcBl != null">#{mzlsSlcBl},</if>
            <if test="skillPriceScale != null">#{skillPriceScale},</if>
            <if test="bxJcType != null">#{bxJcType},</if>
            <if test="sumPutGl != null">#{sumPutGl},</if>
            <if test="sumOutGl != null">#{sumOutGl},</if>
            <if test="sumProfitGl != null">#{sumProfitGl},</if>
            <if test="extractedNumGl != null">#{extractedNumGl},</if>
            <if test="mzlsBlGl != null">#{mzlsBlGl},</if>
            <if test="minKzl != null">#{minKzl},</if>
            <if test="maxKzl != null">#{maxKzl},</if>
        </trim>
    </insert>

    <update id="updateAppConfig" parameterType="AppConfig">
        update app_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="serviceUserId != null">service_user_id = #{serviceUserId},</if>
            <if test="familyMaxNum != null">family_max_num = #{familyMaxNum},</if>
            <if test="voiceMinutesGold != null">voice_minutes_gold = #{voiceMinutesGold},</if>
            <if test="videoMinutesGold != null">video_minutes_gold = #{videoMinutesGold},</if>
            <if test="isEnableSendVirtualMsg != null">is_enable_send_virtual_msg = #{isEnableSendVirtualMsg},</if>
            <if test="communicateTelephoneIncomeScale != null">communicate_telephone_income_scale =
                #{communicateTelephoneIncomeScale},
            </if>
            <if test="temporarilyGoldPrice != null">temporarily_gold_price = #{temporarilyGoldPrice},</if>
            <if test="sendMsgGoldPrice != null">send_msg_gold_price = #{sendMsgGoldPrice},</if>
            <if test="temporarilyIncomeScale != null">temporarily_income_scale = #{temporarilyIncomeScale},</if>
            <if test="sendMsgIncomeScale != null">send_msg_income_scale = #{sendMsgIncomeScale},</if>
            <if test="giftIncomeScale != null">gift_income_scale = #{giftIncomeScale},</if>
            <if test="oneGoldEqRmb != null">one_gold_eq_rmb = #{oneGoldEqRmb},</if>
            <if test="oneRmbEqPoints != null">one_rmb_eq_points = #{oneRmbEqPoints},</if>
            <if test="onePointsEqRmb != null">one_points_eq_rmb = #{onePointsEqRmb},</if>
            <if test="onePointsEqGold != null">one_points_eq_gold = #{onePointsEqGold},</if>
            <if test="oneVideoCardEqGold != null">one_video_card_eq_gold = #{oneVideoCardEqGold},</if>
            <if test="minWithdrawPoints != null">min_withdraw_points = #{minWithdrawPoints},</if>
            <if test="inviteGold != null">invite_gold = #{inviteGold},</if>
            <if test="inviteTopupScale != null">invite_topup_scale = #{inviteTopupScale},</if>
            <if test="inviteGiveGiftScale != null">invite_give_gift_scale = #{inviteGiveGiftScale},</if>
            <if test="shareUrl != null">share_url = #{shareUrl},</if>
            <if test="newUserGoldCount != null">new_user_gold_count = #{newUserGoldCount},</if>
            <if test="withdrawServiceChargeScale != null">withdraw_service_charge_scale =
                #{withdrawServiceChargeScale},
            </if>
            <if test="rechargeLimitAmount != null">recharge_limit_amount = #{rechargeLimitAmount},</if>
            <if test="guildCommissionScale != null">guild_commission_scale = #{guildCommissionScale},</if>
            <if test="hallOwnerCommissionScale != null">hall_owner_commission_scale = #{hallOwnerCommissionScale},</if>
            <if test="isFloatingScreen != null">is_floating_screen = #{isFloatingScreen},</if>
            <if test="isChest != null">is_chest = #{isChest},</if>
            <if test="turntable1Price != null">turntable_1_price = #{turntable1Price},</if>
            <if test="turntable10Price != null">turntable_10_price = #{turntable10Price},</if>
            <if test="turntable100Price != null">turntable_100_price = #{turntable100Price},</if>
            <if test="turntableStatus != null">turntable_status = #{turntableStatus},</if>
            <if test="isAllFloatingScreen != null">is_all_floating_screen = #{isAllFloatingScreen},</if>
            <if test="isOpenGfWxPay != null">is_open_gf_wx_pay = #{isOpenGfWxPay},</if>
            <if test="isOpenGfZfbPay != null">is_open_gf_zfb_pay = #{isOpenGfZfbPay},</if>
            <if test="isOpenSdWxPay != null">is_open_sd_wx_pay = #{isOpenSdWxPay},</if>
            <if test="isOpenSdZfbPay != null">is_open_sd_zfb_pay = #{isOpenSdZfbPay},</if>
            <if test="zfbTxConfigId != null">zfb_tx_config_id = #{zfbTxConfigId},</if>
            <if test="mzlsJcType != null">mzls_jc_type = #{mzlsJcType},</if>
            <if test="bxMinKzl != null">bx_min_kzl = #{bxMinKzl},</if>
            <if test="bxMaxKzl != null">bx_max_kzl = #{bxMaxKzl},</if>
            <if test="mzlsMinKzl != null">mzls_min_kzl = #{mzlsMinKzl},</if>
            <if test="mzlsMaxKzl != null">mzls_max_kzl = #{mzlsMaxKzl},</if>
            <if test="bxSlcBl != null">bx_slc_bl = #{bxSlcBl},</if>
            <if test="mzlsSlcBl != null">mzls_slc_bl = #{mzlsSlcBl},</if>
            <if test="skillPriceScale != null">skill_price_scale = #{skillPriceScale},</if>
            <if test="bxJcType != null">bx_jc_type = #{bxJcType},</if>
            <if test="sumPutGl != null">sum_put_gl = #{sumPutGl},</if>
            <if test="sumOutGl != null">sum_out_gl = #{sumOutGl},</if>
            <if test="sumProfitGl != null">sum_profit_gl = #{sumProfitGl},</if>
            <if test="extractedNumGl != null">extracted_num_gl = #{extractedNumGl},</if>
            <if test="mzlsBlGl != null">mzls_bl_gl = #{mzlsBlGl},</if>
            <if test="minKzl != null">min_kzl = #{minKzl},</if>
            <if test="maxKzl != null">max_kzl = #{maxKzl},</if>
            <if test="gameShowAmount != null">game_show_amount = #{gameShowAmount},</if>
            <if test="chatRoomGiftPrice != null">chat_room_gift_price = #{chatRoomGiftPrice},</if>
            <if test="fullServerFloatingScreenPrice != null">full_server_floating_screen_price= #{fullServerFloatingScreenPrice},</if>
            <if test="homeGiftTopLine != null">home_gift_top_line= #{homeGiftTopLine},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppConfigById" parameterType="Long">
        delete from app_config where id = #{id}
    </delete>

    <delete id="deleteAppConfigByIds" parameterType="String">
        delete from app_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectAppCustomerServiceById" resultType="com.hzy.core.entity.AppConfig">
        select * from app_config limit 0,1
    </select>
    <select id="getAppConfig" resultType="com.hzy.core.entity.AppConfig">
        select * from app_config  limit 0,1
    </select>

</mapper>
