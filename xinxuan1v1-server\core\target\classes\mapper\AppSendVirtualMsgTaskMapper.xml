<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppSendVirtualMsgTaskMapper">

    <resultMap type="AppSendVirtualMsgTask" id="AppSendVirtualMsgTaskResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="goalUserId" column="goal_user_id"/>
        <result property="isPause" column="is_pause"/>
        <result property="nextSendTime" column="next_send_time"/>
    </resultMap>

    <sql id="selectAppSendVirtualMsgTaskVo">
        select id, create_time, update_time, goal_user_id, is_pause, next_send_time
        from app_send_virtual_msg_task
    </sql>

    <select id="selectAppSendVirtualMsgTaskList" parameterType="AppSendVirtualMsgTask"
            resultMap="AppSendVirtualMsgTaskResult">
        <include refid="selectAppSendVirtualMsgTaskVo"/>
        <where>
            <if test="goalUserId != null ">and goal_user_id = #{goalUserId}</if>
            <if test="isPause != null ">and is_pause = #{isPause}</if>
            <if test="nextSendTime != null ">and next_send_time = #{nextSendTime}</if>
        </where>
    </select>

    <select id="selectAppSendVirtualMsgTaskById" parameterType="Long" resultMap="AppSendVirtualMsgTaskResult">
        <include refid="selectAppSendVirtualMsgTaskVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppSendVirtualMsgTask" parameterType="AppSendVirtualMsgTask" useGeneratedKeys="true"
            keyProperty="id">
        insert into app_send_virtual_msg_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="goalUserId != null">goal_user_id,</if>
            <if test="isPause != null">is_pause,</if>
            <if test="nextSendTime != null">next_send_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="goalUserId != null">#{goalUserId},</if>
            <if test="isPause != null">#{isPause},</if>
            <if test="nextSendTime != null">#{nextSendTime},</if>
        </trim>
    </insert>

    <update id="updateAppSendVirtualMsgTask" parameterType="AppSendVirtualMsgTask">
        update app_send_virtual_msg_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="goalUserId != null">goal_user_id = #{goalUserId},</if>
            <if test="isPause != null">is_pause = #{isPause},</if>
            <if test="nextSendTime != null">next_send_time = #{nextSendTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppSendVirtualMsgTaskById" parameterType="Long">
        delete
        from app_send_virtual_msg_task
        where id = #{id}
    </delete>

    <delete id="deleteAppSendVirtualMsgTaskByIds" parameterType="String">
        delete from app_send_virtual_msg_task where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="selectAppSendVirtualMsgTaskByUserId" parameterType="Long" resultMap="AppSendVirtualMsgTaskResult">
        <include refid="selectAppSendVirtualMsgTaskVo"/>
        where goal_user_id = #{userId}
    </select>

    <delete id="deleteAppSendVirtualMsgTaskByUserId" parameterType="Long">
        delete
        from app_send_virtual_msg_task
        where goal_user_id = #{userId}
    </delete>

    <select id="getAllEffectiveTaskList" resultMap="AppSendVirtualMsgTaskResult">
        <include refid="selectAppSendVirtualMsgTaskVo"/>
        where is_pause = false
        and goal_user_id > 0
        and now() >= next_send_time
    </select>

    <update id="pauseTask" parameterType="java.lang.Long">
        update app_send_virtual_msg_task
        set is_pause= true,
        update_time=now()
        where id = #{id}
    </update>
</mapper>