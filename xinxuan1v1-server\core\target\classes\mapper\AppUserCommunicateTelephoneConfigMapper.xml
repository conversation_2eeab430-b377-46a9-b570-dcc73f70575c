<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppUserCommunicateTelephoneConfigMapper">

    <resultMap type="AppUserCommunicateTelephoneConfig" id="AppUserCommunicateTelephoneConfigResult">
        <result property="id" column="id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="videoMinutesGold" column="video_minutes_gold"/>
        <result property="voiceMinutesGold" column="voice_minutes_gold"/>
        <result property="isEnableVideo" column="is_enable_video"/>
        <result property="isEnableVoice" column="is_enable_voice"/>
        <result property="sendMsgGoldPrice" column="send_msg_gold_price"/>
    </resultMap>

    <sql id="selectAppUserCommunicateTelephoneConfigVo">
        select id, create_time, update_time, user_id, video_minutes_gold, voice_minutes_gold, is_enable_video,
        is_enable_voice, voice_total_time, video_total_time, is_banned_voice, is_banned_video, is_banned_chat,
        send_msg_gold_price from app_user_communicate_telephone_config
    </sql>

    <select id="selectAppUserCommunicateTelephoneConfigList" parameterType="AppUserCommunicateTelephoneConfig"
            resultMap="AppUserCommunicateTelephoneConfigResult">
        <include refid="selectAppUserCommunicateTelephoneConfigVo"/>
        <where>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="videoMinutesGold != null ">and video_minutes_gold = #{videoMinutesGold}</if>
            <if test="voiceMinutesGold != null ">and voice_minutes_gold = #{voiceMinutesGold}</if>
            <if test="isEnableVideo != null ">and is_enable_video = #{isEnableVideo}</if>
            <if test="sendMsgGoldPrice != null ">and send_msg_gold_price = #{sendMsgGoldPrice}</if>
        </where>
    </select>

    <select id="selectAppUserCommunicateTelephoneConfigById" parameterType="Long"
            resultMap="AppUserCommunicateTelephoneConfigResult">
        <include refid="selectAppUserCommunicateTelephoneConfigVo"/>
        where id = #{id}
    </select>

    <insert id="insertAppUserCommunicateTelephoneConfig" parameterType="AppUserCommunicateTelephoneConfig"
            useGeneratedKeys="true" keyProperty="id">
        insert into app_user_communicate_telephone_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="videoMinutesGold != null">video_minutes_gold,</if>
            <if test="voiceMinutesGold != null">voice_minutes_gold,</if>
            <if test="isEnableVideo != null">is_enable_video,</if>
            <if test="isEnableVoice != null">is_enable_voice,</if>
            <if test="sendMsgGoldPrice != null">send_msg_gold_price,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="videoMinutesGold != null">#{videoMinutesGold},</if>
            <if test="voiceMinutesGold != null">#{voiceMinutesGold},</if>
            <if test="isEnableVideo != null">#{isEnableVideo},</if>
            <if test="isEnableVoice != null">#{isEnableVoice},</if>
            <if test="sendMsgGoldPrice != null">#{sendMsgGoldPrice},</if>
        </trim>
    </insert>

    <update id="updateAppUserCommunicateTelephoneConfig" parameterType="AppUserCommunicateTelephoneConfig">
        update app_user_communicate_telephone_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="videoMinutesGold != null">video_minutes_gold = #{videoMinutesGold},</if>
            <if test="voiceMinutesGold != null">voice_minutes_gold = #{voiceMinutesGold},</if>
            <if test="isEnableVideo != null">is_enable_video = #{isEnableVideo},</if>
            <if test="isEnableVoice != null">is_enable_voice = #{isEnableVoice},</if>
            <if test="voiceTotalTime != null">voice_total_time = #{voiceTotalTime},</if>
            <if test="videoTotalTime != null">video_total_time = #{videoTotalTime},</if>
            <if test="isBannedVoice != null">is_banned_voice = #{isBannedVoice},</if>
            <if test="isBannedVideo != null">is_banned_video = #{isBannedVideo},</if>
            <if test="isBannedChat != null">is_banned_chat = #{isBannedChat},</if>
            <if test="sendMsgGoldPrice != null">send_msg_gold_price = #{sendMsgGoldPrice},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAppUserCommunicateTelephoneConfigById" parameterType="Long">
        delete
        from app_user_communicate_telephone_config
        where id = #{id}
    </delete>

    <delete id="deleteAppUserCommunicateTelephoneConfigByIds" parameterType="String">
        delete from app_user_communicate_telephone_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getUserCommunicateTelephoneConfigByUserId" parameterType="Long"
            resultMap="AppUserCommunicateTelephoneConfigResult">
        <include refid="selectAppUserCommunicateTelephoneConfigVo"/>
        where user_id = #{userId}
        order by create_time desc limit 0,1
    </select>
</mapper>