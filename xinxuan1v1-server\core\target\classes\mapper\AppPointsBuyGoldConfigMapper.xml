<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzy.core.mapper.AppPointsBuyGoldConfigMapper">

    <resultMap type="AppPointsBuyGoldConfig" id="AppPointsBuyGoldConfigResult">
        <result property="id" column="id"/>
        <result property="pointsPrice" column="points_price"/>
        <result property="goldNum" column="gold_num"/>
        <result property="isDel" column="is_del"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="selectAppPointsBuyGoldConfigVo">
        select id, points_price, gold_num, is_del, create_time, update_time, create_by, update_by from
        app_points_buy_gold_config
    </sql>

    <select id="selectAppPointsBuyGoldConfigList" parameterType="AppPointsBuyGoldConfig"
            resultMap="AppPointsBuyGoldConfigResult">
        <include refid="selectAppPointsBuyGoldConfigVo"/>
        where is_del=false
        <if test="pointsPrice != null ">and points_price = #{pointsPrice}</if>
        <if test="goldNum != null ">and gold_num = #{goldNum}</if>
        order by points_price asc,id desc
    </select>

    <select id="selectAppPointsBuyGoldConfigById" parameterType="Long" resultMap="AppPointsBuyGoldConfigResult">
        <include refid="selectAppPointsBuyGoldConfigVo"/>
        where id = #{id} and is_del=false
    </select>

    <insert id="insertAppPointsBuyGoldConfig" parameterType="AppPointsBuyGoldConfig" useGeneratedKeys="true"
            keyProperty="id">
        insert into app_points_buy_gold_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pointsPrice != null">points_price,</if>
            <if test="goldNum != null">gold_num,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pointsPrice != null">#{pointsPrice},</if>
            <if test="goldNum != null">#{goldNum},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateAppPointsBuyGoldConfig" parameterType="AppPointsBuyGoldConfig">
        update app_points_buy_gold_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="pointsPrice != null">points_price = #{pointsPrice},</if>
            <if test="goldNum != null">gold_num = #{goldNum},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteAppPointsBuyGoldConfigById">
        update app_points_buy_gold_config set is_del=1,update_time=now()
        <if test="updateBy!=null and updateBy!=''">
            ,update_by=#{updateBy}
        </if>
        where id = #{id}
    </update>

    <delete id="deleteAppPointsBuyGoldConfigByIds" parameterType="String">
        delete from app_points_buy_gold_config where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getPointsBuyGoldConfigList" resultType="com.hzy.core.model.vo.app.AppPointsBuyGoldConfigVo">
        select
        id,
        points_price as pointsPrice,
        gold_num as goldNum
        from
        app_points_buy_gold_config
        where is_del=false
        order by points_price asc,id desc
    </select>
</mapper>