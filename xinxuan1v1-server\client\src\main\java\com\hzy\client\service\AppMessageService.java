package com.hzy.client.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.hzy.client.controller.core.AppBaseController;
import com.hzy.client.webSocket.PrivateLetterWebSocket;
import com.hzy.core.config.RedisCache;
import com.hzy.core.constant.*;
import com.hzy.core.entity.*;
import com.hzy.core.enums.*;
import com.hzy.core.exception.AppException;
import com.hzy.core.mapper.*;
import com.hzy.core.model.bo.TelResult;
import com.hzy.core.model.vo.AjaxResult;
import com.hzy.core.model.vo.R;
import com.hzy.core.model.vo.app.*;
import com.hzy.core.page.TableDataInfo;
import com.hzy.core.service.*;
import com.hzy.core.service.async.ClientAsyncFactory;
import com.hzy.core.service.common.AppTitleNobilityService;
import com.hzy.core.utils.AppAliPushUtil;
import com.hzy.core.utils.PageHelperUtils;
import com.hzy.core.utils.RegionUtil;
import com.hzy.core.utils.StringUtils;
import com.tencentyun.TLSSigAPIv2;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 消息服务
 */
@Slf4j
@Service
public class AppMessageService extends AppBaseController {

    /**
     * 发送红包手续费
     */
//    private final static BigDecimal sendRedPacketHandlingCharge = new BigDecimal("0.06");

    private final static ArrayBlockingQueue<Runnable> WORK_QUEUE = new ArrayBlockingQueue<>(3000);
    private final static RejectedExecutionHandler HANDLER = new ThreadPoolExecutor.CallerRunsPolicy();
    // 认证申请关键词
    private static final String AUTH_APPLY_KEYWORD = "认证申请";
    private static ThreadPoolExecutor executorService = new ThreadPoolExecutor(ThreadPoolConstants.corePoolSize, ThreadPoolConstants.maxPoolSize, ThreadPoolConstants.keepAliveSeconds, TimeUnit.SECONDS, WORK_QUEUE, HANDLER);
    private static ThreadPoolExecutor executorService2 = new ThreadPoolExecutor(50, 300, ThreadPoolConstants.keepAliveSeconds, TimeUnit.SECONDS, new LinkedBlockingQueue<>());
    @Resource
    private Redisson redisson;
    @Resource
    private AppChattingRecordsMapper appChattingRecordsMapper;
    @Resource
    private AppPrivateLetterListMapper appPrivateLetterListMapper;
    @Resource
    private AppPrivateLetterListService appPrivateLetterListService;
    @Resource
    private AppUserMapper appUserMapper;
    @Resource
    private PrivateLetterWebSocket privateLetterWebSocket;
    @Resource
    private AppFamilyMapper appFamilyMapper;
    @Resource
    private AppUserIntimacyMapper appUserIntimacyMapper;
    @Resource
    private AppFamilyMemberMapper appFamilyMemberMapper;
    @Resource
    private AppCommonService appCommonService;
    @Resource
    private RedisCache redisCache;
    @Resource
    private AppBlacklistMapper appBlacklistMapper;
    @Resource
    private AppCommunicateTelephoneRecordsService appCommunicateTelephoneRecordsService;
    @Resource
    private AppCommunicateTelephoneRecordsMapper appCommunicateTelephoneRecordsMapper;
    @Resource
    private AppChatBtnTypeMapper appChatBtnTypeMapper;
    @Resource
    private AppUserGoldBillMapper appUserGoldBillMapper;
    @Resource
    private AppUserPointsBillMapper appUserPointsBillMapper;
    @Resource
    private AppViewUserRecordsMapper appViewUserRecordsMapper;
    @Resource
    private AppUserPersonalDressingMapper appUserPersonalDressingMapper;
    @Resource
    private AppTitleNobilityService appTitleNobilityService;
    @Resource
    private AppSysMsgMapper appSysMsgMapper;
    @Resource
    private ClientAsyncFactory clientAsyncFactory;
    @Resource
    private AppUserTopUpGradeMapper appUserTopUpGradeMapper;
    @Resource
    private AppConfigMapper appConfigMapper;
    @Resource
    private AppUserService appUserService;
    @Resource
    private AppChattingRecordsService appChattingRecordsService;
    @Resource
    private AppUserCommunicateTelephoneConfigService appUserCommunicateTelephoneConfigService;
    @Resource
    private AppGiftService appGiftService;
    @Resource
    private AppUserGoldBillService appUserGoldBillService;
    @Resource
    private AppGiftIncomeConfigService appGiftIncomeConfigService;
    @Resource
    private AppUserFollowMapper appUserFollowMapper;
    @Resource
    private AppContentCheckService appContentCheckService;
    @Resource
    private AppAliPushUtil appAliPushUtil;
    @Resource
    private SysDictTypeService sysDictTypeService;
    @Resource
    private AppChatRoomMapper appChatRoomMapper;
    @Resource
    private AppGuildMemberMapper appGuildMemberMapper;
    @Resource
    private AppVipService appVipService;
    @Resource
    private AppUserVideoCardMapper appUserVideoCardMapper;

    /**
     * 保存语音通话的消息表记录
     *
     * @param communicateTelephoneRecords 沟通电话记录
     * @param isRead                      读取
     * @param type3                       通话状态
     * @return {@link AppChattingRecords }
     */
    private static AppChattingRecords saveVoiceRecord(AppCommunicateTelephoneRecords communicateTelephoneRecords, int isRead, AppCommunicateTelephoneStatusTypeEnums type3) {
        AppChattingRecords appChattingRecords = new AppChattingRecords();
        appChattingRecords.setSendUserId(communicateTelephoneRecords.getInitiateUserId());
        appChattingRecords.setReceiveUserId(communicateTelephoneRecords.getReceiveUserId());
        appChattingRecords.setSendTime(new Date());
        appChattingRecords.setIsRead(isRead);

        // 根据通话类型设置消息类型
        int callType = communicateTelephoneRecords.getType();
        if (callType == AppCommunicateTelephoneTypeEnums.TYPE2.getId() || callType == AppCommunicateTelephoneTypeEnums.TYPE4.getId()) {
            // 视频通话
            appChattingRecords.setMsgType((long) AppSendMsgTypeEnums.TYPE12.getId());
        } else {
            // 语音通话
            appChattingRecords.setMsgType((long) AppSendMsgTypeEnums.TYPE10.getId());
        }

        JSONObject content = new JSONObject();
        // 根据通话类型设置content内容
        if (callType == AppCommunicateTelephoneTypeEnums.TYPE2.getId() || callType == AppCommunicateTelephoneTypeEnums.TYPE4.getId()) {
            content.put("content", AppSendMsgTypeEnums.TYPE12.getDesc());
        } else {
            content.put("content", AppSendMsgTypeEnums.TYPE10.getDesc());
        }

        // 使用枚举值的desc作为flag
        content.put("flag", type3.getDesc());
        appChattingRecords.setContent(content.toJSONString());
        return appChattingRecords;
    }

    /**
     * 获取用户系统消息列表
     *
     * @param page
     * @param size
     * @param request
     * @return
     */
    public TableDataInfo getAppSysMsgList(int page, int size, HttpServletRequest request) {
        Long userId = getUserId(request);
        Page p = PageHelperUtils.startPage(page, size, true);
        List<AppSysMsgVo> list = appSysMsgMapper.getUserAppSysMsgList(userId);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        } else {
            list.forEach(msgVo -> {
                if (msgVo.getIsRead().intValue() == WhetherTypeEnum.NO.getName()) {// 未读就异步设置为已读
                    clientAsyncFactory.asyncSetAppSysMsgRead(msgVo.getMsgId());
                }
            });
        }
        return AjaxResult.getDataTable(list, p.getTotal());
    }

    /**
     * 获取用户系统消息详情
     *
     * @param msgId
     * @param request
     * @return
     */
    public AjaxResult getAppSysMsgDetails(Long msgId, HttpServletRequest request) {
        if (null == msgId || msgId.intValue() <= 0) {
            return AjaxResult.error("参数【msgId】为空");
        }
        Long userId = getUserId(request);
        AppSysMsgVo msgVo = appSysMsgMapper.getUserAppSysMsgDetails(userId, msgId);
        if (null == msgVo) {
            return AjaxResult.error("消息不存在");
        }
        if (msgVo.getIsRead().intValue() == WhetherTypeEnum.NO.getName()) {// 未读就异步设置为已读
            clientAsyncFactory.asyncSetAppSysMsgRead(msgVo.getMsgId());
        }
        return AjaxResult.success("查询成功", msgVo);
    }

    /**
     * 获取消息未读数
     *
     * @param request
     * @return
     */
    public AjaxResult getMsgUnreadCount(HttpServletRequest request) {
        Long userId = getUserId(request);

//        Integer greetUnreadCount =  appPrivateLetterListMapper.getTemporarilyPrivateLetterUnreadCount(userId);//打招呼未读数量
//        if(null==greetUnreadCount){
//            greetUnreadCount=0;
//        }
        int toViewRecordsUnreadCount = appViewUserRecordsMapper.getToViewRecordsUnreadCount(userId);// 谁看过我的未读数量

        Integer chatUnreadCount = appPrivateLetterListMapper.getChatUnreadCount(userId);// 聊天兑换框未读数量
        if (null == chatUnreadCount) {
            chatUnreadCount = 0;
        }
        Map<String, Object> result = new HashMap<>();

        result.put("sysMsgNoReadCount", appSysMsgMapper.getUserSysMsgNoReadCount(userId));// 系统消息未读

        result.put("greetUnreadCount", 0);

        result.put("toViewRecordsUnreadCount", toViewRecordsUnreadCount);

        result.put("chatUnreadCount", chatUnreadCount);


        return AjaxResult.success(result);
    }

    /**
     * 聊天对话框列表
     *
     * @param page
     * @param size
     * @param request
     * @return
     */
    public TableDataInfo getPrivateLetterList(int page, int size, HttpServletRequest request) {
        if (size > 10) {
            size = 10;
        }
        Long userId = getUserId(request);
        Page p = PageHelperUtils.startPage(page, size, true);
        List<AppPrivateLetterListVo> list = appPrivateLetterListMapper.getUserPrivateLetterList(userId, WhetherTypeEnum.NO.getName());
        if (CollectionUtils.isEmpty(list)) {
            return AjaxResult.getDataTable(new ArrayList<>(), 0);
        } else {
            Callable<List<AppPrivateLetterListVo>> callable = () -> handlePrivateLetterList(userId, list);
            FutureTask<List<AppPrivateLetterListVo>> callableTask = new FutureTask<>(callable);
            executorService.submit(callableTask);
            try {
                return AjaxResult.getDataTable(callableTask.get(), p.getTotal());
            } catch (Exception e) {
                return AjaxResult.getDataTableError("线程处理异常");
            }
        }
    }

    /**
     * 处理用户聊天列表
     *
     * @param userId
     * @param list
     * @return
     */
    public List<AppPrivateLetterListVo> handlePrivateLetterList(Long userId, List<AppPrivateLetterListVo> list) {
        list.forEach(item -> {
            if (item.getIsFamily().intValue() == WhetherTypeEnum.NO.getName()) {// 非家族聊天


                AppUserEntity user = appUserMapper.selectAppUserById(item.getReceiveUserId());
                if (null != user) {
                    item.setReceiveUserLocation(user.getLocation());
                    item.setReceiveUserCity(user.getCity());
                    item.setIsRealPersonAuth(user.getIsRealPersonAuth());
                    item.setIsRealNameAuth(user.getIsRealNameAuth());
                    item.setIsPhoneAuth(StringUtils.isBlank(user.getPhone()) ? WhetherTypeEnum.NO.getName() : WhetherTypeEnum.YES.getName());
                    item.setReceiveUserHeadIcon(user.getHeadPortrait());
                    item.setReceiveUserNickname(user.getNickName());
                    // 新增：设置靓号和性别
                    item.setRecodeCode(user.getRecodeCode());
                    item.setSex(user.getSex());
                    item.setIsOnline(user.getIsOnline());
                }

                // 获取该对话框私信最新的一条消息
                AppChattingRecords chattingRecords = appChattingRecordsMapper.getPrivateLetterNewMsg(userId, item.getReceiveUserId());
                if (null != chattingRecords) {
                    item.setContentNew(chattingRecords.getContent());
                    item.setMsgTypeNew(chattingRecords.getMsgType());
                    // item.setSendTimeNew(chattingRecords.getSendTime());
                    item.setSendTimeNew(item.getUpdateTime());
                }
                // 获取该对话框私信未读数量
                Integer unreadCount = appChattingRecordsMapper.getPrivateLetterNewMsgUnreadCount(userId, item.getReceiveUserId());
                item.setUnreadCount(null == unreadCount ? 0 : unreadCount);
                // item.setCurrentIcoUrl(appTitleNobilityService.getUserCurrentIcoUrl(item.getReceiveUserId(), item.getReceiveUserSex()));
                item.setCurrentIcoUrl(appTitleNobilityService.getCurrentIcoUrl(item.getReceiveUserId()).getCurrentIcoUrl());
                item.setContributeCurrentIcoUrl(appTitleNobilityService.getCurrentIcoUrl(item.getReceiveUserId())
                        .getContributeCurrentIcoUrl());

                item.setUseInColorNickName(appUserPersonalDressingMapper.getUserUseInPersonalDressingInfo(AppPersonalDressingCategoryEnums.COLOR_NICK_NAME.getId(), item.getReceiveUserId()));
            } else {// 家族聊天

                AppFamily appFamily = appFamilyMapper.getFamilyById(item.getFamilyId());
                if (null != appFamily) {
                    item.setFamilyName(appFamily.getFamilyName());
                    item.setFamilyHeadIcon(appFamily.getHeadPortrait());
                }

                // 获取该对话框私信最新的一条消息
                AppChattingRecords chattingRecords = appChattingRecordsMapper.getFamilyNewMsg(item.getFamilyId(), userId);
                if (null != chattingRecords) {
                    item.setContentNew(chattingRecords.getContent());
                    item.setMsgTypeNew(chattingRecords.getMsgType());
                    // item.setSendTimeNew(chattingRecords.getSendTime());
                    item.setSendTimeNew(item.getUpdateTime());
                }
                // 获取该对话框私信未读数量
                Integer unreadCount = appChattingRecordsMapper.getFamilyNewMsgUnreadCount(item.getFamilyId(), userId);
                item.setUnreadCount(null == unreadCount ? 0 : unreadCount);
            }
        });

        return list;
    }

    /**
     * 删除聊天
     *
     * @param request
     * @param privateLetterId 聊天id
     * @return
     */
    public AjaxResult deletePrivateLetter(HttpServletRequest request, Long privateLetterId) {
        if (null == privateLetterId || privateLetterId.intValue() < 1) {
            return AjaxResult.error("参数【privateLetterId】为空");
        }
        Long userId = getUserId(request);
        RLock lock = redisson.getLock("app:deletePrivateLetter:" + userId);
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(60, TimeUnit.SECONDS);

        int count = appPrivateLetterListMapper.deletePrivateLetter(userId, privateLetterId);

        lock.unlock();
        return count > 0 ? AjaxResult.success("删除成功") : AjaxResult.error("数据不存在");
    }

    /**
     * 根据当前用户id和接收人id获取聊天记录
     *
     * @param page
     * @param size
     * @param receiveUserId 接收人id
     * @param request
     * @return
     */
    public TableDataInfo getChattingRecordsList(int page, int size, Long receiveUserId, HttpServletRequest request) {
        if (null == receiveUserId || receiveUserId.intValue() <= 0) {
            return AjaxResult.getDataTableError("参数【receiveUserId】为空");
        }
        if (size > 10) {
            size = 10;
        }
        // 已读消息记录id集合
        List<Long> isReadChattingRecordsIdList = new ArrayList<>();

        Long userId = getUserId(request);
        Page p = PageHelperUtils.startPage(page, size, true);
        List<AppChattingRecordsVo> list = appChattingRecordsMapper.getChattingRecordsListBySendUserIdAndReceiveUserId(userId, receiveUserId);
        if (CollectionUtils.isEmpty(list)) {
            list = new ArrayList<>();
        } else {

            list.forEach(item -> {
                try {
                    if (userId.equals(item.getReceiveUserId()) && item.getIsRead()
                            .intValue() == WhetherTypeEnum.NO.getName()) {// 如果该条消息的接收人是当前用户,并且消息是未读，那就设置为已读
                        isReadChattingRecordsIdList.add(item.getId());
                    }
                } catch (Exception e) {
                }
                item.setUseInColorNickName(appUserPersonalDressingMapper.getUserUseInPersonalDressingInfo(AppPersonalDressingCategoryEnums.COLOR_NICK_NAME.getId(), item.getSendUserId()));

                if (item.getMsgType() == AppSendMsgTypeEnums.RoomInvitation.getId()) {
                    item.setContent(setRoomInvitationInfoInContent(item.getContent()));
                }
            });


        }


        // 设置所有私信消息为已读(当前用户针对对方的)
        appChattingRecordsMapper.setAllPrivateLetterMsgRead(receiveUserId, userId);

        if (!CollectionUtils.isEmpty(isReadChattingRecordsIdList)) {// 已读消息记录id集合不为空
            executorService2.execute(() -> {
                // 给消息发送人推送消息已读标识
                privateLetterWebSocket.sendPrivateLetterIsRed(receiveUserId.toString(), isReadChattingRecordsIdList);
            });
        }


        return AjaxResult.getDataTable(list, p.getTotal());
    }

    /**
     * 获取家族聊天记录
     *
     * @param page
     * @param size
     * @param familyId
     * @param request
     * @return
     */
    public TableDataInfo getFamilyChattingRecordsList(int page, int size, Long familyId, HttpServletRequest request) {
        if (null == familyId || familyId.intValue() <= 0) {
            return AjaxResult.getDataTableError("参数【familyId】为空");
        }

        if (size > 10) {
            size = 10;
        }

        Long userId = getUserId(request);
        AppFamily family = appFamilyMapper.selectAppFamilyById(familyId);
        if (null == family) {
            return AjaxResult.getDataTableError("家族不存在或已解散");
        }

        if (!userId.equals(family.getUserId()) && !appFamilyMemberMapper.isJoinByFamilyIdAndUserId(familyId, userId)) {
            return AjaxResult.getDataTableError("非该家族的成员,无法查看");
        }

        Page p = PageHelperUtils.startPage(page, size, true);
        List<AppChattingRecordsVo> list = appChattingRecordsMapper.getFamilyChattingRecordsList(userId, familyId);
        if (CollectionUtils.isEmpty(list)) {
            return AjaxResult.getDataTable(new ArrayList<>(), 0);
        } else {
            // 设置用户某个家族消息全部为已读
            appChattingRecordsMapper.setAllFamilyMsgRead(userId, familyId);
        }

        return AjaxResult.getDataTable(list, p.getTotal());
    }

    /**
     * 设置消息为已读(接收到新消息就要调用)
     *
     * @param id 聊天记录id
     * @return
     */
    public AjaxResult setMsgRead(HttpServletRequest request, Long id) {
        if (null == id || id.intValue() < 1) {
            return AjaxResult.error("参数【id】为空");
        }

        Long userId = getUserId(request);

        AppChattingRecords chattingRecords = appChattingRecordsMapper.selectAppChattingRecordsById(id);
        if (null == chattingRecords || !chattingRecords.getReceiveUserId().equals(userId)) {
            return AjaxResult.error("消息不存在");
        }
        if (chattingRecords.getIsRead().intValue() == 1) {
            return AjaxResult.success("设置成功");
        }
        AppChattingRecords chattingRecordsUpd = new AppChattingRecords();
        chattingRecordsUpd.setId(chattingRecords.getId());
        chattingRecordsUpd.setIsRead(WhetherTypeEnum.YES.getName());
        appChattingRecordsMapper.updateAppChattingRecords(chattingRecordsUpd);
        if (chattingRecords.getIsFamily().intValue() != WhetherTypeEnum.YES.getName()) {// 非家族消息
            // 给消息发送人推送消息已读标识
            List<Long> isReadChattingRecordsIdList = new ArrayList<>();
            isReadChattingRecordsIdList.add(id);
            privateLetterWebSocket.sendPrivateLetterIsRed(chattingRecords.getSendUserId()
                    .toString(), isReadChattingRecordsIdList);
        }

        return AjaxResult.success("设置成功");
    }

    /**
     * 发送红包
     *
     * @param request
     * @param toUserId
     * @param amount
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult sendRedPacket(HttpServletRequest request, Long toUserId, BigDecimal amount) {
        if (null == toUserId || toUserId.intValue() < 1) {
            return AjaxResult.error("参数【toUserId】为空");
        }

        if (null == amount) {
            return AjaxResult.error("请填写红包金额");
        }

        if (amount.compareTo(new BigDecimal("1")) < 0) {
            return AjaxResult.error("最低发送1");
        }

        AppUserEntity user = getUser(request);
        Long userId = user.getId();

        RLock lock = redisson.getLock("app:sendRedPacket:" + userId);
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(60, TimeUnit.SECONDS);

        if (user.getGoldBalance().compareTo(amount) < 0) {
            lock.unlock();
            return AjaxResult.error("金币余额不足");
        }

        if (userId.equals(toUserId)) {
            lock.unlock();
            return AjaxResult.error("不能给自己发送红包哦");
        }

        // 判断当前用户有没有拉黑对方
        AppBlacklist blacklistByUser = appBlacklistMapper.selectAppBlacklistByUserIdAndToUserId(userId, toUserId);
        if (null != blacklistByUser) {
            lock.unlock();
            return AjaxResult.error("发送失败,您已将对方加入黑名单");
        }

        // 判断对方有没有拉黑当前用户
        AppBlacklist blacklistByReceiveUser = appBlacklistMapper.selectAppBlacklistByUserIdAndToUserId(toUserId, userId);
        if (null != blacklistByReceiveUser) {
            lock.unlock();
            return AjaxResult.error("发送失败,对方已将您加入黑名单");
        }

        AppUserEntity receiveUser = appUserMapper.selectAppUserById(toUserId);
        if (null == receiveUser) {
            lock.unlock();
            return AjaxResult.error("发送失败,对方账号不存在");
        }
        AppUserStatusTypeEnums userStatusTypeEnums = AppUserStatusTypeEnums.getEnum(receiveUser.getUserStatus().intValue());
        if (null == userStatusTypeEnums) {
            lock.unlock();
            return AjaxResult.error("发送失败,对方账号状态异常");
        }
        if (userStatusTypeEnums.getCode() != AppUserStatusTypeEnums.TYPE1.getCode()) {
            lock.unlock();
            return AjaxResult.error("发送失败,对方账号" + userStatusTypeEnums.getInfo());
        }
        // 扣除发送人金币余额
        if (appUserMapper.subtractUserGoldBalance(userId, amount) <= 0) {
            lock.unlock();
            throw new AppException("金币余额不足");
        }
        // 记录发送人金币账单
        AppUserGoldBill userGoldBill = new AppUserGoldBill();
        userGoldBill.setUserId(userId);
        userGoldBill.setObjectId(toUserId);
        userGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
        userGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE17.getId());
        userGoldBill.setAmount(amount.negate());
        appUserGoldBillMapper.insertAppUserGoldBill(userGoldBill);


//        BigDecimal handlingCharge = amount.multiply(sendRedPacketHandlingCharge);
        BigDecimal handlingCharge = amount;


        // 增加接收人人金币余额
        appUserMapper.addUserGoldBalance(toUserId, handlingCharge);
        // 记录发送人金币账单
        AppUserGoldBill toUserGoldBill = new AppUserGoldBill();
        toUserGoldBill.setUserId(toUserId);
        toUserGoldBill.setObjectId(userId);
        toUserGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
        toUserGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE18.getId());
        toUserGoldBill.setAmount(handlingCharge);
        appUserGoldBillMapper.insertAppUserGoldBill(toUserGoldBill);

        lock.unlock();
        return AjaxResult.success("发送成功");
    }

    /**
     * 发送消息
     *
     * @param vo
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult sendMsg(AppUserEntity user, AppSendMsgVo vo) {
        if (null != vo.getIsFamily() && null == WhetherTypeEnum.getEnum(vo.getIsFamily())) {
            return AjaxResult.error("参数【isFamily】错误");
        }
        if (null == vo.getIsFamily()) {
            vo.setIsFamily(WhetherTypeEnum.NO.getName());
        }

        Long userId = user.getId();
        AppSendMsgTypeEnums msgTypeEnums = AppSendMsgTypeEnums.getEnum(vo.getMsgType().intValue());
        if (null == msgTypeEnums || msgTypeEnums.getId() == AppSendMsgTypeEnums.TYPE6.getId() || msgTypeEnums.getId() == AppSendMsgTypeEnums.TYPE7.getId()) {
            return AjaxResult.error("参数【msgType】错误");
        }

        // if (msgTypeEnums.getId() == AppSendMsgTypeEnums.TYPE3.getId()) {
        //     return AjaxResult.error("暂不支持发送视频");
        // }

        // 检查是否为家族消息
        if (vo.getIsFamily() == WhetherTypeEnum.NO.getName()) {
            // 处理普通消息
            return sendNormalMsg(user, vo, userId, msgTypeEnums);
        } else {
            // 处理家族消息
            return sendFamilyMsg(user, vo, userId, msgTypeEnums);
        }
    }

    /**
     * 处理普通消息
     *
     * @param user         发送用户
     * @param vo           消息内容
     * @param userId       发送用户ID
     * @param msgTypeEnums 消息类型
     * @return 操作结果
     */
    private AjaxResult sendNormalMsg(AppUserEntity user, AppSendMsgVo vo, Long userId, AppSendMsgTypeEnums msgTypeEnums) {
        if (null == vo.getReceiveUserId() || vo.getReceiveUserId().intValue() < 1) {
            return AjaxResult.error("参数【receiveUserId】为空");
        }

        RLock lock = redisson.getLock("app:sendMsg:" + userId + ":user:" + vo.getReceiveUserId());
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(60, TimeUnit.SECONDS);

        try {
            // 进行消息前置检查
            AppUserEntity receiveUser = appUserMapper.selectAppUserById(vo.getReceiveUserId());
            if (null == receiveUser) {
                return AjaxResult.error("发送失败,对方账号不存在");
            }

            // 处理AppSendMsgTypeEnums.RoomInvitation类型的消息
            if (msgTypeEnums.getId() == AppSendMsgTypeEnums.RoomInvitation.getId()) {
                vo.setContent(setRoomInvitationInfoInContent(vo.getContent()));

                AppChattingRecordsVo chattingRecordsVo = saveAndSendMessage(user, receiveUser, vo, msgTypeEnums);

                return AjaxResult.success("发送成功", chattingRecordsVo);
            }


            AjaxResult checkResult = checkBeforeSendMsg(userId, vo.getReceiveUserId(), msgTypeEnums);
            if (checkResult != null) {
                return checkResult;
            }

            // 检查通话配置
            AjaxResult configCheckResult = checkCommunicateConfig(userId, vo.getReceiveUserId());
            if (configCheckResult != null) {
                return configCheckResult;
            }

            // 内容安全检查
            AjaxResult contentCheckResult = checkMessageContent(vo.getContent(), msgTypeEnums);
            if (contentCheckResult != null) {
                return contentCheckResult;
            }

            if(vo.getIsVideoGift() == WhetherTypeEnum.YES.getName()) {
                receiveUser.setPhotoAlbum(null);
                user.setPhotoAlbum(null);
                // 推送送礼消息
                Map<String, Object> pushResult = new HashMap<>();
                pushResult.put("pushType", AppPrivateLetterMsgTypeEnums.VIDEO_GIFT.getId());
                pushResult.put("title", user.getNickName() + "送礼给" + receiveUser.getNickName());
                pushResult.put("content", user.getNickName() + "送礼给" + receiveUser.getNickName());
                Map<String, Object> details = new HashMap<>();
                details.put("sendUserId", userId);// 发送用户id
                details.put("sendUserInfo", user);// 发送用户详情
                
                details.put("deliveryUserId", vo.getReceiveUserId());// 送达用户id:-1代表所有
                details.put("toUserId", vo.getReceiveUserId());// 接收用户id
                details.put("toUserInfo", receiveUser);// 接收用户详情
                JSONObject jsonObject = JSON.parseObject(vo.getContent());
                final JSONObject giftInfo = (JSONObject) jsonObject.get("giftInfo");
                final Integer num = giftInfo.getInteger("num");
                details.put("giftInfo", giftInfo);
                details.put("num", num);// 礼物数量
                pushResult.put("details", details);

                privateLetterWebSocket.sendCommonMsgAsyc(userId.toString(), pushResult);
                privateLetterWebSocket.sendCommonMsgAsyc(receiveUser.getId().toString(), pushResult);
            }
            // 处理消息发送
            AppChattingRecordsVo chattingRecordsVo = saveAndSendMessage(user, receiveUser, vo, msgTypeEnums);

            // 检测关键词并自动回复
            if (msgTypeEnums.getId() == AppSendMsgTypeEnums.TYPE1.getId() &&
                    vo.getContent() != null && vo.getContent().contains(AUTH_APPLY_KEYWORD)) {
                // 异步发送自动回复
                sendAutoReply(receiveUser, user, vo.getReceiveUserId(), userId);
            }
            appPrivateLetterListService.addChatRelation(user.getId(), vo.getReceiveUserId(), false);

            // 文字和图片 腾讯云检测  异步检测 检测结果推送给前端
            if (msgTypeEnums.getId() == AppSendMsgTypeEnums.TYPE1.getId() || msgTypeEnums.getId() == AppSendMsgTypeEnums.TYPE2.getId()) {
                int type = msgTypeEnums.getId() == AppSendMsgTypeEnums.TYPE1.getId() ? ContentCheckType.TEXT : ContentCheckType.IMAGE;
                appContentCheckService.checkTextOrImage(SceneConstants.CHAT, vo.getContent(), type, chattingRecordsVo.getId(), userId);
            }
            return AjaxResult.success("发送成功", chattingRecordsVo);
        } finally {
            lock.unlock();
        }
    }

    private String setRoomInvitationInfoInContent(String content) {
        // 处理房间邀请消息

        // 解析房间邀请消息
        JSONObject jsonObject;
        try {
            jsonObject = JSON.parseObject(content);
        } catch (Exception e) {
            return "";
        }
        String roomId = jsonObject.getString("roomId");

        // 判断房间是否存在
        AppChatRoom appChatRoom = appChatRoomMapper.selectAppChatRoomById(Long.valueOf(roomId));
        if (null == appChatRoom) {
            throw new AppException("房间不存在");
        }

        AppChatRoomTypeEnums appChatRoomTypeEnums = AppChatRoomTypeEnums.getEnum(appChatRoom.getType().intValue());
        String typeStr;
        if (appChatRoom.getStatus().intValue() == 1) {
            typeStr = appChatRoomTypeEnums.getDesc() + "中";
        } else {
            typeStr = appChatRoomTypeEnums.getDesc();
        }
        jsonObject.put("lable", "邀请你一起互动");
        String roomDesc = appChatRoomTypeEnums.getDesc();
        String roomName = appChatRoom.getName();
        String avatarUrl = appChatRoom.getAvatarUrl();
        Integer status = appChatRoom.getStatus() != null ? appChatRoom.getStatus().intValue() : 0;

        jsonObject.put("roomName", "{" + roomDesc + "}" + roomName);
        jsonObject.put("img", avatarUrl);
        jsonObject.put("desc", "互动派对" + typeStr);
        jsonObject.put("status", status);
        jsonObject.put("statusDesc", typeStr);
        jsonObject.put("btnStr", "进房找ta");
        return jsonObject.toJSONString();
    }

    /**
     * 发送自动回复
     *
     * @param sender     自动回复发送者
     * @param receiver   自动回复接收者
     * @param senderId   发送者ID
     * @param receiverId 接收者ID
     */
    private void sendAutoReply(AppUserEntity sender, AppUserEntity receiver, Long senderId, Long receiverId) {
        executorService.execute(() -> {
            try {
                // 从字典中获取自动回复内容
                List<SysDictData> customerService = sysDictTypeService.selectDictDataByType("service_user_id");
                final Optional<SysDictData> matchedService = customerService.stream()
                        .filter(item -> item.getDictValue().equals(senderId.toString()))
                        .findFirst();

                final String replyContent = matchedService
                        .map(SysDictData::getRemark)
                        .orElse("您好,很高兴为您服务,请问有什么可以帮助您?客服服务时间为9:00-18:00");
                // 本次消息入库保存（文本消息）
                AppChattingRecords chattingRecords = new AppChattingRecords();
                chattingRecords.setContent(replyContent);
                chattingRecords.setMsgType((long) AppSendMsgTypeEnums.TYPE1.getId()); // 文本消息
                chattingRecords.setSendTime(new Date(System.currentTimeMillis() + 1000));
                chattingRecords.setSendUserId(senderId);
                chattingRecords.setReceiveUserId(receiverId);
                chattingRecords.setIsRead(WhetherTypeEnum.NO.getName());
                chattingRecords.setIsFamily(WhetherTypeEnum.NO.getName());
                appChattingRecordsMapper.insertAppChattingRecords(chattingRecords);

                // 如果有图片，发送图片消息
                matchedService.map(SysDictData::getImg)
                        .filter(StringUtils::isNotBlank)
                        .ifPresent(imgUrl -> {
                            AppChattingRecords imgRecord = new AppChattingRecords();
                            imgRecord.setContent(imgUrl);
                            imgRecord.setMsgType((long) AppSendMsgTypeEnums.TYPE2.getId()); // 图片消息
                            imgRecord.setSendTime(new Date(System.currentTimeMillis() + 2000));
                            imgRecord.setSendUserId(senderId);
                            imgRecord.setReceiveUserId(receiverId);
                            imgRecord.setIsRead(WhetherTypeEnum.NO.getName());
                            imgRecord.setIsFamily(WhetherTypeEnum.NO.getName());
                            appChattingRecordsMapper.insertAppChattingRecords(imgRecord);
                        });

                // 准备发送的VO对象
                AppChattingRecordsVo chattingRecordsVo = new AppChattingRecordsVo();
                BeanUtils.copyProperties(chattingRecords, chattingRecordsVo);

                // 设置发送者用户头像和名称
                chattingRecordsVo.setSendUserNickname(sender.getNickName());
                chattingRecordsVo.setSendUserHeadIcon(sender.getHeadPortrait());

                // 设置接收者用户头像和昵称
                chattingRecordsVo.setReceiveUserNickname(receiver.getNickName());
                chattingRecordsVo.setReceiveUserHeadIcon(receiver.getHeadPortrait());

                // 给接收者推送消息
                privateLetterWebSocket.sendPrivateLetter(receiverId.toString(), chattingRecordsVo);

                // 更新聊天对话框
                updateChatDialogForAutoReply(senderId, receiverId);

            } catch (Exception e) {
                logger.error("自动回复消息发送失败", e);
            }
        });
    }

    /**
     * 更新聊天对话框状态（用于自动回复）
     *
     * @param senderId   发送者ID
     * @param receiverId 接收者ID
     */
    private void updateChatDialogForAutoReply(Long senderId, Long receiverId) {
        // 判断发送者是否有接收者聊天对话框
        AppPrivateLetterList senderPrivateLetter = appPrivateLetterListMapper.selectAppPrivateLetterListByUserIdAndReceiveUserId(senderId, receiverId);
        if (null == senderPrivateLetter) {
            // 没有就新增
            AppPrivateLetterList privateLetter = new AppPrivateLetterList();
            privateLetter.setUserId(senderId);
            privateLetter.setReceiveUserId(receiverId);
            privateLetter.setIsFamily(WhetherTypeEnum.NO.getName());
            privateLetter.setUpdateTime(new Date());
            privateLetter.setIsTemporarily(WhetherTypeEnum.NO.getName());
            appPrivateLetterListMapper.insertAppPrivateLetterList(privateLetter);
        } else {
            // 有就更新时间
            AppPrivateLetterList senderLetterUpd = new AppPrivateLetterList();
            senderLetterUpd.setId(senderPrivateLetter.getId());
            senderLetterUpd.setUpdateTime(new Date());
            appPrivateLetterListMapper.updateAppPrivateLetterList(senderLetterUpd);
        }
    }

    /**
     * 发送消息前的检查
     *
     * @param userId        发送用户ID
     * @param receiveUserId 接收用户ID
     * @param msgTypeEnums  消息类型
     * @return 错误结果，null表示检查通过
     */
    private AjaxResult checkBeforeSendMsg(Long userId, Long receiveUserId, AppSendMsgTypeEnums msgTypeEnums) {
        if (msgTypeEnums.getId() != AppSendMsgTypeEnums.TYPE5.getId()) {
            // 判断当前用户有没有拉黑对方
            AppBlacklist blacklistByUser = appBlacklistMapper.selectAppBlacklistByUserIdAndToUserId(userId, receiveUserId);
            if (null != blacklistByUser) {
                return AjaxResult.error("发送失败,您已将对方加入黑名单");
            }

            // 判断对方有没有拉黑当前用户
            AppBlacklist blacklistByReceiveUser = appBlacklistMapper.selectAppBlacklistByUserIdAndToUserId(receiveUserId, userId);
            if (null != blacklistByReceiveUser) {
                return AjaxResult.error("发送失败,对方已将您加入黑名单");
            }
        }
        return null;
    }

    /**
     * 检查通话配置
     *
     * @param userId        发送用户ID
     * @param receiveUserId 接收用户ID
     * @return 错误结果，null表示检查通过
     */
    private AjaxResult checkCommunicateConfig(Long userId, Long receiveUserId) {
        // 获取发送用户的通话配置
        AppUserCommunicateTelephoneConfig sendUserConfig = appUserCommunicateTelephoneConfigService.getUserCommunicateTelephoneConfigByUserId(userId);
        if (sendUserConfig != null && sendUserConfig.getIsBannedChat() == WhetherTypeEnum.YES.getName()) {
            return AjaxResult.error("发送失败,你的私聊功能被禁用");
        }

        // 获取接收用户的通话配置
        AppUserCommunicateTelephoneConfig receiveUserConfig = appUserCommunicateTelephoneConfigService.getUserCommunicateTelephoneConfigByUserId(receiveUserId);
        if (receiveUserConfig != null && receiveUserConfig.getIsBannedChat() == WhetherTypeEnum.YES.getName()) {
            return AjaxResult.error("发送失败,对方私聊功能被禁用");
        }
        return null;
    }

    /**
     * 检查消息内容安全性
     *
     * @param content      消息内容
     * @param msgTypeEnums 消息类型
     * @return 错误结果，null表示检查通过
     */
    private AjaxResult checkMessageContent(String content, AppSendMsgTypeEnums msgTypeEnums) {
        if (msgTypeEnums.getId() == AppSendMsgTypeEnums.TYPE1.getId()) {
            if (appCommonService.isContainsSensitiveLexicon(content)) {
                return AjaxResult.error("发送失败,内容中包含违禁词");
            }
        }
        return null;
    }


    /**
     * 保存并发送消息
     *
     * @param user         发送用户
     * @param receiveUser  接收用户
     * @param vo           消息内容
     * @param msgTypeEnums 消息类型
     * @return 消息记录VO
     */
    private AppChattingRecordsVo saveAndSendMessage(AppUserEntity user, AppUserEntity receiveUser,
                                                    AppSendMsgVo vo, AppSendMsgTypeEnums msgTypeEnums) {
        Date time = new Date();
        Long userId = user.getId();
        Long receiveUserId = vo.getReceiveUserId();

        // 本次消息入库保存
        AppChattingRecords chattingRecords = new AppChattingRecords();
        chattingRecords.setContent(vo.getContent());
        chattingRecords.setMsgType(vo.getMsgType());
        chattingRecords.setSendTime(time);
        chattingRecords.setSendUserId(userId);
        chattingRecords.setReceiveUserId(receiveUserId);
        chattingRecords.setIsRead(WhetherTypeEnum.NO.getName());
        chattingRecords.setIsFamily(WhetherTypeEnum.NO.getName());
        appChattingRecordsMapper.insertAppChattingRecords(chattingRecords);

        AppChattingRecordsVo chattingRecordsVo = new AppChattingRecordsVo();
        BeanUtils.copyProperties(chattingRecords, chattingRecordsVo);
        // 设置发送者用户头像和名称
        chattingRecordsVo.setSendUserNickname(user.getNickName());
        chattingRecordsVo.setSendUserHeadIcon(user.getHeadPortrait());
        // 设置接收者用户头像和昵称
        chattingRecordsVo.setReceiveUserNickname(receiveUser.getNickName());
        chattingRecordsVo.setReceiveUserHeadIcon(receiveUser.getHeadPortrait());

        // 先判断这两个用户的聊天按钮类型
        int btnType = vo.getIsChatMessage();

        // 处理收费逻辑
        handleMessageFee(user, receiveUser, vo, msgTypeEnums, btnType, time);

        // 更新聊天对话框记录
        updateChatDialog(userId, receiveUserId, btnType);

        // 若是私信并且非客服互发，计算亲密值
        calculateIntimacy(userId, receiveUserId, btnType);

        try {
            // 给接收者推送消息
            privateLetterWebSocket.sendPrivateLetter(receiveUserId.toString(), chattingRecordsVo);
        } catch (Exception e) {
            logger.info("给指定用户发送私信 webSocket 执行失败,msg:{}", e.getMessage());
        }

        return chattingRecordsVo;
    }

    /**
     * 处理消息收费
     *
     * @param user         发送用户
     * @param receiveUser  接收用户
     * @param vo           消息内容
     * @param msgTypeEnums 消息类型
     * @param btnType      按钮类型
     * @param time         时间
     */
    private void handleMessageFee(AppUserEntity user, AppUserEntity receiveUser, AppSendMsgVo vo,
                                  AppSendMsgTypeEnums msgTypeEnums, int btnType, Date time) {
        Long userId = user.getId();
        Long receiveUserId = vo.getReceiveUserId();

        // 双方是否互相关注,0没有,1互相关注
        int isAttentionToEachOther = appUserFollowMapper.isAttentionToEachOther(userId, receiveUserId);

        // 不限资费判断
        boolean enabled = appVipService.enableVipPrivileges(VipPrivilegesConstants.NO_PRICE_LIMIT, userId);

        // 判断发送消息是否需要收费:
        // 1. 男给女发消息需要收费
        // 2. 女给男发消息不收费,男给男不收,女给女不收
        // 3. 如果双方互相关注，则男女互发消息都不收费
        boolean needCharge = user.getSex() == AppUserSexTypeEnums.TYPE0.getId() // 发送方为男性
                && receiveUser.getSex() == AppUserSexTypeEnums.TYPE1.getId() // 接收方为女性
                && !enabled // 不限资费
                && !isServiceUser(userId) // 发送方不是客服
                && !isServiceUser(receiveUserId) // 接收方不是客服
                && isAttentionToEachOther != 1 // 双方没有互相关注
                && (msgTypeEnums.getId() == 1 || msgTypeEnums.getId() == 2
                || msgTypeEnums.getId() == 3 || msgTypeEnums.getId() == 4); // 只有这些消息类型才收费

        if (needCharge) {
            // 获取接收用户的通话配置
            AppUserCommunicateTelephoneConfig receiveUserConfig = appUserCommunicateTelephoneConfigService
                    .getUserCommunicateTelephoneConfigByUserId(receiveUserId);

            if (btnType == WhetherTypeEnum.NO.getName()) {
                // 打招呼按钮类型收费逻辑
                handleGreetingFee(user, userId, receiveUserId, receiveUserConfig, appConfigMapper.getAppConfig(), time);
            } else {
                // 私信按钮类型收费逻辑
                handlePrivateMessageFee(user, userId, receiveUserId, receiveUserConfig, appConfigMapper.getAppConfig(), time);
            }
        }
    }

    /**
     * 处理打招呼收费
     */
    private void handleGreetingFee(AppUserEntity user, Long userId, Long receiveUserId,
                                   AppUserCommunicateTelephoneConfig receiveUserConfig,
                                   AppConfig appConfig, Date time) {
        // 打招呼属于搭讪，暂时不收费
//        if (receiveUserConfig.getTemporarilyGoldPrice().compareTo(new BigDecimal("0")) > 0) {
//            // 打招呼收费金额大于0时
//            if (user.getGoldBalance().compareTo(receiveUserConfig.getTemporarilyGoldPrice()) < 0) {
//                throw new AppException("金币余额不足");
//            }
//
//            // 扣除用户金币余额
//            if (appUserMapper.subtractUserGoldBalance(userId, receiveUserConfig.getTemporarilyGoldPrice()) <= 0) {
//                throw new AppException("金币余额不足");
//            }
//
//            // 记录发送人金币账单
//            AppUserGoldBill userGoldBill = new AppUserGoldBill();
//            userGoldBill.setUserId(userId);
//            userGoldBill.setObjectId(receiveUserId);// 产生账单的相关id为该接收用户的id
//            userGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
//            userGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE9.getId());// 账单类型为给用户打招呼
//            userGoldBill.setAmount(receiveUserConfig.getTemporarilyGoldPrice().negate());// 金额为本次打招呼的金额(取负数)
//            appUserGoldBillMapper.insertAppUserGoldBill(userGoldBill);
//
//            // 计算本次消耗的金币等于多少人民币
//            BigDecimal consumptionRmb = receiveUserConfig.getTemporarilyGoldPrice().multiply(appConfig.getOneGoldEqRmb());
//            // 计算本次收入的人民币
//            BigDecimal earningsRmb = consumptionRmb.multiply(appConfig.getTemporarilyIncomeScale());
//            // 计算本次收入的钻石
//            BigDecimal earningsPoints = earningsRmb.multiply(appConfig.getOneRmbEqPoints());
//
//            if (earningsPoints.compareTo(new BigDecimal("0")) > 0) {
//                // 增加接收人钻石余额
//                appUserMapper.addUserPointsBalance(receiveUserId, earningsPoints);
//                // 记录接收人钻石账单
//                AppUserPointsBill receiveUserPointsBill = new AppUserPointsBill();
//                receiveUserPointsBill.setUserId(receiveUserId);
//                receiveUserPointsBill.setGiftIncomeScale(appConfig.getTemporarilyIncomeScale());
//                receiveUserPointsBill.setCreateTime(time);
//                receiveUserPointsBill.setObjectId(userId);// 产生账单的相关id为该发送用户的id
//                receiveUserPointsBill.setIsDel(WhetherTypeEnum.NO.getName());
//                receiveUserPointsBill.setBillType((long) AppPointsBillTypeEnums.TYPE11.getId());// 账单类型为给收到用户打招呼
//                receiveUserPointsBill.setAmount(earningsPoints);// 金额为本次收益金额
//                appUserPointsBillMapper.insertAppUserPointsBill(receiveUserPointsBill);
//            }
//        }
    }

    /**
     * 处理私信收费
     */
    private void handlePrivateMessageFee(AppUserEntity user, Long userId, Long receiveUserId,
                                         AppUserCommunicateTelephoneConfig receiveUserConfig,
                                         AppConfig appConfig, Date time) {
        if (receiveUserConfig.getSendMsgGoldPrice().compareTo(new BigDecimal("0")) > 0) {
            // 接收用户设置的消息收费金额大于0时
            if (user.getGoldBalance().compareTo(receiveUserConfig.getSendMsgGoldPrice()) < 0) {
                throw new AppException("金币余额不足");
            }

            // 扣除发送人金币余额
            if (appUserMapper.subtractUserGoldBalance(userId, receiveUserConfig.getSendMsgGoldPrice()) <= 0) {
                throw new AppException("金币余额不足");
            }

            // 记录发送人金币账单
            AppUserGoldBill userGoldBill = new AppUserGoldBill();
            userGoldBill.setUserId(userId);
            userGoldBill.setObjectId(receiveUserId);// 产生账单的相关id为该接收用户的id
            userGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
            userGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE10.getId());// 账单类型为给用户发消息
            userGoldBill.setAmount(receiveUserConfig.getSendMsgGoldPrice().negate());// 金额为本次发送消息的金额(取负数)
            appUserGoldBillMapper.insertAppUserGoldBill(userGoldBill);

            // 计算本次收入的钻石
            BigDecimal earningsPoints;

            AppGuildMember appGuildMember = appGuildMemberMapper.getGuildMemberByUserId(receiveUserId);
            if (appGuildMember != null) {
                // 接收用户是工会成员
                earningsPoints = receiveUserConfig.getSendMsgGoldPrice().multiply(appGuildMember.getGiftIncomeScale()).multiply(appConfig.getOneGoldEqRmb());
            } else {
                // 计算本次消耗的金币等于多少人民币
                BigDecimal consumptionRmb = receiveUserConfig.getSendMsgGoldPrice().multiply(appConfig.getOneGoldEqRmb());
                // 计算本次收入的人民币
                BigDecimal earningsRmb = consumptionRmb.multiply(appConfig.getSendMsgIncomeScale());
                earningsPoints = earningsRmb.multiply(appConfig.getOneRmbEqPoints());
            }

            // 增加接收人钻石余额
            appUserMapper.addUserPointsBalance(receiveUserId, earningsPoints);

            // 记录接收人钻石账单
            AppUserPointsBill receiveUserPointsBill = new AppUserPointsBill();
            receiveUserPointsBill.setUserId(receiveUserId);
            receiveUserPointsBill.setCreateTime(time);
            if (appGuildMember!= null) {
                receiveUserPointsBill.setGiftIncomeScale(appGuildMember.getGiftIncomeScale());
            }
            receiveUserPointsBill.setObjectId(userId);// 产生账单的相关id为该发送用户的id
            receiveUserPointsBill.setIsDel(WhetherTypeEnum.NO.getName());
            receiveUserPointsBill.setBillType((long) AppPointsBillTypeEnums.TYPE12.getId());// 账单类型为给收到用户发消息
            receiveUserPointsBill.setAmount(earningsPoints);// 金额为本次收益金额
            receiveUserPointsBill.setTotalAmount(receiveUserConfig.getSendMsgGoldPrice());// 金额为本次收益金额
            receiveUserPointsBill.setRemarksMsg(StringUtils.format(AppPointsBillTypeEnums.TYPE12.getDesc(), user.getNickName()));
            receiveUserPointsBill.setGuildId(appGuildMemberMapper.getGuildIdByUserId(receiveUserId));
            appUserPointsBillMapper.insertAppUserPointsBill(receiveUserPointsBill);
        }
    }

    /**
     * 更新聊天对话框
     *
     * @param userId        发送用户ID
     * @param receiveUserId 接收用户ID
     * @param btnType       按钮类型
     */
    private void updateChatDialog(Long userId, Long receiveUserId, int btnType) {
        // 判断当前用户是否有接收者聊天对话框
        AppPrivateLetterList userPrivateLetter = appPrivateLetterListMapper.selectAppPrivateLetterListByUserIdAndReceiveUserId(userId, receiveUserId);
        if (null == userPrivateLetter) {
            // 没有就新增
            AppPrivateLetterList privateLetter = new AppPrivateLetterList();
            privateLetter.setUserId(userId);
            privateLetter.setReceiveUserId(receiveUserId);
            privateLetter.setIsFamily(WhetherTypeEnum.NO.getName());
            privateLetter.setUpdateTime(new Date());
            privateLetter.setIsTemporarily(btnType == WhetherTypeEnum.NO.getName() ? WhetherTypeEnum.YES.getName() : WhetherTypeEnum.NO.getName());// 如果聊天按钮类型为打招呼时，那就是临时消息，相反
            appPrivateLetterListMapper.insertAppPrivateLetterList(privateLetter);
        } else {
            AppPrivateLetterList userPrivateLetterUpd = new AppPrivateLetterList();
            userPrivateLetterUpd.setId(userPrivateLetter.getId());
            if (userPrivateLetter.getIsTemporarily().intValue() == WhetherTypeEnum.YES.getName()
                    && btnType == WhetherTypeEnum.YES.getName()) {// 如果对话框为临时并且聊天按钮类型为私信，那就修改未非临时
                userPrivateLetterUpd.setIsTemporarily(WhetherTypeEnum.NO.getName());
            }
            userPrivateLetterUpd.setUpdateTime(new Date());
            appPrivateLetterListMapper.updateAppPrivateLetterList(userPrivateLetterUpd);
        }

        // 判断接收者是否有当前用户聊天对话框
        AppPrivateLetterList receiveUserPrivateLetter = appPrivateLetterListMapper.selectAppPrivateLetterListByUserIdAndReceiveUserId(receiveUserId, userId);
        if (null == receiveUserPrivateLetter) {
            // 没有就新增
            AppPrivateLetterList privateLetter = new AppPrivateLetterList();
            privateLetter.setUserId(receiveUserId);
            privateLetter.setReceiveUserId(userId);
            privateLetter.setIsFamily(WhetherTypeEnum.NO.getName());
            privateLetter.setUpdateTime(new Date());
            privateLetter.setIsTemporarily(btnType == WhetherTypeEnum.NO.getName() ? WhetherTypeEnum.YES.getName() : WhetherTypeEnum.NO.getName());// 如果聊天按钮类型为打招呼时，那就是临时消息，相反
            appPrivateLetterListMapper.insertAppPrivateLetterList(privateLetter);
        } else {
            AppPrivateLetterList receiveUserPrivateLetterUpd = new AppPrivateLetterList();
            receiveUserPrivateLetterUpd.setId(receiveUserPrivateLetter.getId());
            if (receiveUserPrivateLetter.getIsTemporarily().intValue() == WhetherTypeEnum.YES.getName()
                    && btnType == WhetherTypeEnum.YES.getName()) {// 如果对话框为临时并且聊天按钮类型为私信，那就修改未非临时
                receiveUserPrivateLetterUpd.setIsTemporarily(WhetherTypeEnum.NO.getName());
            }
            receiveUserPrivateLetterUpd.setUpdateTime(new Date());
            appPrivateLetterListMapper.updateAppPrivateLetterList(receiveUserPrivateLetterUpd);
        }
    }

    /**
     * 计算用户亲密度
     *
     * @param userId        发送用户ID
     * @param receiveUserId 接收用户ID
     * @param btnType       按钮类型
     */
    private void calculateIntimacy(Long userId, Long receiveUserId, int btnType) {
        // 如果是私信并且接收和发送方都不为客服的情况下就去计算亲密值
        if (btnType == WhetherTypeEnum.YES.getName()
                && !isServiceUser(userId)
                && !isServiceUser(receiveUserId)) {
            AppUserIntimacy userIntimacy = appUserIntimacyMapper.getUserIntimacy(userId, receiveUserId);
            if (null == userIntimacy) {
                userIntimacy = new AppUserIntimacy();
                userIntimacy.setToUserId(receiveUserId);
                userIntimacy.setUserId(userId);
                userIntimacy.setIntimacyValue(new BigDecimal("0.5"));
                userIntimacy.setCreateTime(new Date());
                appUserIntimacyMapper.insertAppUserIntimacy(userIntimacy);
            } else {
                appUserIntimacyMapper.addUserIntimacy(userIntimacy.getId(), new BigDecimal("0.5"));// 每次增加0.5
            }
        }
    }

    /**
     * 处理家族消息
     *
     * @param user         发送用户
     * @param vo           消息内容
     * @param userId       用户ID
     * @param msgTypeEnums 消息类型
     * @return 操作结果
     */
    private AjaxResult sendFamilyMsg(AppUserEntity user, AppSendMsgVo vo, Long userId, AppSendMsgTypeEnums msgTypeEnums) {
        if (null == vo.getFamilyId() || vo.getFamilyId().intValue() < 1) {
            return AjaxResult.error("参数【familyId】为空");
        }

        RLock lock = redisson.getLock("app:sendMsg:" + userId + ":family:" + vo.getFamilyId());
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(60, TimeUnit.SECONDS);

        try {
            // 判断家族是否存在
            AppFamily family = appFamilyMapper.selectAppFamilyById(vo.getFamilyId());
            if (null == family) {
                return AjaxResult.error("发送失败,家族不存在或已解散");
            }

            // 如果当前用户不是族长，那就判断是否加入了家族
            if (!userId.equals(family.getUserId())) {
                if (!appFamilyMemberMapper.isJoinByFamilyIdAndUserId(vo.getFamilyId(), userId)) {
                    return AjaxResult.error("发送失败,您非该家族的成员");
                }
            }

            // 检查消息内容
            if (msgTypeEnums.getId() == AppSendMsgTypeEnums.TYPE1.getId()) {
                if (appCommonService.isContainsSensitiveLexicon(vo.getContent())) {
                    return AjaxResult.error("发送失败,内容中包含违禁词");
                }
            }

            // 准备并发送家族消息
            AppChattingRecordsVo chattingRecordsVo = prepareFamilyMessage(user, vo, family);

            // 异步发送家族消息
            Callable<Boolean> callable = () -> toSendFamilyMsg(chattingRecordsVo, family.getUserId());
            FutureTask<Boolean> callableTask = new FutureTask<>(callable);
            executorService.submit(callableTask);
            appPrivateLetterListService.addChatRelation(user.getId(), vo.getReceiveUserId(), false);
            return AjaxResult.success("发送成功", chattingRecordsVo);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 准备家族消息
     *
     * @param user   发送用户
     * @param vo     消息内容
     * @param family 家族信息
     * @return 消息记录VO
     */
    private AppChattingRecordsVo prepareFamilyMessage(AppUserEntity user, AppSendMsgVo vo, AppFamily family) {
        AppChattingRecordsVo chattingRecordsVo = new AppChattingRecordsVo();
        // 设置发送者用户头像和名称
        chattingRecordsVo.setSendUserNickname(user.getNickName());
        chattingRecordsVo.setSendUserHeadIcon(user.getHeadPortrait());
        chattingRecordsVo.setIsFamily(WhetherTypeEnum.YES.getName());
        chattingRecordsVo.setFamilyId(vo.getFamilyId());
        chattingRecordsVo.setSendUserId(user.getId());
        chattingRecordsVo.setContent(vo.getContent());
        chattingRecordsVo.setSendTime(new Date());
        chattingRecordsVo.setIsRead(WhetherTypeEnum.NO.getName());
        chattingRecordsVo.setMsgType(vo.getMsgType());

        return chattingRecordsVo;
    }

    /**
     * 执行发送家族消息
     *
     * @param vo
     * @param familyUserId 族长用户id
     * @return
     */
    public Boolean toSendFamilyMsg(final AppChattingRecordsVo vo, final Long familyUserId) {
        // 获取该家族所有成员id集合
        List<Long> userIdList = appFamilyMemberMapper.getFamilyAllMemberUserIds(vo.getFamilyId());
        if (CollectionUtils.isEmpty(userIdList)) {
            userIdList = new ArrayList<>();
        }
        userIdList.add(familyUserId);
        userIdList.forEach(userId -> {
            try {
                // 本次消息入库保存
                AppChattingRecords chattingRecords = new AppChattingRecords();
                chattingRecords.setContent(vo.getContent());
                chattingRecords.setMsgType(vo.getMsgType());
                chattingRecords.setSendTime(vo.getSendTime());
                chattingRecords.setSendUserId(vo.getSendUserId());
                chattingRecords.setReceiveUserId(userId);
                if (!userId.equals(vo.getSendUserId())) {// 接收用户不为消息发送用户就设置为未读
                    chattingRecords.setIsRead(WhetherTypeEnum.NO.getName());
                } else {// 接收用户为消息发送用户就设置为已读
                    chattingRecords.setIsRead(WhetherTypeEnum.YES.getName());
                }
                chattingRecords.setIsFamily(vo.getIsFamily());
                chattingRecords.setFamilyId(vo.getFamilyId());
                appChattingRecordsMapper.insertAppChattingRecords(chattingRecords);

                AppChattingRecordsVo chattingRecordsVo = new AppChattingRecordsVo();
                BeanUtils.copyProperties(chattingRecords, chattingRecordsVo);
                // 设置发送者用户头像和名称
                chattingRecordsVo.setSendUserNickname(vo.getSendUserNickname());
                chattingRecordsVo.setSendUserHeadIcon(vo.getSendUserHeadIcon());
                // 设置接收者用户头像和昵称
                AppViewUserInfoVo receiveUser = appCommonService.getUserInfoByUserId(userId);
                chattingRecordsVo.setReceiveUserNickname(receiveUser.getNickName());
                chattingRecordsVo.setReceiveUserHeadIcon(receiveUser.getHeadPortrait());

                // 根据接收人id和家族id判断是否存在私信对话框
                AppPrivateLetterList familyPrivateLetterList = appPrivateLetterListMapper.getFamilyByReceiveUserIdAndFamilyId(userId, vo.getFamilyId());
                if (null == familyPrivateLetterList) {// 不存在对话框就新增
                    AppPrivateLetterList privateLetter = new AppPrivateLetterList();
                    privateLetter.setUserId(0L);
                    privateLetter.setReceiveUserId(userId);
                    privateLetter.setIsTemporarily(WhetherTypeEnum.NO.getName());
                    privateLetter.setIsFamily(WhetherTypeEnum.YES.getName());
                    privateLetter.setFamilyId(vo.getFamilyId());
                    privateLetter.setUpdateTime(vo.getSendTime());
                    appPrivateLetterListMapper.insertAppPrivateLetterList(privateLetter);
                } else {
                    familyPrivateLetterList.setUpdateTime(vo.getSendTime());
                    appPrivateLetterListMapper.updateAppPrivateLetterList(familyPrivateLetterList);
                }
                if (!userId.equals(vo.getSendUserId())) {// 接收用户不为消息发送用户就给接收者推送消息
                    try {
                        privateLetterWebSocket.sendPrivateLetterByFamily(userId.toString(), chattingRecordsVo);
                    } catch (Exception error) {
                    }
                }
            } catch (Exception e) {

            }
        });
        return true;
    }

    /**
     * 获取客服用户id
     *
     * @return
     */
    public AjaxResult getServiceUserId() {

        List<SysDictData> customerService = sysDictTypeService.selectDictDataByType("service_user_id");
        if (CollectionUtils.isEmpty(customerService)) {
            return AjaxResult.error("系统未配置客服");
        }

        List<Long> collect = this.getOnlineServiceUserIdList();
        if (CollectionUtils.isEmpty(collect)) {
            return AjaxResult.success("成功", Integer.valueOf(customerService.get(0).getDictValue()));
        }

        return AjaxResult.success("成功", collect.get(0).intValue());
    }

    /**
     * 获取所有客服用户ID列表
     *
     * @return 所有客服ID的列表，如果未配置则返回空列表
     */
    public List<Long> getServiceUserIdList() {
        List<SysDictData> customerService = sysDictTypeService.selectDictDataByType("service_user_id");
        if (CollectionUtils.isEmpty(customerService)) {
            return new ArrayList<>();
        }

        return customerService.stream()
                .map(SysDictData::getDictValue)
                .map(Long::valueOf)
                .collect(Collectors.toList());
    }

    /**
     * 获取在线客服用户ID列表
     *
     * @return 所有在线客服ID的列表，如果没有在线客服则返回空列表
     */
    public List<Long> getOnlineServiceUserIdList() {
        List<Long> allServiceIds = getServiceUserIdList();
        if (CollectionUtils.isEmpty(allServiceIds)) {
            return new ArrayList<>();
        }

        return appUserService.lambdaQuery()
                .select(AppUserEntity::getId)
                .in(AppUserEntity::getId, allServiceIds)
                .eq(AppUserEntity::getIsOnline, WhetherTypeEnum.YES.getName())
                .list().stream()
                .map(AppUserEntity::getId)
                .collect(Collectors.toList());
    }


    /**
     * 判断用户ID是否为客服
     *
     * @param userId 用户ID
     * @return 如果是客服返回true，否则返回false
     */
    public boolean isServiceUser(Long userId) {
        if (userId == null) {
            return false;
        }

        List<Long> serviceUserIds = getServiceUserIdList();
        return serviceUserIds.contains(userId);
    }

    /**
     * 获取客服微信
     *
     * <AUTHOR>
     * @date 2025/4/2 下午5:48
     */
    public AjaxResult getServiceUserWeChat() {
        // 从字典中获取客服的微信号和二维码
        List<SysDictData> customerService = sysDictTypeService.selectDictDataByType("customer_service");
        if (CollectionUtils.isEmpty(customerService)) {
            return AjaxResult.error("系统未配置客服微信");
        }
        SysDictData sysDictData = customerService.get(0);

        Map<String, Object> map = new HashMap<>();
        map.put("serviceUserWechat", sysDictData.getDictValue());
        map.put("serviceUserQrCode", sysDictData.getImg());
        return AjaxResult.success("查询成功", map);
    }

    /**
     * 创建腾讯云im的用户签名
     *
     * @param request
     * @return
     */
    public AjaxResult createTencentCloudImUserSig(HttpServletRequest request) {
        Long userId = getUserId(request);
        RLock lock = redisson.getLock("app:createTencentCloudImUserSig:" + userId);
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(60, TimeUnit.SECONDS);
        try {
            TLSSigAPIv2 api = new TLSSigAPIv2(Constants.TENCENT_CLOUD_IM_SDK_APPID, Constants.TENCENT_CLOUD_IM_SDK_KEY);
            Long expire = Long.valueOf(60 * 60 * 24 * 7);
            String userSig = api.genUserSig(String.valueOf(userId), expire);
            return AjaxResult.success("创建成功", userSig);
        } catch (Exception e) {
            return AjaxResult.error("创建失败," + e.getMessage());
        } finally {
            lock.unlock();
        }

    }

    /**
     * 发起通话
     *
     * @param receiveUserId 接收用户id
     * @param type          类型:1语音，2视频，3语音匹配，4视频匹配
     * @param user          发起通话的用户
     * @return {@link AjaxResult }
     */
    public AjaxResult initiateCommunicateTelephone(Long receiveUserId, Long type, AppUserEntity user) {
        Long userId = user.getId();
        // 发起人和接听人同时互相拨打电话,导致并发,业务需要处理这种特殊场景,完善锁机制,对稍微慢一点的用户提示:'进行正在通话中'的通知提醒

        // 确保按照固定顺序加锁,避免死锁
        Long firstLockId = Math.min(userId, receiveUserId);
        Long secondLockId = Math.max(userId, receiveUserId);

        RLock firstLock = redisson.getLock("app:initiateCommunicateTelephone:" + firstLockId);
        RLock secondLock = redisson.getLock("app:initiateCommunicateTelephone:" + secondLockId);

        // 尝试获取第一把锁
        if (!firstLock.tryLock()) {
            return AjaxResult.error("对方正在通话中");
        }

        try {
            // 尝试获取第二把锁
            if (!secondLock.tryLock()) {
                return AjaxResult.error("对方正在通话中");
            }

            try {
                if (null == receiveUserId || receiveUserId.intValue() < 1) {
                    return AjaxResult.error("参数【receiveUserId】为空");
                }

                // 检查是否已经存在通话
                boolean existingCall = appCommunicateTelephoneRecordsMapper.isBusy(userId) ||
                        appCommunicateTelephoneRecordsMapper.isBusy(receiveUserId);

                if (existingCall) {
                    return AjaxResult.error("对方正在通话中");
                }
                if (null == receiveUserId || receiveUserId.intValue() < 1) {
                    return AjaxResult.error("参数【receiveUserId】为空");
                }
                if (null == type) {
                    return AjaxResult.error("参数【type】为空");
                }
                if (null == AppCommunicateTelephoneTypeEnums.getEnum(type.intValue())) {
                    return AjaxResult.error("参数【type】错误");
                }

                if (userId.equals(receiveUserId)) {
                    return AjaxResult.error("无法对自己发起通话");
                }

                // 判断当前用户是否占线中
                if (appCommunicateTelephoneRecordsMapper.isBusy(userId)) {
                    return AjaxResult.error("操作失败,当前有进行中的通话");
                }

                // 判断对方是否占线中
                if (appCommunicateTelephoneRecordsMapper.isBusy(receiveUserId)) {
                    return AjaxResult.error("操作失败,对方占线中");
                }

                // 获取发送用户的通话配置
                AppUserCommunicateTelephoneConfig sendUserCommunicateTelephoneConfig = appUserCommunicateTelephoneConfigService.getUserCommunicateTelephoneConfigByUserId(userId);
                if (type.intValue() == AppCommunicateTelephoneTypeEnums.TYPE2.getId() && sendUserCommunicateTelephoneConfig.getIsBannedVideo() == WhetherTypeEnum.YES.getName()) {
                    return AjaxResult.error("操作失败,你已被禁用视频通话");
                }
                if (type.intValue() == AppCommunicateTelephoneTypeEnums.TYPE1.getId() && sendUserCommunicateTelephoneConfig.getIsBannedVoice() == WhetherTypeEnum.YES.getName()) {
                    return AjaxResult.error("操作失败,你已被禁用语音通话");
                }

                // 获取接收用户的通话配置
                AppUserCommunicateTelephoneConfig receiveUserCommunicateTelephoneConfig = appUserCommunicateTelephoneConfigService.getUserCommunicateTelephoneConfigByUserId(receiveUserId);
                if (type.intValue() == AppCommunicateTelephoneTypeEnums.TYPE2.getId() && receiveUserCommunicateTelephoneConfig.getIsBannedVideo() == WhetherTypeEnum.YES.getName()) {
                    return AjaxResult.error("操作失败,对方已禁用视频通话");
                }
                if (type.intValue() == AppCommunicateTelephoneTypeEnums.TYPE1.getId() && receiveUserCommunicateTelephoneConfig.getIsBannedVoice() == WhetherTypeEnum.YES.getName()) {
                    return AjaxResult.error("操作失败,对方已禁用语音通话");
                }

                // 判断当前用户有没有拉黑对方
                AppBlacklist blacklistByUser = appBlacklistMapper.selectAppBlacklistByUserIdAndToUserId(userId, receiveUserId);
                if (null != blacklistByUser) {
                    return AjaxResult.error("操作失败,您已将对方加入黑名单");
                }

                // 判断对方有没有拉黑当前用户
                AppBlacklist blacklistByReceiveUser = appBlacklistMapper.selectAppBlacklistByUserIdAndToUserId(receiveUserId, userId);
                if (null != blacklistByReceiveUser) {
                    return AjaxResult.error("操作失败,对方已将您加入黑名单");
                }

                // 判断接收用户是否存在
                AppUserEntity receiveUser = appUserMapper.selectAppUserById(receiveUserId);
                if (null == receiveUser) {
                    return AjaxResult.error("操作失败,对方账号不存在");
                }

                // 新增业务规则：女性用户发起对男性用户的视频通话需要先发送至少3条私信，且男用户至少回复过1条消息
                if (type.intValue() == AppCommunicateTelephoneTypeEnums.TYPE2.getId()) {
                    // 检查是否是女性发起者向男性接收者的视频通话
                    if (user.getSex() != null && user.getSex() == 1 && // 发起者是女性 (1女)
                        receiveUser.getSex() != null && receiveUser.getSex() == 0) { // 接收者是男性 (0男)
                        
                        // 查询女性用户向男性用户发送的私信数量（包括已删除的）
                        int femaleToMaleMessageCount = appPrivateLetterListMapper.countPrivateMessagesBetweenUsers(userId, receiveUserId);
                        
                        // 查询男性用户向女性用户发送的回复消息数量（包括已删除的）
                        int maleToFemaleMessageCount = appPrivateLetterListMapper.countPrivateMessagesBetweenUsers(receiveUserId, userId);
                        
                        // 验证女用户发送消息数量
                        if (femaleToMaleMessageCount < 3) {
                            return AjaxResult.error("需要先向对方发送至少3条私信才能发起视频通话");
                        }
                        
                        // 验证男用户回复消息数量
                        if (maleToFemaleMessageCount < 1) {
                            return AjaxResult.error("对方需要至少回复过1条消息才能发起视频通话");
                        }
                    }
                }
                // if (receiveUser.getIsOnline().equals(WhetherTypeEnum.NO.getName())){
                //
                //     return AjaxResult.error("对方用户不在线,无法拨打电话");
                // }

                AppUserStatusTypeEnums userStatusTypeEnums = AppUserStatusTypeEnums.getEnum(receiveUser.getUserStatus()
                        .intValue());
                if (null == userStatusTypeEnums) {
                    return AjaxResult.error("操作失败,对方账号状态异常");
                }
                if (userStatusTypeEnums.getCode() != AppUserStatusTypeEnums.TYPE1.getCode()) {
                    return AjaxResult.error("操作失败,对方账号" + userStatusTypeEnums.getInfo());
                }

                // 每分钟消费的金币
                BigDecimal minutesGold;
                
                // 判断是否为异性通话，如果是异性通话，使用女性的通话配置
                AppUserCommunicateTelephoneConfig feeConfig;
                if (user.getSex() != null && receiveUser.getSex() != null 
                    && !user.getSex().equals(receiveUser.getSex())) {
                    // 异性通话，使用女性的通话配置
                    if (user.getSex() == 1) { // 发起者是女性
                        feeConfig = appUserCommunicateTelephoneConfigService.getUserCommunicateTelephoneConfigByUserId(userId);
                    } else { // 接收者是女性
                        feeConfig = receiveUserCommunicateTelephoneConfig;
                    }
                } else {
                    // 同性通话，使用接收者的通话配置
                    feeConfig = receiveUserCommunicateTelephoneConfig;
                }
                
                if (type.intValue() == AppCommunicateTelephoneTypeEnums.TYPE1.getId()) {
                    if (receiveUserCommunicateTelephoneConfig.getIsEnableVoice() == WhetherTypeEnum.NO.getName()) {
                        return AjaxResult.error("对方未开启语音接听");
                    }
                    minutesGold = feeConfig.getVoiceMinutesGold();
                } else if (type.intValue() == AppCommunicateTelephoneTypeEnums.TYPE2.getId()) {
                    if (receiveUserCommunicateTelephoneConfig.getIsEnableVideo() == WhetherTypeEnum.NO.getName()) {
                        return AjaxResult.error("对方未开启视频接听");
                    }
                    minutesGold = feeConfig.getVideoMinutesGold();
                } else if (type.intValue() == AppCommunicateTelephoneTypeEnums.TYPE3.getId()){
                    if (receiveUserCommunicateTelephoneConfig.getIsEnableVoice() == WhetherTypeEnum.NO.getName()) {
                        return AjaxResult.error("对方未开启语音接听");
                    }
                    minutesGold = new BigDecimal(10);
                } else {
                    if (receiveUserCommunicateTelephoneConfig.getIsEnableVideo() == WhetherTypeEnum.NO.getName()) {
                        return AjaxResult.error("对方未开启视频接听");
                    }
                    minutesGold = new BigDecimal(30);
                }

                // 确定谁应该付费：男给女打，女给男打，都扣男的钱；同性之间，扣发起用户的钱
                AppUserEntity payingUser;
                if (user.getSex() != null && receiveUser.getSex() != null 
                    && user.getSex() != receiveUser.getSex()) {
                    // 异性之间的通话，扣男性的钱
                    if (user.getSex() == 0) { // 男性发起
                        payingUser = user; // 扣男性的钱
                    } else { // 女性发起
                        payingUser = receiveUser; // 扣男性的钱
                    }
                } else {
                    // 同性之间的通话，扣发起用户的钱
                    payingUser = user;
                }
                
                // 移除视频卡结算逻辑，统一使用金币结算
                // 检查付费用户的金币余额
                // 判断付费用户的余额
                if (payingUser.getGoldBalance().compareTo(new BigDecimal("0.00")) < 0 && payingUser.getIsFreeVoice().equals(1)) {
                    if (payingUser.getId().equals(userId)) {
                        return AjaxResult.error(HttpStatus.THE_COST_IS_INSUFFICIENT, "账户欠费");
                    } else {
                        return AjaxResult.error(HttpStatus.THE_COST_IS_INSUFFICIENT, "对方金币不足");
                    }
                }
                if (payingUser.getGoldBalance().compareTo(minutesGold) < 0 && payingUser.getIsFreeVoice().equals(1)) {
                    if (payingUser.getId().equals(userId)) {
                        return AjaxResult.error(HttpStatus.THE_COST_IS_INSUFFICIENT, "金币余额不足");
                    } else {
                        return AjaxResult.error(HttpStatus.THE_COST_IS_INSUFFICIENT, "对方金币不足");
                    }
                }

                // 新增聊天关系,不存在,就新增
                appPrivateLetterListService.addChatRelation(userId, receiveUserId, false);

                // 新增通话记录入库
                AppCommunicateTelephoneRecords communicateTelephoneRecords = new AppCommunicateTelephoneRecords();
                communicateTelephoneRecords.setCreateTime(new Date());
                communicateTelephoneRecords.setInitiateUserId(userId);
                communicateTelephoneRecords.setReceiveUserId(receiveUserId);
                communicateTelephoneRecords.setType(type.intValue());
                // 移除视频卡设置，统一使用金币结算
                communicateTelephoneRecords.setStatus(AppCommunicateTelephoneStatusTypeEnums.TYPE0.getId());// 状态为呼叫中
                communicateTelephoneRecords.setConsumptionGold(minutesGold);
                appCommunicateTelephoneRecordsMapper.insert(communicateTelephoneRecords);

                AppPrivateLetterMsgTypeEnums msgTypeEnums = AppPrivateLetterMsgTypeEnums.getCallType(type.intValue());
                // 插入一个缓存，用于邀请超时监听
                redisCache.setCacheObject(RedisKeyConsts.INITIATE_COMMUNICATE_TELEPHONE_TIME_OUT_LISTENING_KEY + communicateTelephoneRecords.getId(), communicateTelephoneRecords.getId(), RedisKeyConsts.initTelTimeout, TimeUnit.SECONDS);
                // ws通知对方接听语音通话
                Map<String, Object> result = new HashMap<>();
                result.put("pushType", AppPrivateLetterMsgTypeEnums.TYPE27.getId());
                result.put("title", AppPrivateLetterMsgTypeEnums.TYPE27.getDesc());
                result.put("data", communicateTelephoneRecords);
                privateLetterWebSocket.sendCommonMsg(user.getId().toString(), result);

                result.put("pushType", msgTypeEnums.getId());
                result.put("title", msgTypeEnums.getDesc());
                AppUserInfoVo presentUserInfo = appUserService.getPresentUserInfo(user);
                result.put("userInfo", presentUserInfo);

                // result.put("receiveUser", receiveUserInfo);

                privateLetterWebSocket.sendCommonMsg(receiveUserId.toString(), result);
                appAliPushUtil.pushVoiceCallNotification(receiveUserId.toString(), 1, communicateTelephoneRecords.getId());


                AppUserInfoVo receiveUserInfo = appUserService.getPresentUserInfo(receiveUser);
                Map<String, Object> data = new HashMap<>();
                data.put("initiateUserId", userId);// 发起用户id
                data.put("receiveUserId", receiveUserId);// 接收用户id
                data.put("type", type);// 类型
                data.put("status", AppCommunicateTelephoneStatusTypeEnums.TYPE0.getId());// 状态
                data.put("communicateTelephoneId", communicateTelephoneRecords.getId());// 通话id
                data.put("receiveUser", receiveUserInfo);// 对方用户信息


                return AjaxResult.success(data);
            } catch (Exception e) {
                log.error("发起通话异常", e);
                return AjaxResult.error(e.getMessage());
            } finally {
                secondLock.unlock();
            }
        } finally {
            firstLock.unlock();
        }
    }

    /**
     * 是否使用视频卡结算
     * <AUTHOR>
     * @date 2025/5/26 上午10:10
     */
    public boolean useVideoCard(Long userId, int type) {
        // 通话类型必须是视频 并且用户拥有足够的视频卡
        if (type == AppCommunicateTelephoneTypeEnums.TYPE2.getId() || type == AppCommunicateTelephoneTypeEnums.TYPE4.getId()) {
            // 获取用户可用视频卡总数
            int userVideoCardCount = appUserVideoCardMapper.getUserVideoCardCount(userId);
            return userVideoCardCount > 0;
        }
        return false;
    }

    /**
     * 取消发起通话(状态为0才能调用)
     *
     * @param communicateTelephoneId 通讯电话号码
     * @param userId                 用户id
     * @return {@link AjaxResult }
     */
    public AjaxResult cancelInitiateCommunicateTelephone(Long communicateTelephoneId, Long userId) {
        if (null == communicateTelephoneId || communicateTelephoneId.intValue() < 1) {
            return AjaxResult.error("参数【communicateTelephoneId】为空");
        }

        RLock lock = redisson.getLock("app:cancelInitiateCommunicateTelephone:" + communicateTelephoneId);
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(60, TimeUnit.SECONDS);
        // 查询通话记录
        AppCommunicateTelephoneRecords communicateTelephoneRecords = appCommunicateTelephoneRecordsMapper.selectAppCommunicateTelephoneRecordsById(communicateTelephoneId);
        if (null == communicateTelephoneRecords) {
            lock.unlock();
            return AjaxResult.error("通话记录不存在");
        }
        if (!communicateTelephoneRecords.getInitiateUserId().equals(userId)) {
            lock.unlock();
            return AjaxResult.error("非发起用户,无法操作");
        }
        if (communicateTelephoneRecords.getStatus() == AppCommunicateTelephoneStatusTypeEnums.TYPE3.getId()) {
            lock.unlock();
            return AjaxResult.success("通话已经结束");
        }
        if (communicateTelephoneRecords.getStatus() != AppCommunicateTelephoneStatusTypeEnums.TYPE0.getId()) {
            lock.unlock();
            return AjaxResult.error("当前状态不可操作");
        }

        Date time = new Date();

        // 更新通话记录
        AppCommunicateTelephoneRecords communicateTelephoneRecordsUpd = new AppCommunicateTelephoneRecords();
        communicateTelephoneRecordsUpd.setId(communicateTelephoneId);

        // 根据通话类型设置未接通的状态
        int callType = communicateTelephoneRecords.getType();
        int newStatus;
        if (callType == AppCommunicateTelephoneTypeEnums.TYPE2.getId() || callType == AppCommunicateTelephoneTypeEnums.TYPE4.getId()) {
            // 视频通话，设置为未接视频来电
            newStatus = AppCommunicateTelephoneStatusTypeEnums.TYPE4.getId();
        } else {
            // 语音通话，设置为未接语音来电
            newStatus = AppCommunicateTelephoneStatusTypeEnums.TYPE5.getId();
        }
        communicateTelephoneRecordsUpd.setStatus(newStatus);
        communicateTelephoneRecordsUpd.setHangUpTime(time);
        communicateTelephoneRecordsUpd.setHangUpUserId(userId);
        communicateTelephoneRecordsUpd.setUpdateTime(time);
        appCommunicateTelephoneRecordsMapper.updateAppCommunicateTelephoneRecords(communicateTelephoneRecordsUpd);
        try {
            // 删除邀请超时监听
            redisCache.deleteObject(RedisKeyConsts.INITIATE_COMMUNICATE_TELEPHONE_TIME_OUT_LISTENING_KEY + communicateTelephoneId);
            Map<String, Object> result = new HashMap<>();
            result.put("pushType", AppPrivateLetterMsgTypeEnums.TYPE31.getId());
            result.put("title", AppPrivateLetterMsgTypeEnums.TYPE31.getDesc());
            result.put("communicateTelephoneId", communicateTelephoneId);

            privateLetterWebSocket.sendCommonMsg(communicateTelephoneRecords.getReceiveUserId().toString(), result);
            privateLetterWebSocket.sendCommonMsg(communicateTelephoneRecords.getInitiateUserId().toString(), result);
            appAliPushUtil.pushVoiceCallNotification(communicateTelephoneRecords.getReceiveUserId()
                    .toString(), 2, communicateTelephoneId);

            // 根据通话类型获取对应的状态枚举
            AppCommunicateTelephoneStatusTypeEnums statusEnum = AppCommunicateTelephoneStatusTypeEnums.getEnum(newStatus);
            AppChattingRecords appChattingRecords = saveVoiceRecord(communicateTelephoneRecords, 0, statusEnum);
            appChattingRecordsService.insertAppChattingRecords(appChattingRecords);

        } catch (Exception e) {
        }

        appPrivateLetterListService.addChatRelation(communicateTelephoneRecords.getInitiateUserId(), communicateTelephoneRecords.getReceiveUserId(), true);
        Map<String, Object> result = new HashMap<>();
        result.put("initiateUserId", communicateTelephoneRecords.getInitiateUserId());// 发起用户id
        result.put("receiveUserId", communicateTelephoneRecords.getReceiveUserId());// 接收用户id
        result.put("type", communicateTelephoneRecords.getType());// 类型
        result.put("status", newStatus);// 状态
        result.put("communicateTelephoneId", communicateTelephoneId);// 通话id
        result.put("hangUpTime", communicateTelephoneRecordsUpd.getHangUpTime());// 挂断时间
        result.put("hangUpUserId", communicateTelephoneRecordsUpd.getHangUpUserId());// 操作挂断的用户id

        lock.unlock();

        return AjaxResult.success(result);

    }

    /**
     * 获取通话详情
     */
    public AjaxResult getCommunicateTelephoneDetails(Long communicateTelephoneId, Long userId) {
        if (null == communicateTelephoneId || communicateTelephoneId.intValue() < 1) {
            return AjaxResult.error("参数【communicateTelephoneId】为空");
        }
        AppCommunicateTelephoneRecords communicateTelephoneRecords = appCommunicateTelephoneRecordsMapper.selectAppCommunicateTelephoneRecordsById(communicateTelephoneId);
        if (null == communicateTelephoneRecords) {
            return AjaxResult.error("通话记录不存在");
        }
        if (!communicateTelephoneRecords.getReceiveUserId().equals(userId) && !communicateTelephoneRecords.getInitiateUserId()
                .equals(userId)) {
            return AjaxResult.error("通话记录不存在");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("initiateUserId", communicateTelephoneRecords.getInitiateUserId());// 发起用户id
        result.put("receiveUserId", communicateTelephoneRecords.getReceiveUserId());// 接收用户id
        result.put("type", communicateTelephoneRecords.getType());// 类型
        result.put("status", communicateTelephoneRecords.getStatus());// 状态
        result.put("communicateTelephoneId", communicateTelephoneId);// 通话id
        result.put("connectTime", communicateTelephoneRecords.getConnectTime());// 接通时间,状态1,3返回
        result.put("hangUpTime", communicateTelephoneRecords.getHangUpTime());// 挂断时间,状态为2,3返回
        result.put("hangUpUserId", communicateTelephoneRecords.getHangUpUserId());// 操作挂断的用户id,状态为2,3返回
        result.put("consumptionGold", communicateTelephoneRecords.getConsumptionGold());// 消耗金币

        return AjaxResult.success(result);
    }

    /**
     * 结束通话(状态为1才能调用)
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult finishCommunicateTelephone(Long communicateTelephoneId, Long userId) {
        if (null == communicateTelephoneId || communicateTelephoneId.intValue() < 1) {
            return AjaxResult.error("参数【communicateTelephoneId】为空");
        }

        // 查询通话记录
        AppCommunicateTelephoneRecords communicateTelephoneRecords = appCommunicateTelephoneRecordsMapper.selectAppCommunicateTelephoneRecordsById(communicateTelephoneId);
        if (null == communicateTelephoneRecords) {
            return AjaxResult.error("通话记录不存在");
        }
        if (!communicateTelephoneRecords.getReceiveUserId().equals(userId) && !communicateTelephoneRecords.getInitiateUserId()
                .equals(userId)) {
            return AjaxResult.error("通话记录不存在");
        }
        if (communicateTelephoneRecords.getStatus() == AppCommunicateTelephoneStatusTypeEnums.TYPE3.getId()) {
            return AjaxResult.success("已经结束通话");
        }
        if (communicateTelephoneRecords.getStatus() != AppCommunicateTelephoneStatusTypeEnums.TYPE1.getId()) {
            return AjaxResult.error("当前状态不可操作");
        }
        log.warn("排查异常挂断电话业务线（手动挂断电话），通话id：{} 挂断电话用户id：{}", communicateTelephoneId, userId);
        // 获取挂断电话结算
        TelResult telResult = appCommunicateTelephoneRecordsService.getTelResult(userId, communicateTelephoneRecords);
        
        // 处理可能的null返回值（表示另一个线程正在处理该通话记录）
        if (telResult == null) {
            return AjaxResult.frequent("通话正在被其他进程结束，请稍后再试");
        }

        // 获取通话双方用户信息
        AppUserEntity initiateUser = appUserMapper.selectAppUserById(communicateTelephoneRecords.getInitiateUserId());
        AppUserEntity receiveUser = appUserMapper.selectAppUserById(communicateTelephoneRecords.getReceiveUserId());
        
        if (initiateUser == null || receiveUser == null) {
            return AjaxResult.error("用户信息不存在");
        }

        // 重新定义逻辑用户角色
        Long logicalInitiateUserId; // 逻辑发起用户（扣费方）
        Long logicalReceiveUserId;  // 逻辑接收用户（收益方）
        
        // 判断是否为异性通话
        boolean isHeterosewualCall = initiateUser.getSex() != receiveUser.getSex();
        
        if (isHeterosewualCall) {
            // 异性通话：男性为扣费方，女性为收益方
            if (initiateUser.getSex() == AppUserSexTypeEnums.TYPE0.getId()) {
                // 实际发起用户是男性
                logicalInitiateUserId = communicateTelephoneRecords.getInitiateUserId();
                logicalReceiveUserId = communicateTelephoneRecords.getReceiveUserId();
            } else {
                // 实际发起用户是女性，需要调换角色
                logicalInitiateUserId = communicateTelephoneRecords.getReceiveUserId();
                logicalReceiveUserId = communicateTelephoneRecords.getInitiateUserId();
            }
        } else {
            // 同性通话：保持原有逻辑
            logicalInitiateUserId = communicateTelephoneRecords.getInitiateUserId();
            logicalReceiveUserId = communicateTelephoneRecords.getReceiveUserId();
        }

        // 构造扣费方的WebSocket消息
        Map<String, Object> chargeWsBody = new HashMap<>();
        chargeWsBody.put("pushType", AppPrivateLetterMsgTypeEnums.TYPE26.getId());
        chargeWsBody.put("title", AppPrivateLetterMsgTypeEnums.TYPE26.getDesc());
        chargeWsBody.put("communicateTelephoneId", communicateTelephoneId);
        chargeWsBody.put("consumptionGold", telResult.getCommunicateTelephoneRecordsUpd().getConsumptionGold().negate());
        chargeWsBody.put("amountOfIncome", BigDecimal.ZERO); // 扣费方收益为0
        chargeWsBody.put("initiateUserId", logicalInitiateUserId);
        chargeWsBody.put("receiveUserId", logicalReceiveUserId);
        chargeWsBody.put("hangUpTime", telResult.communicateTelephoneRecordsUpd.getHangUpTime());
        chargeWsBody.put("hangUpUserId", telResult.communicateTelephoneRecordsUpd.getHangUpUserId());
        chargeWsBody.put("type", communicateTelephoneRecords.getType());
        chargeWsBody.put("status", AppCommunicateTelephoneStatusTypeEnums.TYPE3.getId());

        // 构造收益方的WebSocket消息
        Map<String, Object> incomeWsBody = new HashMap<>();
        incomeWsBody.put("pushType", AppPrivateLetterMsgTypeEnums.TYPE26.getId());
        incomeWsBody.put("title", AppPrivateLetterMsgTypeEnums.TYPE26.getDesc());
        incomeWsBody.put("communicateTelephoneId", communicateTelephoneId);
        incomeWsBody.put("consumptionGold", BigDecimal.ZERO); // 收益方扣费为0
        incomeWsBody.put("amountOfIncome", telResult.amountOfIncome);
        incomeWsBody.put("initiateUserId", logicalInitiateUserId);
        incomeWsBody.put("receiveUserId", logicalReceiveUserId);
        incomeWsBody.put("hangUpTime", telResult.communicateTelephoneRecordsUpd.getHangUpTime());
        incomeWsBody.put("hangUpUserId", telResult.communicateTelephoneRecordsUpd.getHangUpUserId());
        incomeWsBody.put("type", communicateTelephoneRecords.getType());
        incomeWsBody.put("status", AppCommunicateTelephoneStatusTypeEnums.TYPE3.getId());

        // 发送WebSocket消息
        privateLetterWebSocket.sendCommonMsgAsyc(logicalInitiateUserId.toString(), chargeWsBody);
        privateLetterWebSocket.sendCommonMsgAsyc(logicalReceiveUserId.toString(), incomeWsBody);

        // 删除redis中的通话心跳监听
        String key = RedisKeyConsts.XINXUAN_1V1_VOICE_KEEP_KEY + communicateTelephoneId + ":" + communicateTelephoneRecords.getInitiateUserId();
        redisCache.deleteObject(key);

        // 删除redis中的标记
        redisCache.delLike(RedisKeyConsts.APP_HANG_UP + communicateTelephoneRecords.getInitiateUserId());

        return AjaxResult.success();

    }

    /**
     * 获取通话结束后的信息
     *
     * <AUTHOR>
     * @date 2025/4/8 下午7:31
     */
    public AjaxResult getFinishCommunicateTelephoneInfo(Long communicateTelephoneId, Long userId) {
        if (null == communicateTelephoneId || communicateTelephoneId.intValue() < 1) {
            return AjaxResult.error("通话记录id为空");
        }
        
        // 开始获取通话结束信息
        log.info("开始获取通话结束信息: 通话ID[{}] 查询用户ID[{}]", communicateTelephoneId, userId);

        // 查询通话记录
        AppCommunicateTelephoneRecords communicateTelephoneRecords = appCommunicateTelephoneRecordsMapper.selectAppCommunicateTelephoneRecordsById(communicateTelephoneId);
        if (null == communicateTelephoneRecords) {
            log.warn("通话记录不存在: 通话ID[{}]", communicateTelephoneId);
            return AjaxResult.error("通话记录不存在");
        }
        
        // 通话记录基本信息
        log.info("通话记录信息: 通话ID[{}] 发起用户ID[{}] 接收用户ID[{}] 状态[{}] 消费金币[{}]", 
                communicateTelephoneRecords.getId(), 
                communicateTelephoneRecords.getInitiateUserId(), 
                communicateTelephoneRecords.getReceiveUserId(), 
                communicateTelephoneRecords.getStatus(), 
                communicateTelephoneRecords.getConsumptionGold());

        // 如果当前用户不是发起用户和接收用户，则返回错误信息
        if (!communicateTelephoneRecords.getReceiveUserId().equals(userId) && !communicateTelephoneRecords.getInitiateUserId()
                .equals(userId)) {
            log.warn("用户没有权限查看此通话记录: 通话ID[{}] 查询用户ID[{}] 发起用户ID[{}] 接收用户ID[{}]", 
                    communicateTelephoneId, userId, communicateTelephoneRecords.getInitiateUserId(), communicateTelephoneRecords.getReceiveUserId());
            return AjaxResult.error("通话记录不存在");
        }

        // 获取通话双方用户信息
        AppUserEntity initiateUser = appUserMapper.selectAppUserById(communicateTelephoneRecords.getInitiateUserId());
        AppUserEntity receiveUser = appUserMapper.selectAppUserById(communicateTelephoneRecords.getReceiveUserId());
        
        if (initiateUser == null || receiveUser == null) {
            log.error("用户信息不存在: 通话ID[{}] 发起用户ID[{}] 接收用户ID[{}] 发起用户信息[{}] 接收用户信息[{}]", 
                    communicateTelephoneRecords.getId(), 
                    communicateTelephoneRecords.getInitiateUserId(), 
                    communicateTelephoneRecords.getReceiveUserId(), 
                    initiateUser != null ? "存在" : "不存在", 
                    receiveUser != null ? "存在" : "不存在");
            return AjaxResult.error("用户信息不存在");
        }

        // 重新定义逻辑用户角色（与finishCommunicateTelephone方法保持一致）
        Long logicalInitiateUserId; // 逻辑发起用户（扣费方）
        Long logicalReceiveUserId;  // 逻辑接收用户（收益方）
        
        // 判断是否为异性通话
        boolean isHeterosewualCall = initiateUser.getSex() != receiveUser.getSex();
        
        if (isHeterosewualCall) {
            // 异性通话：男性为扣费方，女性为收益方
            if (initiateUser.getSex() == AppUserSexTypeEnums.TYPE0.getId()) {
                // 实际发起用户是男性
                logicalInitiateUserId = communicateTelephoneRecords.getInitiateUserId();
                logicalReceiveUserId = communicateTelephoneRecords.getReceiveUserId();
            } else {
                // 实际发起用户是女性，需要调换角色
                logicalInitiateUserId = communicateTelephoneRecords.getReceiveUserId();
                logicalReceiveUserId = communicateTelephoneRecords.getInitiateUserId();
            }
        } else {
            // 同性通话：保持原有逻辑
            logicalInitiateUserId = communicateTelephoneRecords.getInitiateUserId();
            logicalReceiveUserId = communicateTelephoneRecords.getReceiveUserId();
        }

        // 获取通信双方的头像（使用逻辑用户ID）
        String initiateUserAvatar = appUserMapper.getUserHeadPortrait(logicalInitiateUserId);
        String receiveUserAvatar = appUserMapper.getUserHeadPortrait(logicalReceiveUserId);

        Map<String, Object> map = new HashMap<>();
        map.put("pushType", AppPrivateLetterMsgTypeEnums.TYPE26.getId());
        map.put("title", AppPrivateLetterMsgTypeEnums.TYPE26.getDesc());
        map.put("content", "正常通话挂断电话");
        map.put("communicateTelephoneId", communicateTelephoneId);
        
        // 通话费用：根据当前用户角色返回相应的金额
        if (communicateTelephoneRecords.getConnectTime() != null && communicateTelephoneRecords.getHangUpTime() != null) {
            if (userId.equals(logicalInitiateUserId)) {
                // 当前用户是扣费方，显示扣费金额
                map.put("consumptionGold", communicateTelephoneRecords.getConsumptionGold().negate());
                // 扣费金额计算
                log.info("当前用户为扣费方: 通话ID[{}] 扣费用户ID[{}] 原消费金币[{}] 显示扣费金额[{}]", 
                        communicateTelephoneRecords.getId(), userId, communicateTelephoneRecords.getConsumptionGold(), communicateTelephoneRecords.getConsumptionGold().negate());
            } else {
                // 当前用户是收益方，扣费为0
                map.put("consumptionGold", BigDecimal.ZERO);
                // 收益方不扣费
                log.info("当前用户为收益方: 通话ID[{}] 收益用户ID[{}] 不扣费", 
                        communicateTelephoneRecords.getId(), userId);
            }
        } else {
            map.put("consumptionGold", BigDecimal.ZERO);
            // 通话未正常结束
            log.info("通话时间不完整: 通话ID[{}] 接通时间[{}] 挂断时间[{}] 不扣费", 
                    communicateTelephoneRecords.getId(), communicateTelephoneRecords.getConnectTime(), communicateTelephoneRecords.getHangUpTime());
        }

        // 移除视频卡相关字段，简化为金币结算
        AppConfig appConfig = appConfigMapper.getAppConfig();
        map.put("useVideoCard", 0);
        map.put("videoCardAmount", 0);

        // 收益计算：根据当前用户角色计算收益
        if (userId.equals(logicalReceiveUserId)) {
            // 当前用户是收益方，计算收益金额
            AppGuildMember appGuildMember = appGuildMemberMapper.getGuildMemberByUserId(userId);
            
            // 收益用户的公会信息
            log.info("收益用户公会信息: 通话ID[{}] 收益用户ID[{}] 公会成员信息[{}] 礼物收益比例[{}]", 
                    communicateTelephoneRecords.getId(), userId, 
                    appGuildMember != null ? "存在" : "不存在", 
                    appGuildMember != null ? appGuildMember.getGiftIncomeScale() : "N/A");
            
            if (appGuildMember != null && appGuildMember.getGiftIncomeScale().compareTo(new BigDecimal("0")) == 0) {
                map.put("isPublicAnchor", 1);
                map.put("amountOfIncome", communicateTelephoneRecords.getConsumptionGold());
                log.info("公会主播金币收益: 通话ID[{}] 消费金币[{}]", 
                        communicateTelephoneRecords.getId(), communicateTelephoneRecords.getConsumptionGold());
            } else {
                map.put("isPublicAnchor", 0);
                
                // 计算本次消耗的金币等于多少人民币（使用逻辑接收用户ID）
                BigDecimal billSumAmountOfMonth = appUserGoldBillService.getBillSumAmonutOfMonth(logicalReceiveUserId, DateUtil.now());
                // 接收人应该采用的通话收益比例
                BigDecimal scale = appGiftIncomeConfigService.getGiftIncomeRate(billSumAmountOfMonth, communicateTelephoneRecords.getConsumptionGold(), AppIntegralScenarioTypeEnums.TYPE1, logicalReceiveUserId);
                
                // 收益比例计算
                log.info("收益比例计算: 通话ID[{}] 逻辑接收用户ID[{}] 月消费金额[{}] 收益比例[{}]", 
                        communicateTelephoneRecords.getId(), logicalReceiveUserId, billSumAmountOfMonth, scale);
                
                // 计算本次收入的钻石=金币*收益比例*金币转钻石比例
                BigDecimal privateAnchorGoldIncome = communicateTelephoneRecords.getConsumptionGold().multiply(scale).multiply(appConfig.getOneGoldEqRmb());
                map.put("amountOfIncome", privateAnchorGoldIncome);
                log.info("私人主播金币收益: 通话ID[{}] 消费金币[{}] 收益比例[{}] 金币转钻石比例[{}] 最终收益[{}]", 
                        communicateTelephoneRecords.getId(), communicateTelephoneRecords.getConsumptionGold(), scale, appConfig.getOneGoldEqRmb(), privateAnchorGoldIncome);
            }
        } else {
            // 当前用户是扣费方，收益为0
            map.put("isPublicAnchor", 0);
            map.put("amountOfIncome", BigDecimal.ZERO);
            // 扣费用户信息
            log.info("当前用户为扣费方: 通话ID[{}] 扣费用户ID[{}] 收益为0", 
                    communicateTelephoneRecords.getId(), userId);
        }

        map.put("initiateUserId", logicalInitiateUserId);// 逻辑发起用户id（扣费方）
        map.put("receiveUserId", logicalReceiveUserId);// 逻辑接收用户id（收益方）
        map.put("connectTime", communicateTelephoneRecords.getConnectTime()); // 接通时间
        map.put("hangUpTime", communicateTelephoneRecords.getHangUpTime());// 挂断时间
        map.put("hangUpUserId", communicateTelephoneRecords.getHangUpUserId());// 操作挂断的用户id
        map.put("type", communicateTelephoneRecords.getType());// 类型
        map.put("status", AppCommunicateTelephoneStatusTypeEnums.TYPE3.getId());// 状态
        map.put("initiateUserAvatar", initiateUserAvatar); // 逻辑发起用户头像
        map.put("receiveUserAvatar", receiveUserAvatar); // 逻辑接收用户头像
        
        // 最终返回结果
        log.info("通话结束信息获取完成: 通话ID[{}] 查询用户ID[{}] 逻辑扣费用户ID[{}] 逻辑收益用户ID[{}] 消费金币[{}] 收益金额[{}]", 
                communicateTelephoneRecords.getId(), userId, logicalInitiateUserId, logicalReceiveUserId, 
                map.get("consumptionGold"), map.get("amountOfIncome"));

        return AjaxResult.success(map);
    }

    /**
     * 处理通话邀请(状态为0才能调用)
     *
     * @param communicateTelephoneId 通话id
     * @param status                 状态:1同意,2拒接
     * @param userId                 用户id
     * @return {@link AjaxResult }
     */
    public AjaxResult handleCommunicateTelephoneInvite(Long communicateTelephoneId, Long status, Long userId) {
        if (null == communicateTelephoneId || communicateTelephoneId.intValue() < 1) {
            return AjaxResult.error("参数【communicateTelephoneId】为空");
        }
        if (null == status) {
            return AjaxResult.error("参数【status】为空");
        }
        if (status.intValue() != AppCommunicateTelephoneStatusTypeEnums.TYPE1.getId() && status.intValue() != AppCommunicateTelephoneStatusTypeEnums.TYPE2.getId()) {
            return AjaxResult.error("参数【status】错误");
        }

        // 改进锁逻辑，使用communicateTelephoneId作为锁的主键
        RLock lock = redisson.getLock("app:handleCommunicateTelephoneInvite:" + communicateTelephoneId);
        try {
            // 尝试获取锁，等待时间更短，避免长时间阻塞
            boolean locked = lock.tryLock(5, 10, TimeUnit.SECONDS);
            if (!locked) {
                return AjaxResult.frequent("请稍后再试");
            }

            // 查询通话记录
            AppCommunicateTelephoneRecords communicateTelephoneRecords = appCommunicateTelephoneRecordsMapper.selectAppCommunicateTelephoneRecordsById(communicateTelephoneId);
            if (null == communicateTelephoneRecords) {
                return AjaxResult.error("通话已结束");
            }

            // 检查是否为接收人
            if (!communicateTelephoneRecords.getReceiveUserId().equals(userId)) {
                return AjaxResult.error("您不是通话接收人");
            }

            // 检查通话状态，简化错误提示
            int currentStatus = communicateTelephoneRecords.getStatus();
            if (currentStatus != AppCommunicateTelephoneStatusTypeEnums.TYPE0.getId()) {
                // 不再区分各种状态，统一提示
                String statusDesc = "";

                switch (currentStatus) {
                    case 1: // 通话中
                        statusDesc = "通话已接通";
                        break;
                    case 2: // 拒接
                        statusDesc = "通话已拒绝";
                        break;
                    case 3: // 通话结束
                    case 4: // 未接视频来电
                    case 5: // 未接语音来电
                        statusDesc = "通话已结束";
                        break;
                    default:
                        statusDesc = "通话状态异常";
                        break;
                }

                return AjaxResult.error(statusDesc);
            }

            // 更新通话记录
            AppCommunicateTelephoneRecords communicateTelephoneRecordsUpd = new AppCommunicateTelephoneRecords();
            communicateTelephoneRecordsUpd.setId(communicateTelephoneId);
            communicateTelephoneRecordsUpd.setStatus(status.intValue());
            communicateTelephoneRecordsUpd.setUpdateTime(new Date());

            if (status.intValue() == AppCommunicateTelephoneStatusTypeEnums.TYPE1.getId()) {
                // 接听通话 - 先执行预扣费逻辑
                try {
                    boolean preChargeResult = processPreCharge(communicateTelephoneRecords);
                    if (!preChargeResult) {
                        // 预扣费失败，返回余额不足错误
                        return AjaxResult.error("余额不足，无法接通通话");
                    }
                } catch (Exception e) {
                    log.error("预扣费处理异常", e);
                    return AjaxResult.error("系统异常，请稍后再试");
                }
                
                communicateTelephoneRecordsUpd.setConnectTime(new Date());
                setVoiceRedisKeep(communicateTelephoneId, communicateTelephoneRecords);
            } else {
                // 拒接通话
                communicateTelephoneRecordsUpd.setHangUpTime(new Date());
                communicateTelephoneRecordsUpd.setHangUpUserId(userId);

                // 拒接记录
                AppChattingRecords appChattingRecords = saveVoiceRecord(communicateTelephoneRecords, 1, AppCommunicateTelephoneStatusTypeEnums.TYPE2);
                appChattingRecordsService.insertAppChattingRecords(appChattingRecords);
            }

            // 更新通话记录
            appCommunicateTelephoneRecordsMapper.updateAppCommunicateTelephoneRecords(communicateTelephoneRecordsUpd);

            try {
                // 删除邀请超时监听
                redisCache.deleteObject(RedisKeyConsts.INITIATE_COMMUNICATE_TELEPHONE_TIME_OUT_LISTENING_KEY + communicateTelephoneId);

                // ws通知发起方,对方接听或拒绝通话
                AppPrivateLetterMsgTypeEnums appPrivateLetterMsgTypeEnums = AppPrivateLetterMsgTypeEnums.selectStatus(status);

                Map<String, Object> wsData = new HashMap<>();
                wsData.put("pushType", appPrivateLetterMsgTypeEnums.getId());
                wsData.put("title", appPrivateLetterMsgTypeEnums.getDesc());
                wsData.put("content", communicateTelephoneRecords);
                wsData.put("data", communicateTelephoneRecords);
                wsData.put("initiateUserId", communicateTelephoneRecords.getInitiateUserId());
                wsData.put("receiveUserId", communicateTelephoneRecords.getReceiveUserId());

                // 使用异步发送，避免阻塞主线程
                privateLetterWebSocket.sendCommonMsgAsyc(communicateTelephoneRecords.getInitiateUserId().toString(), wsData);
            } catch (Exception e) {
                log.error("处理通话邀请WS通知异常", e);
                // 不影响主流程，继续执行
            }

            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("initiateUserId", communicateTelephoneRecords.getInitiateUserId());// 发起用户id
            result.put("receiveUserId", communicateTelephoneRecords.getReceiveUserId());// 接收用户id
            result.put("type", communicateTelephoneRecords.getType());// 类型
            result.put("status", status);// 状态
            result.put("communicateTelephoneId", communicateTelephoneId);// 通话id
            result.put("hangUpTime", communicateTelephoneRecordsUpd.getHangUpTime());// 挂断时间,状态2返回
            result.put("hangUpUserId", communicateTelephoneRecordsUpd.getHangUpUserId());// 操作挂断的用户id,状态2返回
            result.put("connectTime", communicateTelephoneRecordsUpd.getConnectTime());// 接通时间,状态1返回

            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("处理通话邀请异常", e);
            return AjaxResult.error("处理通话请求失败");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 添加通话成功心跳监听redis
     *
     * @param communicateTelephoneId      通话id
     * @param communicateTelephoneRecords 沟通电话记录
     */
    private void setVoiceRedisKeep(Long communicateTelephoneId, AppCommunicateTelephoneRecords communicateTelephoneRecords) {
        String redisKey = RedisKeyConsts.XINXUAN_1V1_VOICE_KEEP_KEY + communicateTelephoneId + ":" + communicateTelephoneRecords.getInitiateUserId();
        redisCache.setCacheObject(redisKey, JSONObject.toJSONString(communicateTelephoneRecords), RedisKeyConsts.XINXUAN_1V1_VOICE_KEEP_TIME, TimeUnit.SECONDS);
    }

    /**
     * 处理预扣费逻辑
     * @param communicateTelephoneRecords 通话记录
     * @return true: 扣费成功，false: 余额不足
     */
    private boolean processPreCharge(AppCommunicateTelephoneRecords communicateTelephoneRecords) {
        try {
            // 获取发起用户和接收用户信息
            AppUserEntity initiateUser = appUserMapper.selectAppUserById(communicateTelephoneRecords.getInitiateUserId());
            AppUserEntity receiveUser = appUserMapper.selectAppUserById(communicateTelephoneRecords.getReceiveUserId());
            
            log.info("预扣费处理 - 通话ID[{}] 发起用户ID[{}] 接收用户ID[{}] 发起用户性别[{}] 接收用户性别[{}]", 
                    communicateTelephoneRecords.getId(), 
                    communicateTelephoneRecords.getInitiateUserId(), 
                    communicateTelephoneRecords.getReceiveUserId(), 
                    initiateUser.getSex(), receiveUser.getSex());
            
            // 确定付费用户和费率用户
            AppUserEntity payingUser;
            AppUserEntity rateUser;
            AppUserEntity incomeUser;
            
            if (initiateUser.getSex() != null && receiveUser.getSex() != null 
                && initiateUser.getSex() != receiveUser.getSex()) {
                // 异性通话：扣男性的钱，费率按女性的标准，收益给女性
                if (initiateUser.getSex() == AppUserSexTypeEnums.TYPE0.getId()) { // 发起者是男性
                    payingUser = initiateUser;
                    rateUser = receiveUser;
                    incomeUser = receiveUser;
                } else { // 发起者是女性
                    payingUser = receiveUser;
                    rateUser = initiateUser;
                    incomeUser = initiateUser;
                }
            } else {
                // 同性通话：扣发起用户的钱，按接收用户的费率，收益给接收用户
                payingUser = initiateUser;
                rateUser = receiveUser;
                incomeUser = receiveUser;
            }
            
            // 获取费率用户的通话配置
            AppUserCommunicateTelephoneConfig rateUserConfig = appUserCommunicateTelephoneConfigService.getUserCommunicateTelephoneConfigByUserId(rateUser.getId());
            
            // 计算第一分钟的费用
            BigDecimal firstMinuteFee = getMinutesGold(communicateTelephoneRecords, rateUserConfig);
            
            log.info("预扣费 - 通话ID[{}] 付费用户ID[{}] 费率用户ID[{}] 收益用户ID[{}] 第一分钟费用[{}]", 
                    communicateTelephoneRecords.getId(), payingUser.getId(), rateUser.getId(), 
                    incomeUser.getId(), firstMinuteFee);
            
            // 检查付费用户余额是否充足
            if (payingUser.getGoldBalance().compareTo(firstMinuteFee) < 0) {
                log.warn("预扣费失败 - 通话ID[{}] 付费用户ID[{}] 余额不足，当前余额[{}]，需要[{}]", 
                        communicateTelephoneRecords.getId(), payingUser.getId(), 
                        payingUser.getGoldBalance(), firstMinuteFee);
                return false;
            }
            
            // 扣除付费用户的金币
            appUserMapper.subUserGoldBalance(payingUser.getId(), firstMinuteFee);
            
            // 记录付费用户的金币账单
            AppUserGoldBill userGoldBill = new AppUserGoldBill();
            userGoldBill.setUserId(payingUser.getId());
            if (communicateTelephoneRecords.getType() == AppCommunicateTelephoneTypeEnums.TYPE1.getId()
                    || communicateTelephoneRecords.getType() == AppCommunicateTelephoneTypeEnums.TYPE3.getId()) {
                userGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE3.getId());// 语音通话
            } else {
                userGoldBill.setBillType((long) AppGoldBillTypeEnums.TYPE4.getId());// 视频通话
            }
            userGoldBill.setObjectId(communicateTelephoneRecords.getId());
            userGoldBill.setAmount(firstMinuteFee.negate());// 扣除金额为负数
            userGoldBill.setIsDel(WhetherTypeEnum.NO.getName());
            userGoldBill.setToUserId(incomeUser.getId());
            userGoldBill.setCreateTime(LocalDateTime.now());
            appUserGoldBillMapper.insertAppUserGoldBill(userGoldBill);
            
            // 为收益用户记录钻石收益
            AppConfig appConfig = appConfigMapper.getAppConfig();
            BigDecimal billSumAmountOfMonth = appUserGoldBillService.getBillSumAmonutOfMonth(incomeUser.getId(), DateUtil.now());
            BigDecimal scale = appGiftIncomeConfigService.getGiftIncomeRate(billSumAmountOfMonth, firstMinuteFee, AppIntegralScenarioTypeEnums.TYPE1, incomeUser.getId());
            BigDecimal earningsPoints = firstMinuteFee.multiply(scale).multiply(appConfig.getOneGoldEqRmb());
            
            // 添加收益用户钻石
            appUserMapper.addUserPointsBalance(incomeUser.getId(), earningsPoints);
            
            // 记录收益用户的钻石账单
            AppUserPointsBill receiveUserPointsBill = new AppUserPointsBill();
            receiveUserPointsBill.setUserId(incomeUser.getId());
            receiveUserPointsBill.setCreateTime(new Date());
            if (communicateTelephoneRecords.getType() == AppCommunicateTelephoneTypeEnums.TYPE1.getId()
                    || communicateTelephoneRecords.getType() == AppCommunicateTelephoneTypeEnums.TYPE3.getId()) {
                receiveUserPointsBill.setBillType((long) AppPointsBillTypeEnums.TYPE6.getId());// 语音通话收入
            } else {
                receiveUserPointsBill.setBillType((long) AppPointsBillTypeEnums.TYPE7.getId());// 视频通话收入
            }
            receiveUserPointsBill.setObjectId(communicateTelephoneRecords.getId());
            receiveUserPointsBill.setAmount(earningsPoints);
            receiveUserPointsBill.setTotalAmount(firstMinuteFee);
            receiveUserPointsBill.setIsDel(WhetherTypeEnum.NO.getName());
            receiveUserPointsBill.setRemarksMsg("预扣费第一分钟收益");
            appUserPointsBillMapper.insertAppUserPointsBill(receiveUserPointsBill);
            
            log.info("预扣费成功 - 通话ID[{}] 付费用户ID[{}] 扣除金币[{}] 收益用户ID[{}] 获得钻石[{}]", 
                    communicateTelephoneRecords.getId(), payingUser.getId(), firstMinuteFee, 
                    incomeUser.getId(), earningsPoints);
            
            return true;
            
        } catch (Exception e) {
            log.error("预扣费处理异常 - 通话ID[{}]", communicateTelephoneRecords.getId(), e);
            return false;
        }
    }

    /**
     * 计算每分钟通话费用
     */
    private BigDecimal getMinutesGold(AppCommunicateTelephoneRecords communicateTelephoneRecords, AppUserCommunicateTelephoneConfig rateUserConfig) {
        BigDecimal minutesGold;
        switch (communicateTelephoneRecords.getType()) {
            case 1: // 语音通话
                minutesGold = rateUserConfig.getVoiceMinutesGold();
                break;
            case 2: // 视频通话
                minutesGold = rateUserConfig.getVideoMinutesGold();
                break;
            case 3: // 匹配语音通话
                minutesGold = new BigDecimal("10"); // 固定10金币每分钟
                break;
            default: // 匹配视频通话
                minutesGold = new BigDecimal("30"); // 固定30金币每分钟
                break;
        }
        return minutesGold;
    }

    /**
     * 通话结束后的:推荐列表
     *
     * <AUTHOR>
     * @date 2025/4/10 上午10:22
     */
    public AjaxResult getRecommendList(Long phoneCallId, AppUserEntity loginUser, Integer page, Integer size) {
        Long sex = loginUser.getSex().equals(0) ? 1L : 0L;
        return getGuildMemberOnlineList(sex, loginUser, null, null, 1, page, size);
    }

    /**
     * 获取首页在线列表主播
     *
     * @param sex       性别
     * @param loginUser 用户
     * @param age       年龄
     * @param label     标签
     * @param tab       HomeTabEnums:1视频,2语音,3新人4附近
     * @return {@link AjaxResult }
     */
    public AjaxResult getGuildMemberOnlineList(Long sex, AppUserEntity loginUser, String age, String label, Integer tab, Integer page, Integer size) {

        String age1 = null;
        String age2 = null;

        if (!ObjectUtil.isEmpty(age)) {
            age1 = age.split("-")[0];
            age2 = age.split("-")[1];
        }

        if (ObjectUtil.isEmpty(sex)) {
            sex = loginUser.getSex().equals(0) ? 1L : 0L;
        }
        // 根据tab选项卡排序
        if (ObjectUtil.isEmpty(tab)) {
            tab = 1;
        }
        PageHelper.startPage(page, size);

        // 根据性别查询当前在线的主播
        List<AppUserEntity> homeGuildUserList = appUserMapper.getHomeUserList(sex, loginUser.getId(), age1, age2, label, tab);
        homeGuildUserList.forEach(item -> {
            boolean busy = appCommunicateTelephoneRecordsMapper.isBusy(item.getId());
            item.setIsBusy(busy ? 1 : 0);
        });
        switch (tab) {
            case 1:
                // 视频,isEnableVoice字段设为null
                homeGuildUserList.forEach(item -> item.setIsEnableVoice(null));
                break;
            case 2:
                // 语音,isEnableVideo字段设为null
                homeGuildUserList.forEach(item -> item.setIsEnableVideo(null));
                break;
            case 3:
                // 设置isEnableVoice为null，确保视频优先
                homeGuildUserList.forEach(item -> {
                    if (item.getIsEnableVideo() == 1) {
                        item.setIsEnableVoice(null);
                    }
                });
                break;
            case 4:
                // 计算距离每个用户的经纬度距离
                homeGuildUserList.forEach(item -> {
                    if (null != item.getLongitude() && null != item.getLatitude()) {
                        item.setDistance(RegionUtil.getDistance(Double.parseDouble(loginUser.getLatitude()), Double.parseDouble(loginUser.getLongitude()), Double.parseDouble(item.getLatitude()), Double.parseDouble(item.getLongitude())));
                    } else {
                        item.setDistance(999999.0);
                    }
                });
                
                // 先按距离排序
                homeGuildUserList.sort(Comparator.comparing(AppUserEntity::getDistance));
                
                // 最后设置isEnableVoice为null，确保视频优先
                homeGuildUserList.forEach(item -> {
                    if (item.getIsEnableVideo() == 1) {
                        item.setIsEnableVoice(null);
                    }
                });
                break;
            default:
                break;
        }

        return AjaxResult.success(homeGuildUserList);
    }
    /**
     * 获取匹配通话费用
     * @return
     */
    public AjaxResult getMatchingCallCost() {
        List<SysDictData> dictDataList = redisCache.getCacheObject(CacheConstants.MATCHING_CALL_COST_KEY);
        if (CollectionUtil.isEmpty(dictDataList)) {
            return AjaxResult.error("暂无匹配通话费用");
        }

        Map<String, Object> map = new HashMap<>();
        dictDataList.forEach(item -> {
            if (item.getRemark().equals(String.valueOf(AppCommunicateTelephoneTypeEnums.TYPE3.getId()))) {
                map.put("voice", item.getDictValue());
            }
            if (item.getRemark().equals(String.valueOf(AppCommunicateTelephoneTypeEnums.TYPE4.getId()))) {
                map.put("video", item.getDictValue());
            }
        });
        return AjaxResult.success(map);
    }
    /**
     * 开始匹配
     *
     * @param user     发起通话的用户
     * @param recordId 记录id
     * @return {@link AjaxResult }
     */
    public AjaxResult startMatching(AppUserEntity user, Long recordId, Integer type) {

        RLock lock = redisson.getLock("app:startMatching:" + user.getId());
        if (lock.isLocked()) {
            return AjaxResult.frequent();
        }
        lock.lock(60, TimeUnit.SECONDS);

        // 查询用户信息
        if (user.getUserStatus() != AppUserStatusTypeEnums.TYPE1.getCode()) {
            lock.unlock();
            return AjaxResult.error("您的账号状态异常");
        }

        long otherSex = user.getSex().equals(0) ? 1L : 0L;
        // 挂断上次电话
        if (recordId != null) {
            this.cancelInitiateCommunicateTelephone(recordId, user.getId());
        }

        // 根据性别查询当前在线的公会主播
        List<AppUserEntity> homeGuildUserList = appUserMapper.getHomeGuildUserList(otherSex, user.getId(), null, null, null);

        // 最近几分钟的拨打电话记录
        List<AppCommunicateTelephoneRecords> appCommunicateTelephoneRecords = appCommunicateTelephoneRecordsService.lambdaQuery()
                .eq(AppCommunicateTelephoneRecords::getInitiateUserId, user.getId())
                .ge(AppCommunicateTelephoneRecords::getCreateTime, DateUtil.offsetMinute(new Date(), -1)).list();

        // 正在通话中的记录和呼叫中
        List<AppCommunicateTelephoneRecords> onCallingRecords = appCommunicateTelephoneRecordsService.lambdaQuery()
                .in(AppCommunicateTelephoneRecords::getStatus, 0, 1).list();

        // 将拨打记录中的接收用户id放入set集合中
        Set<Long> receiveUserIdSet = appCommunicateTelephoneRecords.stream().map(AppCommunicateTelephoneRecords::getReceiveUserId)
                .collect(Collectors.toSet());

        // 将主播列表中已经通话过的用户过滤掉
        homeGuildUserList = homeGuildUserList.stream().filter(item -> !receiveUserIdSet.contains(item.getId()))
                .filter(item -> !onCallingRecords.stream().map(AppCommunicateTelephoneRecords::getReceiveUserId)
                        .collect(Collectors.toSet()).contains(item.getId()))
                .collect(Collectors.toList());

        if (homeGuildUserList.isEmpty()) {
            lock.unlock();
            return AjaxResult.error("暂无匹配对象");
        }

        // 从list中随机选择一个元素
        AppUserEntity guildUser = homeGuildUserList.get(new Random().nextInt(homeGuildUserList.size()));

        AjaxResult ajaxResult = this.initiateCommunicateTelephone(guildUser.getId(), (long) type, user);
        lock.unlock();
        return ajaxResult;
    }

    /**
     * 获取过滤条件
     *
     * @return {@link AjaxResult }
     */
    public AjaxResult getFilterCondition() {

        Map<String, Object> result = new HashMap<>();
        result.put("ageConfig", redisCache.getCacheObject("sys_dict:app_user_age_config"));
        result.put("lableConfig", redisCache.getCacheObject("sys_dict:app_user_label_config"));
        result.put("sexConfig", redisCache.getCacheObject("sys_dict:sys_user_sex"));

        return AjaxResult.success(result);
    }

    /**
     * 最近通话记录
     *
     * @param userId 用户id
     * @return {@link AjaxResult }
     */
    public TableDataInfo recentCallLogs(Integer page, Integer size, Long userId) {
        Page p = PageHelperUtils.startPage(page, size, true);
        List<RecentCallLogsVo> result = appCommunicateTelephoneRecordsMapper.getRecentCallLogs(userId);

        result.forEach(item -> {

            // 根据接听时间和挂断时间计算通话时长
            if (item.getConnectTime() != null && item.getHangUpTime() != null) {
                long time = item.getHangUpTime().getTime() - item.getConnectTime().getTime();
                // 转换为时:分:秒
                long hours = time / 3600000;
                long minutes = (time % 3600000) / 60000;
                long seconds = (time % 60000) / 1000;
                item.setTime(String.format("通话时长%02d:%02d:%02d", hours, minutes, seconds));
            }
            item.setTypeDesc(AppCommunicateTelephoneTypeEnums.getEnum(item.getType()).getDesc());
            item.setStatusDesc(AppCommunicateTelephoneStatusTypeEnums.getEnum(item.getStatus()).getDesc());
            // 计算通话电话距离现在的时间是多少分钟
            if (item.getConnectTime() != null) {
                long minutes = (System.currentTimeMillis() - item.getConnectTime().getTime()) / 60000;
                item.setBeforeTime(String.format("%02d:分钟前", minutes));
            } else {
                item.setStatusDesc("未接听");
            }
        });

        return AjaxResult.getDataTable(result, p.getTotal());
    }

    public AjaxResult backgroundMap() {

        Object cacheObject = redisCache.getCacheObject("sys_dict:app_background_map");
        List<SysDictData> result = JSONArray.parseArray(cacheObject.toString(), SysDictData.class);

        // 随机获取一张背景图
        SysDictData sysDictData = result.get(new Random().nextInt(result.size()));
        return AjaxResult.success(sysDictData);

    }

    public R<UnhandledCommunicateVo> getUnhandledCommunicateTelephone(Long userId) {
        // 查询当前用户是否有未处理的通话
        AppCommunicateTelephoneRecords appCommunicateTelephoneRecords = appCommunicateTelephoneRecordsService.lambdaQuery()
                .and(wrapper -> wrapper
                        .eq(AppCommunicateTelephoneRecords::getReceiveUserId, userId)
                        .or()
                        .eq(AppCommunicateTelephoneRecords::getInitiateUserId, userId))
                .in(AppCommunicateTelephoneRecords::getStatus, 0, 1)
                .orderByDesc(AppCommunicateTelephoneRecords::getId)
                .last("limit 1").one();
        if (appCommunicateTelephoneRecords == null) {
            return R.fail(HttpStatus.NO_CONTENT, "暂无未处理的通话");
        }
        final AppUserEntity initiateUser = appUserMapper.selectMaskingAppUserById(appCommunicateTelephoneRecords.getInitiateUserId());
        final AppUserEntity receiveUser = appUserMapper.selectMaskingAppUserById(appCommunicateTelephoneRecords.getReceiveUserId());

        return R.ok(new UnhandledCommunicateVo(appCommunicateTelephoneRecords, initiateUser, receiveUser, userId));
    }

    public AjaxResult setTop(Long privateLetterId) {
        // 参数校验
        if (privateLetterId == null) {
            return AjaxResult.error("私信ID不能为空");
        }

        // 查询当前置顶状态
        AppPrivateLetterList privateLetter = appPrivateLetterListMapper.selectAppPrivateLetterListById(privateLetterId);
        if (privateLetter == null) {
            return AjaxResult.error("私信不存在");
        }

        // 切换置顶状态：如果当前是置顶(1)，则取消置顶(0)；如果当前未置顶(0)，则置顶(1)
        Integer newTopStatus = privateLetter.getIsTop() == 1 ? 0 : 1;

        // 更新置顶状态
        int result = appPrivateLetterListMapper.updateTopStatus(privateLetterId, newTopStatus);

        if (result > 0) {
            return AjaxResult.success("设置成功");
        } else {
            return AjaxResult.error("设置失败");
        }
    }

    /**
     * 设置聊天备注名
     *
     * <AUTHOR>
     * @date 2025/4/14 下午5:05
     */
    public AjaxResult setRemarkName(Long privateLetterId, String remarkName) {
        // 查询当前置顶状态
        AppPrivateLetterList privateLetter = appPrivateLetterListMapper.selectAppPrivateLetterListById(privateLetterId);
        if (privateLetter == null) {
            return AjaxResult.error("私信不存在");
        }

        privateLetter.setRemarkName(remarkName);
        privateLetter.setUpdateTime(new Date());
        appPrivateLetterListMapper.updateById(privateLetter);

        return AjaxResult.success("设置成功");
    }

    /**
     * 一键清除所有聊天记录
     * 只删除当前用户侧的聊天记录，不影响好友侧的聊天记录显示
     * 
     * @param request
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = {Exception.class})
    public AjaxResult clearAllChatHistory(HttpServletRequest request) {
        Long userId = getUserId(request);
        if (userId == null) {
            return AjaxResult.error("用户未登录");
        }
        
        try {
            // 1. 清除聊天记录中当前用户的数据（软删除）
            int chattingRecordsCount = appChattingRecordsMapper.clearUserChattingRecords(userId);
            
            // 2. 物理删除当前用户的私信列表关系
            appPrivateLetterListMapper.deleteByUserId(userId);
            
            log.info("用户{}清除聊天记录完成，清除聊天记录{}条", userId, chattingRecordsCount);
            
            return AjaxResult.success("清除成功");
        } catch (Exception e) {
            log.error("清除聊天记录失败，用户ID：{}", userId, e);
            return AjaxResult.error("清除失败，请稍后重试");
        }
    }

}
